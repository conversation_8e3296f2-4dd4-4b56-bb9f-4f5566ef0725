<?php
require_once 'config.php';
require_once 'api_functions.php';
require_once 'error_log.php';

requireAdmin();

function getCallCosts() {
    $mysqli = getDbConnection();
    $query = "SELECT SUM(cost) as total_cost FROM vapi_calls WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH)";
    $result = $mysqli->query($query);
    $row = $result->fetch_assoc();
    return $row['total_cost'] ?? 0;
}

$callCosts = getCallCosts();

?>
<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Administrátorský panel - Dentibot</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-2xl font-bold mb-4">Administrátorský panel</h1>
        <div class="mt-8">
            <h2 class="text-xl font-semibold mb-4">Náklady na volání</h2>
            <div class="bg-white rounded-lg shadow-md p-6">
                <p class="text-2xl font-bold"><?php echo number_format($callCosts, 2); ?> Kč</p>
                <p class="text-gray-600">Celkové náklady na volání za poslední měsíc</p>
            </div>
        </div>
        <!-- Zde můžete přidat další sekce administrátorského panelu -->
    </div>
</body>
</html>




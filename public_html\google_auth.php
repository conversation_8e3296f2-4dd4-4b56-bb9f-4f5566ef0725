<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once 'config.php';
require_once 'GoogleCalendarSync.php';

// Debug log
error_log('Auth Start - Session ID: ' . session_id());
error_log('Auth Start - User ID: ' . ($_SESSION['user_id'] ?? 'not set'));

// Ensure we have a user session
if (!isset($_SESSION['user_id'])) {
    error_log('No user session found, redirecting to login');
    header('Location: index.php');
    exit;
}

$googleCalendarSync = new GoogleCalendarSync();

// Generate and store state parameter
$_SESSION['auth_state'] = bin2hex(random_bytes(16));
$_SESSION['original_session_id'] = session_id();

error_log('Generated state: ' . $_SESSION['auth_state']);
error_log('Original session ID: ' . $_SESSION['original_session_id']);

$authUrl = $googleCalendarSync->getAuthUrl($_SESSION['auth_state']);

// Store current user data
$_SESSION['auth_user_id'] = $_SESSION['user_id'];

// Ensure session is written before redirect
session_write_close();

header('Location: ' . $authUrl);
exit;



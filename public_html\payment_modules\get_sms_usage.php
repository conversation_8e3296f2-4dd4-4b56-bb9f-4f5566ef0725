<?php
require_once 'config.php';
require_once 'db_connection.php';
require_once 'error_log.php';

// Initialize session and check for errors
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Uživatel nen<PERSON>']);
    exit;
}

$user_id = $_SESSION['user_id'];

// Get user's current subscription plan
try {
    $current_plan = getUserSubscriptionPlan($user_id) ?? 'Pokročilý';
} catch (Exception $e) {
    $current_plan = 'Pokročilý';
    writeErrorLog("Error getting subscription plan: " . $e->getMessage());
}

$smsPrice = 0.75; // Price per SMS in Kč

// Define SMS limit based on the subscription plan
$smsLimit = 2000; // Default value
if ($current_plan === 'Základní') {
    $smsLimit = 1000;
} else if ($current_plan === 'Pokročilý') {
    $smsLimit = 2000;
} else if ($current_plan === 'Prémiový' || $current_plan === 'Enterprise') {
    $smsLimit = 5000;
}

// Get SMS usage statistics
$smsUsage = [];
$totalSmsSent = 0;
try {
    $db = getDbConnection();
    
    // Get total SMS sent from sms_campaigns table
    $stmt = $db->prepare("
        SELECT SUM(total_messages) as total_sent 
        FROM sms_campaigns 
        WHERE user_id = ?
    ");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();
    $totalSmsSent = $row['total_sent'] ?: 0;
    
    // Get recent SMS campaigns for display in the dashboard
    $stmt = $db->prepare("
        SELECT campaign_name, sent_date, total_messages 
        FROM sms_campaigns 
        WHERE user_id = ? 
        ORDER BY sent_date DESC 
        LIMIT 3
    ");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    while ($row = $result->fetch_assoc()) {
        $smsUsage[] = [
            'campaign_name' => $row['campaign_name'],
            'date' => $row['sent_date'],
            'total_sent' => $row['total_messages']
        ];
    }
    $stmt->close();
    $db->close();
    
    // Return the data as JSON
    header('Content-Type: application/json');
    echo json_encode([
        'success' => true,
        'totalSent' => $totalSmsSent,
        'limit' => $smsLimit,
        'recentUsage' => $smsUsage,
        'smsPrice' => $smsPrice
    ]);
    
} catch (Exception $e) {
    writeErrorLog("Error getting SMS usage: " . $e->getMessage());
    
    // Return error response
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'Chyba při načítání dat o využití SMS: ' . $e->getMessage()
    ]);
}
?>


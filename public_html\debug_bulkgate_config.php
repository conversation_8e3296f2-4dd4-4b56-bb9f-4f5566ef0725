<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Definice funkce getDatabaseConnection přímo v tomto souboru
function getDatabaseConnection() {
    static $db = null;
    
    if ($db === null) {
        $db_host = getenv('DB_HOST') ?: 'localhost';
        $db_user = getenv('DB_USER') ?: 'u345712091_dentibot';
        $db_pass = getenv('DB_PASS') ?: 'your_password_here';
        $db_name = getenv('DB_NAME') ?: 'u345712091_dentibot';
        
        $db = new mysqli($db_host, $db_user, $db_pass, $db_name);
        
        if ($db->connect_error) {
            die("Nepodařilo se připojit k databázi: " . $db->connect_error);
        }
        
        $db->set_charset("utf8mb4");
    }
    
    return $db;
}

// Funkce pro zápis do error logu
function writeErrorLog($message, $data = []) {
    $logFile = __DIR__ . '/error_log.txt';
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[$timestamp] $message: " . json_encode($data) . PHP_EOL;
    file_put_contents($logFile, $logMessage, FILE_APPEND);
}

echo "<h1>Přímé vložení BulkGate API konfigurace</h1>";

// Zpracování formuláře
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $application_id = trim($_POST['application_id'] ?? '');
    $application_token = trim($_POST['application_token'] ?? '');
    $sms_price_per_unit = floatval($_POST['sms_price_per_unit'] ?? 0.75);
    
    if (empty($application_id) || empty($application_token)) {
        echo "<div style='color: red; padding: 10px; background-color: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; margin-bottom: 10px;'>
            Application ID a Application Token jsou povinné!
        </div>";
    } else {
        try {
            $db = getDatabaseConnection();
            
            // Kontrola, zda tabulka existuje
            $tableExists = false;
            $checkTableSQL = "SHOW TABLES LIKE 'bulkgate_api_config'";
            $result = $db->query($checkTableSQL);
            if ($result->num_rows > 0) {
                $tableExists = true;
                echo "<div style='color: green; padding: 10px; background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px; margin-bottom: 10px;'>
                    Tabulka bulkgate_api_config existuje.
                </div>";
            } else {
                echo "<div style='color: orange; padding: 10px; background-color: #fff3cd; border: 1px solid #ffeeba; border-radius: 4px; margin-bottom: 10px;'>
                    Tabulka bulkgate_api_config neexistuje. Pokus o vytvoření...
                </div>";
                
                // Vytvoření tabulky
                $createTableSQL = "
                CREATE TABLE `bulkgate_api_config` (
                  `id` int(11) NOT NULL AUTO_INCREMENT,
                  `api_name` varchar(50) NOT NULL,
                  `application_id` varchar(100) NOT NULL,
                  `application_token` varchar(255) NOT NULL,
                  `sms_price_per_unit` decimal(10,2) DEFAULT 0.75,
                  `is_active` tinyint(1) DEFAULT 1,
                  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
                  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE current_timestamp(),
                  PRIMARY KEY (`id`),
                  UNIQUE KEY `api_name` (`api_name`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
                ";
                
                if ($db->query($createTableSQL)) {
                    $tableExists = true;
                    echo "<div style='color: green; padding: 10px; background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px; margin-bottom: 10px;'>
                        Tabulka bulkgate_api_config byla úspěšně vytvořena.
                    </div>";
                } else {
                    echo "<div style='color: red; padding: 10px; background-color: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; margin-bottom: 10px;'>
                        Chyba při vytváření tabulky: " . $db->error . "
                    </div>";
                }
            }
            
            if ($tableExists) {
                // Kontrola, zda existuje záznam pro BulkGate
                $checkRecordSQL = "SELECT id FROM bulkgate_api_config WHERE api_name = 'bulkgate'";
                $result = $db->query($checkRecordSQL);
                $recordExists = $result->num_rows > 0;
                
                if ($recordExists) {
                    echo "<div style='color: blue; padding: 10px; background-color: #d1ecf1; border: 1px solid #bee5eb; border-radius: 4px; margin-bottom: 10px;'>
                        Záznam pro BulkGate API již existuje. Aktualizuji...
                    </div>";
                    
                    // Aktualizace záznamu
                    $updateSQL = "
                    UPDATE bulkgate_api_config 
                    SET application_id = ?, application_token = ?, sms_price_per_unit = ?, is_active = 1
                    WHERE api_name = 'bulkgate'
                    ";
                    
                    $stmt = $db->prepare($updateSQL);
                    if (!$stmt) {
                        throw new Exception("Chyba při přípravě SQL dotazu: " . $db->error);
                    }
                    
                    $stmt->bind_param("ssd", $application_id, $application_token, $sms_price_per_unit);
                    
                    if ($stmt->execute()) {
                        echo "<div style='color: green; padding: 10px; background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px; margin-bottom: 10px;'>
                            Konfigurace BulkGate API byla úspěšně aktualizována.
                        </div>";
                    } else {
                        echo "<div style='color: red; padding: 10px; background-color: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; margin-bottom: 10px;'>
                            Chyba při aktualizaci záznamu: " . $stmt->error . "
                        </div>";
                    }
                    $stmt->close();
                } else {
                    echo "<div style='color: blue; padding: 10px; background-color: #d1ecf1; border: 1px solid #bee5eb; border-radius: 4px; margin-bottom: 10px;'>
                        Záznam pro BulkGate API neexistuje. Vytvářím nový...
                    </div>";
                    
                    // Vložení nového záznamu
                    $insertSQL = "
                    INSERT INTO bulkgate_api_config 
                    (api_name, application_id, application_token, sms_price_per_unit, is_active) 
                    VALUES ('bulkgate', ?, ?, ?, 1)
                    ";
                    
                    $stmt = $db->prepare($insertSQL);
                    if (!$stmt) {
                        throw new Exception("Chyba při přípravě SQL dotazu: " . $db->error);
                    }
                    
                    $stmt->bind_param("ssd", $application_id, $application_token, $sms_price_per_unit);
                    
                    if ($stmt->execute()) {
                        echo "<div style='color: green; padding: 10px; background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px; margin-bottom: 10px;'>
                            Konfigurace BulkGate API byla úspěšně vytvořena.
                        </div>";
                    } else {
                        echo "<div style='color: red; padding: 10px; background-color: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; margin-bottom: 10px;'>
                            Chyba při vkládání záznamu: " . $stmt->error . "
                        </div>";
                    }
                    $stmt->close();
                }
                
                // Kontrola, zda byly změny úspěšně provedeny
                $checkUpdatedSQL = "SELECT * FROM bulkgate_api_config WHERE api_name = 'bulkgate'";
                $result = $db->query($checkUpdatedSQL);
                if ($result->num_rows > 0) {
                    $updatedRecord = $result->fetch_assoc();
                    echo "<div style='color: green; padding: 10px; background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px; margin-bottom: 10px;'>
                        Kontrola: Záznam existuje v databázi.<br>
                        Aktuální hodnoty: Application ID: " . substr($updatedRecord['application_id'], 0, 5) . "..., 
                        Token: " . substr($updatedRecord['application_token'], 0, 5) . "..., 
                        Cena: " . $updatedRecord['sms_price_per_unit'] . "
                    </div>";
                } else {
                    echo "<div style='color: red; padding: 10px; background-color: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; margin-bottom: 10px;'>
                        Záznam nebyl nalezen po pokusu o aktualizaci/vložení!
                    </div>";
                }
            }
            
        } catch (Exception $e) {
            echo "<div style='color: red; padding: 10px; background-color: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; margin-bottom: 10px;'>
                Chyba: " . $e->getMessage() . "
            </div>";
            
            writeErrorLog('BulkGate Config Insert Error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
}
?>

<div style="max-width: 600px; margin: 20px auto; padding: 20px; background-color: #f8f9fa; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
    <form method="POST" action="">
        <div style="margin-bottom: 15px;">
            <label for="application_id" style="display: block; margin-bottom: 5px; font-weight: bold;">Application ID:</label>
            <input type="text" id="application_id" name="application_id" style="width: 100%; padding: 8px; border: 1px solid #ced4da; border-radius: 4px;" required>
        </div>
        
        <div style="margin-bottom: 15px;">
            <label for="application_token" style="display: block; margin-bottom: 5px; font-weight: bold;">Application Token:</label>
            <input type="text" id="application_token" name="application_token" style="width: 100%; padding: 8px; border: 1px solid #ced4da; border-radius: 4px;" required>
        </div>
        
        <div style="margin-bottom: 15px;">
            <label for="sms_price_per_unit" style="display: block; margin-bottom: 5px; font-weight: bold;">Cena za SMS (CZK):</label>
            <input type="number" id="sms_price_per_unit" name="sms_price_per_unit" value="0.75" step="0.01" min="0.01" style="width: 100px; padding: 8px; border: 1px solid #ced4da; border-radius: 4px;" required>
        </div>
        
        <div>
            <button type="submit" style="padding: 10px 15px; background-color: #4a5568; color: white; border: none; border-radius: 4px; cursor: pointer;">
                Uložit konfiguraci
            </button>
        </div>
    </form>
    
    <div style="margin-top: 20px;">
        <a href="settings.php" style="display: inline-block; padding: 10px 15px; background-color: #6c757d; color: white; text-decoration: none; border-radius: 4px;">
            Přejít na nastavení
        </a>
    </div>
</div>


<?php
require_once 'config.php';
require_once 'error_log.php';

function checkDatabaseConfiguration() {
    global $mysqli;
    
    echo "=== Database Configuration Check ===\n\n";
    
    // Check character set and collation
    $result = $mysqli->query("SHOW VARIABLES LIKE '%character%'");
    echo "Character Set Configuration:\n";
    while ($row = $result->fetch_assoc()) {
        echo $row['Variable_name'] . ": " . $row['Value'] . "\n";
    }
    echo "\n";
    
    // Check table configuration
    $result = $mysqli->query("SHOW CREATE TABLE users");
    $row = $result->fetch_assoc();
    echo "Table Configuration:\n";
    echo $row['Create Table'] . "\n\n";
    
    // Check for triggers
    $result = $mysqli->query("SHOW TRIGGERS LIKE 'users'");
    echo "Triggers on users table:\n";
    if ($result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            echo "Trigger: " . $row['Trigger'] . "\n";
            echo "Event: " . $row['Event'] . "\n";
            echo "Statement: " . $row['Statement'] . "\n\n";
        }
    } else {
        echo "No triggers found\n\n";
    }
    
    // Test password hash storage
    $testPassword = "TestPassword123!";
    $hash = password_hash($testPassword, PASSWORD_DEFAULT);
    
    echo "Testing hash storage:\n";
    echo "Original hash: " . $hash . " (length: " . strlen($hash) . ")\n";
    
    $stmt = $mysqli->prepare("INSERT INTO users (username, email, password_hash) VALUES (?, ?, ?)");
    $testUsername = "test_user_" . time();
    $testEmail = $testUsername . "@example.com";
    $stmt->bind_param("sss", $testUsername, $testEmail, $hash);
    $stmt->execute();
    $userId = $mysqli->insert_id;
    $stmt->close();
    
    $stmt = $mysqli->prepare("SELECT password_hash FROM users WHERE id = ?");
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();
    $user = $result->fetch_assoc();
    $stmt->close();
    
    echo "Stored hash: " . $user['password_hash'] . " (length: " . strlen($user['password_hash']) . ")\n";
    echo "Hash verification: " . (password_verify($testPassword, $user['password_hash']) ? "Success" : "Failure") . "\n\n";
    
    // Cleanup test user
    $mysqli->query("DELETE FROM users WHERE id = " . $userId);
    
    // Check MySQL version and configuration
    $result = $mysqli->query("SELECT VERSION()");
    $row = $result->fetch_row();
    echo "MySQL Version: " . $row[0] . "\n";
    
    $result = $mysqli->query("SHOW VARIABLES LIKE 'sql_mode'");
    $row = $result->fetch_assoc();
    echo "SQL Mode: " . $row['Value'] . "\n";
}

// Run the diagnostic
try {
    checkDatabaseConfiguration();
} catch (Exception $e) {
    echo "Error during diagnostic: " . $e->getMessage() . "\n";
    writeErrorLog("Database diagnostic error", ['error' => $e->getMessage()]);
}

$mysqli->close();
?>


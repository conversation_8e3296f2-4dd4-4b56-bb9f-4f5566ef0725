"use client"

import { useState } from "react"
import { usePayment } from "@/components/payment/payment-provider"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { Skeleton } from "@/components/ui/skeleton"
import { CreditCard, Clock, MessageCircle, AlertTriangle } from "lucide-react"

export function SubscriptionOverview() {
  const { subscription, isLoading, cancelSubscription } = usePayment()
  const [showCancelDialog, setShowCancelDialog] = useState(false)

  // Mock usage data
  const usageData = {
    minutes: {
      used: 450,
      total:
        subscription?.planId === "plan_basic"
          ? 500
          : subscription?.planId === "plan_advanced"
            ? 1000
            : subscription?.planId === "plan_premium"
              ? 10000
              : 1000,
    },
    sms: {
      used: 850,
      total:
        subscription?.planId === "plan_basic"
          ? 1000
          : subscription?.planId === "plan_advanced"
            ? 2000
            : subscription?.planId === "plan_premium"
              ? 5000
              : 2000,
    },
  }

  const minutesPercentage = (usageData.minutes.used / usageData.minutes.total) * 100
  const smsPercentage = (usageData.sms.used / usageData.sms.total) * 100

  if (isLoading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Skeleton className="h-40" />
        <Skeleton className="h-40" />
        <Skeleton className="h-40" />
        <Skeleton className="h-40" />
      </div>
    )
  }

  if (!subscription) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Žádné aktivní předplatné</CardTitle>
          <CardDescription>Nemáte žádné aktivní předplatné</CardDescription>
        </CardHeader>
        <CardContent>
          <p>Pro využívání služeb Dentibot si prosím vyberte některý z našich tarifů.</p>
        </CardContent>
        <CardFooter>
          <Button>Zobrazit dostupné tarify</Button>
        </CardFooter>
      </Card>
    )
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("cs-CZ", {
      day: "numeric",
      month: "long",
      year: "numeric",
    })
  }

  return (
    <>
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Aktuální tarif</CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{subscription.planName}</div>
            <p className="text-xs text-muted-foreground">
              {subscription.price} Kč / {subscription.interval === "month" ? "měsíc" : "rok"}
            </p>
            {subscription.cancelAtPeriodEnd && (
              <div className="mt-2 flex items-center text-xs text-amber-500">
                <AlertTriangle className="mr-1 h-3 w-3" />
                <span>Zrušeno k {formatDate(subscription.currentPeriodEnd)}</span>
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Další platba</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatDate(subscription.currentPeriodEnd)}</div>
            <p className="text-xs text-muted-foreground">
              {subscription.cancelAtPeriodEnd ? "Předplatné bude ukončeno" : "Automatické obnovení"}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Využití minut</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {usageData.minutes.used} / {usageData.minutes.total}
            </div>
            <div className="mt-2">
              <Progress value={minutesPercentage} className="h-2" />
            </div>
            <p className="mt-1 text-xs text-muted-foreground">{Math.round(minutesPercentage)}% využito</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Využití SMS</CardTitle>
            <MessageCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {usageData.sms.used} / {usageData.sms.total}
            </div>
            <div className="mt-2">
              <Progress value={smsPercentage} className="h-2" />
            </div>
            <p className="mt-1 text-xs text-muted-foreground">{Math.round(smsPercentage)}% využito</p>
          </CardContent>
        </Card>
      </div>

      {!subscription.cancelAtPeriodEnd && (
        <div className="mt-4 flex justify-end">
          <Button variant="outline" onClick={() => setShowCancelDialog(true)}>
            Zrušit předplatné
          </Button>
        </div>
      )}

      <AlertDialog open={showCancelDialog} onOpenChange={setShowCancelDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Opravdu chcete zrušit předplatné?</AlertDialogTitle>
            <AlertDialogDescription>
              Vaše předplatné bude aktivní do {formatDate(subscription.currentPeriodEnd)}. Po tomto datu již nebudete
              moci využívat služby Dentibot.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Zrušit</AlertDialogCancel>
            <AlertDialogAction
              onClick={async () => {
                await cancelSubscription()
                setShowCancelDialog(false)
              }}
            >
              Zrušit předplatné
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}


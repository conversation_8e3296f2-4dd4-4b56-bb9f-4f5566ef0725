<?php
require_once 'config.php';

$mysqli = getDatabaseConnection();

$clientId = '846453702560-8bpgok9qg093tqd4ehcin3o9vtv9a83a.apps.googleusercontent.com';
$clientSecret = '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';
$redirectUri = 'https://dentibot.eu/google_auth_callback.php'; // Upravte podle vaší konfigurace
$developerKey = ''; // Doplňte váš Developer Key
$projectId = ''; // Doplňte ID vašeho projektu

$stmt = $mysqli->prepare("INSERT INTO google_api_credentials (client_id, client_secret, redirect_uri, developer_key, project_id) VALUES (?, ?, ?, ?, ?)");
$stmt->bind_param("sssss", $clientId, $clientSecret, $redirectUri, $developerKey, $projectId);

if ($stmt->execute()) {
    echo "Google API credentials byly ú<PERSON><PERSON><PERSON><PERSON><PERSON> ulo<PERSON> do databáze.";
} else {
    echo "Chyba při ukládání Google API credentials: " . $stmt->error;
}

$stmt->close();
$mysqli->close();
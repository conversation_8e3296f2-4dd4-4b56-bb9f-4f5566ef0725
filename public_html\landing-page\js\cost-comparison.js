document.addEventListener("DOMContentLoaded", () => {
  const toggleButton = document.getElementById("toggleComparison")
  const receptionistCost = document.getElementById("receptionistCost")
  const receptionistPeriod = document.getElementById("receptionistPeriod")
  const dentibotCost = document.getElementById("dentibotCost")
  const dentibotPeriod = document.getElementById("dentibotPeriod")
  const yearlySavings = document.getElementById("yearlySavings")
  const savingsAmount = document.getElementById("savingsAmount")
  const savingsPercentage = document.getElementById("savingsPercentage")

  let isYearly = false

  toggleButton.addEventListener("click", () => {
    isYearly = !isYearly
    updateComparison()
  })

  function updateComparison() {
    const monthlyReceptionist = 40000
    const monthlyDentibot = 2500
    const yearlyReceptionist = monthlyReceptionist * 12
    const yearlyDentibot = monthlyDentibot * 12
    const yearlySavingsAmount = yearlyReceptionist - yearlyDentibot

    if (isYearly) {
      receptionistCost.textContent = `${yearlyReceptionist.toLocaleString()} Kč`
      receptionistPeriod.textContent = "ročně"
      dentibotCost.textContent = `${yearlyDentibot.toLocaleString()} Kč`
      dentibotPeriod.textContent = "ročně"
      yearlySavings.classList.remove("hidden")
      savingsAmount.textContent = yearlySavingsAmount.toLocaleString()
      savingsPercentage.textContent = ((yearlySavingsAmount / yearlyReceptionist) * 100).toFixed(1)
      toggleButton.textContent = "Zobrazit měsíční srovnání"
    } else {
      receptionistCost.textContent = `${monthlyReceptionist.toLocaleString()} Kč`
      receptionistPeriod.textContent = "měsíčně"
      dentibotCost.textContent = `${monthlyDentibot.toLocaleString()} Kč`
      dentibotPeriod.textContent = "měsíčně"
      yearlySavings.classList.add("hidden")
      toggleButton.textContent = "Zobrazit roční srovnání"
    }
  }
})


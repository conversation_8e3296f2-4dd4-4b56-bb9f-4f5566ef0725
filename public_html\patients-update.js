// Toto je pouze uk<PERSON><PERSON><PERSON>, k<PERSON>u je potřeba přidat/upravit v patients.php
async function saveCellValue(event) {
  const input = event.target
  const patientId = input.dataset.patientId
  const field = input.dataset.field
  const value = input.value.trim() || "-"

  try {
    // Aktualizujeme hodnotu v UI
    const patientIndex = this.patients.findIndex((p) => p.id == patientId)
    if (patientIndex !== -1) {
      this.patients[patientIndex][field] = value
    }

    // Odstraníme editor
    input.remove()
    document.removeEventListener("click", this.handleDocumentClick)

    console.log(`Odesílám aktualizaci: ID=${patientId}, pole=${field}, hodnota=${value}`)

    // Uložíme hodnotu do databáze
    const response = await fetch("seznam_pacientu/update_patient.php", {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: new URLSearchParams({
        id: patientId,
        field: field,
        value: value,
      }),
    })

    const result = await response.json()

    if (result.success) {
      console.log(`Hodnota byla úspěšně uložena (${field}: ${value})`)
      // Obnovíme data po úspěšné aktualizaci
      await this.fetchPatients()
    } else {
      console.error("Chyba při ukládání hodnoty:", result.message)
      alert("Chyba při ukládání hodnoty: " + result.message)
      // Obnovíme data v případě chyby
      await this.fetchPatients()
    }
  } catch (error) {
    console.error("Chyba při ukládání hodnoty:", error)
    alert("Chyba při ukládání hodnoty: " + error.message)
    await this.fetchPatients()
  }
}


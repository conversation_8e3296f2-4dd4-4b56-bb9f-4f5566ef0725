<?php
/**
 * Demo stránka pro testování <PERSON>row integrace s automatickou kartou pacienta
 */

require_once 'config.php';
require_once 'patient_card/BaserowCardManager.php';

// Kontrola přihlášení
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

$message = null;
$error = null;
$card = null;

// Zpracování akcí
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $phone = $_POST['phone'] ?? '';
    
    $manager = new BaserowCardManager();
    
    switch ($action) {
        case 'load_card':
            $result = getBaserowPatientCard($phone);
            if ($result['success']) {
                $card = $result['card'];
                $message = 'Karta pacienta byla úspěšně načtena z Baserow!';
            } else {
                $error = $result['error'];
            }
            break;
            
        case 'add_event':
            $event_type = $_POST['event_type'] ?? 'note';
            $event_data = [
                'title' => $_POST['title'] ?? 'Testovací událost',
                'content' => $_POST['content'] ?? 'Obsah testovací události',
                'created_at' => date('c'),
                'visit_id' => 'visit_' . uniqid()
            ];
            
            $result = addEventToBaserowCard($phone, $event_type, $event_data);
            if ($result['success']) {
                $message = 'Událost byla úspěšně přidána do Baserow karty!';
                // Znovu načti kartu
                $card_result = getBaserowPatientCard($phone);
                if ($card_result['success']) {
                    $card = $card_result['card'];
                }
            } else {
                $error = $result['error'];
            }
            break;
    }
}

$currentPage = 'cgm_documents';
?>

<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Baserow Karta Pacienta - Demo</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
    </style>
</head>
<body class="bg-gray-50">
    <div class="flex min-h-screen">
        <?php include 'sidebar.php'; ?>
        
        <div class="flex-1 ml-64">
            <div class="p-8">
                <!-- Header -->
                <div class="mb-8">
                    <h1 class="text-3xl font-bold text-gray-900">Baserow Karta Pacienta - Demo</h1>
                    <p class="text-gray-600 mt-2">Testování integrace s Baserow databází</p>
                </div>

                <!-- Alerts -->
                <?php if ($error): ?>
                    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                        <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>

                <?php if ($message): ?>
                    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                        <?php echo htmlspecialchars($message); ?>
                    </div>
                <?php endif; ?>

                <!-- Baserow Setup Info -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
                    <h2 class="text-lg font-semibold text-blue-900 mb-4">📋 Potřebné kolonky v Baserow</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-800">
                        <div>
                            <h3 class="font-semibold mb-2">Základní kolonky:</h3>
                            <ul class="space-y-1">
                                <li>• <code>Karta_pacienta_JSON</code> (Long text)</li>
                                <li>• <code>Verze_karty</code> (Number)</li>
                                <li>• <code>Hash_karty</code> (Single line text)</li>
                                <li>• <code>Posledni_aktivita</code> (Date)</li>
                                <li>• <code>Auto_karta_aktivni</code> (Checkbox)</li>
                            </ul>
                        </div>
                        <div>
                            <h3 class="font-semibold mb-2">Statistické kolonky:</h3>
                            <ul class="space-y-1">
                                <li>• <code>Pocet_TST</code> (Number)</li>
                                <li>• <code>Pocet_zaznamu</code> (Number)</li>
                                <li>• <code>Pocet_snimku</code> (Number)</li>
                                <li>• <code>Pocet_vykonu</code> (Number)</li>
                                <li>• <code>Sync_status</code> (Single select)</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- Load Card -->
                    <div class="bg-white rounded-lg shadow-sm border p-6">
                        <h2 class="text-xl font-semibold text-gray-900 mb-4">Načíst kartu pacienta</h2>
                        <form method="POST">
                            <input type="hidden" name="action" value="load_card">
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Telefon pacienta</label>
                                <input type="text" name="phone" value="<?php echo htmlspecialchars($_POST['phone'] ?? '+420123456789'); ?>" 
                                       class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500" 
                                       placeholder="+420123456789">
                            </div>
                            <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 rounded-lg transition-colors">
                                Načíst kartu z Baserow
                            </button>
                        </form>
                    </div>

                    <!-- Add Event -->
                    <div class="bg-white rounded-lg shadow-sm border p-6">
                        <h2 class="text-xl font-semibold text-gray-900 mb-4">Přidat událost</h2>
                        <form method="POST">
                            <input type="hidden" name="action" value="add_event">
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Telefon pacienta</label>
                                <input type="text" name="phone" value="<?php echo htmlspecialchars($_POST['phone'] ?? '+420123456789'); ?>" 
                                       class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Typ události</label>
                                <select name="event_type" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                    <option value="note">Záznam</option>
                                    <option value="image">Snímek</option>
                                    <option value="procedure">Výkon</option>
                                    <option value="consent">Souhlas</option>
                                    <option value="payment">Platba</option>
                                </select>
                            </div>
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Název</label>
                                <input type="text" name="title" value="Testovací událost" 
                                       class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Obsah</label>
                                <textarea name="content" rows="3" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">Obsah testovací události přidané přes Baserow integraci.</textarea>
                            </div>
                            <button type="submit" class="w-full bg-green-600 hover:bg-green-700 text-white py-2 rounded-lg transition-colors">
                                Přidat do Baserow
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Card Display -->
                <?php if ($card): ?>
                    <div class="mt-8 bg-white rounded-lg shadow-sm border">
                        <div class="p-6 border-b">
                            <h2 class="text-xl font-semibold text-gray-900">Karta pacienta: <?php echo htmlspecialchars($card['patient_name']); ?></h2>
                            <div class="mt-2 text-sm text-gray-600">
                                <span>Verze: <?php echo $card['card_version']; ?></span> |
                                <span>TST: <?php echo $card['tst_count']; ?></span> |
                                <span>Poslední aktivita: <?php echo $card['last_activity'] ?? 'Žádná'; ?></span>
                            </div>
                        </div>
                        <div class="p-6">
                            <!-- Statistics -->
                            <div class="grid grid-cols-2 md:grid-cols-5 gap-4 mb-6">
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-blue-600"><?php echo $card['stats']['notes']; ?></div>
                                    <div class="text-sm text-gray-600">Záznamy</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-green-600"><?php echo $card['stats']['images']; ?></div>
                                    <div class="text-sm text-gray-600">Snímky</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-yellow-600"><?php echo $card['stats']['procedures']; ?></div>
                                    <div class="text-sm text-gray-600">Výkony</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-purple-600"><?php echo $card['stats']['consents']; ?></div>
                                    <div class="text-sm text-gray-600">Souhlasy</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-red-600"><?php echo $card['stats']['payments']; ?></div>
                                    <div class="text-sm text-gray-600">Platby</div>
                                </div>
                            </div>

                            <!-- Visits -->
                            <?php if (!empty($card['card_data']['visits'])): ?>
                                <h3 class="text-lg font-semibold text-gray-900 mb-4">Návštěvy a aktivity</h3>
                                <div class="space-y-4">
                                    <?php foreach ($card['card_data']['visits'] as $visit): ?>
                                        <div class="border rounded-lg p-4">
                                            <div class="flex items-center justify-between mb-3">
                                                <h4 class="font-medium text-gray-900">
                                                    Návštěva: <?php echo date('d.m.Y H:i', strtotime($visit['dateTime'])); ?>
                                                </h4>
                                                <span class="text-sm text-gray-500">
                                                    <?php echo count($visit['items']); ?> položek
                                                </span>
                                            </div>
                                            <div class="space-y-2">
                                                <?php foreach ($visit['items'] as $item): ?>
                                                    <div class="bg-gray-50 rounded p-3">
                                                        <div class="flex items-center justify-between mb-1">
                                                            <span class="font-medium text-sm">
                                                                <?php echo htmlspecialchars($item['data']['title'] ?? ucfirst($item['type'])); ?>
                                                            </span>
                                                            <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                                                                <?php echo ucfirst($item['type']); ?>
                                                            </span>
                                                        </div>
                                                        <?php if (!empty($item['data']['content'])): ?>
                                                            <p class="text-sm text-gray-600">
                                                                <?php echo htmlspecialchars($item['data']['content']); ?>
                                                            </p>
                                                        <?php endif; ?>
                                                        <div class="text-xs text-gray-500 mt-1">
                                                            Hash: <?php echo substr($item['contentHash'], 0, 16); ?>...
                                                        </div>
                                                    </div>
                                                <?php endforeach; ?>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php else: ?>
                                <div class="text-center py-8">
                                    <p class="text-gray-500">Žádné návštěvy v kartě. Přidejte první událost výše.</p>
                                </div>
                            <?php endif; ?>

                            <!-- Raw JSON (for debugging) -->
                            <details class="mt-6">
                                <summary class="cursor-pointer text-sm font-medium text-gray-700">Zobrazit raw JSON data</summary>
                                <pre class="mt-2 p-4 bg-gray-100 rounded text-xs overflow-auto"><?php echo json_encode($card['card_data'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE); ?></pre>
                            </details>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</body>
</html>

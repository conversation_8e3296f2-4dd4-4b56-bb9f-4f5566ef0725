<?php
// This is a simplified script to directly update a field in Baserow
require_once 'config.php';
require_once 'baserow_functions.php';
require_once 'error_log.php';

// Enable error reporting
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Initialize session
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Set content type to JSON
header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

// Get parameters from POST or GET
$patientId = isset($_REQUEST['id']) ? intval($_REQUEST['id']) : 0;
$field = isset($_REQUEST['field']) ? $_REQUEST['field'] : '';
$value = isset($_REQUEST['value']) ? $_REQUEST['value'] : '';

// Validate parameters
if (!$patientId) {
    echo json_encode(['success' => false, 'message' => 'Missing patient ID']);
    exit;
}

if (empty($field)) {
    echo json_encode(['success' => false, 'message' => 'Missing field name']);
    exit;
}

// Log the request
error_log("Direct update request: Patient ID: $patientId, Field: $field, Value: $value");

try {
    // Get Baserow configuration
    $config = getBaserowConfig();
    
    // Map field names
    $fieldMapping = [
        'examination_date' => 'Datum_prohlidky',
        'akutni' => 'Akutní',
        'brouseni' => 'Broušení',
        'endo' => 'Endo',
        'extrakce_chirurgie' => 'Extrakce, chirurgie',
        'postendo' => 'Postendo',
        'predni_protetiky' => 'Předání protetiky',
        'sanace_dite' => 'Sanace - dítě',
        'sanace_dospely' => 'Sanace - dospělý',
        'snimatelna_protetika' => 'Snímatelná protetika - otisky'
    ];
    
    // Check if field exists in mapping
    if (!isset($fieldMapping[$field])) {
        echo json_encode(['success' => false, 'message' => 'Invalid field name']);
        exit;
    }
    
    $baserowField = $fieldMapping[$field];
    
    // Special handling for examination date
    if ($field === 'examination_date') {
        $formattedValue = convertToBaserowDateFormat($value);
        if ($formattedValue === null) {
            echo json_encode(['success' => false, 'message' => 'Invalid date format']);
            exit;
        }
        $value = $formattedValue;
    }
    
    // Prepare update data
    $updateData = [
        $baserowField => $value
    ];
    
    // Make the direct API request
    $url = "https://api.baserow.io/api/database/rows/table/{$config['baserow_table_id']}/$patientId/?user_field_names=true";
    $headers = [
        "Authorization: Token {$config['baserow_api_token']}",
        "Content-Type: application/json"
    ];
    
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PATCH');
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($updateData));
    
    $response = curl_exec($ch);
    $statusCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    
    if (curl_errno($ch)) {
        throw new Exception("Curl error: " . curl_error($ch));
    }
    
    curl_close($ch);
    
    // Log the response
    error_log("Baserow API response: Status: $statusCode, Response: $response");
    
    // Check if the request was successful
    if ($statusCode >= 200 && $statusCode < 300) {
        echo json_encode(['success' => true, 'message' => 'Field updated successfully']);
    } else {
        $errorMessage = "API error with status code $statusCode";
        try {
            $errorData = json_decode($response, true);
            if (isset($errorData['error'])) {
                $errorMessage = $errorData['error'];
            }
        } catch (Exception $e) {
            // Ignore JSON parsing errors
        }
        echo json_encode(['success' => false, 'message' => $errorMessage]);
    }
    
} catch (Exception $e) {
    error_log("Error in direct update: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());
    echo json_encode(['success' => false, 'message' => 'Server error: ' . $e->getMessage()]);
}
?>


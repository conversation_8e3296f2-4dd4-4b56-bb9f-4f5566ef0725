<?php
require_once 'config.php';
require_once 'error_log.php';

function migratePasswords() {
    global $mysqli;

    try {
        $mysqli->begin_transaction();

        $query = "SELECT id, password_hash FROM users WHERE password_hash NOT LIKE '$2y$%'";
        $result = $mysqli->query($query);

        $updatedCount = 0;

        while ($user = $result->fetch_assoc()) {
            // P<PERSON><PERSON><PERSON>kl<PERSON>d<PERSON>me, že staré heslo je uloženo jako MD5 hash
            $oldHash = $user['password_hash'];
            
            // Vytvoříme nový bcrypt hash
            $newHash = password_hash($oldHash, PASSWORD_BCRYPT);

            $updateQuery = "UPDATE users SET password_hash = ? WHERE id = ?";
            $stmt = $mysqli->prepare($updateQuery);
            $stmt->bind_param("si", $newHash, $user['id']);
            $stmt->execute();
            $stmt->close();

            $updatedCount++;
        }

        $mysqli->commit();

        writeErrorLog("Password migration completed", ['updated_count' => $updatedCount]);
        echo "Migrace hesel dokončena. Aktualizováno uživatelů: " . $updatedCount;

    } catch (Exception $e) {
        $mysqli->rollback();
        writeErrorLog("Password migration error", ['error' => $e->getMessage()]);
        echo "Chyba při migraci hesel: " . $e->getMessage();
    }
}

migratePasswords();
?>


<?php
session_start();
require_once '../config.php';
require_once '../error_log.php';
require_once '../api_functions.php';

// Kontrola přihlášení
if (!isset($_SESSION['user_id'])) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Uživatel není přihl<PERSON>šen']);
    exit;
}

// Získání dat z POST požadavku
$input = json_decode(file_get_contents('php://input'), true);
$action = $input['action'] ?? '';

// Zpracování podle typu akce
try {
    switch ($action) {
        case 'change_plan':
            $planId = $input['plan_id'] ?? '';
            if (empty($planId)) {
                throw new Exception('Nebyl vybrán žádný tarif');
            }
            
            // Volání API pro změnu tarifu
            $result = makeApiCall('subscription/change', [
                'user_id' => $_SESSION['user_id'],
                'plan_id' => $planId
            ]);
            
            // Vrácení odpovědi
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'message' => 'Tarif byl úspěšně změněn',
                'redirect_url' => $result['redirect_url'] ?? null
            ]);
            break;
            
        case 'cancel_subscription':
            // Volání API pro zrušení předplatného
            $result = makeApiCall('subscription/cancel', [
                'user_id' => $_SESSION['user_id']
            ]);
            
            // Vrácení odpovědi
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'message' => 'Předplatné bylo úspěšně zrušeno'
            ]);
            break;
            
        case 'delete_payment_method':
            $paymentMethodId = $input['payment_method_id'] ?? '';
            if (empty($paymentMethodId)) {
                throw new Exception('Nebyla vybrána žádná platební metoda');
            }
            
            // Volání API pro smazání platební metody
            $result = makeApiCall('payment/delete-method', [
                'user_id' => $_SESSION['user_id'],
                'payment_method_id' => $paymentMethodId
            ]);
            
            // Vrácení odpovědi
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'message' => 'Platební metoda byla úspěšně smazána'
            ]);
            break;
            
        case 'buy_sms':
            $amount = $input['amount'] ?? 0;
            if ($amount <= 0) {
                throw new Exception('Neplatný počet SMS');
            }
            
            // Volání API pro nákup SMS
            $result = makeApiCall('sms/buy', [
                'user_id' => $_SESSION['user_id'],
                'amount' => $amount
            ]);
            
            // Vrácení odpovědi
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'message' => 'SMS byly úspěšně zakoupeny',
                'redirect_url' => $result['redirect_url'] ?? 'payment_gateway.php?action=buy_sms&amount=' . $amount
            ]);
            break;
            
        case 'export_transactions':
            // Získání historie transakcí
            $transactions = []; // Toto by mělo být nahrazeno skutečným voláním API
            
            // Vytvoření CSV souboru
            $filename = 'transakce_' . date('Y-m-d') . '.csv';
            header('Content-Type: text/csv; charset=utf-8');
            header('Content-Disposition: attachment; filename="' . $filename . '"');
            
            $output = fopen('php://output', 'w');
            
            // Hlavička CSV
            fputcsv($output, ['Datum', 'Popis', 'Částka', 'Stav']);
            
            // Data
            foreach ($transactions as $transaction) {
                fputcsv($output, [
                    date('d.m.Y', strtotime($transaction['date'])),
                    $transaction['description'],
                    $transaction['amount'],
                    $transaction['status'] === 'paid' ? 'Zaplaceno' : 'Nezaplaceno'
                ]);
            }
            
            fclose($output);
            exit;
            
        default:
            throw new Exception('Neznámá akce');
    }
} catch (Exception $e) {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
    
    writeErrorLog('Subscription API Handler Error', [
        'action' => $action,
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ]);
}
?>


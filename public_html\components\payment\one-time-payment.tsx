"use client"

import { useState } from "react"
import { usePayment } from "@/components/payment/payment-provider"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Skeleton } from "@/components/ui/skeleton"
import { ShoppingCart, CreditCard, CheckCircle2 } from "lucide-react"

export function OneTimePayment() {
  const { oneTimeServices, paymentMethods, isLoading, processOneTimePayment } = usePayment()
  const [selectedService, setSelectedService] = useState<string | null>(null)
  const [paymentDialogOpen, setPaymentDialogOpen] = useState(false)
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<string | null>(null)
  const [isProcessing, setIsProcessing] = useState(false)
  const [isSuccess, setIsSuccess] = useState(false)

  if (isLoading) {
    return (
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Jednorázové platby</h2>
        <div className="grid gap-4 md:grid-cols-3">
          <Skeleton className="h-48" />
          <Skeleton className="h-48" />
          <Skeleton className="h-48" />
        </div>
      </div>
    )
  }

  const handlePaymentSubmit = async () => {
    if (!selectedService || !selectedPaymentMethod) return

    setIsProcessing(true)

    try {
      await processOneTimePayment(selectedService, selectedPaymentMethod)
      setIsSuccess(true)

      // Reset after 2 seconds
      setTimeout(() => {
        setIsSuccess(false)
        setPaymentDialogOpen(false)
        setSelectedService(null)
        setSelectedPaymentMethod(null)
      }, 2000)
    } catch (error) {
      console.error("Payment failed:", error)
    } finally {
      setIsProcessing(false)
    }
  }

  const selectedServiceDetails = oneTimeServices.find((service) => service.id === selectedService)

  return (
    <div className="space-y-4">
      <h2 className="text-xl font-semibold">Jednorázové platby</h2>

      <div className="grid gap-4 md:grid-cols-3">
        {oneTimeServices.map((service) => (
          <Card key={service.id} className="flex flex-col">
            <CardHeader>
              <CardTitle>{service.name}</CardTitle>
              <CardDescription>{service.description}</CardDescription>
            </CardHeader>
            <CardContent className="flex-grow">
              <p className="text-2xl font-bold">{service.price.toLocaleString()} Kč</p>
            </CardContent>
            <CardFooter>
              <Button
                className="w-full"
                onClick={() => {
                  setSelectedService(service.id)
                  setPaymentDialogOpen(true)
                }}
              >
                <ShoppingCart className="mr-2 h-4 w-4" />
                Zakoupit
              </Button>
            </CardFooter>
          </Card>
        ))}
      </div>

      <Dialog open={paymentDialogOpen} onOpenChange={setPaymentDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          {isSuccess ? (
            <div className="py-12 flex flex-col items-center justify-center">
              <div className="rounded-full bg-green-100 p-3 dark:bg-green-900">
                <CheckCircle2 className="h-8 w-8 text-green-600 dark:text-green-400" />
              </div>
              <h2 className="mt-4 text-xl font-semibold">Platba byla úspěšná</h2>
              <p className="mt-2 text-center text-muted-foreground">
                Vaše platba byla úspěšně zpracována. Děkujeme za váš nákup.
              </p>
            </div>
          ) : (
            <>
              <DialogHeader>
                <DialogTitle>Dokončit platbu</DialogTitle>
                <DialogDescription>Vyberte platební metodu pro dokončení nákupu.</DialogDescription>
              </DialogHeader>

              <div className="py-4">
                <div className="mb-4 p-4 bg-muted rounded-lg">
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="font-medium">{selectedServiceDetails?.name}</p>
                      <p className="text-sm text-muted-foreground">{selectedServiceDetails?.description}</p>
                    </div>
                    <p className="font-bold">{selectedServiceDetails?.price.toLocaleString()} Kč</p>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <Label>Vyberte platební metodu</Label>
                    {paymentMethods.length > 0 ? (
                      <RadioGroup
                        value={selectedPaymentMethod || ""}
                        onValueChange={setSelectedPaymentMethod}
                        className="mt-2 space-y-2"
                      >
                        {paymentMethods.map((method) => (
                          <div key={method.id} className="flex items-center space-x-2 rounded-md border p-3">
                            <RadioGroupItem value={method.id} id={method.id} />
                            <Label htmlFor={method.id} className="flex-grow cursor-pointer">
                              <div className="flex items-center">
                                {method.type === "card" ? (
                                  <>
                                    <CreditCard className="mr-2 h-4 w-4" />
                                    {method.details.brand} •••• {method.details.last4}
                                  </>
                                ) : method.type === "paypal" ? (
                                  <>
                                    <span className="mr-2 font-bold text-blue-600">P</span>
                                    PayPal ({method.details.email})
                                  </>
                                ) : (
                                  <>
                                    <CreditCard className="mr-2 h-4 w-4" />
                                    {method.details.bankName} •••• {method.details.accountLast4}
                                  </>
                                )}
                                {method.isDefault && (
                                  <span className="ml-2 text-xs text-muted-foreground">(Výchozí)</span>
                                )}
                              </div>
                            </Label>
                          </div>
                        ))}
                      </RadioGroup>
                    ) : (
                      <div className="mt-2 rounded-md border border-dashed p-4 text-center">
                        <p className="text-sm text-muted-foreground">
                          Nemáte přidané žádné platební metody. Přidejte platební metodu pro dokončení nákupu.
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              <DialogFooter>
                <Button variant="outline" onClick={() => setPaymentDialogOpen(false)} disabled={isProcessing}>
                  Zrušit
                </Button>
                <Button onClick={handlePaymentSubmit} disabled={!selectedPaymentMethod || isProcessing}>
                  {isProcessing ? "Zpracování..." : "Zaplatit"}
                </Button>
              </DialogFooter>
            </>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}


<?php
require_once '../config.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

try {
    // Získání ID uživatele ze session
    session_start();
    $userId = $_SESSION['user_id'] ?? null;
    
    if (!$userId) {
        throw new Exception('User not authenticated');
    }
    
    // Načtení nastavení z databáze
    $settings = loadVoiceflowSettings($userId);
    
    if ($settings) {
        echo json_encode(['settings' => json_decode($settings['settings'], true)]);
    } else {
        echo json_encode(['settings' => null]);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}


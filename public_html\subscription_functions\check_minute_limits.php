<?php
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/vapi_assistant_manager_delete_number.php';

function sendWebhookNotification($username, $message, $isTest = false) {
    $webhook_url = MAKE_WEBHOOK_URL;
    
    $data = array(
        'notification_type' => 'low_minutes_alert',
        'user' => array(
            'username' => $username
        ),
        'message' => $message,
        'timestamp' => date('Y-m-d H:i:s'),
        'source' => 'DentiBot Minute Limit Alert',
        'test' => $isTest
    );
    
    $ch = curl_init($webhook_url);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        'Content-Type: application/json',
        'Accept: application/json'
    ));
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    error_log("Webhook response: " . json_encode(['http_code' => $http_code, 'response' => $response]));
    
    return ($http_code >= 200 && $http_code < 300);
}

function removePhoneNumberFromVapiAssistant($userId, $phoneNumber) {
    $vapiApiKey = 'YOUR_VAPI_API_KEY';
    $assistantId = 'YOUR_ASSISTANT_ID';

    $ch = curl_init("https://api.vapi.ai/assistants/{$assistantId}/phone-numbers/{$phoneNumber}");
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "DELETE");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        "Authorization: Bearer {$vapiApiKey}",
        "Content-Type: application/json"
    ]);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    return $httpCode == 200;
}

function addPhoneNumberToVapiAssistant($userId, $phoneNumber) {
    $vapiApiKey = 'YOUR_VAPI_API_KEY';
    $assistantId = 'YOUR_ASSISTANT_ID';

    $ch = curl_init("https://api.vapi.ai/assistants/{$assistantId}/phone-numbers");
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode(['phone_number' => $phoneNumber]));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        "Authorization: Bearer {$vapiApiKey}",
        "Content-Type: application/json"
    ]);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    return $httpCode == 200;
}

function checkMinuteLimits($isTest = false, $forceNotify = false, $thresholdPercentage = 10, $notificationInterval = 7) {
    $conn = getDbConnection();
    if (!$conn) {
        error_log('Failed to establish database connection in checkMinuteLimits');
        return false;
    }
    
    try {
        $sql = "
            SELECT id, username, subscription_plan, minute_limit, minutes_remaining, used_minutes,
                   (minutes_remaining / minute_limit * 100) as percentage_remaining,
                   last_notification_sent, phone_number, vapi_number_active
            FROM users
            WHERE subscription_plan IN ('Základní', 'Pro')
              AND minute_limit > 0
        ";
        
        if (!$forceNotify) {
            $sql .= " AND (minutes_remaining / minute_limit * 100) <= " . $thresholdPercentage;
        }
        
        if (!$isTest) {
            $sql .= " AND (last_notification_sent IS NULL OR last_notification_sent < DATE_SUB(NOW(), INTERVAL " . $notificationInterval . " DAY))";
        }
        
        error_log("SQL Query: " . $sql);
        
        $stmt = $conn->prepare($sql);
        
        if (!$stmt) {
            throw new Exception("Failed to prepare statement: " . $conn->error);
        }
        
        $stmt->execute();
        $result = $stmt->get_result();

        error_log("Number of users found: " . $result->num_rows);

        $notificationsSent = 0;
        
        while ($user = $result->fetch_assoc()) {
            $percentageRemaining = floatval($user['percentage_remaining']);
            
            error_log("Processing user: " . json_encode($user));
            
            if ($percentageRemaining <= 0 && $user['vapi_number_active']) {
                // Blokace služby pro uživatele s vyčerpanými minutami
                $success = removePhoneNumberFromVapiAssistant($user['id'], $user['phone_number']);
                
                if ($success) {
                    $blockMessage = sprintf(
                        "Upozornění: Vaše služba byla pozastavena z důvodu vyčerpání všech minut. Pro obnovení služby prosím navyšte svůj plán nebo dobijte minuty."
                    );
                    sendWebhookNotification($user['username'], $blockMessage, $isTest);
                    
                    if (!$isTest) {
                        $updateStmt = $conn->prepare("UPDATE users SET vapi_number_active = FALSE, last_notification_sent = NOW() WHERE id = ?");
                        $updateStmt->bind_param("i", $user['id']);
                        $updateStmt->execute();
                        $updateStmt->close();
                    }
                    
                    error_log(($isTest ? 'TEST: ' : '') . 'Service blocked for user: ' . json_encode([
                        'user_id' => $user['id'],
                        'username' => $user['username']
                    ]));
                } else {
                    error_log("Failed to remove phone number from vapi assistant for user: " . $user['id']);
                }
            } elseif ($percentageRemaining <= $thresholdPercentage || $forceNotify) {
                $message = sprintf(
                    "Upozornění: U vašeho účtu zbývá pouze %.1f%% minut (zbývá %d z %d minut). Prosím, zvažte navýšení vašeho plánu.",
                    $percentageRemaining,
                    $user['minutes_remaining'],
                    $user['minute_limit']
                );
                
                if (sendWebhookNotification($user['username'], $message, $isTest)) {
                    $notificationsSent++;
                    
                    if (!$isTest) {
                        $updateStmt = $conn->prepare("UPDATE users SET last_notification_sent = NOW() WHERE id = ?");
                        $updateStmt->bind_param("i", $user['id']);
                        $updateStmt->execute();
                        $updateStmt->close();
                    }
                    
                    error_log(($isTest ? 'TEST: ' : '') . 'Low minutes notification sent: ' . json_encode([
                        'user_id' => $user['id'],
                        'username' => $user['username'],
                        'remaining_percentage' => $percentageRemaining,
                        'minutes_remaining' => $user['minutes_remaining'],
                        'minute_limit' => $user['minute_limit']
                    ]));
                } else {
                    error_log(($isTest ? 'TEST: ' : '') . 'Failed to send low minutes notification: ' . json_encode([
                        'user_id' => $user['id'],
                        'username' => $user['username']
                    ]));
                }
            } else {
                error_log("User not notified (above threshold): " . json_encode([
                    'user_id' => $user['id'],
                    'username' => $user['username'],
                    'remaining_percentage' => $percentageRemaining,
                    'threshold' => $thresholdPercentage
                ]));
            }
        }
        
        return $notificationsSent;
    } catch (Exception $e) {
        error_log(($isTest ? 'TEST: ' : '') . 'Check minute limits error: ' . $e->getMessage());
        return false;
    } finally {
        if (isset($stmt)) {
            $stmt->close();
        }
        if ($conn) {
            $conn->close();
        }
    }
}

// Spuštění skriptu
if (php_sapi_name() === 'cli') {
    $isTest = in_array('--test', $argv);
    $forceNotify = in_array('--force', $argv);
    $thresholdPercentage = 10;
    $notificationInterval = 7;
    foreach ($argv as $arg) {
        if (strpos($arg, '--threshold=') === 0) {
            $thresholdPercentage = intval(substr($arg, 12));
        } elseif (strpos($arg, '--interval=') === 0) {
            $notificationInterval = intval(substr($arg, 11));
        }
    }
    $result = checkMinuteLimits($isTest, $forceNotify, $thresholdPercentage, $notificationInterval);
    echo ($isTest ? "TEST: " : "") . "Notifications sent: " . $result . "\n";
} elseif (isset($_GET['test']) || isset($_GET['force']) || isset($_GET['threshold']) || isset($_GET['interval'])) {
    header('Content-Type: application/json');
    $isTest = isset($_GET['test']);
    $forceNotify = isset($_GET['force']);
    $thresholdPercentage = isset($_GET['threshold']) ? intval($_GET['threshold']) : 10;
    $notificationInterval = isset($_GET['interval']) ? intval($_GET['interval']) : 7;
    $result = checkMinuteLimits($isTest, $forceNotify, $thresholdPercentage, $notificationInterval);
    echo json_encode([
        'success' => true,
        'notifications_sent' => $result,
        'mode' => $isTest ? 'test' : 'live',
        'force_notify' => $forceNotify,
        'threshold_percentage' => $thresholdPercentage,
        'notification_interval' => $notificationInterval
    ]);
} else {
    header('Content-Type: application/json');
    $result = checkMinuteLimits(false, false, 10, 7);
    echo json_encode(['success' => true, 'notifications_sent' => $result, 'mode' => 'live']);
}
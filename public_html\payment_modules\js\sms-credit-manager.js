/**
 * SMS Credit Manager - JavaScript funkce pro správu SMS kreditů
 */

// Předpokládáme, že showNotification je definována globálně nebo importována.
// Pokud ne, je třeba ji definovat nebo importovat.
// Pro jednoduchost zde předpokládáme, že je definována globálně.
// Příklad:
// function showNotification(type, message) {
//   alert(message); // Jednoduchá implementace pro demonstraci
// }

// Získání aktuálního zůstatku SMS kreditu
function getSmsCredit() {
  return fetch("credit_system_handler.php", {
    method: "POST",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    body: new URLSearchParams({
      action: "get_credit",
    }),
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.success) {
        // Aktualizace zobrazení kreditu v UI
        updateCreditDisplay(data.credit)
        return data.credit
      } else {
        showNotification("error", data.message || "Nepodařilo se načíst kredit")
        return 0
      }
    })
    .catch((error) => {
      console.error("Chyba při získávání kreditu:", error)
      showNotification("error", "Došlo k chybě při komunikaci se serverem")
      return 0
    })
}

// Dobití SMS kreditu
function rechargeCredit(packageId, credits, price, paymentMethod) {
  return fetch("credit_system_handler.php", {
    method: "POST",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    body: new URLSearchParams({
      action: "recharge_credit",
      package_id: packageId,
      credits: credits,
      price: price,
      payment_method: paymentMethod,
    }),
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.success) {
        showNotification("success", data.message || "Kredit byl úspěšně dobit")
        updateCreditDisplay(data.new_balance)
        return true
      } else {
        showNotification("error", data.message || "Nepodařilo se dobít kredit")
        return false
      }
    })
    .catch((error) => {
      console.error("Chyba při dobíjení kreditu:", error)
      showNotification("error", "Došlo k chybě při komunikaci se serverem")
      return false
    })
}

// Získání historie transakcí
function getTransactionHistory(limit = 10, offset = 0) {
  return fetch("credit_system_handler.php", {
    method: "POST",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    body: new URLSearchParams({
      action: "get_transactions",
      limit: limit,
      offset: offset,
    }),
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.success) {
        return data.transactions
      } else {
        showNotification("error", data.message || "Nepodařilo se načíst historii transakcí")
        return []
      }
    })
    .catch((error) => {
      console.error("Chyba při získávání historie transakcí:", error)
      showNotification("error", "Došlo k chybě při komunikaci se serverem")
      return []
    })
}

// Získání ceny za jednu SMS
function getSmsPricePerUnit() {
  return fetch("credit_system_handler.php", {
    method: "POST",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    body: new URLSearchParams({
      action: "get_sms_price",
    }),
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.success) {
        // Aktualizace zobrazení ceny v UI
        updatePriceDisplay(data.price_per_sms)
        return {
          price_per_sms: data.price_per_sms,
          free_sms_limit: data.free_sms_limit,
        }
      } else {
        showNotification("error", data.message || "Nepodařilo se načíst cenu SMS")
        return {
          price_per_sms: 0.75,
          free_sms_limit: 0,
        }
      }
    })
    .catch((error) => {
      console.error("Chyba při získávání ceny:", error)
      showNotification("error", "Došlo k chybě při komunikaci se serverem")
      return {
        price_per_sms: 0.75,
        free_sms_limit: 0,
      }
    })
}

// Výpočet ceny za odeslání SMS
function calculateSmsCost(smsCount) {
  return fetch("credit_system_handler.php", {
    method: "POST",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    body: new URLSearchParams({
      action: "calculate_sms_cost",
      sms_count: smsCount,
    }),
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.success) {
        return data.cost_info
      } else {
        showNotification("error", data.message || "Nepodařilo se vypočítat cenu SMS")
        return null
      }
    })
    .catch((error) => {
      console.error("Chyba při výpočtu ceny SMS:", error)
      showNotification("error", "Došlo k chybě při komunikaci se serverem")
      return null
    })
}

// Pomocné funkce pro aktualizaci UI
function updateCreditDisplay(credit) {
  const creditElements = document.querySelectorAll(".credit-amount")
  creditElements.forEach((element) => {
    element.textContent = `${credit.toFixed(2)} Kč`
  })

  // Aktualizace počtu SMS, které lze odeslat
  const priceElements = document.querySelectorAll(".sms-price")
  if (priceElements.length > 0) {
    const price = Number.parseFloat(priceElements[0].textContent)
    if (price > 0) {
      const smsCount = Math.floor(credit / price)
      const smsCountElements = document.querySelectorAll(".card p strong")
      smsCountElements.forEach((element) => {
        if (element.closest(".card").querySelector("h3").textContent.includes("SMS")) {
          element.textContent = smsCount
        }
      })
    }
  }
}

function updatePriceDisplay(price) {
  const priceElements = document.querySelectorAll(".sms-price")
  priceElements.forEach((element) => {
    element.textContent = `${price.toFixed(2)} Kč`
  })
}

// Inicializace při načtení stránky
document.addEventListener("DOMContentLoaded", () => {
  // Načtení počátečních dat
  getSmsCredit()
  getSmsPricePerUnit()
})


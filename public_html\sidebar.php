<?php
// Initialize session and check for errors
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Required includes with error checking
$requiredFiles = ['config.php', 'error_log.php'];
foreach ($requiredFiles as $file) {
    if (!file_exists($file)) {
        die("Error: Required file $file not found");
    }
    require_once $file;
}

// Přidejte tuto proměnnou na začátek souboru, hned po require statements
$logo_path = "/images/logo.png"; // Upravte cestu podle skutečného umístění vašeho loga

// Ensure currentPage is defined with a default value
if (!isset($currentPage)) {
    $currentPage = basename($_SERVER['PHP_SELF'], '.php');
}

// Generate CSRF token
$csrfToken = generateCSRFToken();

writeErrorLog("Sidebar included. Current page: " . $currentPage);
writeErrorLog("Session data in sidebar: " . print_r($_SESSION, true));
?>

<style>
    /* Modernizovaný sidebar - společn<PERSON> styly */
    #sidebar {
        width: 16rem;
        min-height: 100vh;
        transition: all 0.3s ease;
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }

    /* Logo a hlavička */
    .sidebar-logo-container {
        display: flex;
        align-items: center;
        padding: 1.25rem 1.5rem;
        border-bottom: 1px solid;
        border-color: inherit;
    }

    .sidebar-logo {
        height: 32px;
        width: 32px;
        object-fit: contain;
    }

    .sidebar-logo-text {
        font-size: 1.25rem;
        font-weight: 600;
        margin-left: 0.75rem;
        letter-spacing: -0.025em;
    }

    /* Navigační menu */
    .nav-container {
        padding: 1rem 0.75rem;
    }

    .nav-link {
        display: flex;
        align-items: center;
        padding: 0.75rem 1rem;
        margin: 0.25rem 0;
        border-radius: 0.5rem;
        transition: all 0.2s ease;
        font-weight: 500;
        font-size: 0.9375rem;
        position: relative;
    }

    .nav-link.active {
        font-weight: 600;
    }

    .nav-link-badge {
        font-size: 0.75rem;
        font-style: italic;
        opacity: 0.7;
        margin-left: auto;
    }

    /* Ikony pro navigaci - optimalizované pro konzistentní velikost */
    .nav-icon-container {
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 0.75rem;
        flex-shrink: 0;
    }

    .nav-icon {
        width: 20px;
        height: 20px;
        stroke-width: 2;
    }

    /* Patička sidebaru */
    .logout-container {
        padding: 1rem;
        border-top: 1px solid;
        border-color: inherit;
        margin-top: auto;
    }

    .logout-button {
        width: 100%;
        padding: 0.75rem;
        border-radius: 0.5rem;
        font-weight: 500;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
    }

    .logout-button:disabled {
        opacity: 0.7;
        cursor: not-allowed;
    }

    .logout-icon-container {
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 0.5rem;
    }

    .logout-icon {
        width: 20px;
        height: 20px;
        stroke-width: 2;
    }

    /* Tmavý motiv */
    body:not(.light-theme) #sidebar {
        background-color: #1a1f2e;
        border-right: 1px solid #2d3748;
        color: #e2e8f0;
    }

    body:not(.light-theme) .sidebar-logo-text {
        color: #4fd1c5;
    }

    body:not(.light-theme) .nav-link {
        color: #cbd5e1;
    }

    body:not(.light-theme) .nav-link:hover {
        background-color: #2d3748;
        color: #4fd1c5;
    }

    body:not(.light-theme) .nav-link.active {
        background-color: #2d3748;
        color: #4fd1c5;
    }

    body:not(.light-theme) .nav-link-badge {
        color: #a0aec0;
    }

    body:not(.light-theme) .logout-button {
        background-color: #e53e3e;
        color: #f8fafc;
        border: none;
    }

    body:not(.light-theme) .logout-button:hover {
        background-color: #c53030;
    }

    /* Světlý motiv */
    .light-theme #sidebar {
        background-color: #f8fafc;
        border-right: 1px solid #e2e8f0;
        color: #334155;
    }

    .light-theme .sidebar-logo-text {
        color: #0d9488;
    }

    .light-theme .nav-link {
        color: #475569;
    }

    .light-theme .nav-link:hover {
        background-color: #f1f5f9;
        color: #0d9488;
    }

    .light-theme .nav-link.active {
        background-color: #e2e8f0;
        color: #0d9488;
    }

    .light-theme .nav-link-badge {
        color: #64748b;
    }

    .light-theme .logout-button {
        background-color: #ef4444;
        color: white;
        border: none;
    }

    .light-theme .logout-button:hover {
        background-color: #dc2626;
    }
</style>

<div id="sidebar" class="flex flex-col">
    <div class="sidebar-logo-container">
        <img src="<?php echo $logo_path; ?>" alt="Dentibot Logo" class="sidebar-logo">
        <span class="sidebar-logo-text">Dentibot</span>
    </div>
    <nav class="nav-container flex-1">
        <?php
        $menuItems = [
            [
                'url' => 'dashboard.php', 
                'title' => 'Přehledy',
                'icon' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" class="nav-icon"><rect x="3" y="3" width="18" height="18" rx="2"/><path d="M3 9h18"/><path d="M9 21V9"/></svg>'
            ],
            [
                'url' => 'call_history.php', 
                'title' => 'Historie hovorů',
                'icon' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" class="nav-icon"><path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path></svg>'
            ],
            [
                'url' => 'sms_campaigns_bulkgate.php', 
                'title' => 'Hromadné SMS',
                'icon' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" class="nav-icon"><path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path></svg>'
            ],
            [
                'url' => 'patients.php',
                'title' => 'Seznam pacientů',
                'icon' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" class="nav-icon"><path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><path d="M23 21v-2a4 4 0 0 0-3-3.87"></path><path d="M16 3.13a4 4 0 0 1 0 7.75"></path></svg>'
            ],
            [
                'url' => 'cgm_documents.php',
                'title' => 'CGM Časová razítka',
                'badge' => 'nové',
                'icon' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" class="nav-icon"><path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path><polyline points="14,2 14,8 20,8"></polyline><line x1="16" y1="13" x2="8" y2="13"></line><line x1="16" y1="17" x2="8" y2="17"></line><polyline points="10,9 9,9 8,9"></polyline><circle cx="12" cy="15" r="2"></circle></svg>'
            ],
          
                        [
                'url' => 'platby.php', 
                'title' => 'Platby',
                'badge' => 'pracujeme na tom',
                'icon' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" class="nav-icon"><rect x="1" y="4" width="22" height="16" rx="2" ry="2"></rect><line x1="1" y1="10" x2="23" y2="10"></line></svg>'
            ],
            [
                'url' => 'settings.php', 
                'title' => 'Nastavení',
                'badge' => 'přístup pouze na požádání',
                'icon' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" class="nav-icon"><circle cx="12" cy="12" r="3"></circle><path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path></svg>'
            ]
        ];

        foreach ($menuItems as $item):
            $isActive = $currentPage === basename($item['url'], '.php');
            $activeClass = $isActive ? 'active' : '';
            $badge = isset($item['badge']) ? '<span class="nav-link-badge">(' . htmlspecialchars($item['badge']) . ')</span>' : '';
        ?>
            <a href="<?php echo htmlspecialchars($item['url']); ?>" 
               class="nav-link <?php echo $activeClass; ?>">
                <div class="nav-icon-container">
                    <?php echo $item['icon']; ?>
                </div>
                <span><?php echo htmlspecialchars($item['title']); ?></span>
                <?php echo $badge; ?>
            </a>
        <?php endforeach; ?>
    </nav>
    <div class="logout-container">
        <button id="logoutButton" class="logout-button" form="logoutForm">
            <div class="logout-icon-container">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" class="logout-icon">
                    <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
                    <polyline points="16 17 21 12 16 7"></polyline>
                    <line x1="21" y1="12" x2="9" y2="12"></line>
                </svg>
            </div>
            Odhlásit se
        </button>
        <form id="logoutForm" action="logout.php" method="post" style="display: none;">
            <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const sidebar = document.getElementById('sidebar');
    const logoutButton = document.getElementById('logoutButton');
    
    // Funkce pro aktualizaci motivu sidebaru
    function updateSidebarTheme(isDark) {
        if (isDark) {
            document.body.classList.remove('light-theme');
        } else {
            document.body.classList.add('light-theme');
        }
    }

    // Inicializace motivu
    const savedTheme = localStorage.getItem('theme');
    updateSidebarTheme(savedTheme === 'dark');

    // Posluchač pro změnu motivu
    window.addEventListener('themeChanged', function(e) {
        updateSidebarTheme(e.detail.isDark);
    });

    // Logout functionality
    if (logoutButton) {
        logoutButton.addEventListener('click', function(e) {
            e.preventDefault();
            document.getElementById('logoutForm').submit();
        });
    }
});
</script>


<?php
/**
 * <PERSON>row integrace pro automatickou kartu pacienta
 * Zachovává původní napojení a přidává podporu pro kartu pacienta
 */

require_once __DIR__ . '/../baserow_functions.php';
require_once __DIR__ . '/PatientCardManager.php';

class BaserowCardManager extends PatientCardManager {
    
    /**
     * Získá kartu pacienta z Baserow
     */
    public function getPatientCardFromBaserow($patient_baserow_id) {
        try {
            // Získej data pacienta z Baserow
            $response = baserowRequest('GET', $patient_baserow_id . '?user_field_names=true');
            
            if (!$response) {
                throw new Exception('Pacient nenalezen v Baserow');
            }
            
            // Zkontroluj, jestli má pacient kartu
            $cardJson = $response['Karta_pacienta_JSON'] ?? null;
            $cardVersion = $response['Verze_karty'] ?? 1;
            $autoKartaAktivni = $response['Auto_karta_aktivni'] ?? true;
            
            if (!$autoKartaAktivni) {
                return ['success' => false, 'error' => 'Automatická karta není aktivní pro tohoto pacienta'];
            }
            
            // Pokud karta neexistuje, vytvoř novou
            if (!$cardJson) {
                return $this->createNewCardInBaserow($patient_baserow_id, $response);
            }
            
            // Parsuj existující kartu
            $cardData = json_decode($cardJson, true);
            if (!$cardData) {
                // Poškozená karta - vytvoř novou
                return $this->createNewCardInBaserow($patient_baserow_id, $response);
            }
            
            return [
                'success' => true,
                'card' => [
                    'patient_baserow_id' => $patient_baserow_id,
                    'patient_name' => $response['Jmeno_pacienta'] ?? $response['jmeno_pacienta'] ?? 'Neznámý',
                    'phone' => $response['Telefonni_cislo'] ?? $response['telefonni_cislo'] ?? '',
                    'email' => $response['Emailova_adresa'] ?? $response['emailova_adresa'] ?? '',
                    'card_version' => $cardVersion,
                    'card_data' => $cardData,
                    'card_hash' => $response['Hash_karty'] ?? '',
                    'last_activity' => $response['Posledni_aktivita'] ?? null,
                    'tst_count' => $response['Pocet_TST'] ?? 0,
                    'stats' => [
                        'notes' => $response['Pocet_zaznamu'] ?? 0,
                        'images' => $response['Pocet_snimku'] ?? 0,
                        'procedures' => $response['Pocet_vykonu'] ?? 0,
                        'consents' => $response['Pocet_souhlasu'] ?? 0,
                        'payments' => $response['Pocet_plateb'] ?? 0
                    ]
                ]
            ];
            
        } catch (Exception $e) {
            error_log("BaserowCardManager getCard error: " . $e->getMessage());
            return ['success' => false, 'error' => 'Chyba při načítání karty z Baserow: ' . $e->getMessage()];
        }
    }
    
    /**
     * Vytvoří novou kartu v Baserow
     */
    private function createNewCardInBaserow($patient_baserow_id, $patientData) {
        try {
            $cardData = [
                'patientId' => $patient_baserow_id,
                'cardVersion' => 1,
                'snapTime' => date('c'),
                'visits' => [],
                'metadata' => [
                    'created' => date('c'),
                    'lastActivity' => null,
                    'source' => 'baserow_integration'
                ]
            ];
            
            $cardJson = json_encode($cardData, JSON_UNESCAPED_UNICODE);
            $cardHash = hash('sha256', $cardJson);
            
            // Aktualizuj pacienta v Baserow
            $updateData = [
                'Karta_pacienta_JSON' => $cardJson,
                'Verze_karty' => 1,
                'Hash_karty' => $cardHash,
                'Posledni_aktivita' => date('Y-m-d'),
                'Pocet_TST' => 0,
                'Pocet_zaznamu' => 0,
                'Pocet_snimku' => 0,
                'Pocet_vykonu' => 0,
                'Pocet_souhlasu' => 0,
                'Pocet_plateb' => 0,
                'Auto_karta_aktivni' => true,
                'Sync_status' => 'Synced'
            ];
            
            $response = baserowRequest('PATCH', $patient_baserow_id . '?user_field_names=true', $updateData);
            
            return [
                'success' => true,
                'card' => [
                    'patient_baserow_id' => $patient_baserow_id,
                    'patient_name' => $patientData['Jmeno_pacienta'] ?? $patientData['jmeno_pacienta'] ?? 'Neznámý',
                    'phone' => $patientData['Telefonni_cislo'] ?? $patientData['telefonni_cislo'] ?? '',
                    'email' => $patientData['Emailova_adresa'] ?? $patientData['emailova_adresa'] ?? '',
                    'card_version' => 1,
                    'card_data' => $cardData,
                    'card_hash' => $cardHash,
                    'last_activity' => null,
                    'tst_count' => 0,
                    'stats' => [
                        'notes' => 0,
                        'images' => 0,
                        'procedures' => 0,
                        'consents' => 0,
                        'payments' => 0
                    ]
                ]
            ];
            
        } catch (Exception $e) {
            error_log("BaserowCardManager createCard error: " . $e->getMessage());
            return ['success' => false, 'error' => 'Chyba při vytváření karty v Baserow: ' . $e->getMessage()];
        }
    }
    
    /**
     * Přidá položku do karty v Baserow
     */
    public function addCardItemToBaserow($patient_baserow_id, $item_data) {
        try {
            // Získej aktuální kartu
            $card_result = $this->getPatientCardFromBaserow($patient_baserow_id);
            if (!$card_result['success']) {
                throw new Exception($card_result['error']);
            }
            
            $card = $card_result['card'];
            $cardData = $card['card_data'];
            
            // Vytvoř novou položku
            $version_id = $this->generateUUID();
            $visit_id = $item_data['visit_id'] ?? $this->generateUUID();
            $content_hash = hash('sha256', json_encode($item_data));
            
            // Najdi nebo vytvoř návštěvu
            $visit_index = null;
            foreach ($cardData['visits'] as $index => $visit) {
                if ($visit['visitId'] === $visit_id) {
                    $visit_index = $index;
                    break;
                }
            }
            
            if ($visit_index === null) {
                // Nová návštěva
                $cardData['visits'][] = [
                    'visitId' => $visit_id,
                    'dateTime' => $item_data['created_at'] ?? date('c'),
                    'items' => []
                ];
                $visit_index = count($cardData['visits']) - 1;
            }
            
            // Přidej položku do návštěvy
            $cardData['visits'][$visit_index]['items'][] = [
                'type' => $item_data['type'],
                'versionId' => $version_id,
                'contentHash' => $content_hash,
                'tstId' => null, // TST se přidá později
                'data' => $item_data
            ];
            
            // Aktualizuj metadata
            $cardData['cardVersion'] = $card['card_version'] + 1;
            $cardData['snapTime'] = date('c');
            $cardData['metadata']['lastActivity'] = date('c');
            
            $newCardJson = json_encode($cardData, JSON_UNESCAPED_UNICODE);
            $newCardHash = hash('sha256', $newCardJson);
            
            // Aktualizuj statistiky
            $stats = $card['stats'];
            switch ($item_data['type']) {
                case 'note':
                    $stats['notes']++;
                    break;
                case 'image':
                    $stats['images']++;
                    break;
                case 'procedure':
                    $stats['procedures']++;
                    break;
                case 'consent':
                    $stats['consents']++;
                    break;
                case 'payment':
                    $stats['payments']++;
                    break;
            }
            
            // Aktualizuj pacienta v Baserow
            $updateData = [
                'Karta_pacienta_JSON' => $newCardJson,
                'Verze_karty' => $cardData['cardVersion'],
                'Hash_karty' => $newCardHash,
                'Posledni_aktivita' => date('Y-m-d'),
                'Pocet_zaznamu' => $stats['notes'],
                'Pocet_snimku' => $stats['images'],
                'Pocet_vykonu' => $stats['procedures'],
                'Pocet_souhlasu' => $stats['consents'],
                'Pocet_plateb' => $stats['payments'],
                'Sync_status' => 'Synced'
            ];
            
            $response = baserowRequest('PATCH', $patient_baserow_id . '?user_field_names=true', $updateData);
            
            return [
                'success' => true,
                'item_id' => $version_id,
                'card_version' => $cardData['cardVersion']
            ];
            
        } catch (Exception $e) {
            error_log("BaserowCardManager addItem error: " . $e->getMessage());
            return ['success' => false, 'error' => 'Chyba při přidávání položky do Baserow: ' . $e->getMessage()];
        }
    }
    
    /**
     * Najde pacienta podle telefonu (zachovává původní funkcionalitu)
     */
    public function findPatientByPhone($phone) {
        try {
            // Použij původní funkci pro vyhledání pacienta
            $patients_result = getPatients(1, 1, $phone);
            
            if (!$patients_result['success'] || empty($patients_result['patients'])) {
                return ['success' => false, 'error' => 'Pacient nenalezen'];
            }
            
            $patient = $patients_result['patients'][0];
            $baserow_id = $patient['row_id'] ?? $patient['id'];
            
            return [
                'success' => true,
                'patient' => $patient,
                'baserow_id' => $baserow_id
            ];
            
        } catch (Exception $e) {
            error_log("BaserowCardManager findPatient error: " . $e->getMessage());
            return ['success' => false, 'error' => 'Chyba při vyhledávání pacienta: ' . $e->getMessage()];
        }
    }
    
    /**
     * Generuje UUID
     */
    private function generateUUID() {
        return sprintf('%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff), mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
        );
    }
}

/**
 * Pomocné funkce pro snadné použití s Baserow
 */

function addEventToBaserowCard($patient_phone, $event_type, $event_data) {
    $manager = new BaserowCardManager();
    
    // Najdi pacienta podle telefonu
    $patient_result = $manager->findPatientByPhone($patient_phone);
    if (!$patient_result['success']) {
        return $patient_result;
    }
    
    $baserow_id = $patient_result['baserow_id'];
    
    // Přidej událost do karty
    return $manager->addCardItemToBaserow($baserow_id, array_merge($event_data, ['type' => $event_type]));
}

function getBaserowPatientCard($patient_phone) {
    $manager = new BaserowCardManager();
    
    // Najdi pacienta podle telefonu
    $patient_result = $manager->findPatientByPhone($patient_phone);
    if (!$patient_result['success']) {
        return $patient_result;
    }
    
    $baserow_id = $patient_result['baserow_id'];
    
    // Získej kartu
    return $manager->getPatientCardFromBaserow($baserow_id);
}

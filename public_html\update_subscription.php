<?php
// Nastavení pro zachycení a logování všech chyb
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Povolení CORS pro lokální vývoj
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-Requested-With');

// Pokud je to OPTIONS požadavek, vrátíme pouze hlavičky
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    header('HTTP/1.1 200 OK');
    exit;
}

// Nastavení hlaviček pro JSON odpověď
header('Content-Type: application/json');

// Načtení potřebných souborů
require_once 'config.php';
require_once 'error_log.php';

// Kontrola, zda existují potřebné soubory
if (file_exists('subscription_constants.php')) {
    require_once 'subscription_constants.php';
} else {
    writeErrorLog('Missing file', 'ERROR', ['file' => 'subscription_constants.php']);
}

if (file_exists('subscription_functions.php')) {
    require_once 'subscription_functions.php';
} else {
    writeErrorLog('Missing file', 'ERROR', ['file' => 'subscription_functions.php']);
}

// Definice funkce validateCSRFToken, pokud neexistuje
if (!function_exists('validateCSRFToken')) {
    function validateCSRFToken($token) {
        // Jednoduchá implementace pro případ, že funkce chybí
        if (!isset($_SESSION['csrf_token'])) {
            return false;
        }
        return hash_equals($_SESSION['csrf_token'], $token);
    }
    writeErrorLog('Created fallback validateCSRFToken function', 'WARNING');
}

// Definice funkce getSubscriptionTypes, pokud neexistuje
if (!function_exists('getSubscriptionTypes')) {
    function getSubscriptionTypes() {
        // Základní implementace pro případ, že funkce chybí
        return [
            'Základní' => [
                'price' => 3000,
                'minutes_limit' => 500,
                'sms_limit' => 1000
            ],
            'Pokročilý' => [
                'price' => 5000,
                'minutes_limit' => 1000,
                'sms_limit' => 2000
            ],
            'Prémiový' => [
                'price' => 10000,
                'minutes_limit' => 5000,
                'sms_limit' => 5000
            ],
            'Enterprise' => [
                'price' => null,
                'minutes_limit' => -1,
                'sms_limit' => 10000
            ]
        ];
    }
    writeErrorLog('Created fallback getSubscriptionTypes function', 'WARNING');
}

try {
    // Inicializace session
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }

    // Logování všech příchozích dat pro debugging
    $input = file_get_contents('php://input');
    $postData = json_decode($input, true);
    $rawPostData = $_POST;

    writeErrorLog('Raw request data', 'DEBUG', [
        'input' => $input,
        'decoded' => $postData,
        'POST' => $rawPostData,
        'REQUEST_METHOD' => $_SERVER['REQUEST_METHOD'],
        'CONTENT_TYPE' => $_SERVER['CONTENT_TYPE'] ?? 'not set'
    ]);

    // Pokud máme JSON data, použijeme je, jinak zkusíme $_POST
    if ($postData) {
        $_POST = $postData;
    }

    // Kontrola přihlášení
    if (!isset($_SESSION['user_id'])) {
        throw new Exception("User not logged in");
    }

    // Získání dat z požadavku
    $data = $postData ?: $_POST;

    // Logování dat pro debugging
    writeErrorLog('Processing with data', 'DEBUG', $data);

    // Kontrola CSRF tokenu
    if (!isset($data['csrf_token'])) {
        writeErrorLog('CSRF token missing', 'ERROR', [
            'user_id' => $_SESSION['user_id']
        ]);
        throw new Exception("CSRF token missing");
    }

    if (!validateCSRFToken($data['csrf_token'])) {
        writeErrorLog('CSRF validation failed', 'ERROR', [
            'user_id' => $_SESSION['user_id'],
            'provided_token' => $data['csrf_token'],
            'session_token' => $_SESSION['csrf_token'] ?? 'none'
        ]);
        throw new Exception("Invalid CSRF token");
    }

    // Získání typu předplatného
    $subscription_type = $data['subscription_type'] ?? $data['plan'] ?? null;

    if (!$subscription_type) {
        throw new Exception("Subscription type missing");
    }

    $custom_minute_limit = isset($data['custom_minute_limit']) ? intval($data['custom_minute_limit']) : null;

    // Získání platných typů předplatného
    $valid_subscription_types = getSubscriptionTypes();

    // Logování pokusu o aktualizaci
    writeErrorLog('Subscription update attempt', 'INFO', [
        'user_id' => $_SESSION['user_id'],
        'requested_type' => $subscription_type,
        'custom_limit' => $custom_minute_limit,
        'valid_types' => array_keys($valid_subscription_types)
    ]);

    // Validace typu předplatného
    if (!array_key_exists($subscription_type, $valid_subscription_types)) {
        throw new Exception("Invalid subscription type: " . $subscription_type);
    }

    // Nastavení výchozích hodnot pro limity podle typu předplatného
    $minutes_limit = 1000; // Výchozí hodnota
    
    // Pokud existuje klíč minutes_limit v definici předplatného, použijeme ho
    if (isset($valid_subscription_types[$subscription_type]['minutes_limit'])) {
        $minutes_limit = $valid_subscription_types[$subscription_type]['minutes_limit'];
    } else {
        // Jinak nastavíme limity podle typu předplatného
        switch ($subscription_type) {
            case 'Základní':
                $minutes_limit = 500;
                break;
            case 'Pokročilý':
                $minutes_limit = 1000;
                break;
            case 'Prémiový':
                $minutes_limit = 5000;
                break;
            case 'Enterprise':
                $minutes_limit = -1; // Neomezeno
                break;
            default:
                $minutes_limit = 1000; // Výchozí hodnota
        }
    }

    // Zpracování Enterprise předplatného s vlastním limitem
    if ($subscription_type === 'Enterprise' && $custom_minute_limit !== null) {
        $minutes_limit = $custom_minute_limit;
    }

    // Připojení k databázi
    $mysqli = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
    if ($mysqli->connect_error) {
        throw new Exception("Database connection failed: " . $mysqli->connect_error);
    }
    
    $user_id = $_SESSION['user_id'];
    
    // Kontrola struktury tabulky users
    $tableInfo = [];
    $result = $mysqli->query("DESCRIBE users");
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $tableInfo[$row['Field']] = $row;
        }
    }
    
    // Logování struktury tabulky pro debugging
    writeErrorLog('Table structure', 'DEBUG', [
        'table_info' => $tableInfo
    ]);
    
    // Kontrola, zda existují potřebné sloupce
    $hasMinuteLimit = isset($tableInfo['minute_limit']);
    $hasSubscriptionPlan = isset($tableInfo['subscription_plan']);
    $hasCustomMinuteLimit = isset($tableInfo['custom_minute_limit']);
    
    // Pokud neexistuje sloupec subscription_plan, nemůžeme pokračovat
    if (!$hasSubscriptionPlan) {
        throw new Exception("Column 'subscription_plan' does not exist in table 'users'");
    }
    
    // Příprava SQL dotazu podle existujících sloupců
    $sql = "UPDATE users SET subscription_plan = ?";
    $types = "s";
    $params = [$subscription_type];
    
    // Přidání minute_limit, pokud existuje
    if ($hasMinuteLimit) {
        $sql .= ", minute_limit = ?";
        $types .= "i";
        $params[] = $minutes_limit;
    }
    
    // Přidání custom_minute_limit, pokud existuje
    if ($hasCustomMinuteLimit) {
        $sql .= ", custom_minute_limit = ?";
        $types .= "i";
        $params[] = $custom_minute_limit;
    }
    
    // Dokončení SQL dotazu
    $sql .= " WHERE id = ?";
    $types .= "i";
    $params[] = $user_id;
    
    // Logování SQL dotazu pro debugging
    writeErrorLog('SQL query', 'DEBUG', [
        'sql' => $sql,
        'types' => $types,
        'params' => $params
    ]);
    
    // Provedení aktualizace
    $stmt = $mysqli->prepare($sql);
    if (!$stmt) {
        throw new Exception("Prepare statement failed: " . $mysqli->error);
    }
    
    // Dynamické bindování parametrů
    $stmt->bind_param($types, ...$params);
    
    if (!$stmt->execute()) {
        throw new Exception("Database update failed: " . $stmt->error);
    }
    
    if ($stmt->affected_rows === 0) {
        writeErrorLog('No rows affected in subscription update', 'WARNING', [
            'user_id' => $user_id,
            'subscription_type' => $subscription_type,
            'minutes_limit' => $minutes_limit
        ]);
        // Pokračujeme, protože to může znamenat, že byla nastavena stejná hodnota
    }
    
    $stmt->close();
    
    // Získání aktuálních dat uživatele pro kontrolu
    $sql = "SELECT subscription_plan";
    if ($hasMinuteLimit) {
        $sql .= ", minute_limit";
    }
    if ($hasCustomMinuteLimit) {
        $sql .= ", custom_minute_limit";
    }
    $sql .= " FROM users WHERE id = ?";
    
    $stmt = $mysqli->prepare($sql);
    if (!$stmt) {
        throw new Exception("Prepare statement failed: " . $mysqli->error);
    }
    
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $userData = $result->fetch_assoc();
    $stmt->close();
    
    if (!$userData) {
        throw new Exception("User data not found after update");
    }
    
    // Logování aktuálních dat uživatele pro debugging
    writeErrorLog('User data after update', 'DEBUG', [
        'user_data' => $userData
    ]);
    
    // Získání hodnot z userData nebo použití výchozích hodnot
    $current_plan = $userData['subscription_plan'] ?? $subscription_type;
    $current_limit = $hasMinuteLimit ? ($userData['minute_limit'] ?? $minutes_limit) : $minutes_limit;
    $isUnlimited = $current_limit === -1;
    
    // Pokud se aktuální plán liší od požadovaného, zkusíme aktualizaci znovu
    if ($current_plan !== $subscription_type) {
        writeErrorLog('Subscription plan not updated correctly', 'WARNING', [
            'requested' => $subscription_type,
            'current' => $current_plan
        ]);
        
        // Zkusíme přímý SQL dotaz
        $mysqli->query("UPDATE users SET subscription_plan = '$subscription_type' WHERE id = $user_id");
    }
    
    // Pokud se aktuální limit liší od požadovaného a máme sloupec minute_limit, zkusíme aktualizaci znovu
    if ($hasMinuteLimit && $current_limit != $minutes_limit) {
        writeErrorLog('Minute limit not updated correctly', 'WARNING', [
            'requested' => $minutes_limit,
            'current' => $current_limit
        ]);
        
        // Zkusíme přímý SQL dotaz
        $mysqli->query("UPDATE users SET minute_limit = $minutes_limit WHERE id = $user_id");
        
        // Aktualizujeme hodnotu pro odpověď
        $current_limit = $minutes_limit;
    }
    
    // Získání aktuálního využití minut
    $minutes_used = 0;
    $result = $mysqli->query("SELECT used_minutes FROM users WHERE id = $user_id");
    if ($result && $row = $result->fetch_assoc()) {
        $minutes_used = $row['used_minutes'] ?? 0;
    }
    
    // After updating the subscription in the database, add this code to retrieve the current minute usage and limits

    // Retrieve the current minute usage and updated limits
    $sql = "SELECT subscription_plan, used_minutes, minute_limit, minutes_remaining FROM users WHERE id = ?";
    $stmt = $mysqli->prepare($sql);
    if (!$stmt) {
        throw new Exception("Prepare statement failed: " . $mysqli->error);
    }

    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $userData = $result->fetch_assoc();
    $stmt->close();

    if (!$userData) {
        throw new Exception("User data not found after update");
    }

    // Get the values from userData
    $current_plan = $userData['subscription_plan'];
    $minutes_used = $userData['used_minutes'] ?? 0;
    $current_limit = $userData['minute_limit'] ?? $minutes_limit;
    $minutes_remaining = $userData['minutes_remaining'] ?? ($current_limit - $minutes_used);
    $isUnlimited = $current_limit === -1;

    // Vrácení úspěšné odpovědi
    echo json_encode([
        'success' => true,
        'message' => 'Předplatné bylo úspěšně aktualizováno na ' . htmlspecialchars($subscription_type),
        'new_plan' => $current_plan,
        'new_limit' => $current_limit,
        'minutesUsed' => $minutes_used,
        'minutesRemaining' => $minutes_remaining,
        'isUnlimited' => $isUnlimited
    ]);
    
    // Logování úspěšné aktualizace
    writeErrorLog('Subscription updated successfully', 'INFO', [
        'user_id' => $user_id,
        'new_subscription' => $subscription_type,
        'new_limit' => $current_limit,
        'custom_limit' => $custom_minute_limit
    ]);
    
    // Uzavření databázového spojení
    $mysqli->close();
    
} catch (Exception $e) {
    // Logování chyby
    writeErrorLog('Error updating subscription', 'ERROR', [
        'user_id' => $_SESSION['user_id'] ?? 'unknown',
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ]);
    
    // Vrácení chybové odpovědi
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'Nepodařilo se aktualizovat předplatné: ' . $e->getMessage()
    ]);
}
?>


<?php
/**
 * Event Handler pro automatické aktualizace karty pacienta
 * Zachytává události z CRM a automaticky aktualizuje kartu
 */

require_once __DIR__ . '/PatientCardManager.php';

class CardEventHandler {
    private $cardManager;
    private $db;
    
    public function __construct() {
        $this->cardManager = new PatientCardManager();
        $this->db = getDbConnection();
    }
    
    /**
     * Hlavní metoda pro zpracování událostí
     */
    public function handleEvent($event_type, $event_data) {
        try {
            // Zaloguj událost
            $this->logEvent($event_type, $event_data);
            
            switch ($event_type) {
                case 'document_saved':
                    return $this->handleDocumentSaved($event_data);
                    
                case 'image_uploaded':
                    return $this->handleImageUploaded($event_data);
                    
                case 'procedure_completed':
                    return $this->handleProcedureCompleted($event_data);
                    
                case 'consent_signed':
                    return $this->handleConsentSigned($event_data);
                    
                case 'payment_received':
                    return $this->handlePaymentReceived($event_data);
                    
                case 'call_completed':
                    return $this->handleCallCompleted($event_data);
                    
                case 'appointment_scheduled':
                    return $this->handleAppointmentScheduled($event_data);
                    
                default:
                    return ['success' => false, 'error' => 'Neznámý typ události: ' . $event_type];
            }
            
        } catch (Exception $e) {
            error_log("CardEventHandler error: " . $e->getMessage());
            return ['success' => false, 'error' => 'Chyba při zpracování události'];
        }
    }
    
    /**
     * Zpracuje uložení dokumentu
     */
    private function handleDocumentSaved($data) {
        $item_data = [
            'type' => 'note',
            'title' => $data['title'] ?? 'Záznam',
            'content' => $data['content'] ?? '',
            'category' => $data['category'] ?? 'general',
            'author' => $data['author'] ?? $_SESSION['user_id'] ?? 1,
            'created_at' => date('c'),
            'visit_id' => $data['visit_id'] ?? null,
            'metadata' => [
                'source' => 'document_editor',
                'original_id' => $data['document_id'] ?? null
            ]
        ];
        
        return $this->cardManager->addCardItem($data['patient_id'], $item_data);
    }
    
    /**
     * Zpracuje nahrání obrázku/snímku
     */
    private function handleImageUploaded($data) {
        $item_data = [
            'type' => 'image',
            'title' => $data['title'] ?? 'Snímek',
            'description' => $data['description'] ?? '',
            'image_type' => $data['image_type'] ?? 'photo', // rtg, intraoral, photo
            'file_path' => $data['file_path'],
            'file_size' => $data['file_size'] ?? 0,
            'mime_type' => $data['mime_type'] ?? 'image/jpeg',
            'created_at' => date('c'),
            'visit_id' => $data['visit_id'] ?? null,
            'metadata' => [
                'source' => 'image_upload',
                'camera_info' => $data['camera_info'] ?? null,
                'original_filename' => $data['original_filename'] ?? null
            ]
        ];
        
        return $this->cardManager->addCardItem($data['patient_id'], $item_data);
    }
    
    /**
     * Zpracuje dokončený výkon
     */
    private function handleProcedureCompleted($data) {
        $item_data = [
            'type' => 'procedure',
            'procedure_code' => $data['procedure_code'],
            'procedure_name' => $data['procedure_name'],
            'description' => $data['description'] ?? '',
            'tooth_number' => $data['tooth_number'] ?? null,
            'duration_minutes' => $data['duration_minutes'] ?? 0,
            'price' => $data['price'] ?? 0,
            'insurance_covered' => $data['insurance_covered'] ?? false,
            'performed_by' => $data['performed_by'] ?? $_SESSION['user_id'] ?? 1,
            'created_at' => date('c'),
            'visit_id' => $data['visit_id'] ?? null,
            'metadata' => [
                'source' => 'procedure_tracker',
                'equipment_used' => $data['equipment_used'] ?? null,
                'complications' => $data['complications'] ?? null
            ]
        ];
        
        return $this->cardManager->addCardItem($data['patient_id'], $item_data);
    }
    
    /**
     * Zpracuje podepsaný souhlas
     */
    private function handleConsentSigned($data) {
        $item_data = [
            'type' => 'consent',
            'consent_type' => $data['consent_type'], // treatment, data_processing, photography
            'consent_text' => $data['consent_text'],
            'signature_data' => $data['signature_data'] ?? null,
            'signed_by' => $data['signed_by'] ?? 'patient',
            'witness' => $data['witness'] ?? null,
            'valid_until' => $data['valid_until'] ?? null,
            'created_at' => date('c'),
            'visit_id' => $data['visit_id'] ?? null,
            'metadata' => [
                'source' => 'consent_manager',
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
            ]
        ];
        
        return $this->cardManager->addCardItem($data['patient_id'], $item_data);
    }
    
    /**
     * Zpracuje přijatou platbu
     */
    private function handlePaymentReceived($data) {
        $item_data = [
            'type' => 'payment',
            'amount' => $data['amount'],
            'currency' => $data['currency'] ?? 'CZK',
            'payment_method' => $data['payment_method'], // cash, card, transfer
            'transaction_id' => $data['transaction_id'] ?? null,
            'description' => $data['description'] ?? '',
            'invoice_number' => $data['invoice_number'] ?? null,
            'created_at' => date('c'),
            'visit_id' => $data['visit_id'] ?? null,
            'metadata' => [
                'source' => 'payment_terminal',
                'terminal_id' => $data['terminal_id'] ?? null,
                'receipt_printed' => $data['receipt_printed'] ?? false
            ]
        ];
        
        return $this->cardManager->addCardItem($data['patient_id'], $item_data);
    }
    
    /**
     * Zpracuje dokončený hovor
     */
    private function handleCallCompleted($data) {
        $item_data = [
            'type' => 'note',
            'title' => 'Telefonní hovor',
            'content' => $data['summary'] ?? 'Hovor s pacientem',
            'call_duration' => $data['duration'] ?? 0,
            'call_type' => $data['call_type'] ?? 'incoming',
            'call_result' => $data['result'] ?? 'completed',
            'phone_number' => $data['phone_number'] ?? '',
            'created_at' => date('c'),
            'visit_id' => null, // Hovory nejsou vázané na návštěvu
            'metadata' => [
                'source' => 'voicebot',
                'call_id' => $data['call_id'] ?? null,
                'recording_url' => $data['recording_url'] ?? null
            ]
        ];
        
        return $this->cardManager->addCardItem($data['patient_id'], $item_data);
    }
    
    /**
     * Zpracuje naplánovanou návštěvu
     */
    private function handleAppointmentScheduled($data) {
        $item_data = [
            'type' => 'note',
            'title' => 'Naplánovaná návštěva',
            'content' => $data['reason'] ?? 'Návštěva u lékaře',
            'appointment_date' => $data['appointment_date'],
            'appointment_time' => $data['appointment_time'],
            'duration_minutes' => $data['duration_minutes'] ?? 30,
            'appointment_type' => $data['appointment_type'] ?? 'checkup',
            'created_at' => date('c'),
            'visit_id' => $data['visit_id'] ?? null,
            'metadata' => [
                'source' => 'calendar',
                'calendar_event_id' => $data['calendar_event_id'] ?? null,
                'reminder_sent' => false
            ]
        ];
        
        return $this->cardManager->addCardItem($data['patient_id'], $item_data);
    }
    
    /**
     * Zaloguje událost do databáze
     */
    private function logEvent($event_type, $event_data) {
        $stmt = $this->db->prepare("
            INSERT INTO card_events (patient_id, event_type, event_data) 
            VALUES (?, ?, ?)
        ");
        
        $patient_id = $event_data['patient_id'] ?? 0;
        $event_json = json_encode($event_data);
        
        $stmt->bind_param("iss", $patient_id, $event_type, $event_json);
        $stmt->execute();
    }
    
    /**
     * Zpracuje nevyřízené události (recovery mechanism)
     */
    public function processUnprocessedEvents() {
        try {
            $stmt = $this->db->prepare("
                SELECT * FROM card_events 
                WHERE processed = FALSE 
                ORDER BY created_at ASC 
                LIMIT 100
            ");
            $stmt->execute();
            $result = $stmt->get_result();
            
            $processed = 0;
            while ($event = $result->fetch_assoc()) {
                $event_data = json_decode($event['event_data'], true);
                $result = $this->handleEvent($event['event_type'], $event_data);
                
                if ($result['success']) {
                    $update_stmt = $this->db->prepare("
                        UPDATE card_events 
                        SET processed = TRUE, processed_at = NOW() 
                        WHERE id = ?
                    ");
                    $update_stmt->bind_param("i", $event['id']);
                    $update_stmt->execute();
                    $processed++;
                }
            }
            
            return ['success' => true, 'processed' => $processed];
            
        } catch (Exception $e) {
            error_log("Process unprocessed events error: " . $e->getMessage());
            return ['success' => false, 'error' => 'Chyba při zpracování nevyřízených událostí'];
        }
    }
}

/**
 * Pomocné funkce pro snadné volání z jiných částí aplikace
 */

function triggerCardEvent($event_type, $event_data) {
    $handler = new CardEventHandler();
    return $handler->handleEvent($event_type, $event_data);
}

function onDocumentSaved($patient_id, $document_data) {
    return triggerCardEvent('document_saved', array_merge($document_data, ['patient_id' => $patient_id]));
}

function onImageUploaded($patient_id, $image_data) {
    return triggerCardEvent('image_uploaded', array_merge($image_data, ['patient_id' => $patient_id]));
}

function onProcedureCompleted($patient_id, $procedure_data) {
    return triggerCardEvent('procedure_completed', array_merge($procedure_data, ['patient_id' => $patient_id]));
}

function onConsentSigned($patient_id, $consent_data) {
    return triggerCardEvent('consent_signed', array_merge($consent_data, ['patient_id' => $patient_id]));
}

function onPaymentReceived($patient_id, $payment_data) {
    return triggerCardEvent('payment_received', array_merge($payment_data, ['patient_id' => $patient_id]));
}

function onCallCompleted($patient_id, $call_data) {
    return triggerCardEvent('call_completed', array_merge($call_data, ['patient_id' => $patient_id]));
}

function onAppointmentScheduled($patient_id, $appointment_data) {
    return triggerCardEvent('appointment_scheduled', array_merge($appointment_data, ['patient_id' => $patient_id]));
}

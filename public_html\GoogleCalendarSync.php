<?php
$autoloadPath = __DIR__ . '/vendor/autoload.php';
if (!file_exists($autoloadPath)) {
    die('Prosím nainstalujte nejdříve z<PERSON>los<PERSON> pomocí p<PERSON>azu: composer install');
}
require_once $autoloadPath;

class GoogleCalendarSync {
    private $client;
    private $service;

    public function __construct() {
        $this->client = new Google_Client();
        $this->client->setAuthConfig('path/to/your/client_secret.json');
        $this->client->addScope(Google_Service_Calendar::CALENDAR);
        $this->client->setAccessType('offline');

        $this->service = new Google_Service_Calendar($this->client);
    }

    public function getAuthUrl() {
        return $this->client->createAuthUrl();
    }

    public function setAccessToken($code) {
        $token = $this->client->fetchAccessTokenWithAuthCode($code);
        $this->client->setAccessToken($token);
        
        // Save the token to the database for future use
        $this->saveTokenToDatabase($token);
    }

    private function saveTokenToDatabase($token) {
        global $mysqli;
        $userId = $_SESSION['user_id'];
        $tokenJson = json_encode($token);
        
        $stmt = $mysqli->prepare("INSERT INTO google_tokens (user_id, token) VALUES (?, ?) ON DUPLICATE KEY UPDATE token = ?");
        $stmt->bind_param("iss", $userId, $tokenJson, $tokenJson);
        $stmt->execute();
        $stmt->close();
    }

    private function loadTokenFromDatabase() {
        global $mysqli;
        $userId = $_SESSION['user_id'];
        
        $stmt = $mysqli->prepare("SELECT token FROM google_tokens WHERE user_id = ?");
        $stmt->bind_param("i", $userId);
        $stmt->execute();
        $result = $stmt->get_result();
        $row = $result->fetch_assoc();
        $stmt->close();

        if ($row) {
            return json_decode($row['token'], true);
        }
        return null;
    }

    public function getEvents($userId) {
        $token = $this->loadTokenFromDatabase();
        if (!$token) {
            throw new Exception("No token found");
        }

        $this->client->setAccessToken($token);

        if ($this->client->isAccessTokenExpired()) {
            if ($this->client->getRefreshToken()) {
                $this->client->fetchAccessTokenWithRefreshToken($this->client->getRefreshToken());
                $this->saveTokenToDatabase($this->client->getAccessToken());
            } else {
                throw new Exception("Refresh token not available");
            }
        }

        $calendarId = 'primary';
        $optParams = array(
            'maxResults' => 250,
            'orderBy' => 'startTime',
            'singleEvents' => true,
            'timeMin' => date('c'),
        );
        $results = $this->service->events->listEvents($calendarId, $optParams);
        return $results->getItems();
    }

    public function syncEvents($userId) {
        // This method would implement the logic to sync events between your system and Google Calendar
        // For now, we'll just fetch the events
        return $this->getEvents($userId);
    }
}


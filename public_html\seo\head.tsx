"use client"

import Head from "next/head"
import { usePathname } from "next/navigation"

interface SEOProps {
  title?: string
  description?: string
  keywords?: string
  ogImage?: string
  ogType?: string
  canonicalUrl?: string
}

export default function SEOHead({
  title = "DentiBot | Moderní řešení pro vaši zubní ordinaci",
  description = "Automatizujte komunikaci s pacienty a zvyšte efektivitu vaší ordinace pomocí našeho inteligentního voicebot systému.",
  keywords = "dentibot, zubní ordinace, automatizace, voicebot, komunikace s pacienty, rezer<PERSON><PERSON><PERSON><PERSON> systém, zubn<PERSON> l<PERSON>, stomatologie",
  ogImage = "/images/dentibot-og-image.jpg",
  ogType = "website",
  canonicalUrl,
}: SEOProps) {
  const pathname = usePathname()
  const siteUrl = "https://dentibot.eu"
  const currentUrl = canonicalUrl || `${siteUrl}${pathname}`

  return (
    <Head>
      <title>{title}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords} />
      <link rel="canonical" href={currentUrl} />

      {/* Open Graph / Facebook */}
      <meta property="og:type" content={ogType} />
      <meta property="og:url" content={currentUrl} />
      <meta property="og:title" content={title} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={`${siteUrl}${ogImage}`} />

      {/* Twitter */}
      <meta property="twitter:card" content="summary_large_image" />
      <meta property="twitter:url" content={currentUrl} />
      <meta property="twitter:title" content={title} />
      <meta property="twitter:description" content={description} />
      <meta property="twitter:image" content={`${siteUrl}${ogImage}`} />

      {/* Další důležité meta tagy */}
      <meta name="robots" content="index, follow" />
      <meta name="viewport" content="width=device-width, initial-scale=1" />
      <meta httpEquiv="Content-Type" content="text/html; charset=utf-8" />
      <meta name="language" content="Czech" />
    </Head>
  )
}


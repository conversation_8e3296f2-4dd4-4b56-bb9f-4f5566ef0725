<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "Kontrola instalace Google Client knihovny:\n";

$googleClientPath = __DIR__ . '/google-client';
echo "Kontroluji cestu: " . $googleClientPath . "\n";

if (!is_dir($googleClientPath)) {
    die("Složka google-client neexistuje!\n");
}

set_include_path(get_include_path() . PATH_SEPARATOR . $googleClientPath);

// Kontrola základních souborů
$requiredFiles = [
    '/vendor/autoload.php',
    '/src/Client.php',
    '/src/Service/Calendar.php'
];

foreach ($requiredFiles as $file) {
    $fullPath = $googleClientPath . $file;
    echo "Kontroluji soubor: " . $fullPath . "\n";
    if (!file_exists($fullPath)) {
        die("<PERSON><PERSON>b<PERSON> požadovaný soubor: " . $file . "\n");
    }
    echo "OK - soubor existuje\n";
}

// Pokus o načtení Google Client
try {
    require_once $googleClientPath . '/vendor/autoload.php';
    $client = new Google_Client();
    echo "Google Client úspěšně načten!\n";
} catch (Exception $e) {
    die("Chyba při načítání Google Client: " . $e->getMessage() . "\n");
}

echo "Instalace je kompletní a funkční!\n";
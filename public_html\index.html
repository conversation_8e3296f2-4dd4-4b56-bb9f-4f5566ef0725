<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dentibot - Kompletní De<PERSON> Aplikace</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="css/cgm_documents.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>
    <style>
        body { font-family: 'Inter', sans-serif; }
        .sidebar {
            width: 16rem;
            background: linear-gradient(135deg, #1e3a8a, #1e40af);
            color: white;
            position: fixed;
            height: 100vh;
            left: 0;
            top: 0;
            z-index: 100;
            overflow-y: auto;
        }
        .main-content {
            margin-left: 16rem;
            min-height: 100vh;
        }
        .nav-link {
            display: flex;
            align-items: center;
            padding: 0.75rem 1.5rem;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.2s;
            cursor: pointer;
        }
        .nav-link:hover, .nav-link.active {
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }
        .nav-icon {
            width: 1.25rem;
            height: 1.25rem;
            margin-right: 0.75rem;
        }
        .page-content {
            display: none;
        }
        .page-content.active {
            display: block;
        }
        .metric-card {
            background: white;
            border-radius: 0.5rem;
            padding: 1.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
        }
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
        }
        .status-successful { background: #dcfce7; color: #166534; }
        .status-failed { background: #fef2f2; color: #dc2626; }
        .status-needs-attention { background: #fef3c7; color: #d97706; }
        .status-completed { background: #dcfce7; color: #166534; }
        .status-draft { background: #f3f4f6; color: #374151; }
        
        .table-container {
            background: white;
            border-radius: 0.5rem;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .table {
            width: 100%;
            border-collapse: collapse;
        }
        .table th {
            background: #f9fafb;
            padding: 0.75rem 1rem;
            text-align: left;
            font-weight: 600;
            color: #374151;
            border-bottom: 1px solid #e5e7eb;
        }
        .table td {
            padding: 0.75rem 1rem;
            border-bottom: 1px solid #f3f4f6;
        }
        .table tr:hover {
            background: #f9fafb;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="p-6 border-b border-blue-600">
            <div class="flex items-center">
                <div class="w-8 h-8 bg-white rounded mr-3 flex items-center justify-center">
                    <span class="text-blue-600 font-bold text-sm">D</span>
                </div>
                <span class="text-xl font-bold">Dentibot</span>
            </div>
            <div class="mt-3 text-sm text-blue-200">
                <div>MUDr. Jan Novák</div>
                <div class="text-xs">Advanced plán</div>
            </div>
        </div>
        <nav class="mt-6">
            <a class="nav-link active" data-page="dashboard" onclick="showPage('dashboard')">
                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <rect x="3" y="3" width="18" height="18" rx="2"/><path d="M3 9h18"/><path d="M9 21V9"/>
                </svg>
                Přehledy
            </a>
            <a class="nav-link" data-page="call-history" onclick="showPage('call-history')">
                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
                </svg>
                Historie hovorů
            </a>
            <a class="nav-link" data-page="sms-campaigns" onclick="showPage('sms-campaigns')">
                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
                </svg>
                Hromadné SMS
            </a>
            <a class="nav-link" data-page="patients" onclick="showPage('patients')">
                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/><circle cx="9" cy="7" r="4"/><path d="M23 21v-2a4 4 0 0 0-3-3.87"/><path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                </svg>
                Seznam pacientů
            </a>
            <a class="nav-link" data-page="cgm-documents" onclick="showPage('cgm-documents')">
                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/><polyline points="14,2 14,8 20,8"/><line x1="16" y1="13" x2="8" y2="13"/><line x1="16" y1="17" x2="8" y2="17"/><polyline points="10,9 9,9 8,9"/><circle cx="12" cy="15" r="2"/>
                </svg>
                Karta pacienta
                <span class="ml-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full">auto</span>
            </a>
            <a href="demo_card_events.php" class="nav-link">
                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                </svg>
                Demo události
                <span class="ml-2 bg-orange-500 text-white text-xs px-2 py-1 rounded-full">test</span>
            </a>
            <a class="nav-link" data-page="settings" onclick="showPage('settings')">
                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <circle cx="12" cy="12" r="3"/><path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
                </svg>
                Nastavení
            </a>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Dashboard Page -->
        <div id="dashboard-page" class="page-content active p-8">
            <div class="mb-8">
                <h1 class="text-3xl font-bold text-gray-900">Dashboard</h1>
                <p class="text-gray-600 mt-2">Přehled aktivit a statistik vaší ordinace</p>
            </div>

            <!-- Metrics Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div class="metric-card">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600">Celkem hovorů</p>
                            <p class="text-2xl font-bold text-gray-900">1,247</p>
                        </div>
                        <div class="text-green-600">
                            <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z"/>
                            </svg>
                        </div>
                    </div>
                    <div class="mt-2">
                        <span class="text-green-600 text-sm font-medium">+12%</span>
                        <span class="text-gray-500 text-sm">vs minulý měsíc</span>
                    </div>
                </div>

                <div class="metric-card">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600">Úspěšné hovory</p>
                            <p class="text-2xl font-bold text-gray-900">1,156</p>
                        </div>
                        <div class="text-blue-600">
                            <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                    </div>
                    <div class="mt-2">
                        <span class="text-green-600 text-sm font-medium">+8%</span>
                        <span class="text-gray-500 text-sm">vs minulý měsíc</span>
                    </div>
                </div>

                <div class="metric-card">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600">Spotřeba minut</p>
                            <p class="text-2xl font-bold text-gray-900">347</p>
                        </div>
                        <div class="text-orange-600">
                            <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                    </div>
                    <div class="mt-2">
                        <span class="text-orange-600 text-sm font-medium">+15%</span>
                        <span class="text-gray-500 text-sm">z 1000 minut</span>
                    </div>
                </div>

                <div class="metric-card">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600">Průměrná délka</p>
                            <p class="text-2xl font-bold text-gray-900">2:34</p>
                        </div>
                        <div class="text-purple-600">
                            <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"/>
                            </svg>
                        </div>
                    </div>
                    <div class="mt-2">
                        <span class="text-red-600 text-sm font-medium">-3%</span>
                        <span class="text-gray-500 text-sm">vs minulý měsíc</span>
                    </div>
                </div>
            </div>

            <!-- Charts -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                <div class="bg-white rounded-lg shadow-sm border p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Hovory za posledních 7 dní</h3>
                    <canvas id="callsChart" width="400" height="200"></canvas>
                </div>
                <div class="bg-white rounded-lg shadow-sm border p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Špičkové hodiny</h3>
                    <canvas id="peakHoursChart" width="400" height="200"></canvas>
                </div>
            </div>

            <!-- Top Reasons -->
            <div class="bg-white rounded-lg shadow-sm border p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Nejčastější důvody hovorů</h3>
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-4 h-4 bg-blue-500 rounded mr-3"></div>
                            <span class="text-gray-700">Objednání termínu</span>
                        </div>
                        <div class="flex items-center">
                            <span class="text-gray-900 font-medium mr-2">456</span>
                            <div class="w-24 bg-gray-200 rounded-full h-2">
                                <div class="bg-blue-500 h-2 rounded-full" style="width: 65%"></div>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-4 h-4 bg-green-500 rounded mr-3"></div>
                            <span class="text-gray-700">Zrušení termínu</span>
                        </div>
                        <div class="flex items-center">
                            <span class="text-gray-900 font-medium mr-2">123</span>
                            <div class="w-24 bg-gray-200 rounded-full h-2">
                                <div class="bg-green-500 h-2 rounded-full" style="width: 18%"></div>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-4 h-4 bg-yellow-500 rounded mr-3"></div>
                            <span class="text-gray-700">Dotaz na ordinační hodiny</span>
                        </div>
                        <div class="flex items-center">
                            <span class="text-gray-900 font-medium mr-2">89</span>
                            <div class="w-24 bg-gray-200 rounded-full h-2">
                                <div class="bg-yellow-500 h-2 rounded-full" style="width: 13%"></div>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-4 h-4 bg-gray-500 rounded mr-3"></div>
                            <span class="text-gray-700">Jiné</span>
                        </div>
                        <div class="flex items-center">
                            <span class="text-gray-900 font-medium mr-2">28</span>
                            <div class="w-24 bg-gray-200 rounded-full h-2">
                                <div class="bg-gray-500 h-2 rounded-full" style="width: 4%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Call History Page -->
        <div id="call-history-page" class="page-content p-8">
            <div class="mb-8">
                <h1 class="text-3xl font-bold text-gray-900">Historie hovorů</h1>
                <p class="text-gray-600 mt-2">Přehled všech hovorů s pacienty</p>
            </div>

            <!-- Filters -->
            <div class="bg-white rounded-lg shadow-sm border p-6 mb-6">
                <div class="flex flex-wrap gap-4">
                    <div class="flex-1 min-w-64">
                        <input type="text" placeholder="Hledat podle jména nebo telefonu..."
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    <div>
                        <select class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                            <option value="">Všechny stavy</option>
                            <option value="successful">Úspěšné</option>
                            <option value="failed">Neúspěšné</option>
                            <option value="needs_attention">Vyžaduje pozornost</option>
                        </select>
                    </div>
                    <div>
                        <select class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                            <option value="">Všechny typy</option>
                            <option value="appointment">Objednání</option>
                            <option value="cancellation">Zrušení</option>
                            <option value="info">Informace</option>
                        </select>
                    </div>
                    <button class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors">
                        Filtrovat
                    </button>
                </div>
            </div>

            <!-- Calls Table -->
            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Pacient</th>
                            <th>Telefon</th>
                            <th>Délka</th>
                            <th>Stav</th>
                            <th>Typ</th>
                            <th>Datum</th>
                            <th>Akce</th>
                        </tr>
                    </thead>
                    <tbody id="callsTableBody">
                        <!-- Data will be populated by JavaScript -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- SMS Campaigns Page -->
        <div id="sms-campaigns-page" class="page-content p-8">
            <div class="mb-8">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">Hromadné SMS</h1>
                        <p class="text-gray-600 mt-2">Správa SMS kampaní pro pacienty</p>
                    </div>
                    <button onclick="openSMSModal()" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                        <svg class="w-5 h-5 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                        </svg>
                        Nová kampaň
                    </button>
                </div>
            </div>

            <!-- SMS Campaigns Table -->
            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Název kampaně</th>
                            <th>Zpráva</th>
                            <th>Příjemci</th>
                            <th>Odesláno</th>
                            <th>Stav</th>
                            <th>Datum</th>
                            <th>Akce</th>
                        </tr>
                    </thead>
                    <tbody id="smsTableBody">
                        <!-- Data will be populated by JavaScript -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Patients Page -->
        <div id="patients-page" class="page-content p-8">
            <div class="mb-8">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">Seznam pacientů</h1>
                        <p class="text-gray-600 mt-2">Správa kartotéky pacientů</p>
                    </div>
                    <button onclick="openPatientModal()" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                        <svg class="w-5 h-5 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                        </svg>
                        Nový pacient
                    </button>
                </div>
            </div>

            <!-- Patients Filters -->
            <div class="bg-white rounded-lg shadow-sm border p-6 mb-6">
                <div class="flex flex-wrap gap-4">
                    <div class="flex-1 min-w-64">
                        <input type="text" placeholder="Hledat pacienta..."
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    <div>
                        <select class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                            <option value="">Všechny pojišťovny</option>
                            <option value="VZP">VZP</option>
                            <option value="ČPZP">ČPZP</option>
                            <option value="VoZP">VoZP</option>
                        </select>
                    </div>
                    <button class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors">
                        Filtrovat
                    </button>
                </div>
            </div>

            <!-- Patients Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" id="patientsGrid">
                <!-- Data will be populated by JavaScript -->
            </div>
        </div>

        <!-- CGM Documents Page -->
        <div id="cgm-documents-page" class="page-content p-8">
            <div class="mb-8">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">Automatická karta pacienta</h1>
                        <p class="text-gray-600 mt-2">Event-driven karta s automatickými časovými razítky</p>
                    </div>
                    <button onclick="openDocumentModal()" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                        <svg class="w-5 h-5 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                        </svg>
                        Nový dokument
                    </button>
                </div>
            </div>

            <!-- Success Alert -->
            <div class="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded mb-6">
                🚀 <strong>Nový koncept:</strong> Automatická karta pacienta! Místo manuálního razítkování dokumentů se karta sama sestavuje z aktivit v CRM.
                <div class="mt-2">
                    <a href="demo_card_events.php" class="underline font-medium">Vyzkoušet demo události</a> |
                    <a href="patient_card_view.php?patient_id=1" class="underline font-medium">Zobrazit kartu pacienta</a>
                </div>
            </div>

            <!-- Documents Filters -->
            <div class="bg-white rounded-lg shadow-sm border p-6 mb-6">
                <div class="flex flex-wrap gap-4">
                    <div class="flex-1 min-w-64">
                        <input type="text" placeholder="Hledat v dokumentech..."
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    <div>
                        <select class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                            <option value="">Všechny kategorie</option>
                            <option value="1">Zdravotnická dokumentace</option>
                            <option value="2">Vyšetření</option>
                            <option value="3">Terapie</option>
                            <option value="4">Recepty</option>
                            <option value="5">Zprávy</option>
                            <option value="6">Preventivní péče</option>
                        </select>
                    </div>
                    <button class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg transition-colors">
                        Filtrovat
                    </button>
                </div>
            </div>

            <!-- Documents Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" id="documentsGrid">
                <!-- Data will be populated by JavaScript -->
            </div>

            <!-- Features Info -->
            <div class="mt-12 bg-gradient-to-r from-blue-50 to-green-50 rounded-lg p-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">Klíčové výhody automatické karty</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="text-center">
                        <div class="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                            </svg>
                        </div>
                        <h3 class="font-semibold text-gray-900 mb-2">Zero-click administrace</h3>
                        <p class="text-sm text-gray-600">Lékař nedělá "Uložit + Razítko", vše běží automaticky po událostech</p>
                    </div>
                    <div class="text-center">
                        <div class="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                        <h3 class="font-semibold text-gray-900 mb-2">Jeden zdroj pravdy</h3>
                        <p class="text-sm text-gray-600">Kontrolor vidí jediný soubor "Karta Jana Nováka" - snadné audity</p>
                    </div>
                    <div class="text-center">
                        <div class="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                            </svg>
                        </div>
                        <h3 class="font-semibold text-gray-900 mb-2">Vyhledávání & reporty</h3>
                        <p class="text-sm text-gray-600">JSON struktura umožní elastické full-text i BI bez parsování PDF</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Settings Page -->
        <div id="settings-page" class="page-content p-8">
            <div class="mb-8">
                <h1 class="text-3xl font-bold text-gray-900">Nastavení</h1>
                <p class="text-gray-600 mt-2">Konfigurace aplikace a uživatelského účtu</p>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- User Profile -->
                <div class="lg:col-span-2">
                    <div class="bg-white rounded-lg shadow-sm border p-6 mb-6">
                        <h2 class="text-xl font-semibold text-gray-900 mb-4">Profil uživatele</h2>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Jméno</label>
                                <input type="text" value="MUDr. Jan Novák" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                                <input type="email" value="<EMAIL>" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Telefon</label>
                                <input type="tel" value="+420 123 456 789" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Ordinace</label>
                                <input type="text" value="Zubní ordinace Dr. Nováka" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                            </div>
                        </div>
                        <div class="mt-6">
                            <button class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors">
                                Uložit změny
                            </button>
                        </div>
                    </div>

                    <!-- API Settings -->
                    <div class="bg-white rounded-lg shadow-sm border p-6">
                        <h2 class="text-xl font-semibold text-gray-900 mb-4">API nastavení</h2>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">VAPI Assistant ID</label>
                                <input type="text" value="asst_1234567890abcdef" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">BulkGate API Key</label>
                                <input type="password" value="••••••••••••••••" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Google Calendar ID</label>
                                <input type="text" value="primary" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                            </div>
                        </div>
                        <div class="mt-6">
                            <button class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg transition-colors">
                                Testovat připojení
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Subscription Info -->
                <div class="lg:col-span-1">
                    <div class="bg-white rounded-lg shadow-sm border p-6 mb-6">
                        <h2 class="text-xl font-semibold text-gray-900 mb-4">Předplatné</h2>
                        <div class="text-center">
                            <div class="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"/>
                                </svg>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900">Advanced</h3>
                            <p class="text-gray-600 text-sm mb-4">Aktivní do 15.2.2025</p>

                            <div class="mb-4">
                                <div class="flex justify-between text-sm mb-1">
                                    <span>Využité minuty</span>
                                    <span>347 / 1000</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-blue-600 h-2 rounded-full" style="width: 34.7%"></div>
                                </div>
                            </div>

                            <button class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 rounded-lg transition-colors">
                                Spravovat předplatné
                            </button>
                        </div>
                    </div>

                    <!-- Quick Stats -->
                    <div class="bg-white rounded-lg shadow-sm border p-6">
                        <h2 class="text-xl font-semibold text-gray-900 mb-4">Rychlé statistiky</h2>
                        <div class="space-y-3">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Celkem pacientů</span>
                                <span class="font-semibold">156</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Dokumentů</span>
                                <span class="font-semibold">89</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">SMS kampaní</span>
                                <span class="font-semibold">12</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Časových razítek</span>
                                <span class="font-semibold">67</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modals -->
    <!-- Document Modal -->
    <div id="documentModal" class="modal">
        <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-screen overflow-y-auto">
            <div class="p-6 border-b">
                <h2 class="text-xl font-semibold text-gray-900">Nový dokument</h2>
            </div>
            <div class="p-6">
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Název dokumentu *</label>
                    <input type="text" id="docTitle" required class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Kategorie</label>
                    <select id="docCategory" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                        <option value="1">Zdravotnická dokumentace</option>
                        <option value="2">Vyšetření</option>
                        <option value="3">Terapie</option>
                        <option value="4">Recepty</option>
                        <option value="5">Zprávy</option>
                        <option value="6">Preventivní péče</option>
                    </select>
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Obsah dokumentu</label>
                    <textarea id="docContent" rows="6" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"></textarea>
                </div>
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Soubor (volitelné)</label>
                    <input type="file" accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.gif,.txt" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    <p class="text-xs text-gray-500 mt-1">Podporované formáty: PDF, DOC, DOCX, JPG, PNG, GIF, TXT (max 10MB)</p>
                </div>
                <div class="flex gap-4">
                    <button onclick="createDocument()" class="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-lg font-medium transition-colors">
                        Vytvořit dokument
                    </button>
                    <button onclick="closeModal('documentModal')" class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                        Zrušit
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Patient Modal -->
    <div id="patientModal" class="modal">
        <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-screen overflow-y-auto">
            <div class="p-6 border-b">
                <h2 class="text-xl font-semibold text-gray-900">Nový pacient</h2>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Jméno a příjmení *</label>
                        <input type="text" id="patientName" required class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Telefon *</label>
                        <input type="tel" id="patientPhone" required class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                        <input type="email" id="patientEmail" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Datum narození</label>
                        <input type="date" id="patientBirth" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Adresa</label>
                    <input type="text" id="patientAddress" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Zdravotní pojišťovna</label>
                    <select id="patientInsurance" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                        <option value="VZP">VZP</option>
                        <option value="ČPZP">ČPZP</option>
                        <option value="VoZP">VoZP</option>
                        <option value="OZP">OZP</option>
                        <option value="ZPŠ">ZPŠ</option>
                        <option value="ZPMV">ZPMV</option>
                        <option value="RBP">RBP</option>
                    </select>
                </div>
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Poznámky</label>
                    <textarea id="patientNotes" rows="3" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"></textarea>
                </div>
                <div class="flex gap-4">
                    <button onclick="createPatient()" class="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-lg font-medium transition-colors">
                        Přidat pacienta
                    </button>
                    <button onclick="closeModal('patientModal')" class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                        Zrušit
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- SMS Modal -->
    <div id="smsModal" class="modal">
        <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-screen overflow-y-auto">
            <div class="p-6 border-b">
                <h2 class="text-xl font-semibold text-gray-900">Nová SMS kampaň</h2>
            </div>
            <div class="p-6">
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Název kampaně *</label>
                    <input type="text" id="smsName" required class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Zpráva *</label>
                    <textarea id="smsMessage" rows="4" maxlength="160" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500" placeholder="Napište vaši SMS zprávu..."></textarea>
                    <div class="text-right text-sm text-gray-500 mt-1">
                        <span id="smsCounter">0</span>/160 znaků
                    </div>
                </div>
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Příjemci</label>
                    <div class="space-y-2">
                        <label class="flex items-center">
                            <input type="checkbox" checked class="mr-2">
                            <span>Všichni pacienti (156 kontaktů)</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="mr-2">
                            <span>Pouze pacienti s nadcházejícími termíny (23 kontaktů)</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="mr-2">
                            <span>Pacienti bez návštěvy za posledních 6 měsíců (45 kontaktů)</span>
                        </label>
                    </div>
                </div>
                <div class="flex gap-4">
                    <button onclick="createSMSCampaign()" class="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-lg font-medium transition-colors">
                        Vytvořit kampaň
                    </button>
                    <button onclick="closeModal('smsModal')" class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                        Zrušit
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Document Detail Modal -->
    <div id="documentDetailModal" class="modal">
        <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-screen overflow-y-auto">
            <div class="p-6 border-b">
                <h2 class="text-xl font-semibold text-gray-900" id="docDetailTitle">Detail dokumentu</h2>
            </div>
            <div class="p-6" id="docDetailContent">
                <!-- Content will be populated by JavaScript -->
            </div>
        </div>
    </div>

    <script src="mock_data.js"></script>
    <script>
        // Global variables
        let currentPage = 'dashboard';
        let callsChart, peakHoursChart;

        // Initialize app
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
        });

        function initializeApp() {
            showPage('dashboard');
            initializeCharts();
            populateCallHistory();
            populateSMSCampaigns();
            populatePatients();
            populateDocuments();

            // SMS counter
            const smsTextarea = document.getElementById('smsMessage');
            const smsCounter = document.getElementById('smsCounter');
            if (smsTextarea && smsCounter) {
                smsTextarea.addEventListener('input', function() {
                    smsCounter.textContent = this.value.length;
                });
            }
        }

        // Navigation
        function showPage(pageId) {
            // Hide all pages
            document.querySelectorAll('.page-content').forEach(page => {
                page.classList.remove('active');
            });

            // Remove active class from all nav links
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });

            // Show selected page
            const page = document.getElementById(pageId + '-page');
            if (page) {
                page.classList.add('active');
            }

            // Add active class to selected nav link
            const navLink = document.querySelector(`[data-page="${pageId}"]`);
            if (navLink) {
                navLink.classList.add('active');
            }

            currentPage = pageId;

            // Refresh charts if dashboard
            if (pageId === 'dashboard') {
                setTimeout(() => {
                    if (callsChart) callsChart.resize();
                    if (peakHoursChart) peakHoursChart.resize();
                }, 100);
            }
        }

        // Charts initialization
        function initializeCharts() {
            // Calls Chart
            const callsCtx = document.getElementById('callsChart');
            if (callsCtx) {
                callsChart = new Chart(callsCtx, {
                    type: 'line',
                    data: {
                        labels: ['1.1', '2.1', '3.1', '4.1', '5.1', '6.1', '7.1'],
                        datasets: [{
                            label: 'Počet hovorů',
                            data: [45, 52, 38, 67, 73, 89, 94],
                            borderColor: '#3b82f6',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            tension: 0.4,
                            fill: true
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            }

            // Peak Hours Chart
            const peakCtx = document.getElementById('peakHoursChart');
            if (peakCtx) {
                peakHoursChart = new Chart(peakCtx, {
                    type: 'bar',
                    data: {
                        labels: ['8:00', '9:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00'],
                        datasets: [{
                            label: 'Počet hovorů',
                            data: [12, 25, 45, 67, 43, 23, 56, 78, 34],
                            backgroundColor: '#10b981',
                            borderRadius: 4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            }
        }

        // Populate Call History
        function populateCallHistory() {
            const tbody = document.getElementById('callsTableBody');
            if (!tbody || !mockCallHistory) return;

            tbody.innerHTML = mockCallHistory.map(call => `
                <tr>
                    <td class="font-medium">${call.patient_name}</td>
                    <td>${call.phone}</td>
                    <td>${Math.floor(call.duration / 60)}:${(call.duration % 60).toString().padStart(2, '0')}</td>
                    <td><span class="status-badge status-${call.status}">${getStatusText(call.status)}</span></td>
                    <td>${getTypeText(call.type)}</td>
                    <td>${formatDate(call.created_at)}</td>
                    <td>
                        <button onclick="viewCallDetail(${call.id})" class="text-blue-600 hover:text-blue-800">
                            Detail
                        </button>
                    </td>
                </tr>
            `).join('');
        }

        // Populate SMS Campaigns
        function populateSMSCampaigns() {
            const tbody = document.getElementById('smsTableBody');
            if (!tbody || !mockSMSCampaigns) return;

            tbody.innerHTML = mockSMSCampaigns.map(campaign => `
                <tr>
                    <td class="font-medium">${campaign.name}</td>
                    <td class="max-w-xs truncate">${campaign.message}</td>
                    <td>${campaign.recipients_count}</td>
                    <td>${campaign.sent_count}</td>
                    <td><span class="status-badge status-${campaign.status}">${getStatusText(campaign.status)}</span></td>
                    <td>${formatDate(campaign.created_at)}</td>
                    <td>
                        <button onclick="viewSMSDetail(${campaign.id})" class="text-blue-600 hover:text-blue-800">
                            Detail
                        </button>
                    </td>
                </tr>
            `).join('');
        }

        // Populate Patients
        function populatePatients() {
            const grid = document.getElementById('patientsGrid');
            if (!grid || !mockPatients) return;

            grid.innerHTML = mockPatients.map(patient => `
                <div class="bg-white rounded-lg shadow-sm border p-6 hover:shadow-md transition-shadow">
                    <div class="flex items-start justify-between mb-4">
                        <div class="flex-1">
                            <h3 class="font-semibold text-gray-900 mb-1">${patient.name}</h3>
                            <p class="text-sm text-gray-600">${patient.phone}</p>
                            <p class="text-sm text-gray-600">${patient.email}</p>
                        </div>
                        <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">${patient.insurance}</span>
                    </div>
                    <div class="space-y-2 text-sm text-gray-600 mb-4">
                        <div class="flex justify-between">
                            <span>Poslední návštěva:</span>
                            <span>${formatDate(patient.last_visit)}</span>
                        </div>
                        ${patient.next_appointment ? `
                            <div class="flex justify-between">
                                <span>Další termín:</span>
                                <span class="text-green-600">${formatDate(patient.next_appointment)}</span>
                            </div>
                        ` : ''}
                    </div>
                    <div class="flex gap-2">
                        <a href="patient_card_view.php?patient_id=${patient.id}" class="flex-1 bg-blue-50 hover:bg-blue-100 text-blue-700 px-4 py-2 rounded text-sm font-medium transition-colors text-center">
                            Karta pacienta
                        </a>
                        <button onclick="editPatient(${patient.id})" class="bg-gray-50 hover:bg-gray-100 text-gray-700 px-4 py-2 rounded text-sm font-medium transition-colors">
                            Upravit
                        </button>
                    </div>
                </div>
            `).join('');
        }

        // Populate Documents
        function populateDocuments() {
            const grid = document.getElementById('documentsGrid');
            if (!grid || !mockDocuments) return;

            grid.innerHTML = mockDocuments.map(doc => `
                <div class="document-card bg-white rounded-lg p-6">
                    <div class="flex items-start justify-between mb-4">
                        <div class="flex-1">
                            <h3 class="font-semibold text-gray-900 mb-2">${doc.title}</h3>
                            <p class="text-sm text-gray-600 mb-2">${doc.category_name}</p>
                            <p class="text-xs text-gray-500">${formatDate(doc.created_at)}</p>
                        </div>
                        ${doc.timestamp_count > 0 ? `
                            <span class="timestamp-badge text-white text-xs px-2 py-1 rounded-full">
                                ${doc.timestamp_count} razítek
                            </span>
                        ` : ''}
                    </div>

                    ${doc.content ? `
                        <p class="text-sm text-gray-700 mb-4 line-clamp-3">
                            ${doc.content.substring(0, 150)}...
                        </p>
                    ` : ''}

                    <div class="flex gap-2">
                        <button onclick="viewDocumentDetail(${doc.id})" class="flex-1 bg-blue-50 hover:bg-blue-100 text-blue-700 px-4 py-2 rounded text-sm font-medium transition-colors">
                            Zobrazit
                        </button>
                        <button onclick="createTimestamp(${doc.id})" class="bg-green-50 hover:bg-green-100 text-green-700 px-4 py-2 rounded text-sm font-medium transition-colors">
                            + Razítko
                        </button>
                    </div>
                </div>
            `).join('');
        }

        // Modal functions
        function openDocumentModal() {
            document.getElementById('documentModal').classList.add('active');
        }

        function openPatientModal() {
            document.getElementById('patientModal').classList.add('active');
        }

        function openSMSModal() {
            document.getElementById('smsModal').classList.add('active');
        }

        function closeModal(modalId) {
            document.getElementById(modalId).classList.remove('active');
        }

        // Create functions
        function createDocument() {
            const title = document.getElementById('docTitle').value;
            const category = document.getElementById('docCategory').value;
            const content = document.getElementById('docContent').value;

            if (!title.trim()) {
                alert('Prosím vyplňte název dokumentu');
                return;
            }

            // Simulate document creation
            const newDoc = {
                id: mockDocuments.length + 1,
                title: title,
                category_name: mockCategories.find(c => c.id == category)?.name || 'Neznámá',
                content: content,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
                author_name: 'MUDr. Jan Novák',
                document_hash: 'mock_hash_' + Date.now(),
                timestamp_count: 1,
                file_path: null,
                file_type: null,
                file_size: null
            };

            mockDocuments.unshift(newDoc);
            populateDocuments();
            closeModal('documentModal');

            // Clear form
            document.getElementById('docTitle').value = '';
            document.getElementById('docContent').value = '';

            showAlert('Dokument byl úspěšně vytvořen a opatřen časovým razítkem!', 'success');
        }

        function createPatient() {
            const name = document.getElementById('patientName').value;
            const phone = document.getElementById('patientPhone').value;
            const email = document.getElementById('patientEmail').value;
            const birth = document.getElementById('patientBirth').value;
            const address = document.getElementById('patientAddress').value;
            const insurance = document.getElementById('patientInsurance').value;
            const notes = document.getElementById('patientNotes').value;

            if (!name.trim() || !phone.trim()) {
                alert('Prosím vyplňte povinná pole (jméno a telefon)');
                return;
            }

            // Simulate patient creation
            const newPatient = {
                id: mockPatients.length + 1,
                name: name,
                phone: phone,
                email: email,
                birth_date: birth,
                address: address,
                insurance: insurance,
                last_visit: null,
                next_appointment: null,
                notes: notes
            };

            mockPatients.unshift(newPatient);
            populatePatients();
            closeModal('patientModal');

            // Clear form
            document.getElementById('patientName').value = '';
            document.getElementById('patientPhone').value = '';
            document.getElementById('patientEmail').value = '';
            document.getElementById('patientBirth').value = '';
            document.getElementById('patientAddress').value = '';
            document.getElementById('patientNotes').value = '';

            showAlert('Pacient byl úspěšně přidán!', 'success');
        }

        function createSMSCampaign() {
            const name = document.getElementById('smsName').value;
            const message = document.getElementById('smsMessage').value;

            if (!name.trim() || !message.trim()) {
                alert('Prosím vyplňte název kampaně a zprávu');
                return;
            }

            // Simulate SMS campaign creation
            const newCampaign = {
                id: mockSMSCampaigns.length + 1,
                name: name,
                message: message,
                recipients_count: 156,
                sent_count: 0,
                status: 'draft',
                created_at: new Date().toISOString(),
                sent_at: null
            };

            mockSMSCampaigns.unshift(newCampaign);
            populateSMSCampaigns();
            closeModal('smsModal');

            // Clear form
            document.getElementById('smsName').value = '';
            document.getElementById('smsMessage').value = '';
            document.getElementById('smsCounter').textContent = '0';

            showAlert('SMS kampaň byla úspěšně vytvořena!', 'success');
        }

        // Detail view functions
        function viewDocumentDetail(docId) {
            const doc = mockDocuments.find(d => d.id === docId);
            if (!doc) return;

            const timestamps = mockTimestamps[docId] || [];

            document.getElementById('docDetailTitle').textContent = doc.title;
            document.getElementById('docDetailContent').innerHTML = `
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <div class="lg:col-span-2">
                        <div class="mb-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Obsah dokumentu</h3>
                            <div class="document-content p-6 rounded-lg">
                                <pre class="whitespace-pre-wrap text-gray-800">${doc.content}</pre>
                            </div>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-gray-700 mb-2">Hash dokumentu (SHA-256)</h3>
                            <div class="hash-display p-3 rounded text-xs">${doc.document_hash}</div>
                        </div>
                    </div>
                    <div class="lg:col-span-1">
                        <div class="bg-white rounded-lg shadow-sm border">
                            <div class="p-6 border-b">
                                <h3 class="text-lg font-semibold text-gray-900">Časová razítka</h3>
                                <p class="text-sm text-gray-600 mt-1">${timestamps.length} razítek</p>
                            </div>
                            <div class="p-6">
                                ${timestamps.length === 0 ? `
                                    <div class="text-center py-8">
                                        <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                                        </svg>
                                        <p class="text-gray-600 text-sm">Žádná časová razítka</p>
                                    </div>
                                ` : `
                                    <div class="space-y-4">
                                        ${timestamps.map(ts => `
                                            <div class="timestamp-card p-4 rounded-lg">
                                                <div class="flex items-start justify-between mb-2">
                                                    <div>
                                                        <p class="font-medium text-green-900 text-sm">Razítko #${ts.id}</p>
                                                        <p class="text-xs text-green-700">${formatDate(ts.created_at)}</p>
                                                    </div>
                                                    <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                                                        ${ts.is_valid ? 'Platné' : 'Neplatné'}
                                                    </span>
                                                </div>
                                                <div class="text-xs text-green-700 mb-3">
                                                    <p>TSA: ${new URL(ts.tsa_url).hostname}</p>
                                                    <p>Platnost do: ${formatDate(ts.expires_at)}</p>
                                                </div>
                                                <div class="flex gap-2">
                                                    <button onclick="verifyTimestamp(${ts.id})" class="flex-1 bg-green-50 hover:bg-green-100 text-green-700 px-3 py-1 rounded text-xs font-medium transition-colors">
                                                        Ověřit
                                                    </button>
                                                </div>
                                            </div>
                                        `).join('')}
                                    </div>
                                `}
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.getElementById('documentDetailModal').classList.add('active');
        }

        function createTimestamp(docId) {
            if (!confirm('Opravdu chcete vytvořit nové časové razítko pro tento dokument?')) {
                return;
            }

            // Simulate timestamp creation
            if (!mockTimestamps[docId]) {
                mockTimestamps[docId] = [];
            }

            const newTimestamp = {
                id: Object.values(mockTimestamps).flat().length + 1,
                document_id: docId,
                created_at: new Date().toISOString(),
                expires_at: new Date(Date.now() + 10 * 365 * 24 * 60 * 60 * 1000).toISOString(),
                is_valid: true,
                tsa_url: 'http://timestamp.digicert.com',
                timestamp_hash: 'ts_mock_' + Date.now()
            };

            mockTimestamps[docId].push(newTimestamp);

            // Update document timestamp count
            const doc = mockDocuments.find(d => d.id === docId);
            if (doc) {
                doc.timestamp_count = mockTimestamps[docId].length;
            }

            populateDocuments();
            showAlert('Časové razítko bylo úspěšně vytvořeno!', 'success');
        }

        function verifyTimestamp(timestampId) {
            // Simulate timestamp verification
            setTimeout(() => {
                showAlert('Časové razítko je platné a ověřené!', 'success');
            }, 500);
        }

        // Utility functions
        function getStatusText(status) {
            const statusMap = {
                'successful': 'Úspěšný',
                'failed': 'Neúspěšný',
                'needs_attention': 'Vyžaduje pozornost',
                'completed': 'Dokončeno',
                'draft': 'Koncept'
            };
            return statusMap[status] || status;
        }

        function getTypeText(type) {
            const typeMap = {
                'appointment': 'Objednání',
                'cancellation': 'Zrušení',
                'info': 'Informace'
            };
            return typeMap[type] || type;
        }

        function formatDate(dateString) {
            if (!dateString) return 'N/A';
            const date = new Date(dateString);
            return date.toLocaleDateString('cs-CZ', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        function showAlert(message, type = 'info') {
            const alertClass = {
                success: 'bg-green-100 border-green-400 text-green-700',
                error: 'bg-red-100 border-red-400 text-red-700',
                warning: 'bg-yellow-100 border-yellow-400 text-yellow-700',
                info: 'bg-blue-100 border-blue-400 text-blue-700'
            }[type];

            const alertHtml = `
                <div class="${alertClass} px-4 py-3 rounded mb-6 alert-message" style="position: fixed; top: 20px; right: 20px; z-index: 1001; min-width: 300px;">
                    ${message}
                    <button onclick="this.parentElement.remove()" class="float-right font-bold ml-4">&times;</button>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', alertHtml);

            // Auto-remove after 5 seconds
            setTimeout(() => {
                const alert = document.querySelector('.alert-message');
                if (alert) alert.remove();
            }, 5000);
        }

        // Placeholder functions for other actions
        function viewCallDetail(callId) {
            showAlert('Detail hovoru #' + callId + ' - funkce bude implementována', 'info');
        }

        function viewSMSDetail(campaignId) {
            showAlert('Detail SMS kampaně #' + campaignId + ' - funkce bude implementována', 'info');
        }

        function viewPatientDetail(patientId) {
            showAlert('Detail pacienta #' + patientId + ' - funkce bude implementována', 'info');
        }

        function editPatient(patientId) {
            showAlert('Úprava pacienta #' + patientId + ' - funkce bude implementována', 'info');
        }

        // Close modals when clicking outside
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('modal')) {
                e.target.classList.remove('active');
            }
        });
    </script>
</body>
</html>

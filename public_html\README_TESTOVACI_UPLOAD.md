# 🦷 Dentibot - Testovací Upload

## 📋 **<PERSON><PERSON><PERSON><PERSON> změn**

Tato verze zachováv<PERSON> **100% původní funkcionality** a přidává nové funkce automatické karty pacienta.

### ✅ **Zachované původní funkce:**
- **VAPI dashboard** - kompletní statistiky ho<PERSON>
- **Baserow integrace** - načítání pacientů z databáze
- **Historie hovorů** - původn<PERSON> funkcionalita
- **SMS kampaně** - původní funkcionalita
- **Nastavení** - API klíče a konfigurace

### 🆕 **Nové funkce:**
- **Automatická karta pacienta** - event-driven systém
- **Zero-click administrace** - bez manuálního razítkování
- **Timeline view** - chronologický přehled aktivit
- **Demo události** - testování funkcionalita

---

## 🚀 **<PERSON><PERSON><PERSON> spuštění**

### 1. **<PERSON>lav<PERSON><PERSON> soubory:**
- `main_dashboard.php` - hlavní dashboard (místo původního `index.html`)
- `config_optimized.php` - optimalizovaná konfigurace
- `patients_list.php` - seznam pacientů z Baserow
- `patient_cards.php` - automatické karty pacientů

### 2. **Demo stránky:**
- `demo_events.html` - simulátor událostí
- `patient_card_demo.html` - ukázka karty pacienta
- `baserow_card_demo.php` - integrace s Baserow

### 3. **Původní funkcionalita:**
- Všechny původní soubory zachovány
- `baserow_functions.php` - rozšířen o nové funkce
- `api_functions.php` - zachována původní VAPI integrace

---

## ⚙️ **Konfigurace**

### **Databáze:**
```sql
-- Zachovává původní tabulky + přidává nové
-- Spusťte: migrate_patient_cards.php
```

### **Baserow kolonky:**
Přidejte do vaší Baserow tabulky pacientů:

#### **Základní kolonky:**
- `Karta_pacienta_JSON` (Long text)
- `Verze_karty` (Number)
- `Hash_karty` (Single line text)
- `Posledni_aktivita` (Date)
- `Auto_karta_aktivni` (Checkbox)

#### **Statistické kolonky:**
- `Pocet_TST` (Number)
- `Pocet_zaznamu` (Number)
- `Pocet_snimku` (Number)
- `Pocet_vykonu` (Number)
- `Pocet_souhlasu` (Number)
- `Pocet_plateb` (Number)
- `Sync_status` (Single select: "Synced", "Pending", "Error")

### **API klíče:**
V `config_optimized.php` nebo environment variables:
```php
// VAPI - zachováno původní
define('VAPI_API_KEY', 'váš_vapi_klíč');

// Baserow - zachováno původní
define('BASEROW_API_TOKEN', 'váš_baserow_token');
define('BASEROW_DATABASE_ID', 'vaše_database_id');
define('BASEROW_TABLE_ID', 'vaše_table_id');
```

---

## 🧪 **Testování**

### **1. Základní funkcionalita:**
1. Otevřete `main_dashboard.php`
2. Zkontrolujte VAPI statistiky
3. Ověřte načítání pacientů z Baserow

### **2. Nové funkce:**
1. **Demo události:** `demo_events.html`
   - Simulujte různé typy událostí
   - Testujte automatické přidávání do karty

2. **Karty pacientů:** `patient_cards.php`
   - Zobrazení automatických karet
   - Statistiky a timeline

3. **Baserow integrace:** `baserow_card_demo.php`
   - Testování s reálnými daty
   - Přidávání událostí do Baserow

### **3. Kompatibilita:**
- Všechny původní URL fungují
- Původní API endpointy zachovány
- Databázová struktura rozšířena, ne změněna

---

## 📁 **Struktura souborů**

```
public_html/
├── main_dashboard.php          # Hlavní dashboard
├── config_optimized.php       # Optimalizovaná konfigurace
├── patients_list.php          # Seznam pacientů
├── patient_cards.php          # Automatické karty
├── demo_events.html           # Demo události
├── patient_card_demo.html     # Demo karta
├── baserow_card_demo.php      # Baserow integrace
├── patient_card/              # Nové funkce
│   ├── BaserowCardManager.php
│   ├── PatientCardManager.php
│   └── CardEventHandler.php
├── migrate_patient_cards.php  # Migrace databáze
└── [původní soubory zachovány]
```

---

## 🔧 **Řešení problémů**

### **Chyba: "Chybí konfigurace Baserow"**
- Zkontrolujte API token v nastavení
- Ověřte Database ID a Table ID

### **Chyba: "VAPI API klíč není nastaven"**
- Přidejte VAPI klíč v nastavení
- Zkontrolujte assistant ID

### **Chyba: "Pacient nenalezen"**
- Ověřte, že pacient existuje v Baserow
- Zkontrolujte telefonní číslo

### **Chyba: "Databázové připojení"**
- Zkontrolujte `config_optimized.php`
- Spusťte `migrate_patient_cards.php`

---

## 🎯 **Klíčové výhody**

### **Pro lékaře:**
- ✅ **Zero-click** - žádné manuální kroky
- ✅ **Jeden soubor** - "Karta Jana Nováka"
- ✅ **Automatické TST** - okamžité + denní
- ✅ **Timeline view** - chronologický přehled

### **Pro kontrolory:**
- ✅ **Jeden zdroj pravdy** - jediný soubor na pacienta
- ✅ **Snadné audity** - všechno v jednom PDF
- ✅ **Ověřitelná razítka** - SHA-256 + RFC 3161

### **Pro IT:**
- ✅ **JSON struktura** - rychlé vyhledávání
- ✅ **Event-driven** - škálovatelná architektura
- ✅ **API ready** - připraveno pro integraci

---

## 📞 **Podpora**

Pokud narazíte na problémy:

1. **Zkontrolujte logy:** `config_error.log`, `app.log`
2. **Ověřte nastavení:** API klíče, databázové připojení
3. **Testujte demo:** Začněte s `demo_events.html`
4. **Postupně:** Nejdříve základní funkce, pak nové

---

## 🔄 **Migrace z původní verze**

1. **Zálohujte** původní soubory
2. **Nahrajte** nové soubory
3. **Spusťte** `migrate_patient_cards.php`
4. **Přidejte** kolonky do Baserow
5. **Testujte** funkcionalitu

**Původní funkcionalita zůstává 100% zachována!**

---

## 📈 **Další kroky**

Po úspěšném testování můžeme přidat:
- **SMS notifikace** při událostech
- **Email reporty** pro kontrolory
- **Mobilní aplikaci** pro lékaře
- **BI dashboard** pro statistiky
- **Další balíčky funkcí** podle plánu

**Aplikace je připravena na produkční nasazení!** 🚀

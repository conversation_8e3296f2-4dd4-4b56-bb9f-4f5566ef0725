<?php
require_once 'config.php';
require_once 'db_connection.php';
require_once 'api_bulkgate.php';
require_once 'baserow_functions.php';
require_once 'error_log.php';

// Initialize session and check for errors
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

$error = null;
$success = null;

// Set the current page for the sidebar
$currentPage = 'sms_campaigns';

// Generate CSRF token if not exists
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
        die('CSRF token validation failed');
    }
}

$csrfToken = $_SESSION['csrf_token'];

// Funkce pro získání seznamu kampaní
function getCampaigns($user_id) {
    try {
        $db = getDbConnection();
        $stmt = $db->prepare("
            SELECT id, campaign_name, message, sent_date, status, 
                   total_messages, delivered_messages, failed_messages 
            FROM sms_campaigns 
            WHERE user_id = ? 
            ORDER BY sent_date DESC
        ");
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $campaigns = $result->fetch_all(MYSQLI_ASSOC);
        $stmt->close();
        $db->close();
        return $campaigns;
    } catch (Exception $e) {
        error_log("Error fetching campaigns: " . $e->getMessage());
        return [];
    }
}

$campaigns = getCampaigns($_SESSION['user_id']);

// Handle AJAX requests
if (isset($_GET['action'])) {
    header('Content-Type: application/json');
    
    switch ($_GET['action']) {
        case 'get_patients':
            $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
            $perPage = isset($_GET['perPage']) ? intval($_GET['perPage']) : 20;
            $searchTerm = isset($_GET['search']) ? $_GET['search'] : '';
            
            try {
                $result = getPatients($page, $perPage, $searchTerm);
                echo json_encode($result);
            } catch (Exception $e) {
                echo json_encode(['error' => $e->getMessage()]);
            }
            exit;
            
        case 'get_campaign_details':
            if (!isset($_GET['id'])) {
                echo json_encode(['error' => 'Missing campaign ID']);
                exit;
            }
            
            try {
                $campaignId = intval($_GET['id']);
                $details = getCampaignDetails($campaignId);
                echo json_encode($details);
            } catch (Exception $e) {
                echo json_encode(['error' => $e->getMessage()]);
            }
            exit;
    }
}
?>
<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hromadné SMS - Dentibot</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <style>
        [x-cloak] { display: none !important; }
        
        body {
            background-color: #1A202C;
            color: white;
        }
        
        .custom-table {
            background-color: #1E2A3B;
        }
        
        .custom-table th {
            color: #94A3B8;
            border-bottom: 1px solid #2D3748;
            padding: 12px 24px;
            font-weight: normal;
            font-size: 0.75rem;
            text-transform: uppercase;
        }
        
        .custom-table td {
            color: #E2E8F0;
            border-bottom: 1px solid #2D3748;
            padding: 12px 24px;
        }
        
        .custom-table tr:hover {
            background-color: #243147;
        }
        
        .btn-primary {
            background-color: #00B8A9;
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 0.875rem;
        }
        
        .btn-primary:hover {
            background-color: #009B8E;
        }
        
        .form-input {
            background-color: #1E2A3B;
            border: 1px solid #2D3748;
            color: white;
            padding: 0.5rem 0.75rem;
            border-radius: 0.375rem;
            width: 100%;
            font-size: 0.875rem;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #00B8A9;
        }
        
        .form-textarea {
            background-color: #1E2A3B;
            border: 1px solid #2D3748;
            color: white;
            padding: 0.5rem 0.75rem;
            border-radius: 0.375rem;
            width: 100%;
            min-height: 100px;
            font-size: 0.875rem;
            resize: vertical;
        }
        
        .form-textarea:focus {
            outline: none;
            border-color: #00B8A9;
        }
        
        .checkbox {
            width: 1rem;
            height: 1rem;
            border-radius: 0.25rem;
            border: 1px solid #2D3748;
            background-color: #1E2A3B;
        }
        
        .checkbox:checked {
            background-color: #00B8A9;
            border-color: #00B8A9;
        }
        
        .patient-list {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .patient-list::-webkit-scrollbar {
            width: 8px;
        }
        
        .patient-list::-webkit-scrollbar-track {
            background: #1E2A3B;
        }
        
        .patient-list::-webkit-scrollbar-thumb {
            background: #2D3748;
            border-radius: 4px;
        }
        
        .patient-list::-webkit-scrollbar-thumb:hover {
            background: #4A5568;
        }
        
        .campaign-status {
            padding: 2px 8px;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
        }
        
        .campaign-status-sent {
            background-color: rgba(0, 184, 169, 0.1);
            color: #00B8A9;
        }
        
        .campaign-status-processing {
            background-color: rgba(251, 191, 36, 0.1);
            color: #FBB224;
        }

        /* Light theme overrides */
        .light-theme {
            background-color: white;
            color: #1A202C;
        }

        .light-theme .custom-table {
            background-color: white;
        }

        .light-theme .custom-table th {
            color: #4A5568;
            border-bottom-color: #E2E8F0;
        }

        .light-theme .custom-table td {
            color: #2D3748;
            border-bottom-color: #E2E8F0;
        }

        .light-theme .custom-table tr:hover {
            background-color: #F7FAFC;
        }

        .light-theme .form-input,
        .light-theme .form-textarea {
            background-color: white;
            border-color: #E2E8F0;
            color: #2D3748;
        }

        .light-theme .checkbox {
            background-color: white;
            border-color: #E2E8F0;
        }
    </style>
</head>
<body x-data="{ darkMode: localStorage.getItem('theme') === 'dark' }" 
      :class="{ 'light-theme': !darkMode }"
      class="min-h-screen">
    
    <div x-data="smsCampaignApp()" 
         x-init="init()" 
         class="flex min-h-screen">
        
        <?php include 'sidebar.php'; ?>

        <div class="flex-1 p-6">
            <div class="max-w-[1400px] mx-auto space-y-6">
                <!-- Header -->
                <div class="flex justify-between items-center">
                    <h1 class="text-2xl font-semibold">Hromadné SMS</h1>
                </div>

                <!-- New Campaign Form -->
                <div class="bg-[#1E2A3B] rounded-lg p-6 space-y-6">
                    <h2 class="text-red-500 font-semibold text-lg">Pozor SMS zpoplatněna 0,75 KČ/SMS!</h2>
                    
                    <form @submit.prevent="submitCampaign" class="space-y-4">
                        <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                        
                        <div>
                            <label class="block text-sm mb-2">Název</label>
                            <input type="text" 
                                   x-model="campaignName" 
                                   class="form-input" 
                                   required>
                        </div>
                        
                        <div>
                            <label class="block text-sm mb-2">Zpráva</label>
                            <textarea x-model="message" 
                                    class="form-textarea" 
                                    required
                                    @input="updateCharCount"></textarea>
                            <div class="text-xs text-gray-400 mt-1" 
                                 x-text="'Zbývá znaků: ' + (160 - message.length)"></div>
                        </div>

                        <!-- Patient Selection -->
                        <div>
                            <label class="block text-sm mb-2">Vybrat příjemce</label>
                            <div class="space-y-4">
                                <div class="flex justify-between items-center">
                                    <input type="text" 
                                           x-model="searchTerm" 
                                           @input="searchPatients" 
                                           placeholder="Vyhledat pacienta..." 
                                           class="form-input w-96">
                                    <button type="button" 
                                            @click="toggleAllPatients" 
                                            class="btn-primary">
                                        <span x-text="allSelected ? 'Odznačit vše' : 'Vybrat všechny pacienty'"></span>
                                    </button>
                                </div>

                                <div class="patient-list">
                                    <table class="w-full custom-table">
                                        <thead>
                                            <tr>
                                                <th class="w-8">
                                                    <input type="checkbox" 
                                                           x-model="allSelected"
                                                           @click="toggleAllPatients"
                                                           class="checkbox">
                                                </th>
                                                <th>JMÉNO</th>
                                                <th>EMAIL</th>
                                                <th>TELEFON</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <template x-for="patient in patients" :key="patient.id">
                                                <tr>
                                                    <td class="text-center">
                                                        <input type="checkbox"
                                                               :checked="patient.selected"
                                                               @click="togglePatient(patient)"
                                                               class="checkbox">
                                                    </td>
                                                    <td x-text="patient.name"></td>
                                                    <td x-text="patient.email"></td>
                                                    <td x-text="patient.phone"></td>
                                                </tr>
                                            </template>
                                        </tbody>
                                    </table>
                                </div>

                                <div class="flex justify-between items-center text-sm text-gray-400">
                                    <div>
                                        Zobrazeno <span x-text="(currentPage - 1) * perPage + 1"></span>
                                        až <span x-text="Math.min(currentPage * perPage, totalPatients)"></span>
                                        z <span x-text="totalPatients"></span> pacientů
                                    </div>
                                    <div class="flex gap-2">
                                        <button type="button"
                                                @click="prevPage"
                                                :disabled="currentPage === 1"
                                                class="px-3 py-1 rounded border border-gray-600 disabled:opacity-50">
                                            Předchozí
                                        </button>
                                        <template x-for="page in pageNumbers" :key="page">
                                            <button type="button"
                                                    @click="goToPage(page)"
                                                    :class="{'bg-[#243147] border-[#00B8A9]': page === currentPage}"
                                                    class="px-3 py-1 rounded border border-gray-600">
                                                <span x-text="page"></span>
                                            </button>
                                        </template>
                                        <button type="button"
                                                @click="nextPage"
                                                :disabled="currentPage === totalPages"
                                                class="px-3 py-1 rounded border border-gray-600 disabled:opacity-50">
                                            Další
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div>
                            <button type="submit" 
                                    class="btn-primary"
                                    :disabled="processing"
                                    x-text="processing ? 'Odesílání...' : 'Odeslat kampaň'">
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Sent Campaigns -->
                <div class="bg-[#1E2A3B] rounded-lg p-6">
                    <h2 class="text-lg font-semibold mb-4">Odeslané kampaně</h2>
                    <?php if (empty($campaigns)): ?>
                        <p class="text-gray-400">Zatím nemáte žádné odeslané kampaně.</p>
                    <?php else: ?>
                        <div class="overflow-x-auto">
                            <table class="w-full custom-table">
                                <thead>
                                    <tr>
                                        <th>NÁZEV KAMPANĚ</th>
                                        <th>DATUM ODESLÁNÍ</th>
                                        <th>ZPRÁVA</th>
                                        <th>STATUS</th>
                                        <th>STATISTIKY</th>
                                        <th>AKCE</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($campaigns as $campaign): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($campaign['campaign_name']); ?></td>
                                            <td><?php echo date('d.m.Y H:i', strtotime($campaign['sent_date'])); ?></td>
                                            <td><?php echo htmlspecialchars($campaign['message']); ?></td>
                                            <td>
                                                <span class="campaign-status <?php echo $campaign['status'] === 'sent' ? 'campaign-status-sent' : 'campaign-status-processing'; ?>">
                                                    <?php echo $campaign['status'] === 'sent' ? 'Odesláno' : 'Zpracovává se'; ?>
                                                </span>
                                            </td>
                                            <td class="text-gray-400">
                                                Celkem: <?php echo $campaign['total_messages']; ?><br>
                                                Doručeno: <?php echo $campaign['delivered_messages']; ?><br>
                                                Selhalo: <?php echo $campaign['failed_messages']; ?>
                                            </td>
                                            <td>
                                                <button @click="showCampaignDetails(<?php echo $campaign['id']; ?>)"
                                                        class="text-[#00B8A9] hover:text-[#009B8E]">
                                                    Detaily
                                                </button>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Campaign Details Modal -->
    <div x-show="showModal" 
         x-cloak
         class="fixed inset-0 bg-black bg-opacity-50 overflow-y-auto h-full w-full"
         @click.self="showModal = false">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-4/5 shadow-lg rounded-md bg-[#1E2A3B]">
            <div class="flex justify-between items-center pb-3">
                <h3 class="text-xl font-bold">Detaily kampaně</h3>
                <button @click="showModal = false" 
                        class="text-gray-400 hover:text-gray-500">
                    <span class="text-2xl">&times;</span>
                </button>
            </div>
            <div id="campaignDetailsContent" class="mt-4">
                <!-- Modal content will be dynamically inserted here -->
            </div>
        </div>
    </div>

    <script>
        function smsCampaignApp() {
    return {
        patients: [],
        totalPatients: 0,
        currentPage: 1,
        perPage: 20,
        totalPages: 1,
        searchTerm: '',
        selectedPatients: [],
        allSelected: false,
        campaignName: '',
        message: '',
        showModal: false,
        processing: false,

        async init() {
            await this.fetchPatients();
            this.$watch('selectedPatients', () => {
                this.updateAllSelected();
            });
        },

        async fetchPatients() {
            try {
                const response = await fetch(`patients.php?action=get_patients&page=${this.currentPage}&perPage=${this.perPage}&search=${this.searchTerm}`);
                if (!response.ok) throw new Error('Network response was not ok');
                
                const data = await response.json();
                
                // Zachováme výběr pacientů při změně stránky
                this.patients = data.patients.map(patient => ({
                    ...patient,
                    selected: this.selectedPatients.some(p => p.id === patient.id)
                }));
                
                this.totalPatients = data.totalRecords;
                this.totalPages = Math.ceil(this.totalPatients / this.perPage);
                this.currentPage = Math.max(1, Math.min(this.currentPage, this.totalPages));
                this.updateAllSelected();
            } catch (error) {
                console.error('Error fetching patients:', error);
                alert('Chyba při načítání pacientů');
            }
        },

        async searchPatients() {
            this.currentPage = 1;
            await this.fetchPatients();
        },

        toggleAllPatients() {
            const newState = !this.allSelected;
            
            // Aktualizujeme stav všech pacientů na aktuální stránce
            this.patients.forEach(patient => {
                patient.selected = newState;
                
                if (newState) {
                    // Přidáme pacienta do selectedPatients, pokud tam ještě není
                    if (!this.selectedPatients.some(p => p.id === patient.id)) {
                        this.selectedPatients.push({
                            id: patient.id,
                            phone: patient.phone,
                            name: patient.name,
                            email: patient.email
                        });
                    }
                } else {
                    // Odebereme pacienta z selectedPatients
                    this.selectedPatients = this.selectedPatients.filter(p => p.id !== patient.id);
                }
            });
            
            this.allSelected = newState;
            console.log(`${newState ? 'Selected' : 'Deselected'} ${this.patients.length} patients on current page`);
            console.log('Total selected patients:', this.selectedPatients.length);
        },

        togglePatient(patient) {
            patient.selected = !patient.selected;
            
            if (patient.selected) {
                // Přidáme pacienta do selectedPatients, pokud tam ještě není
                if (!this.selectedPatients.some(p => p.id === patient.id)) {
                    this.selectedPatients.push({
                        id: patient.id,
                        phone: patient.phone,
                        name: patient.name,
                        email: patient.email
                    });
                }
            } else {
                // Odebereme pacienta z selectedPatients
                this.selectedPatients = this.selectedPatients.filter(p => p.id !== patient.id);
            }
            
            this.updateAllSelected();
        },

        updateAllSelected() {
            // Kontrola, zda jsou všichni pacienti na aktuální stránce vybráni
            this.allSelected = this.patients.length > 0 && this.patients.every(patient => patient.selected);
        },

        async submitCampaign() {
                    if (this.processing) return;

                    try {
                        this.processing = true;

                        if (this.selectedPatients.length === 0) {
                            throw new Error('Vyberte prosím alespoň jednoho příjemce.');
                        }

                        if (!this.campaignName.trim()) {
                            throw new Error('Vyplňte prosím název kampaně.');
                        }

                        if (!this.message.trim()) {
                            throw new Error('Vyplňte prosím text zprávy.');
                        }

                        const formData = {
                            csrf_token: document.querySelector('input[name="csrf_token"]').value,
                            campaign_name: this.campaignName.trim(),
                            message: this.message.trim(),
                            selected_patients: this.selectedPatients.map(p => ({
                                id: p.id,
                                phone: p.phone
                            }))
                        };

                        const response = await fetch('save_campaign_bulkgate.php', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'Accept': 'application/json'
                            },
                            body: JSON.stringify(formData)
                        });

                        const result = await response.json();

                        if (!response.ok) {
                            throw new Error(result.message || `HTTP error! status: ${response.status}`);
                        }

                        if (!result.success) {
                            throw new Error(result.message || 'Neznámá chyba při odesílání kampaně');
                        }

                        alert('Kampaň byla úspěšně odeslána!');
                        window.location.reload();

                    } catch (error) {
                        console.error('Error:', error);
                        alert(error.message || 'Došlo k chybě při odesílání kampaně');
                    } finally {
                        this.processing = false;
                    }
                },

                async showCampaignDetails(campaignId) {
                    try {
                        const response = await fetch(`get_campaign_details.php?id=${campaignId}`);
                        if (!response.ok) throw new Error('Network response was not ok');
                        
                        const data = await response.json();
                        
                        let content = `
                            <div class="space-y-4">
                                <div class="overflow-x-auto">
                                    <table class="w-full custom-table">
                                        <thead>
                                            <tr>
                                                <th>TELEFON</th>
                                                <th>STATUS</th>
                                                <th>ČAS ODESLÁNÍ</th>
                                                <th>ČAS DORUČENÍ</th>
                                                <th>CHYBA</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                        `;
                        
                        data.logs.forEach(log => {
                            content += `
                                <tr>
                                    <td>${log.phone_number}</td>
                                    <td>
                                        <span class="campaign-status ${
                                            log.status === 'delivered' ? 'campaign-status-sent' : 
                                            log.status === 'failed' ? 'bg-red-100 text-red-800' : 
                                            'campaign-status-processing'
                                        }">
                                            ${log.status}
                                        </span>
                                    </td>
                                    <td>${log.sent_at || '-'}</td>
                                    <td>${log.delivered_at || '-'}</td>
                                    <td class="text-red-500">${log.error_message || '-'}</td>
                                </tr>
                            `;
                        });
                        
                        content += `
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        `;
                        
                        document.getElementById('campaignDetailsContent').innerHTML = content;
                        this.showModal = true;
                    } catch (error) {
                        console.error('Error:', error);
                        alert('Chyba při načítání detailů kampaně');
                    }
                },

                prevPage() {
                    if (this.currentPage > 1) {
                        this.currentPage--;
                        this.fetchPatients();
                    }
                },

                nextPage() {
                    if (this.currentPage < this.totalPages) {
                        this.currentPage++;
                        this.fetchPatients();
                    }
                },

                goToPage(page) {
                    if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
                        this.currentPage = page;
                        this.fetchPatients();
                    }
                },

                get pageNumbers() {
                    const totalPageButtons = 5;
                    const pageNumbers = [];
                    let startPage = Math.max(1, this.currentPage - Math.floor(totalPageButtons / 2));
                    let endPage = Math.min(this.totalPages, startPage + totalPageButtons - 1);

                    if (endPage - startPage + 1 < totalPageButtons) {
                        startPage = Math.max(1, endPage - totalPageButtons + 1);
                    }

                    for (let i = startPage; i <= endPage; i++) {
                        pageNumbers.push(i);
                    }

                    return pageNumbers;
                }
            }
        }
    </script>
</body>
</html>


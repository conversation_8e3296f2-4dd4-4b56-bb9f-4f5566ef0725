<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Načtení potřebných souborů
require_once 'db_connection.php';
require_once 'error_log.php';
require_once 'api_bulkgate_wallet.php';

// Funkce pro výpis zprávy s formátováním
function debugMessage($message, $type = 'info') {
    $colors = [
        'info' => 'blue',
        'success' => 'green',
        'warning' => 'orange',
        'error' => 'red'
    ];
    
    $color = $colors[$type] ?? 'black';
    
    echo "<div style='color: $color; padding: 5px; margin: 5px 0; border-left: 3px solid $color;'>";
    echo $message;
    echo "</div>";
}

// Kontrola, zda je uživatel přihlášen
session_start();
if (!isset($_SESSION['user_id'])) {
    debugMessage("Uživatel není přihl<PERSON>en!", 'error');
    echo "<a href='login.php'>Přihlásit se</a>";
    exit;
}

$user_id = $_SESSION['user_id'];
debugMessage("Uživatel ID: $user_id", 'info');

// Kontrola, zda je funkce createWallet dostupná
if (!function_exists('createWallet')) {
    debugMessage("Funkce createWallet není dostupná!", 'error');
    exit;
}
debugMessage("Funkce createWallet je dostupná", 'success');

// Kontrola konfigurace BulkGate API
try {
    $config = getWalletApiConfig();
    debugMessage("BulkGate API konfigurace je dostupná: Application ID: " . substr($config['application_id'], 0, 5) . "...", 'success');
} catch (Exception $e) {
    debugMessage("Chyba při získávání BulkGate API konfigurace: " . $e->getMessage(), 'error');
    exit;
}

// Kontrola, zda existují potřebné tabulky
try {
    $db = getDatabaseConnection();
    
    // Kontrola tabulky user_wallets
    $result = $db->query("SHOW TABLES LIKE 'user_wallets'");
    if ($result->num_rows == 0) {
        debugMessage("Tabulka user_wallets neexistuje! Vytvářím...", 'warning');
        
        $createTableSQL = "
        CREATE TABLE `user_wallets` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `user_id` int(11) NOT NULL,
          `wallet_id` varchar(100) NOT NULL,
          `label` varchar(100) NOT NULL,
          `is_default` tinyint(1) DEFAULT 0,
          `is_active` tinyint(1) DEFAULT 1,
          `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
          `updated_at` timestamp NULL DEFAULT NULL ON UPDATE current_timestamp(),
          PRIMARY KEY (`id`),
          KEY `user_id` (`user_id`),
          KEY `wallet_id` (`wallet_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";
        
        if ($db->query($createTableSQL)) {
            debugMessage("Tabulka user_wallets byla úspěšně vytvořena", 'success');
        } else {
            debugMessage("Chyba při vytváření tabulky user_wallets: " . $db->error, 'error');
        }
    } else {
        debugMessage("Tabulka user_wallets existuje", 'success');
    }
    
    // Kontrola, zda existuje sloupec default_wallet_id v tabulce users
    $result = $db->query("SHOW COLUMNS FROM users LIKE 'default_wallet_id'");
    if ($result->num_rows == 0) {
        debugMessage("Sloupec default_wallet_id v tabulce users neexistuje! Přidávám...", 'warning');
        
        $alterTableSQL = "ALTER TABLE users ADD COLUMN default_wallet_id varchar(100) DEFAULT NULL";
        
        if ($db->query($alterTableSQL)) {
            debugMessage("Sloupec default_wallet_id byl úspěšně přidán do tabulky users", 'success');
        } else {
            debugMessage("Chyba při přidávání sloupce default_wallet_id: " . $db->error, 'error');
        }
    } else {
        debugMessage("Sloupec default_wallet_id v tabulce users existuje", 'success');
    }
    
} catch (Exception $e) {
    debugMessage("Chyba při kontrole tabulek: " . $e->getMessage(), 'error');
}

// Formulář pro vytvoření peněženky
echo "<h1>Test vytvoření peněženky</h1>";
echo "<form method='POST' action=''>";
echo "<div style='margin-bottom: 15px;'>";
echo "<label for='wallet_label' style='display: block; margin-bottom: 5px;'>Název peněženky:</label>";
echo "<input type='text' id='wallet_label' name='wallet_label' style='width: 300px; padding: 5px;' required>";
echo "</div>";

echo "<div style='margin-bottom: 15px;'>";
echo "<input type='checkbox' id='is_default' name='is_default' value='1'>";
echo "<label for='is_default' style='margin-left: 5px;'>Nastavit jako výchozí peněženku</label>";
echo "</div>";

echo "<div>";
echo "<button type='submit' name='create_wallet' style='padding: 8px 15px; background-color: #4a5568; color: white; border: none; border-radius: 4px; cursor: pointer;'>Vytvořit peněženku</button>";
echo "</div>";
echo "</form>";

// Zpracování formuláře
if (isset($_POST['create_wallet'])) {
    $wallet_label = trim($_POST['wallet_label'] ?? '');
    $is_default = isset($_POST['is_default']) && $_POST['is_default'] === '1';
    
    debugMessage("Pokus o vytvoření peněženky:", 'info');
    debugMessage("Název peněženky: $wallet_label", 'info');
    debugMessage("Výchozí: " . ($is_default ? 'Ano' : 'Ne'), 'info');
    
    if (empty($wallet_label)) {
        debugMessage("Název peněženky je povinný!", 'error');
    } else {
        try {
            // Podrobné sledování procesu vytváření peněženky
            debugMessage("Volám funkci createWallet...", 'info');
            
            // Přidáme sledování HTTP požadavku
            echo "<div style='margin-top: 20px; padding: 10px; background-color: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px;'>";
            echo "<h3>HTTP požadavek na BulkGate API:</h3>";
            echo "<pre id='http_request'></pre>";
            echo "</div>";
            
            // Upravíme funkci sendWalletRequest pro sledování
            function debugSendWalletRequest($url, $postData) {
                $ch = curl_init();
                
                // Convert postData to JSON
                $jsonData = json_encode($postData);
                
                echo "<script>
                    document.getElementById('http_request').innerHTML = 'URL: " . $url . "\\n\\nData: " . str_replace("'", "\\'", str_replace($postData['application_token'], '***HIDDEN***', $jsonData)) . "';
                </script>";
                
                curl_setopt_array($ch, [
                    CURLOPT_URL => $url,
                    CURLOPT_POST => true,
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_POSTFIELDS => $jsonData,
                    CURLOPT_HTTPHEADER => [
                        'Content-Type: application/json',
                        'Accept: application/json',
                        'Content-Length: ' . strlen($jsonData)
                    ],
                    CURLOPT_TIMEOUT => 30,
                    CURLOPT_SSL_VERIFYPEER => true,
                    CURLOPT_SSL_VERIFYHOST => 2,
                    CURLOPT_VERBOSE => true
                ]);
                
                // Capture CURL verbose output
                $verbose = fopen('php://temp', 'w+');
                curl_setopt($ch, CURLOPT_STDERR, $verbose);
                
                $response = curl_exec($ch);
                $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                
                // Get verbose information
                rewind($verbose);
                $verboseLog = stream_get_contents($verbose);
                fclose($verbose);
                
                echo "<div style='margin-top: 10px; padding: 10px; background-color: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px;'>";
                echo "<h3>HTTP odpověď:</h3>";
                echo "<pre>HTTP kód: $httpCode\n\nOdpověď: " . htmlspecialchars($response) . "</pre>";
                echo "</div>";
                
                if (curl_errno($ch)) {
                    $error = curl_error($ch);
                    curl_close($ch);
                    debugMessage("CURL Error: " . $error, 'error');
                    throw new Exception("CURL Error: " . $error);
                }
                
                curl_close($ch);
                
                if ($httpCode !== 200) {
                    debugMessage("HTTP Error $httpCode", 'error');
                    
                    $errorMessage = "HTTP Error $httpCode: ";
                    if ($httpCode === 400) {
                        $errorMessage .= "Neplatný požadavek";
                    } elseif ($httpCode === 401) {
                        $errorMessage .= "Neplatné přihlašovací údaje k API";
                    } elseif ($httpCode === 403) {
                        $errorMessage .= "Přístup zamítnut";
                    } else {
                        $errorMessage .= "Neočekávaná chyba";
                    }
                    
                    throw new Exception($errorMessage);
                }
                
                // Parse JSON response
                $json = json_decode($response, true);
                if ($json === null) {
                    debugMessage("Neplatná JSON odpověď: " . json_last_error_msg(), 'error');
                    throw new Exception("Neplatná JSON odpověď: " . json_last_error_msg());
                }
                
                return $json;
            }
            
            // Vytvoření peněženky s použitím naší debugovací funkce
            $config = getWalletApiConfig();
            $url = "https://portal.bulkgate.com/api/1.0/wallet/create";
            
            // Prepare request data
            $requestData = [
                'application_id' => $config['application_id'],
                'application_token' => $config['application_token'],
                'user_id' => $user_id,
                'label' => $wallet_label,
                'initial_credit' => 0
            ];
            
            // Send request and get response
            $response = debugSendWalletRequest($url, $requestData);
            
            // Validate response
            if (!isset($response['data']) || !isset($response['data']['wallet_id'])) {
                debugMessage("Neplatná odpověď od API: Chybí data nebo ID peněženky", 'error');
                exit;
            }
            
            $wallet_id = $response['data']['wallet_id'];
            debugMessage("Peněženka byla úspěšně vytvořena s ID: $wallet_id", 'success');
            
            // Uložení peněženky do databáze
            debugMessage("Ukládám peněženku do databáze...", 'info');
            
            try {
                $db = getDatabaseConnection();
                
                // Pokud má být tato peněženka výchozí, zrušíme výchozí status u ostatních peněženek
                if ($is_default) {
                    $stmt = $db->prepare("UPDATE user_wallets SET is_default = 0 WHERE user_id = ?");
                    $stmt->bind_param("i", $user_id);
                    
                    if ($stmt->execute()) {
                        debugMessage("Výchozí status byl zrušen u ostatních peněženek", 'success');
                    } else {
                        debugMessage("Chyba při aktualizaci ostatních peněženek: " . $stmt->error, 'error');
                    }
                    $stmt->close();
                    
                    // Také aktualizujeme výchozí peněženku v tabulce uživatelů
                    $stmt = $db->prepare("UPDATE users SET default_wallet_id = ? WHERE id = ?");
                    $stmt->bind_param("si", $wallet_id, $user_id);
                    
                    if ($stmt->execute()) {
                        debugMessage("Výchozí peněženka byla aktualizována v tabulce uživatelů", 'success');
                    } else {
                        debugMessage("Chyba při aktualizaci výchozí peněženky v tabulce uživatelů: " . $stmt->error, 'error');
                    }
                    $stmt->close();
                }
                
                // Vložíme novou peněženku
                $stmt = $db->prepare("
                    INSERT INTO user_wallets 
                    (user_id, wallet_id, label, is_default, is_active, created_at) 
                    VALUES (?, ?, ?, ?, 1, NOW())
                ");
                
                if (!$stmt) {
                    debugMessage("Chyba při přípravě SQL dotazu: " . $db->error, 'error');
                    throw new Exception("Chyba při přípravě SQL dotazu: " . $db->error);
                }
                
                $stmt->bind_param("issi", $user_id, $wallet_id, $wallet_label, $is_default);
                
                if ($stmt->execute()) {
                    debugMessage("Peněženka byla úspěšně uložena do databáze", 'success');
                } else {
                    debugMessage("Chyba při ukládání peněženky do databáze: " . $stmt->error, 'error');
                    throw new Exception("Chyba při ukládání peněženky: " . $stmt->error);
                }
                
                $stmt->close();
                
            } catch (Exception $e) {
                debugMessage("Chyba při ukládání peněženky do databáze: " . $e->getMessage(), 'error');
                throw $e;
            }
            
            debugMessage("Peněženka byla úspěšně vytvořena a uložena!", 'success');
            echo "<div style='margin-top: 20px;'>";
            echo "<a href='settings.php' style='padding: 10px 15px; background-color: #4a5568; color: white; text-decoration: none; border-radius: 4px;'>Přejít na nastavení</a>";
            echo "</div>";
            
        } catch (Exception $e) {
            debugMessage("Chyba při vytváření peněženky: " . $e->getMessage(), 'error');
            echo "<pre>" . $e->getTraceAsString() . "</pre>";
        }
    }
}

// Výpis existujících peněženek
try {
    debugMessage("Načítám existující peněženky...", 'info');
    $wallets = getUserWallets($user_id);
    
    if (empty($wallets)) {
        debugMessage("Uživatel nemá žádné peněženky", 'warning');
    } else {
        debugMessage("Nalezeno " . count($wallets) . " peněženek:", 'success');
        
        echo "<table style='width: 100%; border-collapse: collapse; margin-top: 20px;'>";
        echo "<thead style='background-color: #f8f9fa;'>";
        echo "<tr>";
        echo "<th style='padding: 8px; border: 1px solid #dee2e6; text-align: left;'>ID</th>";
        echo "<th style='padding: 8px; border: 1px solid #dee2e6; text-align: left;'>Název</th>";
        echo "<th style='padding: 8px; border: 1px solid #dee2e6; text-align: left;'>Wallet ID</th>";
        echo "<th style='padding: 8px; border: 1px solid #dee2e6; text-align: left;'>Výchozí</th>";
        echo "<th style='padding: 8px; border: 1px solid #dee2e6; text-align: left;'>Aktivní</th>";
        echo "<th style='padding: 8px; border: 1px solid #dee2e6; text-align: left;'>Vytvořeno</th>";
        echo "</tr>";
        echo "</thead>";
        echo "<tbody>";
        
        foreach ($wallets as $wallet) {
            echo "<tr>";
            echo "<td style='padding: 8px; border: 1px solid #dee2e6;'>" . $wallet['id'] . "</td>";
            echo "<td style='padding: 8px; border: 1px solid #dee2e6;'>" . htmlspecialchars($wallet['label']) . "</td>";
            echo "<td style='padding: 8px; border: 1px solid #dee2e6;'>" . htmlspecialchars($wallet['wallet_id']) . "</td>";
            echo "<td style='padding: 8px; border: 1px solid #dee2e6;'>" . ($wallet['is_default'] ? 'Ano' : 'Ne') . "</td>";
            echo "<td style='padding: 8px; border: 1px solid #dee2e6;'>" . ($wallet['is_active'] ? 'Ano' : 'Ne') . "</td>";
            echo "<td style='padding: 8px; border: 1px solid #dee2e6;'>" . $wallet['created_at'] . "</td>";
            echo "</tr>";
        }
        
        echo "</tbody>";
        echo "</table>";
    }
} catch (Exception $e) {
    debugMessage("Chyba při načítání peněženek: " . $e->getMessage(), 'error');
}
?>


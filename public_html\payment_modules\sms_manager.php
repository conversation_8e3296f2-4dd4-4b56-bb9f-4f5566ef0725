<?php
require_once 'api_functions.php';

/**
 * SMS Manager - Správa SMS na bázi pay as you go
 */
class SmsManager {
    
    /**
     * Získá aktuální zůstatek SMS kreditu uživatele
     * @param int $userId ID uživatele
     * @return float Aktuální zůstatek v měně
     */
    public function getUserSmsCredit($userId) {
        $response = makeApiCall('GET', "users/{$userId}/sms-credit");
        
        if ($response && isset($response['credit'])) {
            return $response['credit'];
        }
        
        return 0.0;
    }
    
    /**
     * Získá historii využití SMS
     * @param int $userId ID uživatele
     * @param string $period Období (week, month, year, all)
     * @return array Historie využití SMS
     */
    public function getSmsUsageHistory($userId, $period = 'month') {
        $response = makeApiCall('GET', "users/{$userId}/sms-usage", [
            'period' => $period
        ]);
        
        if ($response && isset($response['usage'])) {
            return $response['usage'];
        }
        
        return [];
    }
    
    /**
     * Dobije SMS kredit
     * @param int $userId ID uživatele
     * @param float $amount Částka k dobití
     * @param string $paymentMethod Platební metoda
     * @return array Výsledek operace
     */
    public function rechargeCredit($userId, $amount, $paymentMethod) {
        return makeApiCall('POST', "users/{$userId}/sms-credit/recharge", [
            'amount' => $amount,
            'payment_method' => $paymentMethod
        ]);
    }
    
    /**
     * Získá cenu za jednu SMS
     * @return float Cena za jednu SMS
     */
    public function getSmsPricePerUnit() {
        $response = makeApiCall('GET', "settings/sms-price");
        
        if ($response && isset($response['price_per_unit'])) {
            return $response['price_per_unit'];
        }
        
        return 0.0;
    }
    
    /**
     * Odešle SMS a odečte kredit
     * @param int $userId ID uživatele
     * @param string $recipient Příjemce SMS
     * @param string $message Text zprávy
     * @return array Výsledek operace
     */
    public function sendSms($userId, $recipient, $message) {
        return makeApiCall('POST', "users/{$userId}/send-sms", [
            'recipient' => $recipient,
            'message' => $message
        ]);
    }
}
?>


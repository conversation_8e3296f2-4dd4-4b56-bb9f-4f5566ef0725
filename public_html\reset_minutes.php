<?php
require_once 'config.php';
require_once 'subscription_constants.php';

function resetMinutes() {
    global $mysqli;
    
    $stmt = $mysqli->prepare("SELECT id, subscription_plan FROM users");
    $stmt->execute();
    $result = $stmt->get_result();
    
    while ($user = $result->fetch_assoc()) {
        $newLimit = getMinutesLimit($user['subscription_plan'], $user['id']);
        
        $updateStmt = $mysqli->prepare("UPDATE users SET minutes_remaining = ? WHERE id = ?");
        $updateStmt->bind_param("ii", $newLimit, $user['id']);
        $updateStmt->execute();
        
        $logStmt = $mysqli->prepare("INSERT INTO minutes_usage_logs (user_id, minutes_deducted, action_type) VALUES (?, ?, ?)");
        $minutesDeducted = 0; // We're resetting, not deducting
        $actionType = 'cron_reset';
        $logStmt->bind_param("iis", $user['id'], $minutesDeducted, $actionType);
        $logStmt->execute();
    }
}

// Run this script via cron job at the beginning of each month
resetMinutes();
<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Definice funkce getDatabaseConnection přímo v tomto souboru, pokud by nebyla dostupná
if (!function_exists('getDatabaseConnection')) {
    function getDatabaseConnection() {
        static $db = null;
        
        if ($db === null) {
            $db_host = getenv('DB_HOST') ?: 'localhost';
            $db_user = getenv('DB_USER') ?: 'u345712091_dentibot';
            $db_pass = getenv('DB_PASS') ?: 'your_password_here';
            $db_name = getenv('DB_NAME') ?: 'u345712091_dentibot';
            
            $db = new mysqli($db_host, $db_user, $db_pass, $db_name);
            
            if ($db->connect_error) {
                die("Nepodařilo se připojit k databázi: " . $db->connect_error);
            }
            
            $db->set_charset("utf8mb4");
        }
        
        return $db;
    }
}

require_once 'config.php';
require_once 'error_log.php';
require_once 'subscription_constants.php';
require_once 'subscription_functions.php';
// Nyní můžeme bezpečně načíst api_bulkgate_wallet.php, protože funkce getDatabaseConnection je již definována
require_once 'api_bulkgate_wallet.php';

// Zkontrolujeme, zda je soubor api_bulkgate_wallet.php správně načten
if (function_exists('createWallet')) {
    echo "<div class='alert alert-success'>API BulkGate Wallet je správně načteno</div>";
} else {
    echo "<div class='alert alert-danger'>API BulkGate Wallet NENÍ správně načteno!</div>";
}

// Zkontrolujeme, zda je dostupná konfigurace API
try {
    $config = getWalletApiConfig();
    echo "<div class='alert alert-success'>API konfigurace je dostupná: Application ID: " . substr($config['application_id'], 0, 5) . "...</div>";
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>Chyba při získávání API konfigurace: " . $e->getMessage() . "</div>";
}

function handleError($errno, $errstr, $errfile, $errline) {
    $error_message = "Error [$errno] $errstr on line $errline in file $errfile";
    error_log($error_message);
    
    writeErrorLog('PHP Error', [
        'errno' => $errno,
        'error' => $errstr,
        'file' => $errfile,
        'line' => $errline
    ]);
    
    return true;
}

set_error_handler('handleError');

ob_start();

try {
    define('DB_HOST', getenv('DB_HOST') ?: 'localhost');
    define('DB_USER', getenv('DB_USER') ?: 'u345712091_dentibot');
    define('DB_PASS', getenv('DB_PASS') ?: 'your_password_here');
    define('DB_NAME', getenv('DB_NAME') ?: 'u345712091_dentibot');

    $mysqli = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
    if ($mysqli->connect_error) {
        writeErrorLog("Database connection failed", "ERROR");
        die("Database connection failed. Please try again later.");
    }

    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }

    if (!isset($_SESSION['user_id'])) {
        header('Location: login.php');
        exit;
    }

    $error = null;
    $success = null;

    $user_id = $_SESSION['user_id'];
    $stmt = $mysqli->prepare("SELECT subscription_plan, custom_minute_limit, used_minutes, vapi_api_key, vapi_api_url, assistant_id, baserow_api_token, baserow_database_id, baserow_table_id, default_wallet_id FROM users WHERE id = ?");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $userData = $result->fetch_assoc();
    $stmt->close();

    if ($userData) {
        $current_subscription = $userData['subscription_plan'];
        $custom_minute_limit = $userData['custom_minute_limit'];
        $minutes_used = $userData['used_minutes'];
        $vapi_api_key = $userData['vapi_api_key'];
        $vapi_api_url = $userData['vapi_api_url'];
        $assistant_id = $userData['assistant_id'];
        $baserow_api_token = $userData['baserow_api_token'];
        $baserow_database_id = $userData['baserow_database_id'];
        $baserow_table_id = $userData['baserow_table_id'];
        $default_wallet_id = $userData['default_wallet_id'];
    } else {
        $error = "Nepodařilo se načíst data o předplatném.";
    }

    // Načtení peněženek uživatele
    try {
        $wallets = getUserWallets($user_id);
    } catch (Exception $e) {
        $wallets = [];
        writeErrorLog('Failed to load user wallets', [
            'error' => $e->getMessage(),
            'user_id' => $user_id
        ]);
    }

    $subscription_types = getSubscriptionTypes();

    if (!isset($_SESSION['data_synced']) || $_SESSION['data_synced'] !== true) {
        $syncResult = syncVapiData();
        if ($syncResult === true) {
            $_SESSION['last_sync'] = time();
            $_SESSION['data_synced'] = true;
            $success = "Data byla úspěšně synchronizována po přihlášení.";
        } else {
            $error = "Nepodařilo se synchronizovat data po přihlášení: " . $syncResult;
        }
    }

    $settings_password = getenv('SETTINGS_PASSWORD');
    if (!$settings_password) {
        writeErrorLog('SETTINGS_PASSWORD not defined in .htaccess', 'ERROR');
        die('Chyba konfigurace: Kontaktujte administrátora.');
    }

    if (!isset($_SESSION['settings_access']) || $_SESSION['settings_access'] !== true) {
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['settings_password'])) {
            if ($_POST['settings_password'] === $settings_password) {
                $_SESSION['settings_access'] = true;
            } else {
                $error = 'Nesprávné heslo. Zkuste to prosím znovu.';
                writeErrorLog('Settings Access Failed', 'ERROR', [
                    'user_id' => $_SESSION['user_id'],
                    'ip' => $_SERVER['REMOTE_ADDR']
                ]);
            }
        }

        if (!isset($_SESSION['settings_access']) || $_SESSION['settings_access'] !== true) {
            ?>
            <!DOCTYPE html>
            <html lang="cs">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Přístup k nastavení - Dentibot</title>
                <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
            </head>
            <body class="bg-gray-100 flex items-center justify-center min-h-screen">
                <div class="bg-white p-8 rounded-lg shadow-md w-96">
                    <h1 class="text-2xl font-bold mb-6 text-center">Přístup k nastavení</h1>
                    <?php if (isset($error)): ?>
                        <p class="text-red-500 mb-4"><?php echo htmlspecialchars($error); ?></p>
                    <?php endif; ?>
                    <form method="POST" action="" class="space-y-4">
                        <div>
                            <label for="settings_password" class="block text-sm font-medium text-gray-700">Heslo pro přístup</label>
                            <input 
                                type="password" 
                                id="settings_password" 
                                name="settings_password" 
                                required 
                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                                autocomplete="new-password"
                            >
                        </div>
                        <button 
                            type="submit" 
                            class="w-full bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
                        >
                            Potvrdit
                        </button>
                    </form>
                </div>
            </body>
            </html>
            <?php
            exit;
        }
    }

    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        if (isset($_POST['update_subscription']) && isset($_POST['subscription_type'])) {
            $new_subscription = $_POST['subscription_type'];
            $new_custom_limit = null;

            if ($new_subscription === 'Enterprise' && isset($_POST['custom_minute_limit'])) {
                $new_custom_limit = intval($_POST['custom_minute_limit']);
            }

            $stmt = $mysqli->prepare("UPDATE users SET subscription_plan = ?, custom_minute_limit = ? WHERE id = ?");
            $stmt->bind_param("sii", $new_subscription, $new_custom_limit, $user_id);

            if ($stmt->execute()) {
                $success = "Předplatné bylo úspěšně aktualizováno na " . htmlspecialchars($new_subscription);
                $current_subscription = $new_subscription;
                $custom_minute_limit = $new_custom_limit;

                echo "<script>window.parent.postMessage('updateDashboard', '*');</script>";
            } else {
                $error = "Nepodařilo se aktualizovat předplatné.";
                writeErrorLog('Subscription Update Error', [
                    'user_id' => $user_id,
                    'new_subscription' => $new_subscription,
                    'error' => $mysqli->error
                ]);
            }
            $stmt->close();
        } elseif (isset($_POST['update_settings'])) {
            $new_vapi_api_key = trim($_POST['vapi_api_key'] ?? '');
            $new_vapi_api_url = trim($_POST['vapi_api_url'] ?? 'https://api.vapi.ai');
            $new_assistant_id = trim($_POST['assistant_id'] ?? '');
            $new_baserow_api_token = trim($_POST['baserow_api_token'] ?? '');
            $new_baserow_database_id = trim($_POST['baserow_database_id'] ?? '');
            $new_baserow_table_id = trim($_POST['baserow_table_id'] ?? '');

            if (empty($new_vapi_api_key) || empty($new_assistant_id) || empty($new_baserow_api_token) || empty($new_baserow_database_id) || empty($new_baserow_table_id)) {
                $error = 'Všechna povinná pole musí být vyplněna.';
            } else {
                $stmt = $mysqli->prepare("UPDATE users SET vapi_api_key = ?, vapi_api_url = ?, assistant_id = ?, baserow_api_token = ?, baserow_database_id = ?, baserow_table_id = ? WHERE id = ?");
                $stmt->bind_param("ssssssi", $new_vapi_api_key, $new_vapi_api_url, $new_assistant_id, $new_baserow_api_token, $new_baserow_database_id, $new_baserow_table_id, $user_id);

                if ($stmt->execute()) {
                    $success = 'Nastavení bylo úspěšně uloženo.';
                    $vapi_api_key = $new_vapi_api_key;
                    $vapi_api_url = $new_vapi_api_url;
                    $assistant_id = $new_assistant_id;
                    $baserow_api_token = $new_baserow_api_token;
                    $baserow_database_id = $new_baserow_database_id;
                    $baserow_table_id = $new_baserow_table_id;
                    $_SESSION['data_synced'] = false;
                } else {
                    $error = 'Nepodařilo se uložit nastavení.';
                }
                $stmt->close();
            }
        } elseif (isset($_POST['create_wallet'])) {
            // Vytvoření nové peněženky
            $wallet_label = trim($_POST['wallet_label'] ?? '');
            $is_default = isset($_POST['is_default']) && $_POST['is_default'] === '1';
            
            if (empty($wallet_label)) {
                $error = 'Název peněženky je povinný.';
            } else {
                // Přidejte tento kód před volání createWallet()
                echo "<pre>";
                echo "Pokus o vytvoření peněženky:\n";
                echo "User ID: " . $user_id . "\n";
                echo "Název peněženky: " . $wallet_label . "\n";
                echo "Výchozí: " . ($is_default ? 'Ano' : 'Ne') . "\n";
                echo "</pre>";

                // Zkontrolujeme, zda je funkce createWallet dostupná
                if (!function_exists('createWallet')) {
                    echo "<div class='alert alert-danger'>Funkce createWallet není dostupná!</div>";
                    writeErrorLog('Create Wallet Function Not Available', [
                        'user_id' => $user_id
                    ]);
                } else {
                    echo "<div class='alert alert-info'>Funkce createWallet je dostupná, pokračuji...</div>";
                }
                
                try {
                    createWallet($user_id, $wallet_label, 0, $is_default);
                    $success = 'Peněženka byla úspěšně vytvořena.';
                    // Znovu načteme peněženky
                    $wallets = getUserWallets($user_id);
                    
                    // Pokud je to výchozí peněženka, aktualizujeme proměnnou
                    if ($is_default && !empty($wallets)) {
                        $default_wallet_id = $wallets[0]['wallet_id'];
                    }
                } catch (Exception $e) {
                    $error = 'Nepodařilo se vytvořit peněženku: ' . $e->getMessage();
                    writeErrorLog('Create Wallet Error', [
                        'error' => $e->getMessage(),
                        'user_id' => $user_id,
                        'wallet_label' => $wallet_label
                    ]);
                }
            }
        } elseif (isset($_POST['set_default_wallet'])) {
            // Nastavení výchozí peněženky
            $wallet_id = trim($_POST['wallet_id'] ?? '');
            
            if (empty($wallet_id)) {
                $error = 'ID peněženky je povinné.';
            } else {
                try {
                    setUserDefaultWallet($user_id, $wallet_id);
                    $success = 'Výchozí peněženka byla úspěšně nastavena.';
                    $default_wallet_id = $wallet_id;
                    // Znovu načteme peněženky
                    $wallets = getUserWallets($user_id);
                } catch (Exception $e) {
                    $error = 'Nepodařilo se nastavit výchozí peněženku: ' . $e->getMessage();
                    writeErrorLog('Set Default Wallet Error', [
                        'error' => $e->getMessage(),
                        'user_id' => $user_id,
                        'wallet_id' => $wallet_id
                    ]);
                }
            }
        } elseif (isset($_POST['deactivate_wallet'])) {
            // Deaktivace peněženky
            $wallet_id = trim($_POST['wallet_id'] ?? '');
            
            if (empty($wallet_id)) {
                $error = 'ID peněženky je povinné.';
            } else {
                try {
                    deactivateWallet($user_id, $wallet_id);
                    $success = 'Peněženka byla úspěšně deaktivována.';
                    // Znovu načteme peněženky
                    $wallets = getUserWallets($user_id);
                    
                    // Pokud byla deaktivována výchozí peněženka, aktualizujeme proměnnou
                    if ($default_wallet_id === $wallet_id) {
                        $default_wallet = getUserDefaultWallet($user_id);
                        $default_wallet_id = $default_wallet ? $default_wallet['wallet_id'] : null;
                    }
                } catch (Exception $e) {
                    $error = 'Nepodařilo se deaktivovat peněženku: ' . $e->getMessage();
                    writeErrorLog('Deactivate Wallet Error', [
                        'error' => $e->getMessage(),
                        'user_id' => $user_id,
                        'wallet_id' => $wallet_id
                    ]);
                }
            }
        } elseif (isset($_POST['update_bulkgate_config'])) {
            // Aktualizace konfigurace BulkGate API
            $application_id = trim($_POST['application_id'] ?? '');
            $application_token = trim($_POST['application_token'] ?? '');
            $sms_price_per_unit = floatval($_POST['sms_price_per_unit'] ?? 0.75);
            
            if (empty($application_id) || empty($application_token)) {
                $error = 'Application ID a Application Token jsou povinné.';
            } else {
                try {
                    $db = getDatabaseConnection();
                    
                    // Zkontrolujeme, zda tabulka existuje
                    $stmt = $db->prepare("SHOW TABLES LIKE 'bulkgate_api_config'");
                    $stmt->execute();
                    $result = $stmt->get_result();
                    $tableExists = $result->num_rows > 0;
                    $stmt->close();
                    
                    if (!$tableExists) {
                        // Vytvoříme tabulku, pokud neexistuje
                        $db->query("
                            CREATE TABLE IF NOT EXISTS `bulkgate_api_config` (
                              `id` int(11) NOT NULL AUTO_INCREMENT,
                              `api_name` varchar(50) NOT NULL,
                              `application_id` varchar(100) NOT NULL,
                              `application_token` varchar(255) NOT NULL,
                              `sms_price_per_unit` decimal(10,2) DEFAULT 0.75,
                              `is_active` tinyint(1) DEFAULT 1,
                              `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
                              `updated_at` timestamp NULL DEFAULT NULL ON UPDATE current_timestamp(),
                              PRIMARY KEY (`id`),
                              UNIQUE KEY `api_name` (`api_name`)
                            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
                        ");
                    }
                    
                    // Zkontrolujeme, zda již existuje záznam pro BulkGate
                    $stmt = $db->prepare("SELECT id FROM bulkgate_api_config WHERE api_name = 'bulkgate'");
                    $stmt->execute();
                    $result = $stmt->get_result();
                    $existingConfig = $result->fetch_assoc();
                    $stmt->close();
                    
                    if ($existingConfig) {
                        // Aktualizujeme existující záznam
                        $stmt = $db->prepare("
                            UPDATE bulkgate_api_config 
                            SET application_id = ?, application_token = ?, sms_price_per_unit = ?, is_active = 1
                            WHERE api_name = 'bulkgate'
                        ");
                        $stmt->bind_param("ssd", $application_id, $application_token, $sms_price_per_unit);
                    } else {
                        // Vytvoříme nový záznam
                        $stmt = $db->prepare("
                            INSERT INTO bulkgate_api_config 
                            (api_name, application_id, application_token, sms_price_per_unit, is_active) 
                            VALUES ('bulkgate', ?, ?, ?, 1)
                        ");
                        $stmt->bind_param("ssd", $application_id, $application_token, $sms_price_per_unit);
                    }
                    
                    if ($stmt->execute()) {
                        $success = 'Konfigurace BulkGate API byla úspěšně uložena.';
                    } else {
                        $error = 'Nepodařilo se uložit konfiguraci BulkGate API: ' . $stmt->error;
                    }
                    $stmt->close();
                    
                } catch (Exception $e) {
                    $error = 'Chyba při ukládání konfigurace BulkGate API: ' . $e->getMessage();
                    writeErrorLog('BulkGate API Config Update Error', [
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                }
            }
        }
    }

    $currentPage = 'settings';
    ?>
    <?php if (!headers_sent()) {
        header('Content-Type: text/html; charset=utf-8');
    } ?>
    <!DOCTYPE html>
    <html lang="cs">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Nastavení - Dentibot</title>
        <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
        <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
        <style>
        .alert {
            padding: 12px;
            margin-bottom: 16px;
            border: 1px solid transparent;
            border-radius: 4px;
        }
        .alert-success {
            color: #155724;
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .alert-info {
            color: #0c5460;
            background-color: #d1ecf1;
            border-color: #bee5eb;
        }
        .alert-warning {
            color: #856404;
            background-color: #fff3cd;
            border-color: #ffeeba;
        }
        .alert-danger {
            color: #721c24;
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        </style>
    </head>
    <body class="bg-gray-100">
        <div class="flex h-screen">
            <?php include 'sidebar.php'; ?>

            <div class="flex-1 overflow-y-auto">
                <div class="p-8">
                    <h1 class="text-2xl font-bold text-indigo-600 mb-6">Nastavení</h1>

                    <?php if ($error): ?>
                    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
                        <span class="block sm:inline"><?php echo htmlspecialchars($error); ?></span>
                    </div>
                    <?php endif; ?>

                    <?php if ($success): ?>
                    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-6" role="alert">
                        <span class="block sm:inline"><?php echo htmlspecialchars($success); ?></span>
                    </div>
                    <?php endif; ?>

                    <div class="bg-white shadow rounded-lg p-6 mb-6">
                        <h2 class="text-xl font-semibold mb-4">Předplatné</h2>
                        <form id="subscriptionForm">
                            <input type="hidden" name="csrf_token" value="<?php echo htmlspecialchars(generateCSRFToken()); ?>">
                            <div class="mb-4">
                                <label for="subscription_type" class="block text-sm font-medium text-gray-700 mb-2">
                                    Typ předplatného
                                </label>
                                <select
                                    id="subscription_type"
                                    name="subscription_type"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                                    onchange="toggleCustomLimitField()"
                                    required
                                >
                                    <?php foreach ($subscription_types as $type => $data): ?>
                                        <?php if ($type !== 'Pay as you go'): ?>
                                            <option value="<?php echo htmlspecialchars($type); ?>" <?php echo $type === $current_subscription ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($type); ?>
                                            </option>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div id="custom_limit_field" class="mb-4" style="display: <?php echo $current_subscription === 'Enterprise' ? 'block' : 'none'; ?>">
                                <label for="custom_minute_limit" class="block text-sm font-medium text-gray-700 mb-2">
                                    Vlastní limit minut (pouze pro Enterprise)
                                </label>
                                <div class="flex items-center">
                                    <input
                                        type="number"
                                        id="custom_minute_limit"
                                        name="custom_minute_limit"
                                        value="<?php echo $custom_minute_limit === -1 ? '' : htmlspecialchars($custom_minute_limit ?? ''); ?>"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                                        min="1"
                                        <?php echo $current_subscription === 'Enterprise' ? '' : 'disabled'; ?>
                                        placeholder="<?php echo $custom_minute_limit === -1 ? 'Neomezeno' : ''; ?>"
                                    >
                                    <span class="ml-2 text-sm text-gray-500">nebo</span>
                                    <button
                                        type="button"
                                        id="set_unlimited"
                                        class="ml-2 bg-gray-200 text-gray-700 px-3 py-2 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                                        onclick="setUnlimited()"
                                        <?php echo $current_subscription === 'Enterprise' ? '' : 'disabled'; ?>
                                    >
                                        Neomezeno
                                    </button>
                                </div>
                            </div>

                            <div class="flex justify-end">
                                <button
                                    type="submit"
                                    class="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                                >
                                    Aktualizovat předplatné
                                </button>
                            </div>
                        </form>
                        <div id="subscriptionUpdateMessage" class="hidden mt-4"></div>
                    </div>

                    <!-- Sekce pro správu peněženek BulkGate -->
                    <div class="bg-white shadow rounded-lg p-6 mb-6">
                        <h2 class="text-xl font-semibold mb-4">Správa peněženek BulkGate</h2>
                        
                        <!-- Seznam existujících peněženek -->
                        <div class="mb-6">
                            <h3 class="text-lg font-medium mb-3">Vaše peněženky</h3>
                            
                            <?php if (empty($wallets)): ?>
                                <p class="text-gray-500 italic">Nemáte zatím žádné peněženky.</p>
                            <?php else: ?>
                                <div class="overflow-x-auto">
                                    <table class="min-w-full divide-y divide-gray-200">
                                        <thead class="bg-gray-50">
                                            <tr>
                                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Název</th>
                                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID peněženky</th>
                                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Zůstatek</th>
                                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Výchozí</th>
                                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Akce</th>
                                            </tr>
                                        </thead>
                                        <tbody class="bg-white divide-y divide-gray-200">
                                            <?php foreach ($wallets as $wallet): ?>
                                                <tr>
                                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                        <?php echo htmlspecialchars($wallet['label']); ?>
                                                    </td>
                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                        <?php echo htmlspecialchars($wallet['wallet_id']); ?>
                                                    </td>
                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                        <?php echo number_format($wallet['balance'], 2); ?> <?php echo htmlspecialchars($wallet['currency']); ?>
                                                    </td>
                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                        <?php if ($wallet['is_default'] || $wallet['wallet_id'] === $default_wallet_id): ?>
                                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                                                Výchozí
                                                            </span>
                                                        <?php else: ?>
                                                            <form method="POST" action="settings.php" class="inline">
                                                                <input type="hidden" name="wallet_id" value="<?php echo htmlspecialchars($wallet['wallet_id']); ?>">
                                                                <button 
                                                                    type="submit" 
                                                                    name="set_default_wallet" 
                                                                    class="text-indigo-600 hover:text-indigo-900 text-xs"
                                                                >
                                                                    Nastavit jako výchozí
                                                                </button>
                                                            </form>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                        <form method="POST" action="settings.php" class="inline" onsubmit="return confirm('Opravdu chcete deaktivovat tuto peněženku?');">
                                                            <input type="hidden" name="wallet_id" value="<?php echo htmlspecialchars($wallet['wallet_id']); ?>">
                                                            <button 
                                                                type="submit" 
                                                                name="deactivate_wallet" 
                                                                class="text-red-600 hover:text-red-900 text-xs"
                                                                <?php echo ($wallet['is_default'] || $wallet['wallet_id'] === $default_wallet_id) && count($wallets) <= 1 ? 'disabled' : ''; ?>
                                                            >
                                                                Deaktivovat
                                                            </button>
                                                        </form>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <!-- Formulář pro vytvoření nové peněženky -->
                        <div class="border-t pt-4">
                            <h3 class="text-lg font-medium mb-3">Vytvořit novou peněženku</h3>
                            <form method="POST" action="settings.php" class="space-y-4">
                                <div>
                                    <label for="wallet_label" class="block text-sm font-medium text-gray-700 mb-2">
                                        Název peněženky
                                    </label>
                                    <input
                                        type="text"
                                        id="wallet_label"
                                        name="wallet_label"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                                        required
                                    >
                                </div>
                                
                                <div class="flex items-center">
                                    <input
                                        type="checkbox"
                                        id="is_default"
                                        name="is_default"
                                        value="1"
                                        class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                                    >
                                    <label for="is_default" class="ml-2 block text-sm text-gray-900">
                                        Nastavit jako výchozí peněženku
                                    </label>
                                </div>
                                
                                <div>
                                    <button
                                        type="submit"
                                        name="create_wallet"
                                        class="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                                    >
                                        Vytvořit peněženku
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Sekce pro konfiguraci BulkGate API -->
                    <div class="bg-white shadow rounded-lg p-6 mb-6">
                        <h2 class="text-xl font-semibold mb-4">BulkGate API Konfigurace</h2>
                        
                        <?php
                        // Zkontrolujeme, zda existuje konfigurace BulkGate API
                        $bulkgateConfig = null;
                        try {
                            $db = getDatabaseConnection();
                            $stmt = $db->prepare("SELECT * FROM bulkgate_api_config WHERE api_name = 'bulkgate' LIMIT 1");
                            $stmt->execute();
                            $result = $stmt->get_result();
                            $bulkgateConfig = $result->fetch_assoc();
                            $stmt->close();
                        } catch (Exception $e) {
                            echo "<div class='alert alert-danger'>Chyba při získávání konfigurace BulkGate API: " . $e->getMessage() . "</div>";
                        }
                        
                        // Zkontrolujeme, zda tabulka existuje
                        $tableExists = false;
                        try {
                            $db = getDatabaseConnection();
                            $stmt = $db->prepare("SHOW TABLES LIKE 'bulkgate_api_config'");
                            $stmt->execute();
                            $result = $stmt->get_result();
                            $tableExists = $result->num_rows > 0;
                            $stmt->close();
                            
                            if (!$tableExists) {
                                echo "<div class='alert alert-warning'>Tabulka bulkgate_api_config neexistuje. Vytvořte ji pomocí následujícího SQL:</div>";
                                echo "<pre class='bg-gray-100 p-4 rounded text-xs overflow-auto'>
CREATE TABLE `bulkgate_api_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `api_name` varchar(50) NOT NULL,
  `application_id` varchar(100) NOT NULL,
  `application_token` varchar(255) NOT NULL,
  `sms_price_per_unit` decimal(10,2) DEFAULT 0.75,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `api_name` (`api_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            </pre>";
                            }
                        } catch (Exception $e) {
                            echo "<div class='alert alert-danger'>Chyba při kontrole existence tabulky: " . $e->getMessage() . "</div>";
                        }
                        ?>
                        
                        <form method="POST" action="settings.php">
                            <input type="hidden" name="update_bulkgate_config" value="1">
                            <div class="mb-4">
                                <label for="application_id" class="block text-sm font-medium text-gray-700 mb-2">
                                    Application ID
                                </label>
                                <input
                                    type="text"
                                    id="application_id"
                                    name="application_id"
                                    value="<?php echo htmlspecialchars($bulkgateConfig['application_id'] ?? ''); ?>"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                                    required
                                >
                            </div>

                            <div class="mb-4">
                                <label for="application_token" class="block text-sm font-medium text-gray-700 mb-2">
                                    Application Token
                                </label>
                                <input
                                    type="text"
                                    id="application_token"
                                    name="application_token"
                                    value="<?php echo htmlspecialchars($bulkgateConfig['application_token'] ?? ''); ?>"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                                    required
                                >
                            </div>

                            <div class="mb-4">
                                <label for="sms_price_per_unit" class="block text-sm font-medium text-gray-700 mb-2">
                                    Cena za SMS (CZK)
                                </label>
                                <input
                                    type="number"
                                    id="sms_price_per_unit"
                                    name="sms_price_per_unit"
                                    value="<?php echo htmlspecialchars($bulkgateConfig['sms_price_per_unit'] ?? '0.75'); ?>"
                                    step="0.01"
                                    min="0.01"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                                    required
                                >
                            </div>

                            <div class="flex justify-end">
                                <button
                                    type="submit"
                                    class="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                                >
                                    Uložit konfiguraci
                                </button>
                            </div>
                        </form>
                    </div>

                    <div class="bg-white shadow rounded-lg p-6">
                        <h2 class="text-xl font-semibold mb-4">API Nastavení</h2>
                        <form method="POST" action="settings.php">
                            <div class="mb-4">
                                <label for="vapi_api_key" class="block text-sm font-medium text-gray-700 mb-2">
                                    Vapi.ai API Klíč
                                </label>
                                <input
                                    type="text"
                                    id="vapi_api_key"
                                    name="vapi_api_key"
                                    value="<?php echo htmlspecialchars($vapi_api_key); ?>"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                                    required
                                >
                            </div>

                            <div class="mb-4">
                                <label for="vapi_api_url" class="block text-sm font-medium text-gray-700 mb-2">
                                    Vapi.ai API URL
                                </label>
                                <input
                                    type="url"
                                    id="vapi_api_url"
                                    name="vapi_api_url"
                                    value="<?php echo htmlspecialchars($vapi_api_url); ?>"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                                    required
                                >
                            </div>

                            <div class="mb-4">
                                <label for="assistant_id" class="block text-sm font-medium text-gray-700 mb-2">
                                    Assistant ID
                                </label>
                                <input
                                    type="text"
                                    id="assistant_id"
                                    name="assistant_id"
                                    value="<?php echo htmlspecialchars($assistant_id); ?>"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                                    required
                                >
                            </div>

                            <div class="mb-4">
                                <label for="baserow_api_token" class="block text-sm font-medium text-gray-700 mb-2">
                                    Baserow API Token
                                </label>
                                <input
                                    type="text"
                                    id="baserow_api_token"
                                    name="baserow_api_token"
                                    value="<?php echo htmlspecialchars($baserow_api_token); ?>"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                                    required
                                >
                            </div>

                            <div class="mb-4">
                                <label for="baserow_database_id" class="block text-sm font-medium text-gray-700 mb-2">
                                    Baserow Database ID
                                </label>
                                <input
                                    type="text"
                                    id="baserow_database_id"
                                    name="baserow_database_id"
                                    value="<?php echo htmlspecialchars($baserow_database_id); ?>"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                                    required
                                >
                            </div>

                            <div class="mb-4">
                                <label for="baserow_table_id" class="block text-sm font-medium text-gray-700 mb-2">
                                    Baserow Table ID
                                </label>
                                <input
                                    type="text"
                                    id="baserow_table_id"
                                    name="baserow_table_id"
                                    value="<?php echo htmlspecialchars($baserow_table_id); ?>"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                                    required
                                >
                            </div>

                            <div class="flex justify-end">
                                <button
                                    type="submit"
                                    name="update_settings"
                                    class="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                                >
                                    Uložit nastavení
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <script>
        window.addEventListener('message', function(event) {
            if (event.data === 'updateDashboard') {
                location.reload();
            }
        });

        function toggleCustomLimitField() {
            const subscriptionType = document.getElementById('subscription_type').value;
            const customLimitField = document.getElementById('custom_limit_field');
            const customLimitInput = document.getElementById('custom_minute_limit');
            const setUnlimitedBtn = document.getElementById('set_unlimited');
            
            if (subscriptionType === 'Enterprise') {
                customLimitField.style.display = 'block';
                customLimitInput.disabled = false;
                setUnlimitedBtn.disabled = false;
            } else {
                customLimitField.style.display = 'none';
                customLimitInput.disabled = true;
                customLimitInput.value = '';
                setUnlimitedBtn.disabled = true;
            }
        }

        function setUnlimited() {
            const customLimitInput = document.getElementById('custom_minute_limit');
            customLimitInput.value = '';
            customLimitInput.placeholder = 'Neomezeno';
        }

        document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('subscriptionForm');
    const messageDiv = document.getElementById('subscriptionUpdateMessage');

    toggleCustomLimitField();

    form.addEventListener('submit', function(event) {
        event.preventDefault();
        const formData = new FormData(form);
        const submitButton = form.querySelector('button[type="submit"]');
        const originalText = submitButton.textContent;

        submitButton.textContent = 'Aktualizuji...';
        submitButton.disabled = true;

        messageDiv.innerHTML = '';
        messageDiv.classList.remove('hidden');
        messageDiv.className = 'bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded relative mt-4';
        messageDiv.innerHTML = '<span class="block sm:inline">Odesílám požadavek...</span>';

        // Opravený objekt requestData - používáme správné názvy parametrů
        const requestData = {
            subscription_type: formData.get('subscription_type'),
            csrf_token: formData.get('csrf_token')
        };

        if (formData.get('subscription_type') === 'Enterprise') {
            const customLimit = formData.get('custom_minute_limit');
            requestData.custom_minute_limit = customLimit === '' ? -1 : parseInt(customLimit);
        }

        console.log('Sending data:', requestData); // Pro debugging

        // Použijeme fetch místo axios pro lepší kontrolu
        fetch('update_subscription.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify(requestData),
            credentials: 'same-origin'
        })
        .then(response => {
            console.log('Response status:', response.status);
            // Pokud je odpověď 500, zkusíme přečíst text odpovědi pro více informací
            if (response.status === 500) {
                return response.text().then(text => {
                    console.error('Server error response:', text);
                    throw new Error('Server returned 500 error');
                });
            }
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Response data:', data);
            if (data.success) {
                showMessage('success', data.message);
                if (document.getElementById('current-plan')) {
                    document.getElementById('current-plan').textContent = data.new_plan;
                }
                if (document.getElementById('minutes-usage')) {
                    const usageText = data.isUnlimited ? 
                        `${data.minutesUsed} / ∞` : 
                        `${data.minutesUsed} / ${data.new_limit}`;
                    document.getElementById('minutes-usage').textContent = usageText;
                }
                const progressContainer = document.getElementById('progress-container');
                if (progressContainer) {
                    progressContainer.style.display = data.isUnlimited ? 'none' : 'block';
                    if (!data.isUnlimited && document.getElementById('progress-fill')) {
                        const percentage = Math.min((data.minutesUsed / data.new_limit) * 100, 100);
                        document.getElementById('progress-fill').style.width = `${percentage}%`;
                    }
                }
                // Explicitně aktualizujeme dashboard
                window.parent.postMessage({
                    type: 'updateDashboard',
                    data: {
                        plan: data.new_plan,
                        minutesUsed: data.minutesUsed,
                        limit: data.new_limit,
                        isUnlimited: data.isUnlimited
                    }
                }, '*');
            } else {
                showMessage('error', data.message || 'Neznámá chyba při aktualizaci předplatného');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showMessage('error', 'Došlo k chybě při komunikaci se serverem: ' + error.message);
        })
        .finally(() => {
            submitButton.textContent = originalText;
            submitButton.disabled = false;
        });
    });

    function showMessage(type, message) {
        messageDiv.className = type === 'success' 
            ? 'bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mt-4'
            : 'bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mt-4';
        messageDiv.innerHTML = `<span class="block sm:inline">${message}</span>`;
        messageDiv.classList.remove('hidden');
    }
});
        </script>
    </body>
    </html>
<?php
} catch (Exception $e) {
    writeErrorLog("Unhandled exception: " . $e->getMessage(), 'CRITICAL', ['trace' => $e->getTraceAsString()]);
    if (ob_get_length()) ob_end_clean();
    http_response_code(500);
    echo "A critical error occurred. Please check the error logs and contact the administrator.";
}
?>
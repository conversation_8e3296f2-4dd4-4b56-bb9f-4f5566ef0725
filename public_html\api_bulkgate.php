<?php
require_once 'config.php';
require_once 'db_connection.php';
require_once 'error_log.php';

function getApiConfig() {
    try {
        $db = getDatabaseConnection();
        $stmt = $db->prepare("SELECT * FROM sms_api_config WHERE api_name = 'bulkgate' AND is_active = 1 LIMIT 1");
        $stmt->execute();
        $result = $stmt->get_result();
        $config = $result->fetch_assoc();
        $stmt->close();
        
        writeErrorLog('API Config Retrieved', [
            'application_id' => $config['application_id'],
            'has_token' => !empty($config['application_token'])
        ]);
        
        if (!$config) {
            throw new Exception('BulkGate API konfigurace nebyla nalezena');
        }
        
        if (empty($config['application_id']) || empty($config['application_token'])) {
            throw new Exception('Neplatné API přihlašovací údaje');
        }
        
        return $config;
    } catch (Exception $e) {
        writeErrorLog('API Config Error', [
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
        throw $e;
    }
}

function sendMessages($messages, $campaignId = null) {
    try {
        writeErrorLog('Incoming Messages', [
            'campaign_id' => $campaignId,
            'message_count' => count($messages),
            'sample_message' => isset($messages[0]) ? array_merge(
                $messages[0],
                ['message' => substr($messages[0]['message'], 0, 50) . '...']
            ) : null
        ]);

        $config = getApiConfig();
        $url = "https://portal.bulkgate.com/api/2.0/advanced/promotional";
        
        // Validate and format messages
        $formattedMessages = [];
        foreach ($messages as $index => $message) {
            $formattedNumber = formatPhoneNumber($message['number']);
            if (!$formattedNumber) {
                writeErrorLog('Invalid Phone Number', [
                    'index' => $index,
                    'original_number' => $message['number']
                ]);
                throw new Exception("Neplatné telefonní číslo na indexu $index: {$message['number']}");
            }
            
            $formattedMessage = validateMessageText($message['message']);
            if (!$formattedMessage) {
                writeErrorLog('Invalid Message Text', [
                    'index' => $index,
                    'message' => substr($message['message'], 0, 50) . '...'
                ]);
                throw new Exception("Neplatný text zprávy na indexu $index");
            }
            
            $formattedMessages[] = [
                'number' => $formattedNumber,
                'text' => $formattedMessage
            ];
        }

        // Prepare request data
        $requestData = [
            'application_id' => $config['application_id'],
            'application_token' => $config['application_token'],
            'number' => $formattedMessages,
            'sender_id' => 'gText',
            'sender_id_value' => 'Dentibot',
            'unicode' => true
        ];

        // Log request (without sensitive data)
        writeErrorLog('API Request Details', [
            'url' => $url,
            'request' => array_merge(
                $requestData,
                ['application_token' => '***HIDDEN***']
            ),
            'messages_count' => count($formattedMessages),
            'campaign_id' => $campaignId
        ]);

        // Send request and get response
        $response = sendRequest($url, $requestData);
        
        // Validate response
        if (!isset($response['data'])) {
            writeErrorLog('Invalid Response Structure', [
                'response' => $response
            ]);
            throw new Exception('Neplatná odpověď od API: Chybí data');
        }

        // Log successful response
        writeErrorLog('API Response Success', [
            'campaign_id' => $campaignId,
            'response_data_count' => count($response['data'])
        ]);

        // Process message statuses
        if ($campaignId) {
            logMessageStatuses($campaignId, $response['data'], $messages);
        }

        return $response['data'];

    } catch (Exception $e) {
        writeErrorLog('Send Messages Error', [
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString(),
            'campaign_id' => $campaignId
        ]);
        throw new Exception('Chyba při odesílání zpráv: ' . $e->getMessage());
    }
}

function sendRequest($url, $postData) {
    $ch = curl_init();
    
    // Convert postData to JSON
    $jsonData = json_encode($postData);
    
    // Log the exact request being sent
    writeErrorLog('CURL Request', [
        'url' => $url,
        'json_data' => str_replace($postData['application_token'], '***HIDDEN***', $jsonData),
        'json_error' => json_last_error_msg()
    ]);

    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_POST => true,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POSTFIELDS => $jsonData,
        CURLOPT_HTTPHEADER => [
            'Content-Type: application/json',
            'Accept: application/json',
            'Content-Length: ' . strlen($jsonData)
        ],
        CURLOPT_TIMEOUT => 30,
        CURLOPT_SSL_VERIFYPEER => true,
        CURLOPT_SSL_VERIFYHOST => 2,
        CURLOPT_VERBOSE => true
    ]);

    // Capture CURL verbose output
    $verbose = fopen('php://temp', 'w+');
    curl_setopt($ch, CURLOPT_STDERR, $verbose);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    
    // Get verbose information
    rewind($verbose);
    $verboseLog = stream_get_contents($verbose);
    fclose($verbose);

    // Log detailed CURL information
    writeErrorLog('CURL Execution Details', [
        'http_code' => $httpCode,
        'curl_error' => curl_error($ch),
        'curl_errno' => curl_errno($ch),
        'verbose_log' => $verboseLog,
        'response_length' => strlen($response),
        'curl_info' => curl_getinfo($ch)
    ]);

    if (curl_errno($ch)) {
        $error = curl_error($ch);
        curl_close($ch);
        writeErrorLog('CURL Error', [
            'error' => $error,
            'errno' => curl_errno($ch)
        ]);
        throw new Exception("CURL Error: " . $error);
    }

    curl_close($ch);

    // Log response based on HTTP code
    if ($httpCode !== 200) {
        writeErrorLog('HTTP Error', [
            'http_code' => $httpCode,
            'response' => $response
        ]);
        
        $errorMessage = "HTTP Error $httpCode: ";
        if ($httpCode === 400) {
            $errorMessage .= "Neplatný požadavek - zkontrolujte formát telefonních čísel a text zprávy";
        } elseif ($httpCode === 401) {
            $errorMessage .= "Neplatné přihlašovací údaje k API";
        } elseif ($httpCode === 403) {
            $errorMessage .= "Přístup zamítnut";
        } else {
            $errorMessage .= "Neočekávaná chyba";
        }
        
        throw new Exception($errorMessage);
    }

    // Parse JSON response
    $json = json_decode($response, true);
    if ($json === null) {
        writeErrorLog('JSON Decode Error', [
            'error' => json_last_error_msg(),
            'response' => $response
        ]);
        throw new Exception("Neplatná JSON odpověď: " . json_last_error_msg());
    }

    return $json;
}

function logMessageStatuses($campaignId, $response, $messages) {
    try {
        $db = getDatabaseConnection();
        
        writeErrorLog('Message Status Logging Start', [
            'campaign_id' => $campaignId,
            'response_count' => count($response),
            'messages_count' => count($messages)
        ]);
        
        $stmt = $db->prepare("
            INSERT INTO sms_campaign_logs 
            (campaign_id, phone_number, status, message_id, error_code, error_message, sent_at) 
            VALUES (?, ?, ?, ?, ?, ?, NOW())
        ");
        
        if (!$stmt) {
            throw new Exception("Chyba při přípravě SQL dotazu: " . $db->error);
        }

        foreach ($messages as $index => $message) {
            $messageResponse = $response[$index] ?? [];
            $status = $messageResponse['status'] ?? 'unknown';
            $messageId = $messageResponse['message_id'] ?? null;
            $errorCode = isset($messageResponse['error']) ? ($messageResponse['error']['code'] ?? null) : null;
            $errorMessage = isset($messageResponse['error']) ? ($messageResponse['error']['message'] ?? null) : null;
            
            writeErrorLog('Processing Message Status', [
                'campaign_id' => $campaignId,
                'index' => $index,
                'phone' => $message['number'],
                'status' => $status,
                'message_id' => $messageId,
                'error_code' => $errorCode
            ]);
            
            if (!$stmt->bind_param("isssss", 
                $campaignId, 
                $message['number'], 
                $status, 
                $messageId, 
                $errorCode, 
                $errorMessage
            )) {
                throw new Exception("Chyba při bindování parametrů: " . $stmt->error);
            }
            
            if (!$stmt->execute()) {
                throw new Exception("Chyba při ukládání logu: " . $stmt->error);
            }
        }
        
        $stmt->close();
        
        // Update campaign statistics
        updateCampaignStats($campaignId);
        
        writeErrorLog('Message Status Logging Complete', [
            'campaign_id' => $campaignId,
            'processed_count' => count($messages)
        ]);
        
    } catch (Exception $e) {
        writeErrorLog('Log Message Status Error', [
            'error' => $e->getMessage(),
            'campaign_id' => $campaignId,
            'trace' => $e->getTraceAsString()
        ]);
        throw $e;
    }
}

function updateCampaignStats($campaignId) {
    try {
        $db = getDatabaseConnection();
        
        // First, log current stats
        $stmt = $db->prepare("
            SELECT delivered_messages, failed_messages 
            FROM sms_campaigns 
            WHERE id = ?
        ");
        $stmt->bind_param("i", $campaignId);
        $stmt->execute();
        $result = $stmt->get_result();
        $currentStats = $result->fetch_assoc();
        $stmt->close();
        
        writeErrorLog('Current Campaign Stats', [
            'campaign_id' => $campaignId,
            'stats' => $currentStats
        ]);
        
        // Update stats
        $stmt = $db->prepare("
            UPDATE sms_campaigns 
            SET 
                delivered_messages = (
                    SELECT COUNT(*) 
                    FROM sms_campaign_logs 
                    WHERE campaign_id = ? AND status = 'delivered'
                ),
                failed_messages = (
                    SELECT COUNT(*) 
                    FROM sms_campaign_logs 
                    WHERE campaign_id = ? AND status IN ('failed', 'error')
                ),
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        ");
        
        $stmt->bind_param("iii", $campaignId, $campaignId, $campaignId);
        $stmt->execute();
        
        // Log updated stats
        $stmt = $db->prepare("
            SELECT delivered_messages, failed_messages 
            FROM sms_campaigns 
            WHERE id = ?
        ");
        $stmt->bind_param("i", $campaignId);
        $stmt->execute();
        $result = $stmt->get_result();
        $newStats = $result->fetch_assoc();
        
        writeErrorLog('Updated Campaign Stats', [
            'campaign_id' => $campaignId,
            'old_stats' => $currentStats,
            'new_stats' => $newStats
        ]);
        
        $stmt->close();
        
    } catch (Exception $e) {
        writeErrorLog('Update Campaign Stats Error', [
            'error' => $e->getMessage(),
            'campaign_id' => $campaignId,
            'trace' => $e->getTraceAsString()
        ]);
        throw $e;
    }
}

function formatPhoneNumber($number) {
    // Odstranění všech nečíselných znaků
    $number = preg_replace('/[^0-9]/', '', $number);
    
    // Kontrola délky čísla (9 číslic pro CZ číslo bez předvolby)
    if (strlen($number) == 9) {
        return '+420' . $number;
    }
    
    // Kontrola, zda číslo již obsahuje mezinárodní předvolbu
    if (strpos($number, '420') === 0 && strlen($number) == 12) {
        return '+' . $number;
    }
    
    // Pokud číslo již začíná '+', považujeme ho za správně formátované
    if (strpos($number, '+') === 0 && strlen($number) >= 11) {
        return $number;
    }
    
    // Pokud se číslo nepodařilo formátovat, vrátíme false
    return false;
}

function validateMessageText($text) {
    // Odstranění přebytečných mezer na začátku a konci
    $text = trim($text);
    
    // Kontrola délky zprávy (BulkGate má limit 612 znaků pro Unicode zprávy)
    if (strlen($text) > 612) {
        return false;
    }
    
    // Zde můžete přidat další validace podle potřeby
    
    return $text;
}

























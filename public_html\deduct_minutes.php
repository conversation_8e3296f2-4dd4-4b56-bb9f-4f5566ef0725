<?php
require_once 'config.php';
require_once 'subscription_constants.php';

function deductMinutes($userId, $minutesToDeduct) {
    global $mysqli;
    
    $stmt = $mysqli->prepare("SELECT minutes_remaining FROM users WHERE id = ?");
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();
    $current = $result->fetch_assoc()['minutes_remaining'];
    
    if ($current === false || $current < $minutesToDeduct) {
        return false;
    }
    
    $newMinutes = $current - $minutesToDeduct;
    $stmt = $mysqli->prepare("UPDATE users SET minutes_remaining = ? WHERE id = ?");
    $stmt->bind_param("ii", $newMinutes, $userId);
    $stmt->execute();
    
    $stmt = $mysqli->prepare("INSERT INTO minutes_usage_logs (user_id, minutes_deducted, action_type) VALUES (?, ?, ?)");
    $actionType = 'voice_call';
    $stmt->bind_param("iis", $userId, $minutesToDeduct, $actionType);
    $stmt->execute();
    
    return true;
}
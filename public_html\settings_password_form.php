<?php
// Ensure this file is not accessed directly
if (!defined('INCLUDE_CHECK')) {
    die('You cannot access this file directly.');
}

// Initialize variables
$password_error = '';
$password_success = '';

// Process form submission
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['change_password'])) {
    $current_password = $_POST['current_password'];
    $new_password = $_POST['new_password'];
    $confirm_password = $_POST['confirm_password'];

    // Validate input
    if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
        $password_error = "Všechna pole jsou povinná.";
    } elseif ($new_password !== $confirm_password) {
        $password_error = "Nové heslo a potvrzení hesla se neshodují.";
    } else {
        // Verify current password
        $user_id = $_SESSION['user_id'];
        $stmt = $mysqli->prepare("SELECT password FROM users WHERE id = ?");
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $user = $result->fetch_assoc();

        if ($user && password_verify($current_password, $user['password'])) {
            // Hash the new password
            $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);

            // Update the password in the database
            $update_stmt = $mysqli->prepare("UPDATE users SET password = ? WHERE id = ?");
            $update_stmt->bind_param("si", $hashed_password, $user_id);
            
            if ($update_stmt->execute()) {
                $password_success = "Heslo bylo úspěšně změněno.";
            } else {
                $password_error = "Nepodařilo se změnit heslo. Zkuste to prosím znovu.";
            }
            $update_stmt->close();
        } else {
            $password_error = "Současné heslo je nesprávné.";
        }
        $stmt->close();
    }
}
?>

<h2 class="text-xl font-semibold mb-4">Změna hesla</h2>

<?php if ($password_error): ?>
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
        <span class="block sm:inline"><?php echo htmlspecialchars($password_error); ?></span>
    </div>
<?php endif; ?>

<?php if ($password_success): ?>
    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
        <span class="block sm:inline"><?php echo htmlspecialchars($password_success); ?></span>
    </div>
<?php endif; ?>

<form method="POST" action="" class="space-y-4">
    <div>
        <label for="current_password" class="block text-sm font-medium text-gray-700">Současné heslo</label>
        <input type="password" id="current_password" name="current_password" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
    </div>
    <div>
        <label for="new_password" class="block text-sm font-medium text-gray-700">Nové heslo</label>
        <input type="password" id="new_password" name="new_password" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
    </div>
    <div>
        <label for="confirm_password" class="block text-sm font-medium text-gray-700">Potvrzení nového hesla</label>
        <input type="password" id="confirm_password" name="confirm_password" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
    </div>
    <div>
        <button type="submit" name="change_password" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
            Změnit heslo
        </button>
    </div>
</form>


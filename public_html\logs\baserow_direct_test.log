[2025-03-24 15:17:55] Použité hodnoty: {
    "table_id": "430539",
    "patient_id": 3798,
    "field_name": "<PERSON>tum_prohlidky",
    "field_value": "2025-03-24",
    "api_token_length": 32
}
[2025-03-24 15:17:55] Příprava požadavku: {
    "url": "https:\/\/api.baserow.io\/api\/database\/rows\/table\/430539\/3798\/?user_field_names=true",
    "method": "PATCH",
    "data": {
        "Datum_prohlidky": "2025-03-24"
    },
    "json": "{\"Datum_prohlidky\":\"2025-03-24\"}"
}
[2025-03-24 15:17:55] cURL Verbose Log: {
    "log": "*   Trying ************:443...\n* Connected to api.baserow.io (************) port 443 (#0)\n* ALPN, offering h2\n* ALPN, offering http\/1.1\n*  CAfile: \/etc\/pki\/tls\/certs\/ca-bundle.crt\n* SSL connection using TLSv1.2 \/ ECDHE-RSA-AES128-GCM-SHA256\n* ALPN, server accepted to use h2\n* Server certificate:\n*  subject: CN=api.baserow.io\n*  start date: Mar  5 00:00:00 2025 GMT\n*  expire date: Apr  3 23:59:59 2026 GMT\n*  subjectAltName: host \"api.baserow.io\" matched cert's \"api.baserow.io\"\n*  issuer: C=US; O=Amazon; CN=Amazon RSA 2048 M03\n*  SSL certificate verify ok.\n* Using HTTP2, server supports multi-use\n* Connection state changed (HTTP\/2 confirmed)\n* Copying HTTP\/2 data in stream buffer to connection buffer after upgrade: len=0\n* Using Stream ID: 1 (easy handle 0x1e058e0)\n> PATCH \/api\/database\/rows\/table\/430539\/3798\/?user_field_names=true HTTP\/2\r\nHost: api.baserow.io\r\naccept: *\/*\r\nauthorization: Token gdCsjmjPXEWTq5ftw2oEaJKa8eIurMyI\r\ncontent-type: application\/json\r\ncontent-length: 32\r\n\r\n* We are completely uploaded and fine\n* Connection state changed (MAX_CONCURRENT_STREAMS == 128)!\n< HTTP\/2 200 \r\n< date: Mon, 24 Mar 2025 15:17:55 GMT\r\n< content-type: application\/json\r\n< content-length: 828\r\n< server: gunicorn\r\n< allow: GET, PATCH, DELETE, HEAD, OPTIONS\r\n< x-frame-options: DENY\r\n< x-content-type-options: nosniff\r\n< referrer-policy: same-origin\r\n< cross-origin-opener-policy: same-origin\r\n< vary: origin\r\n< \r\n* Connection #0 to host api.baserow.io left intact\n"
}
[2025-03-24 15:17:55] Odpověď API: {
    "status_code": 200,
    "response_length": 828,
    "response": "{\"id\":3798,\"order\":\"201.00000000000000000000\",\"Jmeno_pacienta\":\"Adamcová Pavla\",\"Rodne_cislo\":\"8652056336\",\"Telefonni_cislo\":\"733347282\",\"Alternativni_telefonni_cislo\":\"571 444 100\",\"Datum_prohlidky\":\"2025-03-24T00:00:00Z\",\"Stav preventivních prohlídek\":{\"id\":2502731,\"value\":\"Aktivní\",\"color\":\"blue\"},\"Posledni_akutni_pripad\":null,\"Event ID\":\"tcbj99dlmn4bael60ff6u1eoq4\",\"Lékařské poznámky\":\"\",\"Emailova_adresa\":\"\",\"Komunikační preference\":null,\"Připomínky\":\"\",\"2_posledni_cisla_rodneho_cisla\":\"36\",\"Sekundarni_overeni\":\"733347282-36\",\"Telefonni_cislo 2\":\"733 347 282\",\"Sekundarni_overeni 2\":\"Adamcová Pavla-36\",\"akutní\":null,\"broušení\":\"\",\"endo\":null,\"extrakce, chirurgie\":null,\"postendo\":null,\"předání protetiky\":null,\"sanace - dítě\":null,\"sanace - dospělý\":null,\"snímatelná protetika - otisky\":null}"
}
[2025-03-24 15:17:55] Výsledek aktualizace: {
    "field": "Datum_prohlidky",
    "sent_value": "2025-03-24",
    "received_value": "2025-03-24T00:00:00Z",
    "success": true,
    "note": "Pro datumová pole Baserow vrací hodnoty ve formátu ISO 8601 s časem (např. YYYY-MM-DDT00:00:00Z)"
}
[2025-03-24 15:18:11] Testování konkrétního pole: {
    "table_id": "430539",
    "patient_id": 3798,
    "field_name": "broušení",
    "field_value": "ahoj"
}
[2025-03-24 15:18:12] Výsledek aktualizace pole: {
    "field": "broušení",
    "sent_value": "ahoj",
    "received_value": "ahoj",
    "success": true
}
[2025-03-24 16:47:23] Chyba při testování: {
    "error": "Uživatel není přihlášen",
    "trace": "#0 \/home\/<USER>\/domains\/dentibot.eu\/public_html\/seznam_pacientu\/test_baserow_direct.php(52): getBaserowConfig()\n#1 \/home\/<USER>\/domains\/dentibot.eu\/public_html\/seznam_pacientu\/test_baserow_direct.php(346): testDirectBaserowUpdate()\n#2 {main}"
}
[2025-03-24 16:47:44] Chyba při testování: {
    "error": "Uživatel není přihlášen",
    "trace": "#0 \/home\/<USER>\/domains\/dentibot.eu\/public_html\/seznam_pacientu\/test_baserow_direct.php(52): getBaserowConfig()\n#1 \/home\/<USER>\/domains\/dentibot.eu\/public_html\/seznam_pacientu\/test_baserow_direct.php(346): testDirectBaserowUpdate()\n#2 {main}"
}
[2025-03-24 16:50:26] Získána konfigurace Baserow: {
    "table_id": "430539",
    "api_token_length": 32
}
[2025-03-24 16:50:26] Použité hodnoty: {
    "table_id": "430539",
    "patient_id": 3798,
    "field_name": "broušení",
    "field_value": "Test 2025-03-24 16:50:26",
    "api_token_length": 32
}
[2025-03-24 16:50:26] Příprava požadavku: {
    "url": "https:\/\/api.baserow.io\/api\/database\/rows\/table\/430539\/3798\/?user_field_names=true",
    "method": "PATCH",
    "data": {
        "broušení": "Test 2025-03-24 16:50:26"
    },
    "json": "{\"brou\\u0161en\\u00ed\":\"Test 2025-03-24 16:50:26\"}"
}
[2025-03-24 16:50:26] cURL Verbose Log: {
    "log": "*   Trying *************:443...\n* Connected to api.baserow.io (*************) port 443 (#0)\n* ALPN, offering h2\n* ALPN, offering http\/1.1\n*  CAfile: \/etc\/pki\/tls\/certs\/ca-bundle.crt\n* SSL connection using TLSv1.2 \/ ECDHE-RSA-AES128-GCM-SHA256\n* ALPN, server accepted to use h2\n* Server certificate:\n*  subject: CN=api.baserow.io\n*  start date: Mar  5 00:00:00 2025 GMT\n*  expire date: Apr  3 23:59:59 2026 GMT\n*  subjectAltName: host \"api.baserow.io\" matched cert's \"api.baserow.io\"\n*  issuer: C=US; O=Amazon; CN=Amazon RSA 2048 M03\n*  SSL certificate verify ok.\n* Using HTTP2, server supports multi-use\n* Connection state changed (HTTP\/2 confirmed)\n* Copying HTTP\/2 data in stream buffer to connection buffer after upgrade: len=0\n* Using Stream ID: 1 (easy handle 0x241f8a0)\n> PATCH \/api\/database\/rows\/table\/430539\/3798\/?user_field_names=true HTTP\/2\r\nHost: api.baserow.io\r\naccept: *\/*\r\nauthorization: Token gdCsjmjPXEWTq5ftw2oEaJKa8eIurMyI\r\ncontent-type: application\/json\r\ncontent-length: 49\r\n\r\n* We are completely uploaded and fine\n* Connection state changed (MAX_CONCURRENT_STREAMS == 128)!\n< HTTP\/2 200 \r\n< date: Mon, 24 Mar 2025 15:50:26 GMT\r\n< content-type: application\/json\r\n< content-length: 852\r\n< server: gunicorn\r\n< allow: GET, PATCH, DELETE, HEAD, OPTIONS\r\n< x-frame-options: DENY\r\n< x-content-type-options: nosniff\r\n< referrer-policy: same-origin\r\n< cross-origin-opener-policy: same-origin\r\n< vary: origin\r\n< \r\n* Connection #0 to host api.baserow.io left intact\n"
}
[2025-03-24 16:50:26] Odpověď API: {
    "status_code": 200,
    "response_length": 852,
    "response": "{\"id\":3798,\"order\":\"201.00000000000000000000\",\"Jmeno_pacienta\":\"Adamcová Pavla\",\"Rodne_cislo\":\"8652056336\",\"Telefonni_cislo\":\"733347282\",\"Alternativni_telefonni_cislo\":\"571 444 100\",\"Datum_prohlidky\":\"2025-03-24T00:00:00Z\",\"Stav preventivních prohlídek\":{\"id\":2502731,\"value\":\"Aktivní\",\"color\":\"blue\"},\"Posledni_akutni_pripad\":null,\"Event ID\":\"tcbj99dlmn4bael60ff6u1eoq4\",\"Lékařské poznámky\":\"\",\"Emailova_adresa\":\"\",\"Komunikační preference\":null,\"Připomínky\":\"\",\"2_posledni_cisla_rodneho_cisla\":\"36\",\"Sekundarni_overeni\":\"733347282-36\",\"Telefonni_cislo 2\":\"733 347 282\",\"Sekundarni_overeni 2\":\"Adamcová Pavla-36\",\"akutní\":null,\"broušení\":\"Test 2025-03-24 16:50:26\",\"endo\":null,\"extrakce, chirurgie\":null,\"postendo\":null,\"předání protetiky\":null,\"sanace - dítě\":null,\"sanace - dospělý\":null,\"snímatelná protetika - otisky\":null}"
}
[2025-03-24 16:50:26] Výsledek aktualizace: {
    "field": "broušení",
    "sent_value": "Test 2025-03-24 16:50:26",
    "received_value": "Test 2025-03-24 16:50:26",
    "success": true,
    "note": "Pro datumová pole Baserow vrací hodnoty ve formátu ISO 8601 s časem (např. YYYY-MM-DDT00:00:00Z)"
}
[2025-03-24 15:50:45] Testování konkrétního pole: {
    "table_id": "430539",
    "patient_id": 3798,
    "field_name": "broušení",
    "field_value": "aaa"
}
[2025-03-24 15:50:46] Výsledek aktualizace pole: {
    "field": "broušení",
    "sent_value": "aaa",
    "received_value": "aaa",
    "success": true
}

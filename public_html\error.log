2025-01-08 16:33:14 Error: [2] ini_set(): Session ini settings cannot be changed when a session is active in /home/<USER>/domains/dentibot.eu/public_html/config.php on line 3
2025-01-08 16:33:14 Error: [2] ini_set(): Session ini settings cannot be changed when a session is active in /home/<USER>/domains/dentibot.eu/public_html/config.php on line 4
2025-01-08 16:33:14 Error: [2] ini_set(): Session ini settings cannot be changed when a session is active in /home/<USER>/domains/dentibot.eu/public_html/config.php on line 5
2025-01-08 16:33:14 Error: [2] ini_set(): Session ini settings cannot be changed when a session is active in /home/<USER>/domains/dentibot.eu/public_html/config.php on line 6
2025-01-08 16:33:14 Error: [2] ini_set(): Session ini settings cannot be changed when a session is active in /home/<USER>/domains/dentibot.eu/public_html/config.php on line 7
2025-01-08 16:33:14 Error: [8] session_start(): Ignoring session_start() because a session is already active in /home/<USER>/domains/dentibot.eu/public_html/config.php on line 9
2025-01-08 17:33:14 Error: [2] require_once(functions.php): Failed to open stream: No such file or directory in /home/<USER>/domains/dentibot.eu/public_html/reservations.php on line 23
2025-01-08 16:33:14 Error: [2] ini_set(): Session ini settings cannot be changed when a session is active in /home/<USER>/domains/dentibot.eu/public_html/config.php on line 3
2025-01-08 16:33:14 Error: [2] ini_set(): Session ini settings cannot be changed when a session is active in /home/<USER>/domains/dentibot.eu/public_html/config.php on line 4
2025-01-08 16:33:14 Error: [2] ini_set(): Session ini settings cannot be changed when a session is active in /home/<USER>/domains/dentibot.eu/public_html/config.php on line 5
2025-01-08 16:33:14 Error: [2] ini_set(): Session ini settings cannot be changed when a session is active in /home/<USER>/domains/dentibot.eu/public_html/config.php on line 6
2025-01-08 16:33:14 Error: [2] ini_set(): Session ini settings cannot be changed when a session is active in /home/<USER>/domains/dentibot.eu/public_html/config.php on line 7
2025-01-08 16:33:14 Error: [8] session_start(): Ignoring session_start() because a session is already active in /home/<USER>/domains/dentibot.eu/public_html/config.php on line 9
2025-01-08 17:33:14 Error: [2] require_once(functions.php): Failed to open stream: No such file or directory in /home/<USER>/domains/dentibot.eu/public_html/reservations.php on line 23
2025-01-08 20:58:22 Error: [2] ini_set(): Session ini settings cannot be changed when a session is active in /home/<USER>/domains/dentibot.eu/public_html/config.php on line 3
2025-01-08 20:58:22 Error: [2] ini_set(): Session ini settings cannot be changed when a session is active in /home/<USER>/domains/dentibot.eu/public_html/config.php on line 4
2025-01-08 20:58:22 Error: [2] ini_set(): Session ini settings cannot be changed when a session is active in /home/<USER>/domains/dentibot.eu/public_html/config.php on line 5
2025-01-08 20:58:22 Error: [2] ini_set(): Session ini settings cannot be changed when a session is active in /home/<USER>/domains/dentibot.eu/public_html/config.php on line 6
2025-01-08 20:58:22 Error: [2] ini_set(): Session ini settings cannot be changed when a session is active in /home/<USER>/domains/dentibot.eu/public_html/config.php on line 7
2025-01-08 20:58:22 Error: [8] session_start(): Ignoring session_start() because a session is already active in /home/<USER>/domains/dentibot.eu/public_html/config.php on line 9
2025-01-08 21:58:22 Error: [2] require_once(functions.php): Failed to open stream: No such file or directory in /home/<USER>/domains/dentibot.eu/public_html/reservations.php on line 23
2025-01-08 20:58:24 Error: [2] ini_set(): Session ini settings cannot be changed when a session is active in /home/<USER>/domains/dentibot.eu/public_html/config.php on line 3
2025-01-08 20:58:24 Error: [2] ini_set(): Session ini settings cannot be changed when a session is active in /home/<USER>/domains/dentibot.eu/public_html/config.php on line 4
2025-01-08 20:58:24 Error: [2] ini_set(): Session ini settings cannot be changed when a session is active in /home/<USER>/domains/dentibot.eu/public_html/config.php on line 5
2025-01-08 20:58:24 Error: [2] ini_set(): Session ini settings cannot be changed when a session is active in /home/<USER>/domains/dentibot.eu/public_html/config.php on line 6
2025-01-08 20:58:24 Error: [2] ini_set(): Session ini settings cannot be changed when a session is active in /home/<USER>/domains/dentibot.eu/public_html/config.php on line 7
2025-01-08 20:58:24 Error: [8] session_start(): Ignoring session_start() because a session is already active in /home/<USER>/domains/dentibot.eu/public_html/config.php on line 9
2025-01-08 21:58:24 Error: [2] require_once(functions.php): Failed to open stream: No such file or directory in /home/<USER>/domains/dentibot.eu/public_html/reservations.php on line 23
2025-01-08 21:59:56 Error: [2] require_once(functions.php): Failed to open stream: No such file or directory in /home/<USER>/domains/dentibot.eu/public_html/reservations.php on line 23
2025-01-08 21:59:57 Error: [2] require_once(functions.php): Failed to open stream: No such file or directory in /home/<USER>/domains/dentibot.eu/public_html/reservations.php on line 23
2025-01-08 21:59:57 Error: [2] require_once(functions.php): Failed to open stream: No such file or directory in /home/<USER>/domains/dentibot.eu/public_html/reservations.php on line 23
2025-01-08 21:59:57 Error: [2] require_once(functions.php): Failed to open stream: No such file or directory in /home/<USER>/domains/dentibot.eu/public_html/reservations.php on line 23
2025-01-08 22:01:42 Error: [2] require_once(functions.php): Failed to open stream: No such file or directory in /home/<USER>/domains/dentibot.eu/public_html/reservations.php on line 24
2025-01-08 22:01:43 Error: [2] require_once(functions.php): Failed to open stream: No such file or directory in /home/<USER>/domains/dentibot.eu/public_html/reservations.php on line 24
2025-01-08 22:02:00 Error: [2] require_once(functions.php): Failed to open stream: No such file or directory in /home/<USER>/domains/dentibot.eu/public_html/reservations.php on line 23
2025-01-08 22:02:01 Error: [2] require_once(functions.php): Failed to open stream: No such file or directory in /home/<USER>/domains/dentibot.eu/public_html/reservations.php on line 23
2025-01-08 22:02:02 Error: [2] require_once(functions.php): Failed to open stream: No such file or directory in /home/<USER>/domains/dentibot.eu/public_html/reservations.php on line 23
2025-01-08 22:05:14 Error: [2] require_once(functions.php): Failed to open stream: No such file or directory in /home/<USER>/domains/dentibot.eu/public_html/reservations.php on line 22
2025-01-08 22:05:15 Error: [2] require_once(functions.php): Failed to open stream: No such file or directory in /home/<USER>/domains/dentibot.eu/public_html/reservations.php on line 22
2025-01-08 22:05:15 Error: [2] require_once(functions.php): Failed to open stream: No such file or directory in /home/<USER>/domains/dentibot.eu/public_html/reservations.php on line 22
2025-01-08 22:05:21 Error: [2] require_once(functions.php): Failed to open stream: No such file or directory in /home/<USER>/domains/dentibot.eu/public_html/reservations.php on line 22
2025-01-08 22:05:21 Error: [2] require_once(functions.php): Failed to open stream: No such file or directory in /home/<USER>/domains/dentibot.eu/public_html/reservations.php on line 22
2025-01-08 22:06:48 Error: [8192] Implicit conversion from float 68.48387096774194 to int loses precision in /home/<USER>/domains/dentibot.eu/public_html/api_functions.php on line 302
2025-01-08 22:12:01 Error: [2] require_once(functions.php): Failed to open stream: No such file or directory in /home/<USER>/domains/dentibot.eu/public_html/reservations.php on line 12
2025-01-08 22:12:02 Error: [2] require_once(functions.php): Failed to open stream: No such file or directory in /home/<USER>/domains/dentibot.eu/public_html/reservations.php on line 12
2025-01-08 22:12:02 Error: [2] require_once(functions.php): Failed to open stream: No such file or directory in /home/<USER>/domains/dentibot.eu/public_html/reservations.php on line 12
2025-01-08 22:12:02 Error: [2] require_once(functions.php): Failed to open stream: No such file or directory in /home/<USER>/domains/dentibot.eu/public_html/reservations.php on line 12
2025-01-08 22:12:02 Error: [2] require_once(functions.php): Failed to open stream: No such file or directory in /home/<USER>/domains/dentibot.eu/public_html/reservations.php on line 12
2025-01-08 22:12:02 Error: [2] require_once(functions.php): Failed to open stream: No such file or directory in /home/<USER>/domains/dentibot.eu/public_html/reservations.php on line 12
2025-01-08 22:12:02 Error: [2] require_once(functions.php): Failed to open stream: No such file or directory in /home/<USER>/domains/dentibot.eu/public_html/reservations.php on line 12
2025-01-08 22:12:03 Error: [2] require_once(functions.php): Failed to open stream: No such file or directory in /home/<USER>/domains/dentibot.eu/public_html/reservations.php on line 12
2025-01-08 22:12:03 Error: [2] require_once(functions.php): Failed to open stream: No such file or directory in /home/<USER>/domains/dentibot.eu/public_html/reservations.php on line 12
2025-01-08 22:16:33 Error: [2] require_once(functions.php): Failed to open stream: No such file or directory in /home/<USER>/domains/dentibot.eu/public_html/reservations.php on line 12
2025-01-08 22:16:57 Error: [2] require_once(functions.php): Failed to open stream: No such file or directory in /home/<USER>/domains/dentibot.eu/public_html/reservations.php on line 22
2025-01-08 22:16:59 Error: [2] require_once(functions.php): Failed to open stream: No such file or directory in /home/<USER>/domains/dentibot.eu/public_html/reservations.php on line 22
2025-01-08 22:16:59 Error: [2] require_once(functions.php): Failed to open stream: No such file or directory in /home/<USER>/domains/dentibot.eu/public_html/reservations.php on line 22
2025-01-08 22:16:59 Error: [2] require_once(functions.php): Failed to open stream: No such file or directory in /home/<USER>/domains/dentibot.eu/public_html/reservations.php on line 22
2025-01-08 22:17:00 Error: [2] require_once(functions.php): Failed to open stream: No such file or directory in /home/<USER>/domains/dentibot.eu/public_html/reservations.php on line 22
2025-01-08 22:17:00 Error: [2] require_once(functions.php): Failed to open stream: No such file or directory in /home/<USER>/domains/dentibot.eu/public_html/reservations.php on line 22
2025-01-08 22:17:20 Error: [8192] Implicit conversion from float 68.48387096774194 to int loses precision in /home/<USER>/domains/dentibot.eu/public_html/api_functions.php on line 302
2025-01-08 22:17:25 Error: [8] session_start(): Ignoring session_start() because a session is already active in /home/<USER>/domains/dentibot.eu/public_html/sms_campaigns.php on line 3
2025-01-08 22:17:28 Error: [8] session_start(): Ignoring session_start() because a session is already active in /home/<USER>/domains/dentibot.eu/public_html/sms_campaigns.php on line 3
2025-01-08 22:17:31 Error: [8192] Implicit conversion from float 68.48387096774194 to int loses precision in /home/<USER>/domains/dentibot.eu/public_html/api_functions.php on line 302
getReservations called with date: 2025-01-08MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/reservations.php(34): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-08MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/reservations.php(34): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-08MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/reservations.php(34): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-14MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/reservations.php(34): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-21MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/reservations.php(34): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-15MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/reservations.php(34): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-08MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/reservations.php(26): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-08MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/reservations.php(26): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-07MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/reservations.php(26): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-15MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/reservations.php(26): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-15MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/reservations.php(34): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-15MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/reservations.php(26): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-15MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/reservations.php(26): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-16MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/reservations.php(26): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-07MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/reservations.php(26): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-07MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/reservations.php(26): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-07MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/reservations.php(26): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-07MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/reservations.php(26): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-07MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/reservations.php(26): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-07MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/reservations.php(26): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-07MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/reservations.php(26): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-07MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/reservations.php(34): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-07MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/reservations.php(26): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-07MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/reservations.php(26): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-25MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/reservations.php(26): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-08MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/reservations.php(26): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-07MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/reservations.php(26): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-07MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/reservations.php(26): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-07MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/reservations.php(26): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-08MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/reservations.php(26): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-09MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/reservations.php(26): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-08MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/reservations.php(26): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-10MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/reservations.php(26): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-23MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/reservations.php(26): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-20MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/reservations.php(26): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-07MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/reservations.php(26): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-07MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/reservations.php(26): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-07MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/reservations.php(26): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-06MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-08MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-01MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-08MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2024-12-31MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-09MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-16MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-16MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-21MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-23MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-09MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-08MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-08MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-15MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-15MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-15MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-15MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-08MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-22MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-16MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-09MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-07MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-07MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-15MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-09MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-05MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-12MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-19MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-05MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-06MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-02MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2024-12-31MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-08MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-06MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-13MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-21MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-21MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-07MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/reservations.php(26): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-08MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/reservations.php(26): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-16MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-17MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-10MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-09MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-08MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/reservations.php(26): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-09MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-10MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-03MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-10MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-16MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-16MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-15MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-08MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-08MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/reservations.php(26): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-09MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-08MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/reservations.php(26): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-23MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-15MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-16MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-17MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-10MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-14MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-12MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-20MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-21MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-08MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/reservations.php(26): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-08MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/reservations.php(26): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-08MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/reservations.php(26): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-08MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-08MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-08MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-08MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-01MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-08MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-09MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-16MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-17MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-10MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-03MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-02MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-01MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-08MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-07MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-08MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-01MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-01MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-08MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/reservations.php(26): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-08MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-09MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-16MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-15MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-21MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-07MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-06MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-13MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-20MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-22MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-16MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-24MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-24MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-10MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-03MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-10MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-01MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-08MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/reservations.php(26): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-08MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/reservations.php(26): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-08MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-15MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-16MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-09MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-08MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-07MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-06MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-08MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-07MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-14MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-15MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-08MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-03MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-10MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-16MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-08MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/reservations.php(26): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-29MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-08MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/reservations.php(26): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-08MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/reservations.php(26): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-15MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-16MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-09MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-08MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-10MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-24MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-30MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-07MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-08MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-09MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-24MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-15MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-02-01MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-02-13MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-08MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/reservations.php(26): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-08MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/reservations.php(26): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-08MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-09MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-16MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-22MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-21MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-24MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(13): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-08MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/reservations.php(26): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-08MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/reservations.php(26): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-16MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(16): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-09MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(16): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't existgetReservations called with date: 2025-01-08MySQL connection status: connectedPreparing query: SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASCError in getReservations(): Table 'u345712091_dentibot.reservations' doesn't exist
Trace: #0 /home/<USER>/domains/dentibot.eu/public_html/functions.php(32): mysqli->prepare()
#1 /home/<USER>/domains/dentibot.eu/public_html/get_reservations.php(16): getReservations()
#2 {main}MySQL Error: Table 'u345712091_dentibot.reservations' doesn't exist[2025-02-11 16:20:33] Starting update_subscription.php
[2025-02-11 17:20:33] Fatal Error: Cannot redeclare verifyCSRFToken() (previously declared in /home/<USER>/domains/dentibot.eu/public_html/config.php:207) in /home/<USER>/domains/dentibot.eu/public_html/functions.php on line 183
[2025-02-11 16:29:56] Starting update_subscription.php
[2025-02-11 17:29:56] Fatal Error: Cannot redeclare verifyCSRFToken() (previously declared in /home/<USER>/domains/dentibot.eu/public_html/config.php:207) in /home/<USER>/domains/dentibot.eu/public_html/functions.php on line 183
[2025-02-11 16:31:23] Starting update_subscription.php
[2025-02-11 17:31:23] Fatal Error: Cannot redeclare verifyCSRFToken() (previously declared in /home/<USER>/domains/dentibot.eu/public_html/config.php:207) in /home/<USER>/domains/dentibot.eu/public_html/functions.php on line 183
[2025-02-11 16:31:43] Starting update_subscription.php
[2025-02-11 17:31:43] Fatal Error: Cannot redeclare verifyCSRFToken() (previously declared in /home/<USER>/domains/dentibot.eu/public_html/config.php:207) in /home/<USER>/domains/dentibot.eu/public_html/functions.php on line 183
[2025-02-11 16:31:59] Starting update_subscription.php
[2025-02-11 17:31:59] Fatal Error: Cannot redeclare verifyCSRFToken() (previously declared in /home/<USER>/domains/dentibot.eu/public_html/config.php:207) in /home/<USER>/domains/dentibot.eu/public_html/functions.php on line 183
[2025-02-11 16:34:42] PHP Error: [2] Undefined global variable $_SESSION in /home/<USER>/domains/dentibot.eu/public_html/update_subscription.php on line 39
[2025-02-11 16:35:12] PHP Error: [2] Undefined global variable $_SESSION in /home/<USER>/domains/dentibot.eu/public_html/update_subscription.php on line 39
[2025-02-11 16:37:17] PHP Error: [2] Undefined global variable $_SESSION in /home/<USER>/domains/dentibot.eu/public_html/update_subscription.php on line 39
[2025-02-11 16:39:23] Starting update_subscription.php Data: {"csrf_token":"b0dab084a057de5c366e887172b53e6b3bcb94bb4a3c137fd2d2462bd67eb172","subscription_type":"Pokro\u010dil\u00fd","custom_minute_limit":""}
[2025-02-11 17:39:23] Fatal Error: Cannot redeclare verifyCSRFToken() (previously declared in /home/<USER>/domains/dentibot.eu/public_html/config.php:207) in /home/<USER>/domains/dentibot.eu/public_html/functions.php on line 12 Trace: [{"file":"\/home\/<USER>\/domains\/dentibot.eu\/public_html\/update_subscription.php","line":46,"function":"logError","args":["Fatal Error: Cannot redeclare verifyCSRFToken() (previously declared in \/home\/<USER>\/domains\/dentibot.eu\/public_html\/config.php:207) in \/home\/<USER>\/domains\/dentibot.eu\/public_html\/functions.php on line 12",[],true]},{"function":"{closure}","args":[]}]
[2025-02-11 16:39:24] Starting update_subscription.php Data: {"csrf_token":"b0dab084a057de5c366e887172b53e6b3bcb94bb4a3c137fd2d2462bd67eb172","subscription_type":"Pokro\u010dil\u00fd","custom_minute_limit":""}
[2025-02-11 17:39:24] Fatal Error: Cannot redeclare verifyCSRFToken() (previously declared in /home/<USER>/domains/dentibot.eu/public_html/config.php:207) in /home/<USER>/domains/dentibot.eu/public_html/functions.php on line 12 Trace: [{"file":"\/home\/<USER>\/domains\/dentibot.eu\/public_html\/update_subscription.php","line":46,"function":"logError","args":["Fatal Error: Cannot redeclare verifyCSRFToken() (previously declared in \/home\/<USER>\/domains\/dentibot.eu\/public_html\/config.php:207) in \/home\/<USER>\/domains\/dentibot.eu\/public_html\/functions.php on line 12",[],true]},{"function":"{closure}","args":[]}]
[2025-02-11 16:40:05] Starting update_subscription.php Data: {"csrf_token":"b0dab084a057de5c366e887172b53e6b3bcb94bb4a3c137fd2d2462bd67eb172","subscription_type":"Pokro\u010dil\u00fd","custom_minute_limit":""}
[2025-02-11 17:40:05] Fatal Error: Cannot redeclare verifyCSRFToken() (previously declared in /home/<USER>/domains/dentibot.eu/public_html/config.php:207) in /home/<USER>/domains/dentibot.eu/public_html/functions.php on line 183 Trace: [{"file":"\/home\/<USER>\/domains\/dentibot.eu\/public_html\/update_subscription.php","line":46,"function":"logError","args":["Fatal Error: Cannot redeclare verifyCSRFToken() (previously declared in \/home\/<USER>\/domains\/dentibot.eu\/public_html\/config.php:207) in \/home\/<USER>\/domains\/dentibot.eu\/public_html\/functions.php on line 183",[],true]},{"function":"{closure}","args":[]}]
[2025-02-11 16:49:07] Starting update_subscription.php Data: {"csrf_token":"b0dab084a057de5c366e887172b53e6b3bcb94bb4a3c137fd2d2462bd67eb172","subscription_type":"Pokro\u010dil\u00fd","custom_minute_limit":""}
[2025-02-11 17:49:07] Fatal Error: Cannot redeclare verifyCSRFToken() (previously declared in /home/<USER>/domains/dentibot.eu/public_html/config.php:207) in /home/<USER>/domains/dentibot.eu/public_html/functions.php on line 183 Trace: [{"file":"\/home\/<USER>\/domains\/dentibot.eu\/public_html\/update_subscription.php","line":46,"function":"logError","args":["Fatal Error: Cannot redeclare verifyCSRFToken() (previously declared in \/home\/<USER>\/domains\/dentibot.eu\/public_html\/config.php:207) in \/home\/<USER>\/domains\/dentibot.eu\/public_html\/functions.php on line 183",[],true]},{"function":"{closure}","args":[]}]

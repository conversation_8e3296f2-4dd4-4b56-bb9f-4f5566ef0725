<?php
require_once __DIR__ . '/config.php';
require_once __DIR__ . '/error_log.php';
require_once __DIR__ . '/db_connection.php';

function lookupPatientName($phoneNumber) {
    try {
        // Get user's Baserow configuration from database
        $user_id = $_SESSION['user_id'] ?? null;
        if (!$user_id) {
            writeErrorLog('No user ID in session');
            return null;
        }

        $db = getDbConnection();
        $stmt = $db->prepare("SELECT baserow_api_token, baserow_table_id FROM users WHERE id = ?");
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $baserowConfig = $result->fetch_assoc();
        $stmt->close();
        
        if (!$baserowConfig || empty($baserowConfig['baserow_api_token']) || empty($baserowConfig['baserow_table_id'])) {
            writeErrorLog('Missing Baserow configuration', [
                'user_id' => $user_id,
                'config' => $baserowConfig
            ]);
            return null;
        }
        
        // Clean and format phone number
        $phoneNumber = cleanPhoneNumber($phoneNumber);

        // Make API request
        $baserowUrl = "https://api.baserow.io/api/database/rows/table/{$baserowConfig['baserow_table_id']}/?user_field_names=true&filter__Telefonni_cislo__contains={$phoneNumber}";

        $ch = curl_init($baserowUrl);
        curl_setopt_array($ch, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPHEADER => [
                'Authorization: Token ' . $baserowConfig['baserow_api_token'],
                'Content-Type: application/json'
            ],
            CURLOPT_SSL_VERIFYPEER => true,
            CURLOPT_TIMEOUT => 30
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);

        curl_close($ch);

        if ($httpCode !== 200) {
            writeErrorLog('Baserow API error', [
                'http_code' => $httpCode,
                'error' => $error
            ]);
            return null;
        }

        $data = json_decode($response, true);
        if (!$data) {
            writeErrorLog('Invalid JSON response from Baserow');
            return null;
        }

        if (isset($data['results']) && !empty($data['results'])) {
            $patientName = $data['results'][0]['Jmeno_pacienta'] ?? null;
            return $patientName;
        }

        return null;
        
    } catch (Exception $e) {
        writeErrorLog("Error in lookupPatientName: " . $e->getMessage());
        return null;
    }
}

function cleanPhoneNumber($phoneNumber) {
    // Check if $phoneNumber is null or empty
    if ($phoneNumber === null || $phoneNumber === '') {
        return '';
    }

    // Remove all non-digit characters
    $phoneNumber = preg_replace('/[^0-9]/', '', $phoneNumber);
    
    // Remove country code if present (assuming it's always 420 for Czech Republic)
    if (strpos($phoneNumber, '420') === 0) {
        $phoneNumber = substr($phoneNumber, 3);
    }
    
    // Ensure the number starts with 6 or 7 (Czech mobile numbers)
    if (strlen($phoneNumber) === 9 && (substr($phoneNumber, 0, 1) == '6' || substr($phoneNumber, 0, 1) == '7')) {
        return $phoneNumber;
    }
    
    // If the number doesn't match the expected format, return the original cleaned number
    return $phoneNumber;
}

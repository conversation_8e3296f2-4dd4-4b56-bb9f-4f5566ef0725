<?php
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/get_used_minutes.php';

function getMinutesRemaining($user_id) {
    $conn = getDbConnection();
    if (!$conn) {
        error_log('Failed to establish database connection in getMinutesRemaining');
        return 0;
    }
    
    $stmt = null;
    
    try {
        $stmt = $conn->prepare("SELECT minute_limit FROM users WHERE id = ?");
        if (!$stmt) {
            throw new Exception("Failed to prepare statement: " . $conn->error);
        }
        
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        
        $result = $stmt->get_result();
        if ($row = $result->fetch_assoc()) {
            $minute_limit = intval($row['minute_limit']);
            $used_minutes = getUsedMinutes($user_id);
            
            // If minute_limit is -1 (unlimited), return -1
            if ($minute_limit === -1) {
                return -1;
            }
            
            $remaining_minutes = max(0, $minute_limit - $used_minutes);
            return $remaining_minutes;
        }
        
        return 0; // Return 0 if no data found
    } catch (Exception $e) {
        error_log('Get minutes remaining error: ' . json_encode([
            'message' => $e->getMessage(),
            'user_id' => $user_id
        ]));
        return 0; // Return 0 in case of error
    } finally {
        if ($stmt) {
            $stmt->close();
        }
        // We don't close the connection here anymore
    }
}

function updateMinutesRemaining($user_id, $minutes_remaining) {
    $conn = getDbConnection();
    if (!$conn) {
        error_log('Failed to establish database connection in updateMinutesRemaining');
        return false;
    }
    
    $stmt = null;
    
    try {
        $stmt = $conn->prepare("UPDATE users SET minutes_remaining = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
        if (!$stmt) {
            throw new Exception("Failed to prepare statement: " . $conn->error);
        }
        
        $stmt->bind_param("ii", $minutes_remaining, $user_id);
        $result = $stmt->execute();
        
        if ($result) {
            error_log('Minutes remaining updated: ' . json_encode([
                'user_id' => $user_id,
                'minutes_remaining' => $minutes_remaining,
                'timestamp' => date('Y-m-d H:i:s')
            ]));
            return true;
        }
        
        return false;
    } catch (Exception $e) {
        error_log('Update minutes remaining error: ' . json_encode([
            'message' => $e->getMessage(),
            'user_id' => $user_id,
            'minutes_remaining' => $minutes_remaining
        ]));
        return false;
    } finally {
        if ($stmt) {
            $stmt->close();
        }
        // We don't close the connection here anymore
    }
}


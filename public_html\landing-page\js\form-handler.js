document.addEventListener("DOMContentLoaded", () => {
  const form = document.getElementById("demoForm")
  const messageDiv = document.getElementById("message")

  form.addEventListener("submit", (e) => {
    e.preventDefault()

    const phoneNumber = document.getElementById("phoneNumber").value
    const email = document.getElementById("email").value

    // Validate phone number
    if (!/^\d{9}$/.test(phoneNumber)) {
      messageDiv.textContent = "Prosím zadejte platné 9místné telefonní číslo."
      messageDiv.className = "message error"
      return
    }

    // Validate email
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      messageDiv.textContent = "Prosím zadejte platnou e-mailovou adresu."
      messageDiv.className = "message error"
      return
    }

    // If validation passes, send the data
    fetch("https://hook.eu2.make.com/4534wx1wba4snk8uqkjae51qewmf4asa", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        phoneNumber: phoneNumber,
        email: email,
        formName: "dentibot demo",
      }),
    })
      .then((response) => {
        if (response.ok) {
          messageDiv.textContent = "Údaje byly úspěšně odeslány pro dentibot demo."
          messageDiv.className = "message success"
          form.reset()
        } else {
          throw new Error("Nepovedlo se odeslat data")
        }
      })
      .catch((error) => {
        messageDiv.textContent = "Nepodařilo se odeslat údaje. Zkuste to prosím znovu."
        messageDiv.className = "message error"
      })
  })
})


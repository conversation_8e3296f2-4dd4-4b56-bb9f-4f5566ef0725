// Theme management
const themeManager = {
  init() {
    this.themeToggle = document.getElementById("themeToggle")
    this.setupEventListeners()
    this.loadSavedTheme()
  },

  setupEventListeners() {
    this.themeToggle.addEventListener("click", () => this.toggleTheme())

    // Listen for system theme changes
    window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change", (e) => {
      if (!localStorage.getItem("theme")) {
        this.setTheme(e.matches ? "dark" : "light")
      }
    })
  },

  loadSavedTheme() {
    const savedTheme = localStorage.getItem("theme")
    if (savedTheme) {
      this.setTheme(savedTheme)
    } else {
      // Use system preference
      const prefersDark = window.matchMedia("(prefers-color-scheme: dark)").matches
      this.setTheme(prefersDark ? "dark" : "light")
    }
  },

  toggleTheme() {
    const isDark = !document.body.classList.contains("light-theme")
    this.setTheme(isDark ? "light" : "dark")
  },

  setTheme(theme) {
    document.body.classList.toggle("light-theme", theme === "light")
    localStorage.setItem("theme", theme)

    // Update charts if they exist
    if (window.dashboardCharts) {
      this.updateChartTheme(theme)
    }

    // Notify other components
    window.dispatchEvent(
      new CustomEvent("themeChanged", {
        detail: { isDark: theme === "dark" },
      }),
    )
  },

  updateChartTheme(theme) {
    const isLight = theme === "light"
    const gridColor = isLight ? "rgba(226, 232, 240, 0.5)" : "rgba(51, 65, 85, 0.5)"
    const textColor = isLight ? "#475569" : "#cbd5e1"

    Object.values(window.dashboardCharts).forEach((chart) => {
      if (chart) {
        chart.options.scales.y.grid.color = gridColor
        chart.options.scales.y.ticks.color = textColor
        chart.options.scales.x.ticks.color = textColor
        chart.update()
      }
    })
  },
}

// Initialize theme manager
document.addEventListener("DOMContentLoaded", () => themeManager.init())


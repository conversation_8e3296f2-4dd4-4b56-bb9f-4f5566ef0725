<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once 'config.php';

if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Ově<PERSON>ení přihlášení
if (!isLoggedIn()) {
    header('Location: login.php');
    exit();
}

$currentPage = 'reservations';

// Dummy data pro události
$dummyEvents = [
    [
        'id' => 1,
        'title' => 'Kontrola - <PERSON>',
        'start' => '2025-02-15 10:00:00',
        'end' => '2025-02-15 11:00:00',
        'type' => 'kontrola',
        'color' => '#10B981',
        'notes' => 'Pravidelná kontrola'
    ],
    [
        'id' => 2,
        'title' => 'Ošetření - <PERSON>',
        'start' => '2025-02-18 14:00:00',
        'end' => '2025-02-18 15:30:00',
        'type' => 'osetreni',
        'color' => '#3B82F6',
        'notes' => 'Ošetření zubu 26'
    ],
    [
        'id' => 3,
        'title' => 'Konzultace - Petr Dvořák',
        'start' => '2025-02-20 09:00:00',
        'end' => '2025-02-20 09:30:00',
        'type' => 'konzultace',
        'color' => '#8B5CF6',
        'notes' => 'Konzultace ohledně implantátu'
    ],
    [
        'id' => 4,
        'title' => 'Operace - Jana Nováková',
        'start' => '2025-02-22 11:00:00',
        'end' => '2025-02-22 13:00:00',
        'type' => 'operace',
        'color' => '#EC4899',
        'notes' => 'Extrakce zubu moudrosti'
    ]
];

// Získání aktuálního data a parametrů z URL
$currentYear = isset($_GET['year']) ? intval($_GET['year']) : intval(date('Y'));
$currentMonth = isset($_GET['month']) ? intval($_GET['month']) : intval(date('m'));
$currentDay = isset($_GET['day']) ? intval($_GET['day']) : intval(date('d'));
$currentView = isset($_GET['view']) ? $_GET['view'] : 'month';

// Převod dummy dat na JavaScript objekt
$eventsJson = json_encode($dummyEvents);

?>

<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rezervace - Dentibot</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        :root {
            --dentibot-primary: #10B981;
            --dentibot-secondary: #3B82F6;
            --dentibot-accent: #8B5CF6;
            --dentibot-background: #1E293B;
            --dentibot-surface: #2D3748;
            --dentibot-text: #F3F4F6;
        }

        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--dentibot-background);
            color: var(--dentibot-text);
        }

        .calendar-container {
            background: var(--dentibot-surface);
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .calendar-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem;
            border-bottom: 1px solid rgba(16, 185, 129, 0.2);
        }

        .calendar-nav-button {
            width: 2rem;
            height: 2rem;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid var(--dentibot-primary);
            border-radius: 0.25rem;
            color: var(--dentibot-primary);
            background: transparent;
            cursor: pointer;
            transition: all 0.2s;
        }

        .calendar-nav-button:hover {
            background: var(--dentibot-primary);
            color: var(--dentibot-background);
        }

        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 1px;
            background: rgba(16, 185, 129, 0.2);
            padding: 1px;
        }

        .calendar-weekday {
            padding: 0.5rem;
            text-align: center;
            font-weight: 600;
            background: var(--dentibot-surface);
            color: var(--dentibot-text);
        }

        .calendar-day {
            min-height: 100px;
            padding: 0.5rem;
            background: var(--dentibot-surface);
            position: relative;
            transition: all 0.2s;
        }

        .calendar-day:hover {
            background: rgba(16, 185, 129, 0.1);
        }

        .calendar-day-number {
            position: absolute;
            top: 0.25rem;
            right: 0.25rem;
            font-size: 0.75rem;
            font-weight: 600;
            color: var(--dentibot-text);
        }

        .calendar-day.other-month {
            opacity: 0.5;
        }

        .calendar-day.today {
            background: rgba(16, 185, 129, 0.2);
            font-weight: bold;
        }

        .calendar-event {
            margin-top: 0.25rem;
            padding: 0.125rem 0.25rem;
            font-size: 0.75rem;
            border-radius: 0.125rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .calendar-event:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .view-button {
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.875rem;
            font-weight: 600;
            transition: all 0.2s;
            border: 1px solid var(--dentibot-primary);
            color: var(--dentibot-primary);
        }

        .view-button.active {
            background: var(--dentibot-primary);
            color: var(--dentibot-background);
        }

        .view-button:not(.active):hover {
            background: rgba(16, 185, 129, 0.1);
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        .modal-content {
            position: relative;
            background: var(--dentibot-surface);
            margin: 10% auto;
            padding: 1.5rem;
            width: 90%;
            max-width: 500px;
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .close-button {
            position: absolute;
            right: 1rem;
            top: 1rem;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--dentibot-text);
        }

        input[type="text"],
        input[type="datetime-local"],
        select,
        textarea {
            background-color: var(--dentibot-background);
            border-color: var(--dentibot-primary);
            color: var(--dentibot-text);
        }

        input[type="text"]:focus,
        input[type="datetime-local"]:focus,
        select:focus,
        textarea:focus {
            border-color: var(--dentibot-primary);
            ring-color: var(--dentibot-primary);
        }

        .week-view, .day-view {
            display: none;
            overflow-y: auto;
            max-height: calc(100vh - 200px);
        }

        .week-view.active, .day-view.active {
            display: block;
        }

        .time-slot {
            display: flex;
            border-bottom: 1px solid rgba(16, 185, 129, 0.2);
            min-height: 60px;
        }

        .time-label {
            width: 60px;
            padding: 0.25rem;
            text-align: right;
            font-size: 0.75rem;
            color: var(--dentibot-text);
        }

        .day-events {
            flex-grow: 1;
            position: relative;
        }

        .week-day-header {
            position: sticky;
            top: 0;
            background-color: var(--dentibot-surface);
            z-index: 10;
        }

        .week-time-grid {
            display: grid;
            grid-template-columns: auto repeat(7, 1fr);
        }

        .week-event, .day-event {
            position: absolute;
            left: 1px;
            right: 1px;
            padding: 2px 4px;
            font-size: 0.75rem;
            border-radius: 2px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            cursor: pointer;
            z-index: 5;
        }

        #eventColor {
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            background-color: transparent;
            width: 50px;
            height: 25px;
            border: none;
            cursor: pointer;
        }
        #eventColor::-webkit-color-swatch {
            border-radius: 4px;
            border: none;
        }
        #eventColor::-moz-color-swatch {
            border-radius: 4px;
            border: none;
        }

        #eventType option {
            padding: 10px;
        }

        #eventType option[value="kontrola"] {
            background-color: #10B981;
            color: white;
        }

        #eventType option[value="osetreni"] {
            background-color: #3B82F6;
            color: white;
        }

        #eventType option[value="konzultace"] {
            background-color: #8B5CF6;
            color: white;
        }

        #eventType option[value="operace"] {
            background-color: #EC4899;
            color: white;
        }

        #eventType option[value="vlastni"] {
            background-color: var(--dentibot-surface);
            color: var(--dentibot-text);
        }

        #eventColor {
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            background-color: transparent;
            border: none;
            cursor: pointer;
            padding: 0;
        }

        #eventColor::-webkit-color-swatch-wrapper {
            padding: 0;
        }

        #eventColor::-webkit-color-swatch {
            border: none;
            border-radius: 50%;
            box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
        }

        #eventColor::-moz-color-swatch {
            border: none;
            border-radius: 50%;
            box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
        }
    </style>
</head>
<body class="bg-gray-900 text-gray-100">
    <div class="flex min-h-screen">
        <?php include 'sidebar.php'; ?>
        
        <main class="flex-1 p-4 lg:p-8">
            <h1 class="text-3xl font-bold mb-6">Rezervace</h1>
            <div class="calendar-container">
                <div class="calendar-header">
                    <div class="flex items-center gap-2">
                        <button class="calendar-nav-button" id="prevButton">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M15 18l-6-6 6-6"/>
                            </svg>
                        </button>
                        <button class="calendar-nav-button" id="nextButton">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M9 18l6-6-6-6"/>
                            </svg>
                        </button>
                        <button class="view-button" id="todayButton">Dnes</button>
                    </div>

                    <h2 id="currentViewDate" class="text-2xl font-bold"></h2>

                    <div class="flex items-center gap-2">
                        <button class="view-button active" data-view="month">Měsíc</button>
                        <button class="view-button" data-view="week">Týden</button>
                        <button class="view-button" data-view="day">Den</button>
                    </div>
                </div>

                <div id="monthView" class="calendar-grid">
                    <div class="calendar-weekday">Po</div>
                    <div class="calendar-weekday">Út</div>
                    <div class="calendar-weekday">St</div>
                    <div class="calendar-weekday">Čt</div>
                    <div class="calendar-weekday">Pá</div>
                    <div class="calendar-weekday">So</div>
                    <div class="calendar-weekday">Ne</div>

                    <?php
                    $currentYear = isset($_GET['year']) ? intval($_GET['year']) : intval(date('Y'));
                    $currentMonth = isset($_GET['month']) ? intval($_GET['month']) : intval(date('m'));
                    $currentDay = isset($_GET['day']) ? intval($_GET['day']) : intval(date('d'));
                    
                    $firstDay = new DateTime("$currentYear-$currentMonth-01");
                    $lastDay = new DateTime($firstDay->format('Y-m-t'));
                    
                    $firstWeekday = intval($firstDay->format('N'));
                    $prevMonth = clone $firstDay;
                    $prevMonth->modify('-' . ($firstWeekday - 1) . ' days');
                    
                    for ($i = 0; $i < 42; $i++) {
                        $currentDate = clone $prevMonth;
                        $currentDate->modify("+$i days");
                        
                        $isCurrentMonth = $currentDate->format('m') == $currentMonth;
                        $isToday = $currentDate->format('Y-m-d') == date('Y-m-d');
                        
                        $classes = 'calendar-day';
                        if (!$isCurrentMonth) $classes .= ' other-month';
                        if ($isToday) $classes .= ' today';
                        
                        echo "<div class='$classes'>";
                        echo "<span class='calendar-day-number'>" . $currentDate->format('j') . "</span>";
                        
                        // Zobrazení dummy událostí
                        foreach ($dummyEvents as $event) {
                            $eventDate = new DateTime($event['start']);
                            if ($eventDate->format('Y-m-d') === $currentDate->format('Y-m-d')) {
                                echo "<div class='calendar-event' style='background-color: {$event['color']}' data-event-id='{$event['id']}'>";
                                echo htmlspecialchars($event['title']);
                                echo "</div>";
                            }
                        }
                        
                        echo "</div>";
                    }
                    ?>
                </div>

                <div id="weekView" class="week-view">
                    <!-- Week view content will be dynamically generated by JavaScript -->
                </div>

                <div id="dayView" class="day-view">
                    <!-- Day view content will be dynamically generated by JavaScript -->
                </div>
            </div>

            <!-- Modal pro události -->
            <div id="eventModal" class="modal">
                <div class="modal-content">
                    <span class="close-button">&times;</span>
                    <h3 class="text-2xl font-bold mb-4">Detail události</h3>
                    <form id="eventForm">
                        <input type="hidden" id="eventId">
                        <div class="mb-4">
                            <label class="block text-sm font-medium mb-1" for="eventTitle">Název události</label>
                            <input type="text" id="eventTitle" class="w-full p-2 border rounded focus:border-dentibot-primary focus:ring focus:ring-dentibot-primary focus:ring-opacity-50">
                        </div>
                        <div class="mb-4">
                            <label class="block text-sm font-medium mb-1" for="eventType">Typ události</label>
                            <select id="eventType" class="w-full p-2 border rounded focus:border-dentibot-primary focus:ring focus:ring-dentibot-primary focus:ring-opacity-50">
                                <option value="kontrola" style="background-color: #10B981; color: white;">Kontrola</option>
                                <option value="osetreni" style="background-color: #3B82F6; color: white;">Ošetření</option>
                                <option value="konzultace" style="background-color: #8B5CF6; color: white;">Konzultace</option>
                                <option value="operace" style="background-color: #EC4899; color: white;">Operace</option>
                                <option value="vlastni">Vlastní</option>
                            </select>
                        </div>
                        <div id="customColorContainer" class="mb-4" style="display: none;">
                            <label class="block text-sm font-medium mb-1" for="eventColor">Barva události</label>
                            <div class="flex items-center space-x-2">
                                <input type="color" id="eventColor" class="w-12 h-12 rounded-full border-2 border-gray-300 cursor-pointer">
                                <span id="selectedColorHex" class="text-sm"></span>
                            </div>
                        </div>
                        <div class="mb-4">
                            <label class="block text-sm font-medium mb-1" for="eventStart">Datum a čas začátku</label>
                            <input type="datetime-local" id="eventStart" class="w-full p-2 border rounded focus:border-dentibot-primary focus:ring focus:ring-dentibot-primary focus:ring-opacity-50">
                        </div>
                        <div class="mb-4">
                            <label class="block text-sm font-medium mb-1" for="eventEnd">Datum a čas konce</label>
                            <input type="datetime-local" id="eventEnd" class="w-full p-2 border rounded focus:border-dentibot-primary focus:ring focus:ring-dentibot-primary focus:ring-opacity-50">
                        </div>
                        <div class="mb-4">
                            <label class="block text-sm font-medium mb-1" for="eventNotes">Poznámky</label>
                            <textarea id="eventNotes" class="w-full p-2 border rounded focus:border-dentibot-primary focus:ring focus:ring-dentibot-primary focus:ring-opacity-50"></textarea>
                        </div>
                        <div class="flex justify-end gap-2">
                            <button type="button" id="deleteEvent" class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-50">Smazat</button>
                            <button type="submit" class="px-4 py-2 bg-dentibot-primary text-white rounded hover:bg-dentibot-primary-dark focus:outline-none focus:ring-2 focus:ring-dentibot-primary focus:ring-opacity-50">Uložit</button>
                        </div>
                    </form>
                </div>
            </div>
        </main>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const viewButtons = document.querySelectorAll('.view-button[data-view]');
            const modal = document.getElementById('eventModal');
            const closeButton = modal.querySelector('.close-button');
            const eventForm = document.getElementById('eventForm');
            const monthView = document.getElementById('monthView');
            const weekView = document.getElementById('weekView');
            const dayView = document.getElementById('dayView');
            const currentViewDateElement = document.getElementById('currentViewDate');
            const eventTypeSelect = document.getElementById('eventType');
            const customColorContainer = document.getElementById('customColorContainer');

            // Načtení událostí z PHP
            const events = <?php echo $eventsJson; ?>;

            let currentView = '<?php echo $currentView; ?>';
            let currentDate = new Date(<?php echo $currentYear; ?>, <?php echo $currentMonth - 1; ?>, <?php echo $currentDay; ?>);

            const eventColors = {
                kontrola: '#10B981',
                osetreni: '#3B82F6',
                konzultace: '#8B5CF6',
                operace: '#EC4899'
            };

            function updateURL(date, view) {
                const params = new URLSearchParams({
                    year: date.getFullYear(),
                    month: date.getMonth() + 1,
                    day: date.getDate(),
                    view: view
                });
                window.history.pushState({}, '', `?${params.toString()}`);
            }

            function updateCalendar() {
                updateCurrentViewDate();
                updateURL(currentDate, currentView);

                monthView.style.display = 'none';
                weekView.style.display = 'none';
                dayView.style.display = 'none';

                switch(currentView) {
                    case 'month':
                        monthView.style.display = 'grid';
                        renderMonthView();
                        break;
                    case 'week':
                        weekView.style.display = 'block';
                        renderWeekView();
                        break;
                    case 'day':
                        dayView.style.display = 'block';
                        renderDayView();
                        break;
                }
            }

            function updateCurrentViewDate() {
                let options;
                switch(currentView) {
                    case 'month':
                        options = { year: 'numeric', month: 'long' };
                        break;
                    case 'week':
                        const weekStart = getWeekStart(currentDate);
                        const weekEnd = new Date(weekStart);
                        weekEnd.setDate(weekStart.getDate() + 6);
                        currentViewDateElement.textContent = 
                            `${weekStart.toLocaleDateString('cs-CZ', { day: 'numeric', month: 'long' })} - 
                             ${weekEnd.toLocaleDateString('cs-CZ', { day: 'numeric', month: 'long', year: 'numeric' })}`;
                        return;
                    case 'day':
                        options = { year: 'numeric', month: 'long', day: 'numeric', weekday: 'long' };
                        break;
                }
                currentViewDateElement.textContent = currentDate.toLocaleDateString('cs-CZ', options);
            }

            function getWeekStart(date) {
                const weekStart = new Date(date);
                const day = weekStart.getDay();
                const diff = weekStart.getDate() - day + (day === 0 ? -6 : 1);
                weekStart.setDate(diff);
                return weekStart;
            }

            function renderMonthView() {
                const year = currentDate.getFullYear();
                const month = currentDate.getMonth();
                const firstDay = new Date(year, month, 1);
                const lastDay = new Date(year, month + 1, 0);
                
                let date = new Date(firstDay);
                date.setDate(date.getDate() - (date.getDay() === 0 ? 6 : date.getDay() - 1));

                let monthHtml = '';
                for (let i = 0; i < 42; i++) {
                    const isCurrentMonth = date.getMonth() === month;
                    const isToday = date.toDateString() === new Date().toDateString();
                    const dateString = date.toISOString().split('T')[0];

                    monthHtml += `
                        <div class="calendar-day${isCurrentMonth ? '' : ' other-month'}${isToday ? ' today' : ''}" data-date="${dateString}">
                            <span class="calendar-day-number">${date.getDate()}</span>
                            ${renderEventsForDay(date)}
                        </div>
                    `;

                    date.setDate(date.getDate() + 1);
                }

                monthView.innerHTML = `
                    <div class="calendar-weekday">Po</div>
                    <div class="calendar-weekday">Út</div>
                    <div class="calendar-weekday">St</div>
                    <div class="calendar-weekday">Čt</div>
                    <div class="calendar-weekday">Pá</div>
                    <div class="calendar-weekday">So</div>
                    <div class="calendar-weekday">Ne</div>
                    ${monthHtml}
                `;
            }

            function renderWeekView() {
                const weekStart = getWeekStart(currentDate);
                let weekHtml = `
                    <div class="week-time-grid">
                        <div class="week-day-header"></div>
                        ${Array.from({ length: 7 }, (_, i) => {
                            const date = new Date(weekStart);
                            date.setDate(date.getDate() + i);
                            return `
                                <div class="week-day-header bg-dentibot-surface p-2 text-center">
                                    <div class="font-semibold">${date.toLocaleDateString('cs-CZ', { weekday: 'short' })}</div>
                                    <div class="text-sm">${date.getDate()}. ${date.getMonth() + 1}.</div>
                                </div>
                            `;
                        }).join('')}
                `;

                for (let hour = 0; hour < 24; hour++) {
                    weekHtml += `
                        <div class="time-label bg-dentibot-surface p-2 text-right text-sm">
                            ${hour.toString().padStart(2, '0')}:00
                        </div>
                        ${Array.from({ length: 7 }, (_, day) => {
                            const date = new Date(weekStart);
                            date.setDate(date.getDate() + day);
                            return `
                                <div class="time-slot bg-dentibot-surface relative" 
                                     data-date="${date.toISOString().split('T')[0]}"
                                     data-hour="${hour}">
                                    ${renderEventsForTimeSlot(date, hour)}
                                </div>
                            `;
                        }).join('')}
                    `;
                }

                weekHtml += '</div>';
                weekView.innerHTML = weekHtml;
            }

            function renderDayView() {
                let dayHtml = `
                    <div class="day-time-grid">
                        ${Array.from({ length: 24 }, (_, hour) => `
                            <div class="time-slot grid grid-cols-[60px_1fr] bg-dentibot-surface">
                                <div class="time-label p-2 text-right text-sm">
                                    ${hour.toString().padStart(2, '0')}:00
                                </div>
                                <div class="day-events relative" 
                                     data-date="${currentDate.toISOString().split('T')[0]}"
                                     data-hour="${hour}">
                                    ${renderEventsForTimeSlot(currentDate, hour)}
                                </div>
                            </div>
                        `).join('')}
                    </div>
                `;

                dayView.innerHTML = dayHtml;
            }

            function renderEventsForDay(date) {
                return events
                    .filter(event => {
                        const eventDate = new Date(event.start);
                        return eventDate.toDateString() === date.toDateString();
                    })
                    .map(event => `
                        <div class="calendar-event" style="background-color: ${event.color}" data-event-id="${event.id}">
                            ${event.title}
                        </div>
                    `)
                    .join('');
            }

            function renderEventsForTimeSlot(date, hour) {
                return events
                    .filter(event => {
                        const eventStart = new Date(event.start);
                        const eventEnd = new Date(event.end);
                        return eventStart.toDateString() === date.toDateString() 
                            && eventStart.getHours() <= hour 
                            && eventEnd.getHours() > hour;
                    })
                    .map(event => {
                        const eventStart = new Date(event.start);
                        const eventEnd = new Date(event.end);
                        const top = eventStart.getHours() === hour ? (eventStart.getMinutes() / 60) * 100 : 0;
                        const height = Math.min(100, ((eventEnd - Math.max(eventStart, new Date(date.setHours(hour, 0, 0, 0)))) / 3600000) * 100);
                
                        return `
                            <div class="week-event" 
                                 style="top: ${top}%; height: ${height}%; background-color: ${event.color}"
                                 data-event-id="${event.id}">
                                ${event.title}
                            </div>
                        `;
                    })
                    .join('');
            }

            // Přepínání pohledů
            viewButtons.forEach(button => {
                button.addEventListener('click', function() {
                    viewButtons.forEach(btn => btn.classList.remove('active'));
                    this.classList.add('active');
                    currentView = this.dataset.view;
                    updateCalendar();
                });
            });

            // Navigace v kalendáři
            document.getElementById('prevButton').addEventListener('click', () => {
                switch(currentView) {
                    case 'month':
                        currentDate.setMonth(currentDate.getMonth() - 1);
                        break;
                    case 'week':
                        currentDate.setDate(currentDate.getDate() - 7);
                        break;
                    case 'day':
                        currentDate.setDate(currentDate.getDate() - 1);
                        break;
                }
                updateCalendar();
            });

            document.getElementById('nextButton').addEventListener('click', () => {
                switch(currentView) {
                    case 'month':
                        currentDate.setMonth(currentDate.getMonth() + 1);
                        break;
                    case 'week':
                        currentDate.setDate(currentDate.getDate()+ 7);
                        break;
                    case 'day':
                        currentDate.setDate(currentDate.getDate() + 1);
                        break;
                }
                updateCalendar();
            });

            document.getElementById('todayButton').addEventListener('click', () => {
                currentDate = new Date();
                updateCalendar();
            });

            // Správa modálního okna
            function openModal(eventId = null) {
                modal.style.display = 'block';
                if (eventId) {
                    const event = events.find(e => e.id == eventId);
                    if (event) {
                        document.getElementById('eventId').value = event.id;
                        document.getElementById('eventTitle').value = event.title;
                        document.getElementById('eventType').value = event.type;
                        document.getElementById('eventStart').value = event.start.replace(' ', 'T');
                        document.getElementById('eventEnd').value = event.end.replace(' ', 'T');
                        document.getElementById('eventNotes').value = event.notes;
                        if (event.type === 'vlastni') {
                            customColorContainer.style.display = 'block';
                            eventColorInput.value = event.color;
                            selectedColorHex.textContent = event.color.toUpperCase();
                        } else {
                            customColorContainer.style.display = 'none';
                        }
                    }
                } else {
                    eventForm.reset();
                    customColorContainer.style.display = 'none';
                }
            }

            function closeModal() {
                modal.style.display = 'none';
            }

            closeButton.addEventListener('click', closeModal);
            modal.addEventListener('click', (e) => {
                if (e.target === modal) closeModal();
            });

            // Obsluha událostí
            document.addEventListener('click', function(e) {
                if (e.target.classList.contains('calendar-event') || e.target.classList.contains('week-event')) {
                    const eventId = e.target.dataset.eventId;
                    openModal(eventId);
                }
            });

            // Přidání nové události
            document.addEventListener('dblclick', function(e) {
                const timeSlot = e.target.closest('[data-date]');
                if (timeSlot) {
                    const date = timeSlot.dataset.date;
                    const hour = timeSlot.dataset.hour || '00';
                    const defaultStart = new Date(date);
                    defaultStart.setHours(hour, 0, 0, 0);

                    document.getElementById('eventStart').value = defaultStart.toISOString().slice(0, 16);
                    const defaultEnd = new Date(defaultStart);
                    defaultEnd.setHours(defaultStart.getHours() + 1);
                    document.getElementById('eventEnd').value = defaultEnd.toISOString().slice(0, 16);

                    openModal();
                }
            });

            // Zpracování formuláře
            eventForm.addEventListener('submit', function(e) {
                e.preventDefault();
                const eventId = document.getElementById('eventId').value;
                const eventData = {
                    id: eventId ? parseInt(eventId) : Date.now(),
                    title: document.getElementById('eventTitle').value,
                    type: document.getElementById('eventType').value,
                    start: document.getElementById('eventStart').value,
                    end: document.getElementById('eventEnd').value,
                    notes: document.getElementById('eventNotes').value,
                    color: document.getElementById('eventType').value === 'vlastni' 
                        ? document.getElementById('eventColor').value 
                        : eventColors[document.getElementById('eventType').value]
                };

                if (eventId) {
                    const index = events.findIndex(e => e.id == eventId);
                    if (index !== -1) {
                        events[index] = eventData;
                    }
                } else {
                    events.push(eventData);
                }

                closeModal();
                updateCalendar();
            });

            // Smazání události
            document.getElementById('deleteEvent').addEventListener('click', function() {
                const eventId = document.getElementById('eventId').value;
                if (eventId && confirm('Opravdu chcete smazat tuto událost?')) {
                    const index = events.findIndex(e => e.id == eventId);
                    if (index !== -1) {
                        events.splice(index, 1);
                    }
                    closeModal();
                    updateCalendar();
                }
            });

            // Zobrazení/skrytí výběru barvy pro vlastní události a nastavení barvy pro přednastavené typy
            eventTypeSelect.addEventListener('change', function() {
                const selectedType = this.value;
                if (selectedType === 'vlastni') {
                    customColorContainer.style.display = 'block';
                    selectedColorHex.textContent = eventColorInput.value.toUpperCase();
                } else {
                    customColorContainer.style.display = 'none';
                    const eventColor = eventColors[selectedType];
                    eventColorInput.value = eventColor;
                    selectedColorHex.textContent = eventColor.toUpperCase();
                }
            });

            const eventColorInput = document.getElementById('eventColor');
            const selectedColorHex = document.getElementById('selectedColorHex');

            eventColorInput.addEventListener('input', function() {
                selectedColorHex.textContent = this.value.toUpperCase();
            });


            // Inicializace kalendáře
            updateCalendar();
        });
    </script>
</body>
</html>


<?php
require_once 'config.php';
require_once 'api_functions.php';
require_once 'error_log.php';

// Ensure session is started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Kontrola přihlášení
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['error' => 'Unauthorized access']);
    exit;
}

// Get user's current subscription plan
$user_id = $_SESSION['user_id'];
$current_plan = getUserSubscriptionPlan($user_id);

$planUrls = [
    'Základní' => [
        'monthly' => 'https://buy.stripe.com/9AQ3eYg8Q4MrgCI3ce',
        'yearly' => 'https://buy.stripe.com/4gw5n67Ck6UzaekeUX'
    ],
    'Pokročilý' => [
        'monthly' => 'https://buy.stripe.com/fZe3eY3m45Qv2LS8wA',
        'yearly' => 'https://buy.stripe.com/aEUbLu0a45Qv8c85km'
    ],
    'Enterprise' => [
        'contact' => 'https://tknurture.com/inteligentni-voicebot-pro-zubare-dentibot'
    ]
];

$plans = [
    'Základní' => [
        'price' => ['monthly' => '3000 Kč', 'yearly' => '30000 Kč'],
        'features' => ['Až 500 minut hovorů měsíčně', 'E-mailová podpora', 'Základní funkce Dentibota']
    ],
    'Pokročilý' => [
        'price' => ['monthly' => '5000 Kč', 'yearly' => '54000 Kč'],
        'features' => ['Až 1000 minut hovorů měsíčně', 'Podpora v rámci Discord skupiny', 'Pokročilé funkce Dentibota', 'Prioritní zpracování']
    ],
    'Enterprise' => [
        'price' => ['contact' => 'Individuální cena'],
        'features' => ['Neomezené minuty hovorů', 'Dedikovaná podpora', 'Vlastní integrace', 'SLA']
    ]
];

// Default to monthly
$interval = isset($_GET['interval']) && $_GET['interval'] === 'yearly' ? 'yearly' : 'monthly';
?>

<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Předplatné - Dentibot</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
</head>
<body class="bg-gray-900 text-white">
    <div id="subscriptionPopup" class="p-6">
        <div class="flex justify-between items-center mb-6">
            <h2 class="text-2xl font-bold">Vyberte si plán předplatného</h2>
        </div>

        <div class="mb-8 flex justify-center">
            <div class="inline-flex rounded-md shadow-sm bg-gray-800" role="group">
                <button onclick="changeInterval('monthly')" id="monthlyButton" class="px-4 py-2 text-sm font-medium rounded-l-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 <?php echo $interval === 'monthly' ? 'bg-blue-600 text-white' : 'text-gray-400'; ?>">
                    Měsíční
                </button>
                <button onclick="changeInterval('yearly')" id="yearlyButton" class="px-4 py-2 text-sm font-medium rounded-r-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 <?php echo $interval === 'yearly' ? 'bg-blue-600 text-white' : 'text-gray-400'; ?>">
                    Roční
                </button>
            </div>
        </div>

        <div class="grid md:grid-cols-3 gap-6">
            <?php foreach ($plans as $planName => $planDetails): ?>
            <div class="bg-gray-800 rounded-lg overflow-hidden plan-card <?php echo $planName === 'Pokročilý' ? 'border-2 border-yellow-500' : ''; ?>">
                <div class="p-6">
                    <h3 class="text-xl font-semibold text-center mb-4"><?php echo htmlspecialchars($planName); ?></h3>
                    <div class="text-center mb-6">
                        <?php if ($planName !== 'Enterprise'): ?>
                            <p class="text-3xl font-bold price-display" 
                               data-monthly="<?php echo htmlspecialchars($planDetails['price']['monthly']); ?>"
                               data-yearly="<?php echo htmlspecialchars($planDetails['price']['yearly']); ?>">
                                <?php echo htmlspecialchars($planDetails['price'][$interval]); ?>
                            </p>
                            <p class="text-gray-400 interval-text">/ <?php echo $interval === 'monthly' ? 'měsíc' : 'rok'; ?></p>
                        <?php else: ?>
                            <p class="text-3xl font-bold"><?php echo htmlspecialchars($planDetails['price']['contact']); ?></p>
                        <?php endif; ?>
                    </div>
                    <ul class="mb-6 space-y-2">
                        <?php foreach ($planDetails['features'] as $feature): ?>
                            <li class="flex items-center">
                                <svg class="w-4 h-4 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <?php echo htmlspecialchars($feature); ?>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                <div class="px-6 pb-6">
                    <?php if ($planName !== 'Enterprise'): ?>
                        <a href="<?php echo htmlspecialchars($planUrls[$planName][$interval]); ?>" 
                           class="plan-url block w-full text-center <?php echo $planName === 'Pokročilý' ? 'bg-yellow-500 hover:bg-yellow-600' : 'bg-blue-500 hover:bg-blue-600'; ?> text-white py-2 px-4 rounded-full transition duration-200"
                           data-monthly="<?php echo htmlspecialchars($planUrls[$planName]['monthly']); ?>"
                           data-yearly="<?php echo htmlspecialchars($planUrls[$planName]['yearly']); ?>"
                           onclick="selectPlan('<?php echo htmlspecialchars($planName); ?>', '<?php echo $interval; ?>')">
                            Vybrat plán
                        </a>
                    <?php else: ?>
                        <a href="<?php echo htmlspecialchars($planUrls[$planName]['contact']); ?>" 
                           class="block w-full text-center bg-purple-500 text-white py-2 px-4 rounded-full hover:bg-purple-600 transition duration-200"
                           onclick="selectPlan('<?php echo htmlspecialchars($planName); ?>', 'contact')">
                            Kontaktujte nás
                        </a>
                    <?php endif; ?>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        
        <div class="mt-8 text-center">
            <p class="text-lg">Vaše aktuální předplatné: <strong class="text-blue-400"><?php echo htmlspecialchars(ucfirst($current_plan)); ?></strong></p>
        </div>
    </div>

    <script>
    let currentInterval = '<?php echo $interval; ?>';

    function changeInterval(newInterval) {
        if (currentInterval === newInterval) return;

        currentInterval = newInterval;
        
        // Update button states
        document.getElementById('monthlyButton').classList.toggle('bg-blue-600', newInterval === 'monthly');
        document.getElementById('monthlyButton').classList.toggle('text-white', newInterval === 'monthly');
        document.getElementById('monthlyButton').classList.toggle('text-gray-400', newInterval !== 'monthly');
        document.getElementById('yearlyButton').classList.toggle('bg-blue-600', newInterval === 'yearly');
        document.getElementById('yearlyButton').classList.toggle('text-white', newInterval === 'yearly');
        document.getElementById('yearlyButton').classList.toggle('text-gray-400', newInterval !== 'yearly');

        // Update prices and interval text
        document.querySelectorAll('.price-display').forEach(elem => {
            elem.textContent = elem.dataset[newInterval];
        });

        document.querySelectorAll('.interval-text').forEach(elem => {
            elem.textContent = newInterval === 'monthly' ? '/ měsíc' : '/ rok';
        });

        // Update plan URLs
        document.querySelectorAll('.plan-url').forEach(elem => {
            elem.href = elem.dataset[newInterval];
        });
    }

    function selectPlan(planName, interval) {
        // Send message to parent window
        window.parent.postMessage({
            type: 'planSelected',
            plan: planName,
            interval: interval
        }, '*');
        
        // Get the URL for the selected plan
        let url;
        if (planName === 'Enterprise') {
            url = '<?php echo $planUrls['Enterprise']['contact']; ?>';
        } else {
            const planUrls = <?php echo json_encode($planUrls); ?>;
            url = planUrls[planName][interval];
        }
        
        // Redirect to the plan URL
        if (url) {
            window.parent.location.href = url;
        }
    }
    </script>
</body>
</html>

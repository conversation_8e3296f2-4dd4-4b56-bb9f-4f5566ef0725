"use client"

import type React from "react"

import { useState } from "react"
import { usePayment, type PaymentMethod } from "@/components/payment/payment-provider"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { Skeleton } from "@/components/ui/skeleton"
import { Trash2, CheckCircle2, CreditCardIcon, BanknoteIcon as BankIcon } from "lucide-react"

export function PaymentMethods() {
  const { paymentMethods, isLoading, addPaymentMethod, removePaymentMethod, setDefaultPaymentMethod } = usePayment()
  const [addDialogOpen, setAddDialogOpen] = useState(false)
  const [removeDialogOpen, setRemoveDialogOpen] = useState(false)
  const [selectedMethodId, setSelectedMethodId] = useState<string | null>(null)

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-semibold">Platební metody</h2>
          <Skeleton className="h-9 w-32" />
        </div>
        <div className="grid gap-4 md:grid-cols-2">
          <Skeleton className="h-40" />
          <Skeleton className="h-40" />
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">Platební metody</h2>
        <Dialog open={addDialogOpen} onOpenChange={setAddDialogOpen}>
          <DialogTrigger asChild>
            <Button>Přidat platební metodu</Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px]">
            <AddPaymentMethodForm
              onSubmit={(method) => {
                addPaymentMethod(method)
                setAddDialogOpen(false)
              }}
            />
          </DialogContent>
        </Dialog>
      </div>

      {paymentMethods.length === 0 ? (
        <Card>
          <CardHeader>
            <CardTitle>Žádné platební metody</CardTitle>
            <CardDescription>Nemáte přidané žádné platební metody</CardDescription>
          </CardHeader>
          <CardContent>
            <p>Pro využívání služeb Dentibot si prosím přidejte platební metodu.</p>
          </CardContent>
          <CardFooter>
            <Button onClick={() => setAddDialogOpen(true)}>Přidat platební metodu</Button>
          </CardFooter>
        </Card>
      ) : (
        <div className="grid gap-4 md:grid-cols-2">
          {paymentMethods.map((method) => (
            <PaymentMethodCard
              key={method.id}
              method={method}
              onSetDefault={() => setDefaultPaymentMethod(method.id)}
              onRemove={() => {
                setSelectedMethodId(method.id)
                setRemoveDialogOpen(true)
              }}
            />
          ))}
        </div>
      )}

      <AlertDialog open={removeDialogOpen} onOpenChange={setRemoveDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Odstranit platební metodu</AlertDialogTitle>
            <AlertDialogDescription>
              Opravdu chcete odstranit tuto platební metodu? Tuto akci nelze vrátit zpět.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Zrušit</AlertDialogCancel>
            <AlertDialogAction
              onClick={async () => {
                if (selectedMethodId) {
                  await removePaymentMethod(selectedMethodId)
                  setRemoveDialogOpen(false)
                }
              }}
            >
              Odstranit
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}

function PaymentMethodCard({
  method,
  onSetDefault,
  onRemove,
}: {
  method: PaymentMethod
  onSetDefault: () => void
  onRemove: () => void
}) {
  return (
    <Card className={method.isDefault ? "border-primary" : ""}>
      <CardHeader className="flex flex-row items-start justify-between space-y-0">
        <div>
          <CardTitle className="flex items-center">
            {method.type === "card" ? (
              <CreditCardIcon className="mr-2 h-4 w-4" />
            ) : method.type === "paypal" ? (
              <span className="mr-2 font-bold text-blue-600">P</span>
            ) : (
              <BankIcon className="mr-2 h-4 w-4" />
            )}
            {method.type === "card"
              ? `${method.details.brand} •••• ${method.details.last4}`
              : method.type === "paypal"
                ? "PayPal"
                : "Bankovní převod"}
          </CardTitle>
          <CardDescription>
            {method.type === "card"
              ? `Platnost do: ${method.details.expiryMonth}/${method.details.expiryYear}`
              : method.type === "paypal"
                ? method.details.email
                : `${method.details.bankName} •••• ${method.details.accountLast4}`}
          </CardDescription>
        </div>
        {method.isDefault && (
          <span className="inline-flex items-center rounded-full bg-primary/10 px-2 py-1 text-xs font-medium text-primary">
            <CheckCircle2 className="mr-1 h-3 w-3" />
            Výchozí
          </span>
        )}
      </CardHeader>
      <CardFooter className="flex justify-between">
        {!method.isDefault ? (
          <Button variant="outline" size="sm" onClick={onSetDefault}>
            Nastavit jako výchozí
          </Button>
        ) : (
          <span />
        )}
        <Button variant="ghost" size="sm" className="text-destructive" onClick={onRemove}>
          <Trash2 className="mr-2 h-4 w-4" />
          Odstranit
        </Button>
      </CardFooter>
    </Card>
  )
}

function AddPaymentMethodForm({ onSubmit }: { onSubmit: (method: Omit<PaymentMethod, "id">) => void }) {
  const [paymentType, setPaymentType] = useState<"card" | "paypal" | "bank_transfer">("card")
  const [isDefault, setIsDefault] = useState(false)

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    const formData = new FormData(e.target as HTMLFormElement)

    if (paymentType === "card") {
      onSubmit({
        type: "card",
        details: {
          last4: formData.get("cardNumber")?.toString().slice(-4) || "0000",
          brand: getCardBrand(formData.get("cardNumber")?.toString() || ""),
          expiryMonth: formData.get("expiryMonth")?.toString() || "",
          expiryYear: formData.get("expiryYear")?.toString() || "",
        },
        isDefault,
      })
    } else if (paymentType === "paypal") {
      onSubmit({
        type: "paypal",
        details: {
          email: formData.get("paypalEmail")?.toString() || "",
        },
        isDefault,
      })
    } else {
      onSubmit({
        type: "bank_transfer",
        details: {
          bankName: formData.get("bankName")?.toString() || "",
          accountLast4: formData.get("accountNumber")?.toString().slice(-4) || "0000",
        },
        isDefault,
      })
    }
  }

  // Simple function to determine card brand based on first digit
  const getCardBrand = (cardNumber: string): string => {
    const firstDigit = cardNumber.charAt(0)
    switch (firstDigit) {
      case "4":
        return "Visa"
      case "5":
        return "Mastercard"
      case "3":
        return "Amex"
      default:
        return "Unknown"
    }
  }

  return (
    <form onSubmit={handleSubmit}>
      <DialogHeader>
        <DialogTitle>Přidat platební metodu</DialogTitle>
        <DialogDescription>Přidejte novou platební metodu pro platby v systému Dentibot.</DialogDescription>
      </DialogHeader>

      <div className="py-4">
        <Tabs defaultValue="card" onValueChange={(value) => setPaymentType(value as any)}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="card">Karta</TabsTrigger>
            <TabsTrigger value="paypal">PayPal</TabsTrigger>
            <TabsTrigger value="bank_transfer">Bankovní převod</TabsTrigger>
          </TabsList>

          <TabsContent value="card" className="space-y-4 mt-4">
            <div className="space-y-2">
              <Label htmlFor="cardNumber">Číslo karty</Label>
              <Input id="cardNumber" name="cardNumber" placeholder="1234 5678 9012 3456" required />
            </div>
            <div className="grid grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="expiryMonth">Měsíc</Label>
                <Input id="expiryMonth" name="expiryMonth" placeholder="MM" maxLength={2} required />
              </div>
              <div className="space-y-2">
                <Label htmlFor="expiryYear">Rok</Label>
                <Input id="expiryYear" name="expiryYear" placeholder="YY" maxLength={2} required />
              </div>
              <div className="space-y-2">
                <Label htmlFor="cvc">CVC</Label>
                <Input id="cvc" name="cvc" placeholder="123" maxLength={4} required />
              </div>
            </div>
          </TabsContent>

          <TabsContent value="paypal" className="space-y-4 mt-4">
            <div className="space-y-2">
              <Label htmlFor="paypalEmail">E-mail PayPal účtu</Label>
              <Input id="paypalEmail" name="paypalEmail" type="email" placeholder="<EMAIL>" required />
            </div>
          </TabsContent>

          <TabsContent value="bank_transfer" className="space-y-4 mt-4">
            <div className="space-y-2">
              <Label htmlFor="bankName">Název banky</Label>
              <Input id="bankName" name="bankName" placeholder="Česká spořitelna" required />
            </div>
            <div className="space-y-2">
              <Label htmlFor="accountNumber">Číslo účtu</Label>
              <Input id="accountNumber" name="accountNumber" placeholder="**********/0800" required />
            </div>
          </TabsContent>
        </Tabs>

        <div className="flex items-center space-x-2 mt-4">
          <input
            type="checkbox"
            id="isDefault"
            checked={isDefault}
            onChange={(e) => setIsDefault(e.target.checked)}
            className="rounded border-gray-300"
          />
          <Label htmlFor="isDefault">Nastavit jako výchozí platební metodu</Label>
        </div>
      </div>

      <DialogFooter>
        <Button type="submit">Přidat platební metodu</Button>
      </DialogFooter>
    </form>
  )
}


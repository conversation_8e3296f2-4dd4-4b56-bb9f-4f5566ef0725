.message-widget-container {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 9999;
  font-family: "Arial", sans-serif;
}

.message-widget-button {
  display: flex;
  align-items: center;
  padding: 0 16px 0 12px;
  height: 48px;
  border-radius: 8px;
  background-color: #26c9c3;
  color: white;
  cursor: pointer;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  position: relative;
}

.message-widget-button:hover {
  background-color: #1fb5af;
  transform: translateY(-2px);
}

.message-widget-button-icon {
  margin-right: 8px;
}

.message-widget-button-text {
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
}

.message-widget-button .unread-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: #e74c3c;
  color: white;
  border-radius: 50%;
  width: 22px;
  height: 22px;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

.message-widget-panel {
  position: absolute;
  bottom: 60px;
  right: 0;
  width: 480px; /* Zvět<PERSON>eno z 320px */
  max-height: 600px; /* Zvětšeno z 400px */
  background-color: #121b27;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.4);
  overflow: hidden;
  display: none;
  flex-direction: column;
  transition: all 0.3s ease;
  transform-origin: bottom right;
  color: #fff;
  border: 1px solid #2d3a4a;
}

.message-widget-panel.active {
  display: flex;
  animation: scaleUp 0.2s forwards;
}

@keyframes scaleUp {
  0% {
    transform: scale(0.8);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.message-widget-header {
  padding: 15px;
  background-color: #121b27;
  color: #26c9c3;
  font-weight: bold;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #2d3a4a;
}

.message-widget-actions {
  display: flex;
  align-items: center;
}

.message-widget-mark-all-read {
  font-size: 12px;
  color: #8a9aac;
  cursor: pointer;
  margin-right: 15px;
  transition: color 0.2s;
}

.message-widget-mark-all-read:hover {
  color: #26c9c3;
}

.message-widget-refresh {
  color: #8a9aac;
  transition: color 0.2s transform 0.2s;
}

.message-widget-refresh:hover {
  color: #26c9c3;
  transform: rotate(30deg);
}

.message-widget-close {
  cursor: pointer;
  font-size: 18px;
  color: #8a9aac;
}

.message-widget-close:hover {
  color: #26c9c3;
}

.message-widget-content {
  flex: 1;
  overflow-y: auto;
  padding: 0;
  background-color: #1e2a38;
}

.message-widget-empty {
  padding: 20px;
  text-align: center;
  color: #8a9aac;
}

.message-item {
  padding: 18px; /* Zvětšeno z 15px */
  border-bottom: 1px solid #2d3a4a;
  transition: background-color 0.2s;
}

.message-item:last-child {
  border-bottom: none;
}

.message-item:hover {
  background-color: #263545;
}

.message-item.unread {
  background-color: rgba(38, 201, 195, 0.05);
  border-left: 3px solid #26c9c3;
}

.message-item-header {
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  width: 100%;
}

.message-item-summary {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
}

.message-item-name {
  font-weight: bold;
  color: #26c9c3;
  font-size: 16px; /* Zvětšeno z výchozí hodnoty */
}

.message-item-preview {
  color: #8a9aac;
  font-size: 14px; /* Zvětšeno z 13px */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-top: 5px; /* Zvětšeno z 3px */
}

.message-item-phone {
  color: #8a9aac;
  font-size: 13px;
}

.message-item-phone-detail {
  margin-bottom: 8px;
  color: #8a9aac;
}

.message-item-content {
  margin-bottom: 10px; /* Zvětšeno z 8px */
  line-height: 1.5; /* Zvětšeno z 1.4 */
  font-size: 15px; /* Přidáno pro lepší čitelnost */
}

.message-time {
  font-size: 13px; /* Zvětšeno z 12px */
  color: #8a9aac;
  white-space: nowrap;
  margin-left: 10px;
}

.message-item-details {
  padding-top: 12px; /* Zvětšeno z 10px */
  margin-top: 12px; /* Zvětšeno z 10px */
  border-top: 1px solid #2d3a4a;
  font-size: 15px; /* Zvětšeno z 14px */
  line-height: 1.6; /* Zvětšeno z 1.5 */
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease-out, padding 0.3s ease-out, margin 0.3s ease-out;
  padding-top: 0;
  margin-top: 0;
}

.message-item-details[style*="display: block"] {
  max-height: 500px;
  padding-top: 12px;
  margin-top: 12px;
}

.message-widget-footer {
  padding: 10px;
  text-align: center;
  border-top: 1px solid #2d3a4a;
  font-size: 12px;
  color: #8a9aac;
  background-color: #121b27;
}

/* Scrollbar styling */
.message-widget-content::-webkit-scrollbar {
  width: 6px;
}

.message-widget-content::-webkit-scrollbar-track {
  background: #1e2a38;
}

.message-widget-content::-webkit-scrollbar-thumb {
  background-color: #2d3a4a;
  border-radius: 3px;
}

.message-widget-content::-webkit-scrollbar-thumb:hover {
  background-color: #3a4a5a;
}

/* Responsive styles */
@media (max-width: 768px) {
  .message-widget-button {
    width: 48px;
    height: 48px;
    padding: 0;
    justify-content: center;
  }

  .message-widget-button-text {
    display: none;
  }

  .message-widget-button-icon {
    margin-right: 0;
  }

  .message-widget-panel {
    width: 90%; /* Použijeme procenta místo pevné šířky */
    max-width: 480px; /* Maximální šířka */
    max-height: 80vh; /* Maximální výška jako procento výšky viewportu */
    left: 5%; /* Centrování */
    right: 5%; /* Centrování */
  }

  .message-widget-mark-all-read {
    font-size: 11px;
    margin-right: 10px;
  }
}

/* Animace pro rozbalení detailů */
@keyframes expandDetails {
  from {
    max-height: 0;
    opacity: 0;
  }
  to {
    max-height: 500px;
    opacity: 1;
  }
}

@keyframes collapseDetails {
  from {
    max-height: 500px;
    opacity: 1;
  }
  to {
    max-height: 0;
    opacity: 0;
  }
}

/* Vylepšení pro dotykové obrazovky */
@media (hover: none) {
  .message-item:hover {
    background-color: inherit;
  }

  .message-item.unread:hover {
    background-color: rgba(38, 201, 195, 0.05);
  }

  .message-item-header {
    padding: 5px 0;
  }
}

/* Vylepšení pro tmavý režim */
@media (prefers-color-scheme: dark) {
  .message-widget-panel {
    background-color: #121b27;
    border-color: #2d3a4a;
  }

  .message-widget-content {
    background-color: #1e2a38;
  }

  .message-item:hover {
    background-color: #263545;
  }
}

/* Vylepšení pro vysoký kontrast */
@media (forced-colors: active) {
  .message-widget-button {
    border: 2px solid ButtonText;
  }

  .message-widget-panel {
    border: 2px solid ButtonText;
  }

  .message-item.unread {
    border-left: 3px solid Highlight;
  }
}

/* Vylepšení pro tisk */
@media print {
  .message-widget-container {
    display: none !important;
  }
}

/* Animace pro notifikační badge */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

.message-widget-button .unread-badge {
  animation: pulse 2s infinite;
}

/* Vylepšení pro přístupnost */
.message-widget-mark-all-read:focus,
.message-widget-close:focus,
.message-widget-refresh:focus,
.message-item-header:focus {
  outline: 2px solid #26c9c3;
  outline-offset: 2px;
}

.message-widget-button:focus {
  outline: 2px solid white;
  outline-offset: 2px;
}

/* Přidání hover efektu pro lepší UX */
.message-widget-mark-all-read:hover,
.message-widget-close:hover,
.message-widget-refresh:hover {
  opacity: 0.8;
}


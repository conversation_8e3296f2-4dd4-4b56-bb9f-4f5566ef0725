import { generateLocalBusinessSchema, generateFAQSchema } from "@/seo/schema"

interface StructuredDataProps {
  type: "business" | "faq"
  data?: any
}

export default function StructuredData({ type, data }: StructuredDataProps) {
  let schema = {}

  if (type === "business") {
    schema = generateLocalBusinessSchema()
  } else if (type === "faq") {
    schema = generateFAQSchema(data || [])
  }

  return <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(schema) }} />
}


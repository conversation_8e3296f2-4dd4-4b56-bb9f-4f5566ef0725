<?php
/**
 * CGM Časová razítka - Databázová migrace
 * Skript pro vytvoření tabulek pro CGM modul
 */

require_once 'config.php';

echo "<h1>CGM Časová razítka - Databázová migrace</h1>\n";

try {
    $db = getDbConnection();
    
    // Kontrola existence tabulek
    $tables_to_check = [
        'document_categories',
        'medical_documents', 
        'document_timestamps',
        'document_signatures',
        'document_audit_log'
    ];
    
    echo "<h2>Kontrola existujících tabulek:</h2>\n";
    foreach ($tables_to_check as $table) {
        $result = $db->query("SHOW TABLES LIKE '$table'");
        if ($result->num_rows > 0) {
            echo "✅ Tabulka $table již existuje<br>\n";
        } else {
            echo "❌ Tabulka $table neexistuje<br>\n";
        }
    }
    
    echo "<h2>Vytváření tabulek:</h2>\n";
    
    // Vytvoření tabulky kategorií dokumentů
    $sql_categories = "
        CREATE TABLE IF NOT EXISTS document_categories (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    if ($db->query($sql_categories)) {
        echo "✅ Tabulka document_categories vytvořena<br>\n";
    } else {
        echo "❌ Chyba při vytváření tabulky document_categories: " . $db->error . "<br>\n";
    }
    
    // Vytvoření tabulky dokumentů
    $sql_documents = "
        CREATE TABLE IF NOT EXISTS medical_documents (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            patient_id INT,
            category_id INT,
            title VARCHAR(255) NOT NULL,
            content LONGTEXT,
            file_path VARCHAR(500),
            file_type VARCHAR(50),
            file_size INT,
            document_hash VARCHAR(64) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id),
            FOREIGN KEY (category_id) REFERENCES document_categories(id),
            INDEX idx_user_id (user_id),
            INDEX idx_patient_id (patient_id),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    if ($db->query($sql_documents)) {
        echo "✅ Tabulka medical_documents vytvořena<br>\n";
    } else {
        echo "❌ Chyba při vytváření tabulky medical_documents: " . $db->error . "<br>\n";
    }
    
    // Vytvoření tabulky časových razítek
    $sql_timestamps = "
        CREATE TABLE IF NOT EXISTS document_timestamps (
            id INT AUTO_INCREMENT PRIMARY KEY,
            document_id INT NOT NULL,
            timestamp_data LONGTEXT NOT NULL,
            timestamp_hash VARCHAR(64) NOT NULL,
            tsa_url VARCHAR(255),
            certificate_info TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            expires_at TIMESTAMP,
            is_valid BOOLEAN DEFAULT TRUE,
            FOREIGN KEY (document_id) REFERENCES medical_documents(id) ON DELETE CASCADE,
            INDEX idx_document_id (document_id),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    if ($db->query($sql_timestamps)) {
        echo "✅ Tabulka document_timestamps vytvořena<br>\n";
    } else {
        echo "❌ Chyba při vytváření tabulky document_timestamps: " . $db->error . "<br>\n";
    }
    
    // Vytvoření tabulky podpisů
    $sql_signatures = "
        CREATE TABLE IF NOT EXISTS document_signatures (
            id INT AUTO_INCREMENT PRIMARY KEY,
            document_id INT NOT NULL,
            user_id INT NOT NULL,
            signature_data LONGTEXT NOT NULL,
            signature_hash VARCHAR(64) NOT NULL,
            certificate_data TEXT,
            signature_algorithm VARCHAR(50) DEFAULT 'SHA256withRSA',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            expires_at TIMESTAMP,
            is_valid BOOLEAN DEFAULT TRUE,
            FOREIGN KEY (document_id) REFERENCES medical_documents(id) ON DELETE CASCADE,
            FOREIGN KEY (user_id) REFERENCES users(id),
            INDEX idx_document_id (document_id),
            INDEX idx_user_id (user_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    if ($db->query($sql_signatures)) {
        echo "✅ Tabulka document_signatures vytvořena<br>\n";
    } else {
        echo "❌ Chyba při vytváření tabulky document_signatures: " . $db->error . "<br>\n";
    }
    
    // Vytvoření tabulky audit logu
    $sql_audit = "
        CREATE TABLE IF NOT EXISTS document_audit_log (
            id INT AUTO_INCREMENT PRIMARY KEY,
            document_id INT NOT NULL,
            user_id INT NOT NULL,
            action ENUM('created', 'viewed', 'updated', 'signed', 'timestamped', 'exported', 'deleted') NOT NULL,
            details TEXT,
            ip_address VARCHAR(45),
            user_agent TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (document_id) REFERENCES medical_documents(id) ON DELETE CASCADE,
            FOREIGN KEY (user_id) REFERENCES users(id),
            INDEX idx_document_id (document_id),
            INDEX idx_user_id (user_id),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    if ($db->query($sql_audit)) {
        echo "✅ Tabulka document_audit_log vytvořena<br>\n";
    } else {
        echo "❌ Chyba při vytváření tabulky document_audit_log: " . $db->error . "<br>\n";
    }
    
    echo "<h2>Vkládání základních dat:</h2>\n";
    
    // Kontrola a vložení kategorií
    $result = $db->query("SELECT COUNT(*) as count FROM document_categories");
    $row = $result->fetch_assoc();
    
    if ($row['count'] == 0) {
        $categories = [
            ['Zdravotnická dokumentace', 'Základní zdravotnická dokumentace pacienta'],
            ['Vyšetření', 'Výsledky vyšetření a diagnostiky'],
            ['Terapie', 'Terapeutické plány a postupy'],
            ['Recepty', 'Elektronické recepty a předpisy'],
            ['Zprávy', 'Lékařské zprávy a posudky'],
            ['Preventivní péče', 'Dokumentace preventivních prohlídek'],
            ['Administrativní', 'Administrativní dokumenty a formuláře']
        ];
        
        $stmt = $db->prepare("INSERT INTO document_categories (name, description) VALUES (?, ?)");
        
        foreach ($categories as $category) {
            $stmt->bind_param("ss", $category[0], $category[1]);
            if ($stmt->execute()) {
                echo "✅ Kategorie '{$category[0]}' vložena<br>\n";
            } else {
                echo "❌ Chyba při vkládání kategorie '{$category[0]}': " . $stmt->error . "<br>\n";
            }
        }
        
        $stmt->close();
    } else {
        echo "ℹ️ Kategorie již existují (počet: {$row['count']})<br>\n";
    }
    
    // Vytvoření adresáře pro nahrávání souborů
    $upload_dir = __DIR__ . '/uploads/documents/';
    if (!is_dir($upload_dir)) {
        if (mkdir($upload_dir, 0755, true)) {
            echo "✅ Adresář pro nahrávání vytvořen: $upload_dir<br>\n";
        } else {
            echo "❌ Chyba při vytváření adresáře: $upload_dir<br>\n";
        }
    } else {
        echo "ℹ️ Adresář pro nahrávání již existuje<br>\n";
    }
    
    // Nastavení oprávnění
    if (is_dir($upload_dir)) {
        chmod($upload_dir, 0755);
        echo "✅ Oprávnění adresáře nastavena<br>\n";
    }
    
    echo "<h2>Migrace dokončena!</h2>\n";
    echo "<p><a href='cgm_documents.php'>Přejít na CGM Časová razítka</a></p>\n";
    
} catch (Exception $e) {
    echo "<h2>Chyba při migraci:</h2>\n";
    echo "<p style='color: red;'>" . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>\n";
}
?>

<?php
require_once __DIR__ . '/../config.php';

function removePhoneNumberFromVapiAssistant($userId, $phoneNumber) {
    $vapiApiKey = VAPI_API_KEY;
    $assistantId = VAPI_ASSISTANT_ID;

    $ch = curl_init("https://api.vapi.ai/assistants/{$assistantId}/phone-numbers/{$phoneNumber}");
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "DELETE");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        "Authorization: Bearer {$vapiApiKey}",
        "Content-Type: application/json"
    ]);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($httpCode == 200) {
        $conn = getDbConnection();
        if (!$conn) {
            error_log("Failed to establish database connection in removePhoneNumberFromVapiAssistant");
            return false;
        }

        try {
            $stmt = $conn->prepare("UPDATE users SET vapi_number_active = FALSE WHERE id = ?");
            $stmt->bind_param("i", $userId);
            $result = $stmt->execute();
            $stmt->close();

            if ($result) {
                error_log("Successfully removed phone number from vapi assistant and updated database for user: " . $userId);
                return true;
            } else {
                error_log("Failed to update database after removing phone number from vapi assistant for user: " . $userId);
                return false;
            }
        } catch (Exception $e) {
            error_log("Database error in removePhoneNumberFromVapiAssistant: " . $e->getMessage());
            return false;
        } finally {
            $conn->close();
        }
    } else {
        error_log("Failed to remove phone number from vapi assistant for user: " . $userId . ". HTTP Code: " . $httpCode);
        return false;
    }
}
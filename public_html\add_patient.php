<?php
require_once 'config.php';
require_once 'baserow_functions.php';
require_once 'error_log.php';

// Kontrola přihlášení
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

$error = null;
$success = null;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $patientData = [
            'Jmeno_pacienta' => $_POST['name'] ?? '',
            'Emailova_adresa' => $_POST['email'] ?? '',
            'Telefonni_cislo' => $_POST['phone'] ?? '',
            'Rodne_cislo' => $_POST['birth_number'] ?? '',
            'Datum_prohlidky' => date('Y-m-d'), // Current date as default
            'Stav preventivních prohlídek' => 2324745, // "Aktivní" by default
            'Komunikační preference' => 2324747 // "SMS" by default
        ];

        // Validate data
        if (empty($patientData['Jmeno_pacienta']) || empty($patientData['Emailova_adresa']) || empty($patientData['Telefonni_cislo'])) {
            throw new Exception("Všechna pole jsou povinná");
        }

        // Log the data being sent to Baserow
        writeErrorLog('Patient Data to be Sent', $patientData);

        // Add patient to Baserow
        $config = getBaserowConfig();
        writeErrorLog('Baserow Config', $config);

        $response = baserowRequest('POST', '?user_field_names=true', $patientData);

        writeErrorLog('Baserow Raw Response', $response);

        if (!is_array($response) || !isset($response['id'])) {
            throw new Exception("Neočekávaná odpověď při přidávání pacienta do Baserow: " . json_encode($response));
        }

        $patientId = $response['id'];

        // Log the successful addition
        writeErrorLog('Patient Added', [
            'patient_id' => $patientId,
            'response_data' => $response
        ]);

        $success = "Pacient byl úspěšně přidán s ID: " . $patientId . ". Jméno: " . ($response['Jmeno_pacienta'] ?? 'N/A');
        header("Location: patients.php?success=" . urlencode($success));
        exit;
    } catch (Exception $e) {
        $error = $e->getMessage();
        writeErrorLog('Add Patient Error', [
            'error' => $error,
            'patient_data' => $patientData ?? null,
            'trace' => $e->getTraceAsString()
        ]);
    }
}
?>
<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Přidat pacienta - Dentibot</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
</head>
<body class="bg-gray-50">
    <div class="flex h-screen">
        <?php include 'sidebar.php'; ?>

        <div class="flex-1 overflow-auto">
            <div class="p-6">
                <h1 class="text-2xl font-semibold text-gray-900 mb-6">Přidat pacienta</h1>

                <?php if ($error): ?>
                <div class="mb-4 p-4 bg-red-50 border-l-4 border-red-500 text-red-700">
                    <p class="text-sm"><?php echo htmlspecialchars($error); ?></p>
                </div>
                <?php endif; ?>

                <form method="POST" class="max-w-md">
                    <div class="mb-4">
                        <label for="name" class="block text-sm font-medium text-gray-700">Jméno pacienta</label>
                        <input type="text" name="name" id="name" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                    </div>

                    <div class="mb-4">
                        <label for="email" class="block text-sm font-medium text-gray-700">Emailová adresa</label>
                        <input type="email" name="email" id="email" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                    </div>

                    <div class="mb-4">
                        <label for="phone" class="block text-sm font-medium text-gray-700">Telefonní číslo</label>
                        <input type="tel" name="phone" id="phone" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                    </div>

                    <div class="mb-4">
                        <label for="birth_number" class="block text-sm font-medium text-gray-700">Rodné číslo</label>
                        <input type="text" name="birth_number" id="birth_number" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                    </div>

                    <div class="mt-6">
                        <button type="submit" class="w-full px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            Přidat pacienta
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</body>
</html>



















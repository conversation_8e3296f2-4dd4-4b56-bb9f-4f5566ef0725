export const mainKeywords = [
  "dentibot",
  "zubní ordinace automatizace",
  "voicebot pro zubaře",
  "komunikace s pacienty",
  "rezervační systém zubní ordinace",
  "stomatologie software",
  "automatizace objednávek pacientů",
  "zubní lékař systém",
  "efektivita zubní ordinace",
  "moderní řešení pro zubní ordinaci",
]

export const longTailKeywords = [
  "jak zefektivnit chod zubní ordinace",
  "automatické objednávání pacientů k zubaři",
  "komunikační systém pro stomatology",
  "jak snížit počet zmeškaných návštěv u zubaře",
  "software pro správu zubní ordinace",
  "jak automatizovat komunikaci s pacienty",
  "digitalizace zubní ordinace",
  "voicebot pro zdravotnictví",
  "připomínání term<PERSON>ů zubn<PERSON> p<PERSON>e",
  "online rezervace k zubnímu lékaři",
]

export function getPageKeywords(page: string): string[] {
  const keywordMap: Record<string, string[]> = {
    home: [...mainKeywords.slice(0, 5)],
    sluzby: ["služby dentibot", "voicebot služby", "automatizace ordinace"],
    "o-nas": ["o dentibot", "tým dentibot", "historie dentibot"],
    kontakt: ["kontakt dentibot", "podpora dentibot", "zákaznický servis dentibot"],
    blog: ["blog dentibot", "novinky stomatologie", "trendy v zubním lékařství"],
    cenik: ["ceník dentibot", "cena voicebot", "náklady na automatizaci ordinace"],
    reference: ["reference dentibot", "recenze dentibot", "spokojení zákazníci dentibot"],
    faq: ["časté dotazy dentibot", "jak funguje dentibot", "otázky o voicebotu"],
  }

  return keywordMap[page] || mainKeywords
}


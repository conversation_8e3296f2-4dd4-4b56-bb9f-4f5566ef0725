<?php
/**
 * CGM Časová razítka - Spr<PERSON><PERSON><PERSON> dokumentů
 * Správa elektronických zdravotnických dokumentů s časovými razítky
 */

require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/TimestampManager.php';

class DocumentManager {
    private $db;
    private $timestampManager;
    private $upload_dir;
    
    public function __construct() {
        $this->db = getDbConnection();
        $this->timestampManager = new TimestampManager();
        $this->upload_dir = __DIR__ . '/../uploads/documents/';
        
        // Vytvoření adresáře pro nahrávání, pokud neexistuje
        if (!is_dir($this->upload_dir)) {
            mkdir($this->upload_dir, 0755, true);
        }
    }
    
    /**
     * Vytvoří nový dokument
     * @param array $data Data dokumentu
     * @param array $file Nahraný soubor (volitelné)
     * @return array Výsledek operace
     */
    public function createDocument($data, $file = null) {
        try {
            $user_id = $_SESSION['user_id'] ?? 1;
            
            // Validace povinných polí
            if (empty($data['title'])) {
                return ['success' => false, 'error' => 'Název dokumentu je povinný'];
            }
            
            // Zpracování nahraného souboru
            $file_path = null;
            $file_type = null;
            $file_size = null;
            $content_hash = '';
            
            if ($file && $file['error'] === UPLOAD_ERR_OK) {
                $upload_result = $this->handleFileUpload($file);
                if (!$upload_result['success']) {
                    return $upload_result;
                }
                
                $file_path = $upload_result['file_path'];
                $file_type = $upload_result['file_type'];
                $file_size = $upload_result['file_size'];
                $content_hash = $upload_result['file_hash'];
            } else {
                // Pokud není soubor, použijeme textový obsah
                $content_hash = hash('sha256', $data['content'] ?? '');
            }
            
            // Vytvoření hash dokumentu
            $document_data = [
                'title' => $data['title'],
                'content' => $data['content'] ?? '',
                'patient_id' => $data['patient_id'] ?? null,
                'category_id' => $data['category_id'] ?? 1,
                'file_path' => $file_path,
                'created_at' => date('Y-m-d H:i:s')
            ];
            
            $document_hash = hash('sha256', json_encode($document_data) . $content_hash);
            
            // Uložení do databáze
            $stmt = $this->db->prepare("
                INSERT INTO medical_documents 
                (user_id, patient_id, category_id, title, content, file_path, file_type, file_size, document_hash) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->bind_param("iiissssss", 
                $user_id,
                $data['patient_id'],
                $data['category_id'],
                $data['title'],
                $data['content'],
                $file_path,
                $file_type,
                $file_size,
                $document_hash
            );
            
            if ($stmt->execute()) {
                $document_id = $this->db->insert_id;
                
                // Automatické vytvoření časového razítka
                $timestamp_result = $this->timestampManager->createTimestamp($document_id, $document_hash);
                
                // Záznam do audit logu
                $this->logDocumentAction($document_id, $user_id, 'created', 
                    'Dokument vytvořen: ' . $data['title']);
                
                return [
                    'success' => true,
                    'document_id' => $document_id,
                    'document_hash' => $document_hash,
                    'timestamp_created' => $timestamp_result['success'] ?? false
                ];
            } else {
                return ['success' => false, 'error' => 'Chyba při ukládání dokumentu'];
            }
            
        } catch (Exception $e) {
            error_log("Document creation error: " . $e->getMessage());
            return ['success' => false, 'error' => 'Systémová chyba při vytváření dokumentu'];
        }
    }
    
    /**
     * Získá dokument podle ID
     */
    public function getDocument($document_id, $user_id = null) {
        try {
            $sql = "
                SELECT md.*, dc.name as category_name, u.username as author_name
                FROM medical_documents md
                LEFT JOIN document_categories dc ON md.category_id = dc.id
                LEFT JOIN users u ON md.user_id = u.id
                WHERE md.id = ?
            ";
            
            if ($user_id) {
                $sql .= " AND md.user_id = ?";
            }
            
            $stmt = $this->db->prepare($sql);
            
            if ($user_id) {
                $stmt->bind_param("ii", $document_id, $user_id);
            } else {
                $stmt->bind_param("i", $document_id);
            }
            
            $stmt->execute();
            $result = $stmt->get_result();
            $document = $result->fetch_assoc();
            
            if (!$document) {
                return ['success' => false, 'error' => 'Dokument nebyl nalezen'];
            }
            
            // Získání časových razítek
            $timestamps = $this->timestampManager->getDocumentTimestamps($document_id);
            $document['timestamps'] = $timestamps;
            
            // Záznam do audit logu
            $this->logDocumentAction($document_id, $_SESSION['user_id'] ?? 1, 'viewed');
            
            return ['success' => true, 'document' => $document];
            
        } catch (Exception $e) {
            error_log("Document retrieval error: " . $e->getMessage());
            return ['success' => false, 'error' => 'Chyba při načítání dokumentu'];
        }
    }
    
    /**
     * Získá seznam dokumentů pro uživatele
     */
    public function getUserDocuments($user_id, $filters = []) {
        try {
            $sql = "
                SELECT md.*, dc.name as category_name, 
                       COUNT(dt.id) as timestamp_count,
                       MAX(dt.created_at) as last_timestamp
                FROM medical_documents md
                LEFT JOIN document_categories dc ON md.category_id = dc.id
                LEFT JOIN document_timestamps dt ON md.id = dt.document_id
                WHERE md.user_id = ?
            ";
            
            $params = [$user_id];
            $param_types = "i";
            
            // Filtry
            if (!empty($filters['category_id'])) {
                $sql .= " AND md.category_id = ?";
                $params[] = $filters['category_id'];
                $param_types .= "i";
            }
            
            if (!empty($filters['patient_id'])) {
                $sql .= " AND md.patient_id = ?";
                $params[] = $filters['patient_id'];
                $param_types .= "i";
            }
            
            if (!empty($filters['search'])) {
                $sql .= " AND (md.title LIKE ? OR md.content LIKE ?)";
                $search_term = '%' . $filters['search'] . '%';
                $params[] = $search_term;
                $params[] = $search_term;
                $param_types .= "ss";
            }
            
            $sql .= " GROUP BY md.id ORDER BY md.created_at DESC";
            
            // Stránkování
            if (isset($filters['limit'])) {
                $sql .= " LIMIT ?";
                $params[] = $filters['limit'];
                $param_types .= "i";
                
                if (isset($filters['offset'])) {
                    $sql .= " OFFSET ?";
                    $params[] = $filters['offset'];
                    $param_types .= "i";
                }
            }
            
            $stmt = $this->db->prepare($sql);
            $stmt->bind_param($param_types, ...$params);
            $stmt->execute();
            $result = $stmt->get_result();
            
            $documents = [];
            while ($row = $result->fetch_assoc()) {
                $documents[] = $row;
            }
            
            return ['success' => true, 'documents' => $documents];
            
        } catch (Exception $e) {
            error_log("Document list error: " . $e->getMessage());
            return ['success' => false, 'error' => 'Chyba při načítání seznamu dokumentů'];
        }
    }
    
    /**
     * Zpracuje nahrání souboru
     */
    private function handleFileUpload($file) {
        $allowed_types = [
            'application/pdf',
            'image/jpeg',
            'image/png',
            'image/gif',
            'text/plain',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        ];
        
        if (!in_array($file['type'], $allowed_types)) {
            return ['success' => false, 'error' => 'Nepodporovaný typ souboru'];
        }
        
        if ($file['size'] > 10 * 1024 * 1024) { // 10MB limit
            return ['success' => false, 'error' => 'Soubor je příliš velký (max 10MB)'];
        }
        
        $file_extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $file_name = uniqid() . '_' . time() . '.' . $file_extension;
        $file_path = $this->upload_dir . $file_name;
        
        if (move_uploaded_file($file['tmp_name'], $file_path)) {
            $file_hash = hash_file('sha256', $file_path);
            
            return [
                'success' => true,
                'file_path' => 'uploads/documents/' . $file_name,
                'file_type' => $file['type'],
                'file_size' => $file['size'],
                'file_hash' => $file_hash
            ];
        } else {
            return ['success' => false, 'error' => 'Chyba při nahrávání souboru'];
        }
    }
    
    /**
     * Získá kategorie dokumentů
     */
    public function getCategories() {
        $stmt = $this->db->prepare("SELECT * FROM document_categories ORDER BY name");
        $stmt->execute();
        $result = $stmt->get_result();
        
        $categories = [];
        while ($row = $result->fetch_assoc()) {
            $categories[] = $row;
        }
        
        return $categories;
    }
    
    /**
     * Exportuje dokument do PDF s časovými razítky
     */
    public function exportDocumentToPDF($document_id) {
        // Implementace exportu do PDF s časovými razítky
        // Tato funkce by v produkci používala knihovnu jako TCPDF nebo FPDF
        return ['success' => false, 'error' => 'Export do PDF bude implementován v další verzi'];
    }
    
    /**
     * Zaznamenává akci do audit logu
     */
    private function logDocumentAction($document_id, $user_id, $action, $details = null) {
        $stmt = $this->db->prepare("
            INSERT INTO document_audit_log (document_id, user_id, action, details, ip_address, user_agent) 
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        
        $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
        
        $stmt->bind_param("iissss", $document_id, $user_id, $action, $details, $ip_address, $user_agent);
        $stmt->execute();
    }
}

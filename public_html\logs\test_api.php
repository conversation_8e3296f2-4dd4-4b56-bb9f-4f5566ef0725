<?php
require_once 'config.php';
require_once 'api_functions.php';

try {
    // Test API connection by fetching last 5 calls
    $calls = getCallHistoryFromAPI(5);
    
    echo "API Response:\n";
    echo "=============\n";
    if (is_array($calls) && !empty($calls)) {
        echo "Successfully fetched " . count($calls) . " calls.\n\n";
        foreach ($calls as $index => $call) {
            echo "Call " . ($index + 1) . ":\n";
            echo json_encode($call, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";
        }
    } else {
        echo "No calls found or invalid response format.\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString();
}


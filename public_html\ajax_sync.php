<?php
require_once 'config.php';
require_once 'sync_vapi_data.php';

session_start();

if (!isset($_SESSION['user_id'])) {
    echo json_encode(['error' => 'Uživatel není <PERSON>.']);
    exit;
}

$user_id = $_SESSION['user_id'];

try {
    $result = syncVapiData($user_id);
    if ($result === true) {
        echo json_encode(['success' => 'Data byla úspěšně synchronizována.']);
    } else {
        echo json_encode(['error' => $result]);
    }
} catch (Exception $e) {
    echo json_encode(['error' => 'Synchronizace selhala: ' . $e->getMessage()]);
}
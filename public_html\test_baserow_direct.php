<?php
// Jednoduchý testovací skript pro přímou aktualizaci Baserow bez použití databáze
// Tento skript používá přímo zadané hodnoty pro testování

// Inicializace session pro přístup k $_SESSION
session_start();

// Nastavení hlavičky pro výstup
header('Content-Type: text/html; charset=utf-8');

// Funkce pro zápis do logu
function writeTestLog($message, $data = []) {
    $logFile = '../logs/baserow_direct_test.log';
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[{$timestamp}] {$message}: " . json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
    
    // Vytvoření adresáře pro logy, pokud neexistuje
    if (!file_exists('../logs')) {
        mkdir('../logs', 0777, true);
    }
    
    // Zápis do logu
    file_put_contents($logFile, $logMessage, FILE_APPEND);
    
    // Výpis na obrazovku
    echo "<pre>{$logMessage}</pre>";
}

// Funkce pro testování aktualizace s přímo zadanými hodnotami
function testDirectBaserowUpdate() {
    try {
        // DŮLEŽITÉ: Zde zadejte vaše vlastní hodnoty pro testování
        $apiToken = "gdCsjmjPXEWTq5ftw2oEaJKa8eIurMyI"; // Zadejte váš Baserow API token
        $tableId = "430539"; // Zadejte ID tabulky v Baserow
        $patientId = 3798; // ID pacienta, kterého chcete aktualizovat
        $fieldName = "Datum_prohlidky"; // Přesný název pole v Baserow
        $fieldValue = date('Y-m-d'); // Dnešní datum ve formátu YYYY-MM-DD
        
        // Kontrola, zda byly zadány všechny potřebné hodnoty
        if (empty($apiToken) || empty($tableId)) {
            throw new Exception("Prosím, zadejte API token a ID tabulky v kódu skriptu.");
        }
        
        writeTestLog("Použité hodnoty", [
            'table_id' => $tableId,
            'patient_id' => $patientId,
            'field_name' => $fieldName,
            'field_value' => $fieldValue,
            'api_token_length' => strlen($apiToken)
        ]);
        
        // Sestavení URL pro API požadavek
        $url = "https://api.baserow.io/api/database/rows/table/{$tableId}/{$patientId}/?user_field_names=true";
        
        // Data pro aktualizaci
        $updateData = [
            $fieldName => $fieldValue
        ];
        $jsonData = json_encode($updateData);
        
        writeTestLog("Příprava požadavku", [
            'url' => $url,
            'method' => 'PATCH',
            'data' => $updateData,
            'json' => $jsonData
        ]);
        
        // Inicializace cURL
        $ch = curl_init($url);
        
        // Nastavení cURL
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PATCH');
        curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            "Authorization: Token {$apiToken}",
            "Content-Type: application/json",
            "Content-Length: " . strlen($jsonData)
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
        curl_setopt($ch, CURLOPT_VERBOSE, true);
        
        // Zachycení verbose výstupu
        $verbose = fopen('php://temp', 'w+');
        curl_setopt($ch, CURLOPT_STDERR, $verbose);
        
        // Provedení požadavku
        $response = curl_exec($ch);
        $statusCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        
        // Získání verbose logu
        rewind($verbose);
        $verboseLog = stream_get_contents($verbose);
        
        // Kontrola chyb cURL
        if (curl_errno($ch)) {
            throw new Exception("cURL chyba: " . curl_error($ch));
        }
        
        curl_close($ch);
        
        writeTestLog("cURL Verbose Log", ['log' => $verboseLog]);
        
        // Zpracování odpovědi
        writeTestLog("Odpověď API", [
            'status_code' => $statusCode,
            'response_length' => strlen($response),
            'response' => $response
        ]);
        
        // Kontrola statusu
        if ($statusCode >= 400) {
            $errorResponse = json_decode($response, true);
            $errorMessage = isset($errorResponse['error']) 
                ? $errorResponse['error'] 
                : "Neznámá chyba (Status: $statusCode)";
            throw new Exception("Chyba API: " . $errorMessage);
        }
        
        // Dekódování odpovědi
        $decodedResponse = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception("Chyba při dekódování JSON odpovědi: " . json_last_error_msg());
        }
        
        // Kontrola, zda byla hodnota aktualizována
        $updatedValue = isset($decodedResponse[$fieldName]) ? $decodedResponse[$fieldName] : null;
        
        writeTestLog("Výsledek aktualizace", [
            'field' => $fieldName,
            'sent_value' => $fieldValue,
            'received_value' => $updatedValue,
            'success' => ($updatedValue == $fieldValue)
        ]);
        
        echo "<h2>Test dokončen</h2>";
        echo "<p>Podívejte se na výsledky výše a zkontrolujte, zda byla hodnota úspěšně aktualizována.</p>";
        echo "<p>Zkontrolujte také Baserow, zda se změna projevila.</p>";
        
    } catch (Exception $e) {
        writeTestLog("Chyba při testování", [
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
        
        echo "<h2>Test selhal</h2>";
        echo "<p>Chyba: " . $e->getMessage() . "</p>";
    }
}

// Spuštění testu
echo "<h1>Test přímé aktualizace Baserow</h1>";
echo "<p>Tento test používá přímo zadané hodnoty v kódu skriptu (bez použití databáze).</p>";
echo "<p><strong>DŮLEŽITÉ:</strong> Před spuštěním testu je nutné upravit hodnoty API tokenu a ID tabulky přímo v kódu skriptu.</p>";

// Formulář pro spuštění testu
if (isset($_POST['run_test'])) {
    testDirectBaserowUpdate();
} else {
?>
    <form method="post">
        <p>Klikněte na tlačítko pro spuštění testu:</p>
        <button type="submit" name="run_test" style="background-color: #4CAF50; color: white; padding: 10px 15px; border: none; border-radius: 4px; cursor: pointer;">Spustit test</button>
    </form>
<?php
}
?>

<style>
    body {
        font-family: Arial, sans-serif;
        margin: 20px;
        line-height: 1.6;
    }
    h1, h2 {
        color: #333;
    }
    pre {
        background-color: #f5f5f5;
        padding: 10px;
        border-radius: 5px;
        overflow-x: auto;
    }
</style>


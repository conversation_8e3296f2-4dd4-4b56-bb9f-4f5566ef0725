class MessageWidget {
  constructor(options = {}) {
    this.apiEndpoint = options.apiEndpoint || "/api/messages.php"
    this.refreshInterval = options.refreshInterval || 30000
    this.maxMessages = options.maxMessages || 50
    this.userId = options.userId || null
    this.messages = []
    this.unreadCount = 0
    this.isOpen = false
    this.expandedMessageId = null
    this.refreshTimer = null
    this.forceDisableRefresh = false

    // Load read messages from localStorage
    this.readMessageIds = new Set(this.loadReadMessages())

    if (window._messageWidgetInstance) {
      window._messageWidgetInstance.destroy()
    }
    window._messageWidgetInstance = this

    this.init()
  }

  init() {
    // Create widget container
    this.container = document.createElement("div")
    this.container.className = "message-widget-container"
    document.body.appendChild(this.container)

    // Create button
    this.button = document.createElement("div")
    this.button.className = "message-widget-button"
    this.button.setAttribute("tabindex", "0")
    this.button.setAttribute("role", "button")
    this.button.setAttribute("aria-label", "Zobrazit zprávy od pacientů")

    // Add icon and text to button
    const buttonIcon = document.createElement("div")
    buttonIcon.className = "message-widget-button-icon"
    buttonIcon.innerHTML =
      '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path></svg>'

    const buttonText = document.createElement("div")
    buttonText.className = "message-widget-button-text"
    buttonText.textContent = "Zprávy od pacientů"

    this.button.appendChild(buttonIcon)
    this.button.appendChild(buttonText)
    this.container.appendChild(this.button)

    // Create unread badge
    this.unreadBadge = document.createElement("div")
    this.unreadBadge.className = "unread-badge"
    this.unreadBadge.style.display = "none"
    this.button.appendChild(this.unreadBadge)

    // Create panel
    this.panel = document.createElement("div")
    this.panel.className = "message-widget-panel"
    this.container.appendChild(this.panel)

    // Create panel header
    this.panelHeader = document.createElement("div")
    this.panelHeader.className = "message-widget-header"

    const headerTitle = document.createElement("div")
    headerTitle.textContent = "Zprávy od pacientů"

    const headerActions = document.createElement("div")
    headerActions.className = "message-widget-actions"

    // Add "Mark all as read" button
    this.markAllReadBtn = document.createElement("div")
    this.markAllReadBtn.className = "message-widget-mark-all-read"
    this.markAllReadBtn.textContent = "Označit vše jako přečtené"
    this.markAllReadBtn.setAttribute("tabindex", "0")
    this.markAllReadBtn.setAttribute("role", "button")

    // Add refresh button
    this.refreshBtn = document.createElement("div")
    this.refreshBtn.className = "message-widget-refresh"
    this.refreshBtn.innerHTML =
      '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M23 4v6h-6"/><path d="M1 20v-6h6"/><path d="M3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15"/></svg>'
    this.refreshBtn.setAttribute("tabindex", "0")
    this.refreshBtn.setAttribute("role", "button")
    this.refreshBtn.setAttribute("aria-label", "Obnovit zprávy")
    this.refreshBtn.style.marginRight = "15px"
    this.refreshBtn.style.cursor = "pointer"

    const closeBtn = document.createElement("div")
    closeBtn.className = "message-widget-close"
    closeBtn.textContent = "×"
    closeBtn.setAttribute("tabindex", "0")
    closeBtn.setAttribute("role", "button")
    closeBtn.setAttribute("aria-label", "Zavřít panel zpráv")

    headerActions.appendChild(this.markAllReadBtn)
    headerActions.appendChild(this.refreshBtn)
    headerActions.appendChild(closeBtn)

    this.panelHeader.appendChild(headerTitle)
    this.panelHeader.appendChild(headerActions)
    this.panel.appendChild(this.panelHeader)

    // Create panel content
    this.panelContent = document.createElement("div")
    this.panelContent.className = "message-widget-content"
    this.panel.appendChild(this.panelContent)

    // Create panel footer
    this.panelFooter = document.createElement("div")
    this.panelFooter.className = "message-widget-footer"
    this.panelFooter.textContent = "Powered by make.com"
    this.panel.appendChild(this.panelFooter)

    // Add event listeners
    this.button.addEventListener("click", () => this.togglePanel())
    closeBtn.addEventListener("click", () => this.closePanel())
    this.markAllReadBtn.addEventListener("click", () => this.markAllAsRead())
    this.refreshBtn.addEventListener("click", () => this.fetchMessages(true))

    // Initial fetch
    this.fetchMessages()

    // Set up refresh interval
    this.startAutoRefresh()
  }

  // Save read messages to localStorage
  saveReadMessages() {
    try {
      localStorage.setItem("messageWidget_readIds", JSON.stringify(Array.from(this.readMessageIds)))
    } catch (e) {
      console.error("Failed to save read messages to localStorage", e)
    }
  }

  // Load read messages from localStorage
  loadReadMessages() {
    try {
      const saved = localStorage.getItem("messageWidget_readIds")
      return saved ? JSON.parse(saved) : []
    } catch (e) {
      console.error("Failed to load read messages from localStorage", e)
      return []
    }
  }

  startAutoRefresh() {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer)
    }
    this.refreshTimer = setInterval(() => this.fetchMessages(), this.refreshInterval)
  }

  stopAutoRefresh() {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer)
      this.refreshTimer = null
    }
  }

  fetchMessages(isManualRefresh = false) {
    if (this.forceDisableRefresh && !isManualRefresh) return

    let url = this.apiEndpoint

    if (this.userId) {
      url += (url.includes("?") ? "&" : "?") + "user_id=" + encodeURIComponent(this.userId)
    }

    if (isManualRefresh) {
      url += (url.includes("?") ? "&" : "?") + "_=" + Date.now()
    }

    fetch(url)
      .then((response) => response.json())
      .then((data) => {
        if (data.messages) {
          this.messages = data.messages.slice(0, this.maxMessages)

          // Apply our saved read status to messages
          this.messages.forEach((msg) => {
            if (this.readMessageIds.has(msg.id.toString())) {
              msg.read = true
            }
          })

          // Update unread count
          this.updateUnreadCount()

          // Update UI if panel is open
          if (this.isOpen) {
            this.renderMessages()
          }
        }
      })
      .catch((error) => {
        console.error("Error fetching messages:", error)
      })
  }

  updateUnreadCount() {
    const unreadCount = this.messages.filter((msg) => !msg.read).length
    this.unreadCount = unreadCount
    this.updateUnreadBadge()
  }

  renderMessages() {
    this.panelContent.innerHTML = ""

    if (this.messages.length === 0) {
      const emptyEl = document.createElement("div")
      emptyEl.className = "message-widget-empty"
      emptyEl.textContent = "Žádné zprávy"
      this.panelContent.appendChild(emptyEl)
      return
    }

    this.messages.forEach((message) => {
      const messageEl = document.createElement("div")
      messageEl.className = `message-item${message.read ? "" : " unread"}`
      messageEl.dataset.id = message.id

      // Create message header
      const messageHeader = document.createElement("div")
      messageHeader.className = "message-item-header"
      messageHeader.setAttribute("tabindex", "0")
      messageHeader.setAttribute("role", "button")
      messageHeader.setAttribute("aria-expanded", "false")
      messageHeader.setAttribute("aria-controls", `message-details-${message.id}`)

      // Create message summary
      const messageSummary = document.createElement("div")
      messageSummary.className = "message-item-summary"

      const messageName = document.createElement("div")
      messageName.className = "message-item-name"
      messageName.textContent = message.name || "Neznámý pacient"

      const messagePreview = document.createElement("div")
      messagePreview.className = "message-item-preview"
      messagePreview.textContent = message.message

      messageSummary.appendChild(messageName)
      messageSummary.appendChild(messagePreview)

      const messageTime = document.createElement("div")
      messageTime.className = "message-time"
      messageTime.textContent = this.formatTimestamp(message.timestamp)

      messageHeader.appendChild(messageSummary)
      messageHeader.appendChild(messageTime)
      messageEl.appendChild(messageHeader)

      // Create message details
      const messageDetails = document.createElement("div")
      messageDetails.className = "message-item-details"
      messageDetails.id = `message-details-${message.id}`
      messageDetails.style.display = "none"

      if (message.phone) {
        const messagePhone = document.createElement("div")
        messagePhone.className = "message-item-phone-detail"
        messagePhone.textContent = `Telefon: ${message.phone}`
        messageDetails.appendChild(messagePhone)
      }

      const messageContent = document.createElement("div")
      messageContent.className = "message-item-content"
      messageContent.textContent = message.message
      messageDetails.appendChild(messageContent)

      messageEl.appendChild(messageDetails)

      // Add event listener to toggle details
      messageHeader.addEventListener("click", () => {
        this.toggleMessageDetails(message.id, messageDetails, messageHeader)

        // Mark as read if unread
        if (!message.read) {
          this.markAsRead(message.id, messageEl)
        }
      })

      this.panelContent.appendChild(messageEl)
    })
  }

  toggleMessageDetails(id, detailsEl, headerEl) {
    const isExpanded = detailsEl.style.display === "block"

    // Collapse currently expanded message if different
    if (this.expandedMessageId && this.expandedMessageId !== id) {
      const currentExpandedEl = this.panelContent.querySelector(`#message-details-${this.expandedMessageId}`)
      const currentHeaderEl = this.panelContent.querySelector(
        `.message-item[data-id="${this.expandedMessageId}"] .message-item-header`,
      )

      if (currentExpandedEl) {
        currentExpandedEl.style.display = "none"
        if (currentHeaderEl) {
          currentHeaderEl.setAttribute("aria-expanded", "false")
        }
      }
    }

    // Toggle current message details
    if (isExpanded) {
      detailsEl.style.display = "none"
      headerEl.setAttribute("aria-expanded", "false")
      this.expandedMessageId = null
    } else {
      detailsEl.style.display = "block"
      headerEl.setAttribute("aria-expanded", "true")
      this.expandedMessageId = id
    }
  }

  markAsRead(id, messageEl) {
    const index = this.messages.findIndex((msg) => msg.id == id)
    if (index === -1 || this.messages[index].read) return

    // Disable auto-refresh temporarily
    this.forceDisableRefresh = true

    // Update local state
    this.messages[index].read = true
    this.readMessageIds.add(id.toString())
    this.saveReadMessages()

    // Update UI
    if (messageEl) {
      messageEl.classList.remove("unread")
    }

    // Update badge
    this.updateUnreadCount()

    // Update server
    let url = this.apiEndpoint
    if (this.userId) {
      url += (url.includes("?") ? "&" : "?") + "user_id=" + encodeURIComponent(this.userId)
    }

    fetch(url, {
      method: "PUT",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ id: id, read: true }),
    })
      .then((response) => response.json())
      .then((data) => {
        if (!data.success) {
          console.error("Failed to mark message as read on server", data)
        }

        // Re-enable auto-refresh after delay
        setTimeout(() => {
          this.forceDisableRefresh = false
        }, 5000)
      })
      .catch((error) => {
        console.error("Error updating message status:", error)

        // Re-enable auto-refresh after delay
        setTimeout(() => {
          this.forceDisableRefresh = false
        }, 5000)
      })
  }

  markAllAsRead() {
    const unreadIds = this.messages.filter((msg) => !msg.read).map((msg) => msg.id)
    if (unreadIds.length === 0) return

    // Disable auto-refresh temporarily
    this.forceDisableRefresh = true

    // Update local state
    this.messages.forEach((msg) => {
      if (!msg.read) msg.read = true
    })

    // Add all IDs to read set
    unreadIds.forEach((id) => this.readMessageIds.add(id.toString()))
    this.saveReadMessages()

    // Update UI
    const unreadEls = this.panelContent.querySelectorAll(".message-item.unread")
    unreadEls.forEach((el) => el.classList.remove("unread"))

    // Reset unread count
    this.unreadCount = 0
    this.updateUnreadBadge()

    // Update server
    let url = this.apiEndpoint
    if (this.userId) {
      url += (url.includes("?") ? "&" : "?") + "user_id=" + encodeURIComponent(this.userId)
    }

    const updatePromises = unreadIds.map((id) =>
      fetch(url, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ id: id, read: true }),
      }).then((response) => response.json()),
    )

    Promise.all(updatePromises)
      .then(() => {
        // Re-enable auto-refresh after delay
        setTimeout(() => {
          this.forceDisableRefresh = false
        }, 5000)
      })
      .catch((error) => {
        console.error("Error marking all as read:", error)

        // Re-enable auto-refresh after delay
        setTimeout(() => {
          this.forceDisableRefresh = false
        }, 5000)
      })
  }

  updateUnreadBadge() {
    if (this.unreadCount <= 0) {
      this.unreadBadge.style.display = "none"
      return
    }

    this.unreadBadge.textContent = this.unreadCount > 99 ? "99+" : this.unreadCount
    this.unreadBadge.style.display = "flex"
  }

  togglePanel() {
    if (this.isOpen) {
      this.closePanel()
    } else {
      this.openPanel()
    }
  }

  openPanel() {
    this.panel.classList.add("active")
    this.isOpen = true
    this.renderMessages()
  }

  closePanel() {
    this.panel.classList.remove("active")
    this.isOpen = false
    this.expandedMessageId = null

    // Fetch messages after closing
    setTimeout(() => {
      if (!this.forceDisableRefresh) {
        this.fetchMessages()
      }
    }, 500)
  }

  formatTimestamp(timestamp) {
    const date = new Date(timestamp * 1000)
    const now = new Date()
    const diffMs = now - date
    const diffSec = Math.floor(diffMs / 1000)
    const diffMin = Math.floor(diffSec / 60)
    const diffHour = Math.floor(diffMin / 60)
    const diffDay = Math.floor(diffHour / 24)

    if (diffDay > 0) {
      return `${diffDay} ${diffDay === 1 ? "den" : diffDay < 5 ? "dny" : "dní"} zpět`
    } else if (diffHour > 0) {
      return `${diffHour} ${diffHour === 1 ? "hodinu" : diffHour < 5 ? "hodiny" : "hodin"} zpět`
    } else if (diffMin > 0) {
      return `${diffMin} ${diffMin === 1 ? "minutu" : diffMin < 5 ? "minuty" : "minut"} zpět`
    } else {
      return "právě teď"
    }
  }

  destroy() {
    this.stopAutoRefresh()
    if (this.container && this.container.parentNode) {
      this.container.parentNode.removeChild(this.container)
    }
    if (window._messageWidgetInstance === this) {
      window._messageWidgetInstance = null
    }
  }
}


<?php
/**
 * Migrace pro automatickou kartu pacienta
 * Vytvoří nové tabulky pro event-driven systém
 */

require_once 'config.php';

echo "<h1>Migrace - Automatická karta pacienta</h1>\n";

try {
    $db = getDbConnection();
    
    echo "<h2>Vytváření nových tabulek:</h2>\n";
    
    // Patient Cards
    $sql_patient_cards = "
        CREATE TABLE IF NOT EXISTS patient_cards (
            id INT AUTO_INCREMENT PRIMARY KEY,
            patient_id INT NOT NULL,
            card_version INT NOT NULL DEFAULT 1,
            card_data JSON NOT NULL,
            card_hash VARCHAR(64) NOT NULL,
            snap_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            aggregated_tst_id INT,
            is_current BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_patient_id (patient_id),
            INDEX idx_card_version (card_version),
            INDEX idx_snap_time (snap_time),
            INDEX idx_is_current (is_current)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    if ($db->query($sql_patient_cards)) {
        echo "✅ Tabulka patient_cards vytvořena<br>\n";
    } else {
        echo "❌ Chyba při vytváření tabulky patient_cards: " . $db->error . "<br>\n";
    }
    
    // Card Items
    $sql_card_items = "
        CREATE TABLE IF NOT EXISTS card_items (
            id INT AUTO_INCREMENT PRIMARY KEY,
            card_id INT NOT NULL,
            visit_id VARCHAR(36),
            item_type ENUM('note', 'image', 'file', 'procedure', 'consent', 'payment') NOT NULL,
            version_id VARCHAR(36) NOT NULL,
            content_hash VARCHAR(64) NOT NULL,
            tst_id INT,
            item_data JSON,
            file_path VARCHAR(500),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (card_id) REFERENCES patient_cards(id) ON DELETE CASCADE,
            FOREIGN KEY (tst_id) REFERENCES document_timestamps(id),
            INDEX idx_card_id (card_id),
            INDEX idx_visit_id (visit_id),
            INDEX idx_item_type (item_type),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    if ($db->query($sql_card_items)) {
        echo "✅ Tabulka card_items vytvořena<br>\n";
    } else {
        echo "❌ Chyba při vytváření tabulky card_items: " . $db->error . "<br>\n";
    }
    
    // Card Events
    $sql_card_events = "
        CREATE TABLE IF NOT EXISTS card_events (
            id INT AUTO_INCREMENT PRIMARY KEY,
            patient_id INT NOT NULL,
            event_type VARCHAR(50) NOT NULL,
            event_data JSON,
            processed BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            processed_at TIMESTAMP NULL,
            INDEX idx_patient_id (patient_id),
            INDEX idx_event_type (event_type),
            INDEX idx_processed (processed),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    if ($db->query($sql_card_events)) {
        echo "✅ Tabulka card_events vytvořena<br>\n";
    } else {
        echo "❌ Chyba při vytváření tabulky card_events: " . $db->error . "<br>\n";
    }
    
    // Daily Card Stamps
    $sql_daily_stamps = "
        CREATE TABLE IF NOT EXISTS daily_card_stamps (
            id INT AUTO_INCREMENT PRIMARY KEY,
            stamp_date DATE NOT NULL,
            patient_id INT NOT NULL,
            card_version INT NOT NULL,
            aggregated_hash VARCHAR(64) NOT NULL,
            tst_id INT,
            status ENUM('pending', 'completed', 'failed') DEFAULT 'pending',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            completed_at TIMESTAMP NULL,
            FOREIGN KEY (tst_id) REFERENCES document_timestamps(id),
            UNIQUE KEY unique_daily_stamp (stamp_date, patient_id),
            INDEX idx_stamp_date (stamp_date),
            INDEX idx_patient_id (patient_id),
            INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    if ($db->query($sql_daily_stamps)) {
        echo "✅ Tabulka daily_card_stamps vytvořena<br>\n";
    } else {
        echo "❌ Chyba při vytváření tabulky daily_card_stamps: " . $db->error . "<br>\n";
    }
    
    // Vytvoření mock tabulky pacientů pokud neexistuje
    $sql_patients = "
        CREATE TABLE IF NOT EXISTS patients (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            phone VARCHAR(20),
            email VARCHAR(255),
            birth_date DATE,
            address TEXT,
            insurance VARCHAR(10),
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    if ($db->query($sql_patients)) {
        echo "✅ Tabulka patients vytvořena/ověřena<br>\n";
    } else {
        echo "❌ Chyba při vytváření tabulky patients: " . $db->error . "<br>\n";
    }
    
    echo "<h2>Vkládání testovacích dat:</h2>\n";
    
    // Kontrola a vložení testovacích pacientů
    $result = $db->query("SELECT COUNT(*) as count FROM patients");
    $row = $result->fetch_assoc();
    
    if ($row['count'] == 0) {
        $patients = [
            ['Jan Novák', '+420 123 456 789', '<EMAIL>', '1985-03-15', 'Hlavní 123, Praha 1', 'VZP', 'Pravidelný pacient, bez alergií'],
            ['Marie Svobodová', '+420 987 654 321', '<EMAIL>', '1978-07-22', 'Nová 456, Praha 2', 'ČPZP', 'Alergie na penicilin'],
            ['Pavel Dvořák', '+420 555 123 456', '<EMAIL>', '1992-11-08', 'Krátká 789, Praha 3', 'VoZP', 'Ortodontická léčba v průběhu'],
            ['Anna Procházková', '+420 777 888 999', '<EMAIL>', '1965-12-03', 'Dlouhá 321, Praha 4', 'VZP', 'Diabetik - pozor na hojení']
        ];
        
        $stmt = $db->prepare("INSERT INTO patients (name, phone, email, birth_date, address, insurance, notes) VALUES (?, ?, ?, ?, ?, ?, ?)");
        
        foreach ($patients as $patient) {
            $stmt->bind_param("sssssss", ...$patient);
            if ($stmt->execute()) {
                echo "✅ Pacient '{$patient[0]}' vložen<br>\n";
            } else {
                echo "❌ Chyba při vkládání pacienta '{$patient[0]}': " . $stmt->error . "<br>\n";
            }
        }
        
        $stmt->close();
    } else {
        echo "ℹ️ Pacienti již existují (počet: {$row['count']})<br>\n";
    }
    
    // Vytvoření adresáře pro demo soubory
    $demo_dir = __DIR__ . '/uploads/demo/';
    if (!is_dir($demo_dir)) {
        if (mkdir($demo_dir, 0755, true)) {
            echo "✅ Adresář pro demo soubory vytvořen: $demo_dir<br>\n";
        } else {
            echo "❌ Chyba při vytváření adresáře: $demo_dir<br>\n";
        }
    } else {
        echo "ℹ️ Adresář pro demo soubory již existuje<br>\n";
    }
    
    echo "<h2>Migrace dokončena!</h2>\n";
    echo "<div style='background: #e6f7ff; border: 1px solid #91d5ff; padding: 15px; border-radius: 5px; margin: 20px 0;'>\n";
    echo "<h3>🎉 Automatická karta pacienta je připravena!</h3>\n";
    echo "<p><strong>Co je nového:</strong></p>\n";
    echo "<ul>\n";
    echo "<li>✅ Event-driven architektura pro automatické aktualizace</li>\n";
    echo "<li>✅ Okamžité TST pro každou položku + denní agregované TST</li>\n";
    echo "<li>✅ JSON struktura pro rychlé vyhledávání a reporty</li>\n";
    echo "<li>✅ Timeline view všech aktivit pacienta</li>\n";
    echo "<li>✅ Zero-click administrace pro lékaře</li>\n";
    echo "</ul>\n";
    echo "<p><strong>Další kroky:</strong></p>\n";
    echo "<p><a href='demo_card_events.php' style='color: #1890ff; text-decoration: none;'>→ Vyzkoušet demo události</a></p>\n";
    echo "<p><a href='patient_card_view.php?patient_id=1' style='color: #1890ff; text-decoration: none;'>→ Zobrazit kartu pacienta</a></p>\n";
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<h2>Chyba při migraci:</h2>\n";
    echo "<p style='color: red;'>" . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>\n";
}
?>

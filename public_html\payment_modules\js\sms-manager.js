/**
 * SMS Manager - JavaScript funkce pro správu SMS na bázi pay as you go
 */

// Získání aktuálního z<PERSON>tku SMS kreditu
function getSmsCredit() {
  return fetch("payment_modules/handlers/sms_payment_handler.php", {
    method: "POST",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    body: new URLSearchParams({
      action: "get_credit",
    }),
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.success) {
        // Aktualizace zobrazení kreditu v UI
        updateCreditDisplay(data.credit)
        return data.credit
      } else {
        showNotification("error", data.message || "Nepodařilo se načíst kredit")
        return 0
      }
    })
    .catch((error) => {
      console.error("Chyba při získávání kreditu:", error)
      showNotification("error", "<PERSON><PERSON><PERSON> k <PERSON> při komunikaci se serverem")
      return 0
    })
}

// Dobití SMS kreditu
function rechargeCredit(amount, paymentMethod) {
  return fetch("payment_modules/handlers/sms_payment_handler.php", {
    method: "POST",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    body: new URLSearchParams({
      action: "recharge_credit",
      amount: amount,
      payment_method: paymentMethod,
    }),
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.success) {
        showNotification("success", data.message || "Kredit byl úspěšně dobit")
        updateCreditDisplay(data.new_balance)
        return true
      } else {
        showNotification("error", data.message || "Nepodařilo se dobít kredit")
        return false
      }
    })
    .catch((error) => {
      console.error("Chyba při dobíjení kreditu:", error)
      showNotification("error", "Došlo k chybě při komunikaci se serverem")
      return false
    })
}

// Získání historie využití SMS
function getSmsUsageHistory(period = "month") {
  return fetch("payment_modules/handlers/sms_payment_handler.php", {
    method: "POST",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    body: new URLSearchParams({
      action: "get_usage_history",
      period: period,
    }),
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.success) {
        // Aktualizace zobrazení historie v UI
        updateUsageHistoryDisplay(data.history, period)
        return data.history
      } else {
        showNotification("error", data.message || "Nepodařilo se načíst historii využití")
        return []
      }
    })
    .catch((error) => {
      console.error("Chyba při získávání historie:", error)
      showNotification("error", "Došlo k chybě při komunikaci se serverem")
      return []
    })
}

// Získání ceny za jednu SMS
function getSmsPricePerUnit() {
  return fetch("payment_modules/handlers/sms_payment_handler.php", {
    method: "POST",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    body: new URLSearchParams({
      action: "get_price",
    }),
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.success) {
        // Aktualizace zobrazení ceny v UI
        updatePriceDisplay(data.price)
        return data.price
      } else {
        showNotification("error", data.message || "Nepodařilo se načíst cenu SMS")
        return 0
      }
    })
    .catch((error) => {
      console.error("Chyba při získávání ceny:", error)
      showNotification("error", "Došlo k chybě při komunikaci se serverem")
      return 0
    })
}

// Pomocné funkce pro aktualizaci UI
function updateCreditDisplay(credit) {
  const creditElement = document.getElementById("sms-credit")
  if (creditElement) {
    creditElement.textContent = `${credit.toFixed(2)} Kč`
  }
}

function updatePriceDisplay(price) {
  const priceElements = document.querySelectorAll(".sms-price")
  priceElements.forEach((element) => {
    element.textContent = `${price.toFixed(2)} Kč`
  })
}

function updateUsageHistoryDisplay(history, period) {
  const historyContainer = document.getElementById("sms-usage-history")
  if (!historyContainer) return

  // Vyčištění kontejneru
  historyContainer.innerHTML = ""

  if (history.length === 0) {
    historyContainer.innerHTML = "<p>Žádná historie využití SMS za vybrané období.</p>"
    return
  }

  // Vytvoření tabulky s historií
  const table = document.createElement("table")
  table.className = "sms-history-table"

  // Hlavička tabulky
  const thead = document.createElement("thead")
  thead.innerHTML = `
        <tr>
            <th>Datum</th>
            <th>Příjemce</th>
            <th>Zpráva</th>
            <th>Cena</th>
        </tr>
    `
  table.appendChild(thead)

  // Tělo tabulky
  const tbody = document.createElement("tbody")
  history.forEach((item) => {
    const row = document.createElement("tr")
    row.innerHTML = `
            <td>${formatDate(item.date)}</td>
            <td>${item.recipient}</td>
            <td>${item.message.substring(0, 30)}${item.message.length > 30 ? "..." : ""}</td>
            <td>${item.price.toFixed(2)} Kč</td>
        `
    tbody.appendChild(row)
  })
  table.appendChild(tbody)

  historyContainer.appendChild(table)
}

// Formátování data
function formatDate(dateString) {
  const date = new Date(dateString)
  return (
    date.toLocaleDateString("cs-CZ") + " " + date.toLocaleTimeString("cs-CZ", { hour: "2-digit", minute: "2-digit" })
  )
}

// Zobrazení notifikace
function showNotification(type, message) {
  // Předpokládám, že máte funkci pro zobrazení notifikací
  // Pokud ne, můžete použít tuto jednoduchou implementaci
  const notificationContainer = document.getElementById("notification-container")
  if (!notificationContainer) return

  const notification = document.createElement("div")
  notification.className = `notification ${type}`
  notification.textContent = message

  notificationContainer.appendChild(notification)

  // Automatické odstranění notifikace po 5 sekundách
  setTimeout(() => {
    notification.classList.add("fade-out")
    setTimeout(() => {
      notification.remove()
    }, 500)
  }, 5000)
}

// Inicializace při načtení stránky
document.addEventListener("DOMContentLoaded", () => {
  // Načtení počátečních dat
  getSmsCredit()
  getSmsPricePerUnit()
  getSmsUsageHistory()

  // Nastavení event listenerů pro formuláře a tlačítka
  setupEventListeners()
})

// Nastavení event listenerů
function setupEventListeners() {
  // Formulář pro dobití kreditu
  const rechargeForm = document.getElementById("recharge-form")
  if (rechargeForm) {
    rechargeForm.addEventListener("submit", (e) => {
      e.preventDefault()

      const amount = Number.parseFloat(document.getElementById("recharge-amount").value)
      const paymentMethod = document.getElementById("payment-method").value

      if (isNaN(amount) || amount <= 0) {
        showNotification("error", "Zadejte platnou částku")
        return
      }

      rechargeCredit(amount, paymentMethod)
    })
  }

  // Přepínače období pro historii
  const periodButtons = document.querySelectorAll(".period-button")
  periodButtons.forEach((button) => {
    button.addEventListener("click", function () {
      const period = this.dataset.period

      // Odstranění aktivní třídy ze všech tlačítek
      periodButtons.forEach((btn) => btn.classList.remove("active"))

      // Přidání aktivní třídy na kliknuté tlačítko
      this.classList.add("active")

      // Načtení historie pro vybrané období
      getSmsUsageHistory(period)
    })
  })
}


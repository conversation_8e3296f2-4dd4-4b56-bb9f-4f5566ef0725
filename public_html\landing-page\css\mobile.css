/* Mobile-first responsive styles */

/* Base mobile styles */
body {
    font-size: 16px;
    overflow-x: hidden;
}

/* Container adjustments */
.container {
    width: 100%;
    padding: 0 15px;
}

/* Background glow adjustments */
.background-glow {
    opacity: 0.4;
    transform: scale(0.7);
}

/* Section spacing */
section {
    padding: 40px 0;
    margin-bottom: 30px;
}

/* Headings */
h1 {
    font-size: 28px;
    line-height: 1.3;
}

h2 {
    font-size: 24px;
}

h3 {
    font-size: 20px;
}

/* Buttons */
button, .button {
    width: 100%;
    padding: 12px 16px;
    margin-bottom: 10px;
}

/* Forms */
input, select, textarea {
    width: 100%;
    padding: 12px;
    margin-bottom: 15px;
    font-size: 16px; /* Prevents iOS zoom on focus */
}

/* Images */
img {
    max-width: 100%;
    height: auto;
}

/* Mobile Navigation Styles */
.mobile-navigation .nav-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
}

.mobile-menu-toggle {
    display: block;
    background: none;
    border: none;
    width: 30px;
    height: 25px;
    position: relative;
    cursor: pointer;
    z-index: 100;
}

.mobile-menu-toggle span {
    display: block;
    width: 100%;
    height: 3px;
    background-color: #0cf4ff;
    margin: 5px 0;
    transition: all 0.3s ease;
}

.mobile-menu-toggle.active span:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.mobile-menu-toggle.active span:nth-child(2) {
    opacity: 0;
}

.mobile-menu-toggle.active span:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -7px);
}

.mobile-nav-container {
    position: fixed;
    top: 0;
    right: -100%;
    width: 80%;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.95);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    transition: right 0.3s ease;
    z-index: 99;
}

.mobile-nav-container.active {
    right: 0;
}

.mobile-navigation .nav-menu {
    list-style: none;
    padding: 0;
    margin: 0;
    width: 100%;
    text-align: center;
}

.mobile-navigation .nav-menu li {
    margin: 15px 0;
}

.mobile-navigation .nav-menu a {
    font-size: 18px;
    color: white;
    text-decoration: none;
    display: block;
    padding: 10px;
}

.mobile-navigation .cta-button {
    margin-top: 20px;
}

.mobile-navigation .cta-button a {
    background-color: #0cf4ff;
    color: #000;
    border-radius: 4px;
}

body.menu-open {
    overflow: hidden;
}

/* Cal floating button adjustment */
#cal-floating-button {
    transform: scale(0.85);
    right: 10px !important;
    bottom: 10px !important;
}

/* Touch-friendly improvements */
a, button, .button, input[type="submit"] {
    min-height: 44px;
    min-width: 44px;
}

/* Add space between clickable elements */
li, .clickable {
    margin-bottom: 10px;
}
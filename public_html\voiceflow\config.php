<?php
// Konfigurace pro Voiceflow
define('VOICEFLOW_API_KEY', 'YOUR_API_KEY');
define('VOICEFLOW_VERSION_ID', 'production');
define('VOICEFLOW_PROJECT_ID', 'YOUR_PROJECT_ID');

// Databázo<PERSON><PERSON>
define('DB_HOST', 'localhost');
define('DB_NAME', 'dentibot');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');

// Cesty k souborům
define('VOICEFLOW_DIR', __DIR__);
define('TEMPLATES_DIR', VOICEFLOW_DIR . '/templates');
define('CSS_DIR', VOICEFLOW_DIR . '/css');
define('JS_DIR', VOICEFLOW_DIR . '/js');

// Funkce pro načtení uložených nastavení
function loadVoiceflowSettings($userId) {
    global $db;
    
    $stmt = $db->prepare("SELECT settings FROM voiceflow_settings WHERE user_id = ?");
    $stmt->execute([$userId]);
    
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

// Funkce pro uložení nastavení
function saveVoiceflowSettings($userId, $settings) {
    global $db;
    
    $stmt = $db->prepare("
        INSERT INTO voiceflow_settings (user_id, settings) 
        VALUES (?, ?) 
        ON DUPLICATE KEY UPDATE settings = ?
    ");
    
    return $stmt->execute([$userId, $settings, $settings]);
}


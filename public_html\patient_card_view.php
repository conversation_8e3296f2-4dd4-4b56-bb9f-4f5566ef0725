<?php
require_once 'config.php';
require_once 'patient_card/PatientCardManager.php';

// Kontrola přihlášení
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

$patient_id = $_GET['patient_id'] ?? 0;
if (!$patient_id) {
    header('Location: patients.php');
    exit;
}

$cardManager = new PatientCardManager();
$card_result = $cardManager->getPatientCard($patient_id);

if (!$card_result['success']) {
    $error = $card_result['error'];
    $card = null;
} else {
    $card = $card_result['card'];
    $error = null;
}

$currentPage = 'patients';
?>

<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Karta pacienta - <?php echo htmlspecialchars($card['patient_name'] ?? 'Neznámý'); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="css/cgm_documents.css" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
        .timeline-item {
            position: relative;
            padding-left: 2rem;
            margin-bottom: 2rem;
        }
        .timeline-item::before {
            content: '';
            position: absolute;
            left: 0.5rem;
            top: 0;
            bottom: -2rem;
            width: 2px;
            background: #e5e7eb;
        }
        .timeline-item:last-child::before {
            bottom: 0;
        }
        .timeline-dot {
            position: absolute;
            left: 0;
            top: 0.5rem;
            width: 1rem;
            height: 1rem;
            border-radius: 50%;
            border: 2px solid white;
            box-shadow: 0 0 0 2px #e5e7eb;
        }
        .timeline-dot.note { background: #3b82f6; }
        .timeline-dot.image { background: #10b981; }
        .timeline-dot.procedure { background: #f59e0b; }
        .timeline-dot.consent { background: #8b5cf6; }
        .timeline-dot.payment { background: #ef4444; }
        
        .card-header {
            background: linear-gradient(135deg, #1e3a8a, #3b82f6);
            color: white;
        }
        
        .auto-badge {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
            border-radius: 9999px;
            font-weight: 500;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="flex min-h-screen">
        <?php include 'sidebar.php'; ?>
        
        <div class="flex-1 ml-64">
            <div class="p-8">
                <!-- Header -->
                <div class="mb-8">
                    <nav class="text-sm text-gray-600 mb-2">
                        <a href="patients.php" class="hover:text-blue-600">Seznam pacientů</a>
                        <span class="mx-2">/</span>
                        <span>Karta pacienta</span>
                    </nav>
                    
                    <?php if ($card): ?>
                        <div class="card-header rounded-lg p-6 mb-6">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h1 class="text-3xl font-bold"><?php echo htmlspecialchars($card['patient_name']); ?></h1>
                                    <div class="flex items-center gap-4 mt-2 text-blue-100">
                                        <span><?php echo htmlspecialchars($card['phone'] ?? 'Bez telefonu'); ?></span>
                                        <span>•</span>
                                        <span><?php echo htmlspecialchars($card['email'] ?? 'Bez emailu'); ?></span>
                                        <span>•</span>
                                        <span>Verze karty: <?php echo $card['card_version']; ?></span>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <div class="auto-badge mb-2">
                                        Automaticky generováno
                                    </div>
                                    <div class="text-sm text-blue-200">
                                        Poslední aktualizace: <?php echo date('d.m.Y H:i', strtotime($card['updated_at'])); ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>

                <?php if ($error): ?>
                    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                        <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>

                <?php if ($card): ?>
                    <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
                        <!-- Timeline -->
                        <div class="lg:col-span-3">
                            <div class="bg-white rounded-lg shadow-sm border">
                                <div class="p-6 border-b">
                                    <div class="flex items-center justify-between">
                                        <h2 class="text-xl font-semibold text-gray-900">Timeline aktivit</h2>
                                        <span class="text-sm text-gray-500">
                                            <?php echo count($card['items']); ?> položek
                                        </span>
                                    </div>
                                </div>
                                <div class="p-6">
                                    <?php if (empty($card['items'])): ?>
                                        <div class="text-center py-12">
                                            <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                            </svg>
                                            <h3 class="text-lg font-medium text-gray-900 mb-2">Žádné aktivity</h3>
                                            <p class="text-gray-600">Karta pacienta se automaticky naplní při aktivitách v CRM.</p>
                                        </div>
                                    <?php else: ?>
                                        <div class="timeline">
                                            <?php foreach ($card['items'] as $item): ?>
                                                <div class="timeline-item">
                                                    <div class="timeline-dot <?php echo $item['item_type']; ?>"></div>
                                                    <div class="bg-gray-50 rounded-lg p-4">
                                                        <div class="flex items-start justify-between mb-2">
                                                            <div>
                                                                <h3 class="font-semibold text-gray-900">
                                                                    <?php echo htmlspecialchars($item['item_data']['title'] ?? ucfirst($item['item_type'])); ?>
                                                                </h3>
                                                                <p class="text-sm text-gray-600">
                                                                    <?php echo date('d.m.Y H:i', strtotime($item['created_at'])); ?>
                                                                </p>
                                                            </div>
                                                            <div class="flex items-center gap-2">
                                                                <span class="bg-<?php echo getTypeColor($item['item_type']); ?>-100 text-<?php echo getTypeColor($item['item_type']); ?>-800 text-xs px-2 py-1 rounded-full">
                                                                    <?php echo getTypeLabel($item['item_type']); ?>
                                                                </span>
                                                                <?php if ($item['tst_id']): ?>
                                                                    <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full" title="Časové razítko">
                                                                        TST
                                                                    </span>
                                                                <?php endif; ?>
                                                            </div>
                                                        </div>
                                                        
                                                        <?php if (!empty($item['item_data']['content'])): ?>
                                                            <p class="text-gray-700 mb-3">
                                                                <?php echo nl2br(htmlspecialchars($item['item_data']['content'])); ?>
                                                            </p>
                                                        <?php endif; ?>
                                                        
                                                        <?php if ($item['file_path']): ?>
                                                            <div class="flex items-center gap-2 text-sm text-blue-600">
                                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path>
                                                                </svg>
                                                                <a href="<?php echo htmlspecialchars($item['file_path']); ?>" target="_blank">
                                                                    Zobrazit přílohu
                                                                </a>
                                                            </div>
                                                        <?php endif; ?>
                                                        
                                                        <div class="mt-3 text-xs text-gray-500">
                                                            Hash: <?php echo substr($item['content_hash'], 0, 16); ?>...
                                                            <?php if ($item['tst_valid']): ?>
                                                                | TST platné do: <?php echo date('d.m.Y', strtotime($item['tst_created'] . ' +10 years')); ?>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <!-- Sidebar -->
                        <div class="lg:col-span-1">
                            <!-- Card Info -->
                            <div class="bg-white rounded-lg shadow-sm border mb-6">
                                <div class="p-6 border-b">
                                    <h3 class="text-lg font-semibold text-gray-900">Informace o kartě</h3>
                                </div>
                                <div class="p-6 space-y-4">
                                    <div>
                                        <p class="text-sm font-medium text-gray-700">ID pacienta</p>
                                        <p class="text-sm text-gray-600"><?php echo $card['patient_id']; ?></p>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-700">Verze karty</p>
                                        <p class="text-sm text-gray-600"><?php echo $card['card_version']; ?></p>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-700">Hash karty</p>
                                        <p class="text-xs text-gray-600 font-mono break-all">
                                            <?php echo $card['card_hash']; ?>
                                        </p>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-700">Vytvořeno</p>
                                        <p class="text-sm text-gray-600"><?php echo date('d.m.Y H:i', strtotime($card['created_at'])); ?></p>
                                    </div>
                                </div>
                            </div>

                            <!-- Actions -->
                            <div class="bg-white rounded-lg shadow-sm border mb-6">
                                <div class="p-6 border-b">
                                    <h3 class="text-lg font-semibold text-gray-900">Akce</h3>
                                </div>
                                <div class="p-6 space-y-3">
                                    <button onclick="exportCard()" class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition-colors">
                                        Export PDF/A
                                    </button>
                                    <button onclick="verifyCard()" class="w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg transition-colors">
                                        Ověřit razítka
                                    </button>
                                    <button onclick="refreshCard()" class="w-full bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-lg transition-colors">
                                        Obnovit kartu
                                    </button>
                                </div>
                            </div>

                            <!-- Statistics -->
                            <div class="bg-white rounded-lg shadow-sm border">
                                <div class="p-6 border-b">
                                    <h3 class="text-lg font-semibold text-gray-900">Statistiky</h3>
                                </div>
                                <div class="p-6 space-y-3">
                                    <?php
                                    $stats = [];
                                    foreach ($card['items'] as $item) {
                                        $type = $item['item_type'];
                                        $stats[$type] = ($stats[$type] ?? 0) + 1;
                                    }
                                    ?>
                                    <?php foreach ($stats as $type => $count): ?>
                                        <div class="flex justify-between">
                                            <span class="text-gray-600"><?php echo getTypeLabel($type); ?></span>
                                            <span class="font-semibold"><?php echo $count; ?></span>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script>
        function exportCard() {
            alert('Export PDF/A s embedded soubory a všemi TST bude implementován.');
        }
        
        function verifyCard() {
            alert('Ověření všech časových razítek v kartě bude implementováno.');
        }
        
        function refreshCard() {
            window.location.reload();
        }
    </script>
</body>
</html>

<?php
function getTypeColor($type) {
    $colors = [
        'note' => 'blue',
        'image' => 'green',
        'procedure' => 'yellow',
        'consent' => 'purple',
        'payment' => 'red'
    ];
    return $colors[$type] ?? 'gray';
}

function getTypeLabel($type) {
    $labels = [
        'note' => 'Záznam',
        'image' => 'Snímek',
        'procedure' => 'Výkon',
        'consent' => 'Souhlas',
        'payment' => 'Platba'
    ];
    return $labels[$type] ?? ucfirst($type);
}
?>

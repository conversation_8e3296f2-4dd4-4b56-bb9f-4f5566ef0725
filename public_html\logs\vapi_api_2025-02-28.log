{
    "timestamp": "2025-02-28 02:30:16",
    "message": "Starting sync process",
    "data": [],
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:30:16",
    "message": "API Config loaded",
    "data": {
        "url": "https:\/\/api.vapi.ai"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:30:16",
    "message": "Fetching data from API",
    "data": {
        "startDate": "2025-01-29",
        "endDate": "2025-02-28",
        "api_url": "https:\/\/api.vapi.ai"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:30:16",
    "message": "Making API call with query",
    "data": {
        "query": "{\"queries\":[{\"table\":\"call\",\"name\":\"calls_data\",\"operations\":[{\"operation\":\"history\",\"column\":\"*\"}],\"timeRange\":{\"start\":\"2025-01-29T00:00:00Z\",\"end\":\"2025-02-28T23:59:59Z\"},\"filters\":[{\"field\":\"assistantId\",\"operator\":\"=\",\"value\":\"377ba32d-396b-4f6a-a91a-d45b12db11c7\"}]}]}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:30:17",
    "message": "Sync error",
    "data": {
        "error": "API vrátila chybový kód: 400",
        "trace": "#0 \/home\/<USER>\/domains\/dentibot.eu\/public_html\/sync_vapi_data.php(72): makeApiCall()\n#1 \/home\/<USER>\/domains\/dentibot.eu\/public_html\/dashboard.php(77): syncVapiData()\n#2 {main}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:30:18",
    "message": "Starting sync process",
    "data": [],
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:30:18",
    "message": "API Config loaded",
    "data": {
        "url": "https:\/\/api.vapi.ai"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:30:18",
    "message": "Fetching data from API",
    "data": {
        "startDate": "2025-01-29",
        "endDate": "2025-02-28",
        "api_url": "https:\/\/api.vapi.ai"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:30:18",
    "message": "Making API call with query",
    "data": {
        "query": "{\"queries\":[{\"table\":\"call\",\"name\":\"calls_data\",\"operations\":[{\"operation\":\"history\",\"column\":\"*\"}],\"timeRange\":{\"start\":\"2025-01-29T00:00:00Z\",\"end\":\"2025-02-28T23:59:59Z\"},\"filters\":[{\"field\":\"assistantId\",\"operator\":\"=\",\"value\":\"377ba32d-396b-4f6a-a91a-d45b12db11c7\"}]}]}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:30:18",
    "message": "Sync error",
    "data": {
        "error": "API vrátila chybový kód: 400",
        "trace": "#0 \/home\/<USER>\/domains\/dentibot.eu\/public_html\/sync_vapi_data.php(72): makeApiCall()\n#1 \/home\/<USER>\/domains\/dentibot.eu\/public_html\/dashboard.php(77): syncVapiData()\n#2 {main}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:30:29",
    "message": "Starting sync process",
    "data": [],
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:30:29",
    "message": "API Config loaded",
    "data": {
        "url": "https:\/\/api.vapi.ai"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:30:29",
    "message": "Fetching data from API",
    "data": {
        "startDate": "2025-01-29",
        "endDate": "2025-02-28",
        "api_url": "https:\/\/api.vapi.ai"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:30:29",
    "message": "Making API call with query",
    "data": {
        "query": "{\"queries\":[{\"table\":\"call\",\"name\":\"calls_data\",\"operations\":[{\"operation\":\"history\",\"column\":\"*\"}],\"timeRange\":{\"start\":\"2025-01-29T00:00:00Z\",\"end\":\"2025-02-28T23:59:59Z\"},\"filters\":[{\"field\":\"assistantId\",\"operator\":\"=\",\"value\":\"377ba32d-396b-4f6a-a91a-d45b12db11c7\"}]}]}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:30:29",
    "message": "Sync error",
    "data": {
        "error": "API vrátila chybový kód: 400",
        "trace": "#0 \/home\/<USER>\/domains\/dentibot.eu\/public_html\/sync_vapi_data.php(72): makeApiCall()\n#1 \/home\/<USER>\/domains\/dentibot.eu\/public_html\/dashboard.php(77): syncVapiData()\n#2 {main}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:30:29",
    "message": "Starting sync process",
    "data": [],
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:30:29",
    "message": "API Config loaded",
    "data": {
        "url": "https:\/\/api.vapi.ai"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:30:29",
    "message": "Fetching data from API",
    "data": {
        "startDate": "2025-01-29",
        "endDate": "2025-02-28",
        "api_url": "https:\/\/api.vapi.ai"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:30:29",
    "message": "Making API call with query",
    "data": {
        "query": "{\"queries\":[{\"table\":\"call\",\"name\":\"calls_data\",\"operations\":[{\"operation\":\"history\",\"column\":\"*\"}],\"timeRange\":{\"start\":\"2025-01-29T00:00:00Z\",\"end\":\"2025-02-28T23:59:59Z\"},\"filters\":[{\"field\":\"assistantId\",\"operator\":\"=\",\"value\":\"377ba32d-396b-4f6a-a91a-d45b12db11c7\"}]}]}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:30:30",
    "message": "Sync error",
    "data": {
        "error": "API vrátila chybový kód: 400",
        "trace": "#0 \/home\/<USER>\/domains\/dentibot.eu\/public_html\/sync_vapi_data.php(72): makeApiCall()\n#1 \/home\/<USER>\/domains\/dentibot.eu\/public_html\/dashboard.php(77): syncVapiData()\n#2 {main}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:30:30",
    "message": "Starting sync process",
    "data": [],
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:30:30",
    "message": "API Config loaded",
    "data": {
        "url": "https:\/\/api.vapi.ai"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:30:30",
    "message": "Fetching data from API",
    "data": {
        "startDate": "2025-01-29",
        "endDate": "2025-02-28",
        "api_url": "https:\/\/api.vapi.ai"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:30:30",
    "message": "Making API call with query",
    "data": {
        "query": "{\"queries\":[{\"table\":\"call\",\"name\":\"calls_data\",\"operations\":[{\"operation\":\"history\",\"column\":\"*\"}],\"timeRange\":{\"start\":\"2025-01-29T00:00:00Z\",\"end\":\"2025-02-28T23:59:59Z\"},\"filters\":[{\"field\":\"assistantId\",\"operator\":\"=\",\"value\":\"377ba32d-396b-4f6a-a91a-d45b12db11c7\"}]}]}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:30:30",
    "message": "Starting sync process",
    "data": [],
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:30:30",
    "message": "API Config loaded",
    "data": {
        "url": "https:\/\/api.vapi.ai"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:30:30",
    "message": "Fetching data from API",
    "data": {
        "startDate": "2025-01-29",
        "endDate": "2025-02-28",
        "api_url": "https:\/\/api.vapi.ai"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:30:30",
    "message": "Making API call with query",
    "data": {
        "query": "{\"queries\":[{\"table\":\"call\",\"name\":\"calls_data\",\"operations\":[{\"operation\":\"history\",\"column\":\"*\"}],\"timeRange\":{\"start\":\"2025-01-29T00:00:00Z\",\"end\":\"2025-02-28T23:59:59Z\"},\"filters\":[{\"field\":\"assistantId\",\"operator\":\"=\",\"value\":\"377ba32d-396b-4f6a-a91a-d45b12db11c7\"}]}]}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:30:30",
    "message": "Sync error",
    "data": {
        "error": "API vrátila chybový kód: 400",
        "trace": "#0 \/home\/<USER>\/domains\/dentibot.eu\/public_html\/sync_vapi_data.php(72): makeApiCall()\n#1 \/home\/<USER>\/domains\/dentibot.eu\/public_html\/dashboard.php(77): syncVapiData()\n#2 {main}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:30:30",
    "message": "Starting sync process",
    "data": [],
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:30:30",
    "message": "API Config loaded",
    "data": {
        "url": "https:\/\/api.vapi.ai"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:30:30",
    "message": "Fetching data from API",
    "data": {
        "startDate": "2025-01-29",
        "endDate": "2025-02-28",
        "api_url": "https:\/\/api.vapi.ai"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:30:30",
    "message": "Making API call with query",
    "data": {
        "query": "{\"queries\":[{\"table\":\"call\",\"name\":\"calls_data\",\"operations\":[{\"operation\":\"history\",\"column\":\"*\"}],\"timeRange\":{\"start\":\"2025-01-29T00:00:00Z\",\"end\":\"2025-02-28T23:59:59Z\"},\"filters\":[{\"field\":\"assistantId\",\"operator\":\"=\",\"value\":\"377ba32d-396b-4f6a-a91a-d45b12db11c7\"}]}]}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:30:31",
    "message": "Sync error",
    "data": {
        "error": "API vrátila chybový kód: 400",
        "trace": "#0 \/home\/<USER>\/domains\/dentibot.eu\/public_html\/sync_vapi_data.php(72): makeApiCall()\n#1 \/home\/<USER>\/domains\/dentibot.eu\/public_html\/dashboard.php(77): syncVapiData()\n#2 {main}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:32:57",
    "message": "Starting sync process",
    "data": [],
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:32:57",
    "message": "API Config loaded",
    "data": {
        "url": "https:\/\/api.vapi.ai"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:32:57",
    "message": "Fetching data from API",
    "data": {
        "startDate": "2025-01-29",
        "endDate": "2025-02-28",
        "api_url": "https:\/\/api.vapi.ai"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:32:57",
    "message": "Making API call with query",
    "data": {
        "query": "{\"queries\":[{\"table\":\"call\",\"name\":\"calls_data\",\"operations\":[{\"operation\":\"history\",\"column\":\"*\"}],\"timeRange\":{\"start\":\"2025-01-29T00:00:00Z\",\"end\":\"2025-02-28T23:59:59Z\"},\"filters\":[{\"field\":\"assistantId\",\"operator\":\"=\",\"value\":\"377ba32d-396b-4f6a-a91a-d45b12db11c7\"}]}]}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:32:57",
    "message": "Sync error",
    "data": {
        "error": "API vrátila chybový kód: 400",
        "trace": "#0 \/home\/<USER>\/domains\/dentibot.eu\/public_html\/sync_vapi_data.php(72): makeApiCall()\n#1 \/home\/<USER>\/domains\/dentibot.eu\/public_html\/dashboard.php(77): syncVapiData()\n#2 {main}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:33:20",
    "message": "Starting sync process",
    "data": [],
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:33:20",
    "message": "API Config loaded",
    "data": {
        "url": "https:\/\/api.vapi.ai"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:33:20",
    "message": "Fetching data from API",
    "data": {
        "startDate": "2025-01-29",
        "endDate": "2025-02-28",
        "api_url": "https:\/\/api.vapi.ai"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:33:20",
    "message": "Making API call with query",
    "data": {
        "query": "{\"queries\":[{\"table\":\"call\",\"name\":\"calls_data\",\"operations\":[{\"operation\":\"history\",\"column\":\"*\"}],\"timeRange\":{\"start\":\"2025-01-29T00:00:00Z\",\"end\":\"2025-02-28T23:59:59Z\"},\"filters\":[{\"field\":\"assistantId\",\"operator\":\"=\",\"value\":\"377ba32d-396b-4f6a-a91a-d45b12db11c7\"}]}]}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:33:20",
    "message": "Sync error",
    "data": {
        "error": "API vrátila chybový kód: 400",
        "trace": "#0 \/home\/<USER>\/domains\/dentibot.eu\/public_html\/sync_vapi_data.php(72): makeApiCall()\n#1 \/home\/<USER>\/domains\/dentibot.eu\/public_html\/dashboard.php(77): syncVapiData()\n#2 {main}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:33:22",
    "message": "Starting sync process",
    "data": [],
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:33:22",
    "message": "API Config loaded",
    "data": {
        "url": "https:\/\/api.vapi.ai"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:33:22",
    "message": "Fetching data from API",
    "data": {
        "startDate": "2025-01-29",
        "endDate": "2025-02-28",
        "api_url": "https:\/\/api.vapi.ai"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:33:22",
    "message": "Making API call with query",
    "data": {
        "query": "{\"queries\":[{\"table\":\"call\",\"name\":\"calls_data\",\"operations\":[{\"operation\":\"history\",\"column\":\"*\"}],\"timeRange\":{\"start\":\"2025-01-29T00:00:00Z\",\"end\":\"2025-02-28T23:59:59Z\"},\"filters\":[{\"field\":\"assistantId\",\"operator\":\"=\",\"value\":\"377ba32d-396b-4f6a-a91a-d45b12db11c7\"}]}]}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:33:22",
    "message": "Sync error",
    "data": {
        "error": "API vrátila chybový kód: 400",
        "trace": "#0 \/home\/<USER>\/domains\/dentibot.eu\/public_html\/sync_vapi_data.php(72): makeApiCall()\n#1 \/home\/<USER>\/domains\/dentibot.eu\/public_html\/dashboard.php(77): syncVapiData()\n#2 {main}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:33:23",
    "message": "Starting sync process",
    "data": [],
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:33:23",
    "message": "API Config loaded",
    "data": {
        "url": "https:\/\/api.vapi.ai"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:33:23",
    "message": "Fetching data from API",
    "data": {
        "startDate": "2025-01-29",
        "endDate": "2025-02-28",
        "api_url": "https:\/\/api.vapi.ai"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:33:23",
    "message": "Making API call with query",
    "data": {
        "query": "{\"queries\":[{\"table\":\"call\",\"name\":\"calls_data\",\"operations\":[{\"operation\":\"history\",\"column\":\"*\"}],\"timeRange\":{\"start\":\"2025-01-29T00:00:00Z\",\"end\":\"2025-02-28T23:59:59Z\"},\"filters\":[{\"field\":\"assistantId\",\"operator\":\"=\",\"value\":\"377ba32d-396b-4f6a-a91a-d45b12db11c7\"}]}]}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:33:23",
    "message": "Sync error",
    "data": {
        "error": "API vrátila chybový kód: 400",
        "trace": "#0 \/home\/<USER>\/domains\/dentibot.eu\/public_html\/sync_vapi_data.php(72): makeApiCall()\n#1 \/home\/<USER>\/domains\/dentibot.eu\/public_html\/dashboard.php(77): syncVapiData()\n#2 {main}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:33:24",
    "message": "Starting sync process",
    "data": [],
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:33:24",
    "message": "API Config loaded",
    "data": {
        "url": "https:\/\/api.vapi.ai"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:33:24",
    "message": "Fetching data from API",
    "data": {
        "startDate": "2025-01-29",
        "endDate": "2025-02-28",
        "api_url": "https:\/\/api.vapi.ai"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:33:24",
    "message": "Making API call with query",
    "data": {
        "query": "{\"queries\":[{\"table\":\"call\",\"name\":\"calls_data\",\"operations\":[{\"operation\":\"history\",\"column\":\"*\"}],\"timeRange\":{\"start\":\"2025-01-29T00:00:00Z\",\"end\":\"2025-02-28T23:59:59Z\"},\"filters\":[{\"field\":\"assistantId\",\"operator\":\"=\",\"value\":\"377ba32d-396b-4f6a-a91a-d45b12db11c7\"}]}]}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:33:24",
    "message": "Sync error",
    "data": {
        "error": "API vrátila chybový kód: 400",
        "trace": "#0 \/home\/<USER>\/domains\/dentibot.eu\/public_html\/sync_vapi_data.php(72): makeApiCall()\n#1 \/home\/<USER>\/domains\/dentibot.eu\/public_html\/dashboard.php(77): syncVapiData()\n#2 {main}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:33:25",
    "message": "Starting sync process",
    "data": [],
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:33:25",
    "message": "API Config loaded",
    "data": {
        "url": "https:\/\/api.vapi.ai"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:33:25",
    "message": "Fetching data from API",
    "data": {
        "startDate": "2025-01-29",
        "endDate": "2025-02-28",
        "api_url": "https:\/\/api.vapi.ai"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:33:25",
    "message": "Making API call with query",
    "data": {
        "query": "{\"queries\":[{\"table\":\"call\",\"name\":\"calls_data\",\"operations\":[{\"operation\":\"history\",\"column\":\"*\"}],\"timeRange\":{\"start\":\"2025-01-29T00:00:00Z\",\"end\":\"2025-02-28T23:59:59Z\"},\"filters\":[{\"field\":\"assistantId\",\"operator\":\"=\",\"value\":\"377ba32d-396b-4f6a-a91a-d45b12db11c7\"}]}]}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:33:25",
    "message": "Sync error",
    "data": {
        "error": "API vrátila chybový kód: 400",
        "trace": "#0 \/home\/<USER>\/domains\/dentibot.eu\/public_html\/sync_vapi_data.php(72): makeApiCall()\n#1 \/home\/<USER>\/domains\/dentibot.eu\/public_html\/dashboard.php(77): syncVapiData()\n#2 {main}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:37:11",
    "message": "Starting sync process",
    "data": [],
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:37:11",
    "message": "API Config loaded",
    "data": {
        "url": "https:\/\/api.vapi.ai"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:37:11",
    "message": "Fetching data from API",
    "data": {
        "startDate": "2025-01-29",
        "endDate": "2025-02-28",
        "api_url": "https:\/\/api.vapi.ai"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:37:11",
    "message": "Making API call with query",
    "data": {
        "query": "{\"queries\":[{\"table\":\"call\",\"name\":\"calls_data\",\"operations\":[{\"operation\":\"history\",\"column\":\"*\"}],\"timeRange\":{\"start\":\"2025-01-29T00:00:00Z\",\"end\":\"2025-02-28T23:59:59Z\"},\"filters\":[{\"field\":\"assistantId\",\"operator\":\"=\",\"value\":\"377ba32d-396b-4f6a-a91a-d45b12db11c7\"}]}]}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:37:11",
    "message": "Starting makeApiCall",
    "data": {
        "endpoint": "analytics",
        "data_sample": "{\"queries\":[{\"table\":\"call\",\"name\":\"calls_data\",\"operations\":[{\"operation\":\"history\",\"column\":\"*\"}],\"timeRange\":{\"start\":\"2025-01-29T00:00:00Z\",\"end\":\"2025-02-28T23:59:59Z\"},\"filters\":[{\"field\":\"assistantId\",\"operator\":\"=\",\"value\":\"377ba32d-396b-4f6a-a91a-d45b12db11c7\"}]}]}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:37:11",
    "message": "Checking usage limits",
    "data": {
        "usage": 0,
        "limit": 500
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:37:11",
    "message": "API configuration loaded",
    "data": {
        "url": "https:\/\/api.vapi.ai",
        "has_key": true
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:37:11",
    "message": "Preparing analytics API request",
    "data": {
        "url": "https:\/\/api.vapi.ai\/analytics",
        "request_body": "{\"queries\":[{\"table\":\"call\",\"name\":\"calls_data\",\"operations\":[{\"operation\":\"history\",\"column\":\"*\"}],\"timeRange\":{\"start\":\"2025-01-29T00:00:00Z\",\"end\":\"2025-02-28T23:59:59Z\"},\"filters\":[{\"field\":\"assistantId\",\"operator\":\"=\",\"value\":\"377ba32d-396b-4f6a-a91a-d45b12db11c7\"}]}]}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:37:12",
    "message": "API Response Info",
    "data": {
        "http_code": 400,
        "headers": "HTTP\/2 400 \r\ndate: Fri, 28 Feb 2025 01:37:12 GMT\r\ncontent-type: application\/json; charset=utf-8\r\ncontent-length: 601\r\nx-powered-by: Express\r\naccess-control-allow-origin: *\r\netag: W\/\"259-MvPs2YiKvAYkYhFFydotsJbf72o\"\r\ncf-cache-status: DYNAMIC\r\nset-cookie: _cfuvid=kRPYTIXkmmzenTwrcnI2Z0eZwrXJWJdMJWR6.CY3FLQ-1740706632505-*******-604800000; path=\/; domain=.vapi.ai; HttpOnly; Secure; SameSite=None\r\nserver: cloudflare\r\ncf-ray: 918cb0223e9cdcaa-FRA\r\nalt-svc: h3=\":443\"; ma=86400\r\n\r\n",
        "error": "",
        "verbose_log": "*   Trying 2606:4700::6812:1940:443...\n* Connected to api.vapi.ai (2606:4700::6812:1940) port 443 (#0)\n* ALPN, offering h2\n* ALPN, offering http\/1.1\n*  CAfile: \/etc\/pki\/tls\/certs\/ca-bundle.crt\n* SSL connection using TLSv1.3 \/ TLS_AES_256_GCM_SHA384\n* ALPN, server accepted to use h2\n* Server certificate:\n*  subject: CN=api.vapi.ai\n*  start date: Jan 12 19:44:51 2025 GMT\n*  expire date: Apr 12 20:44:49 2025 GMT\n*  subjectAltName: host \"api.vapi.ai\" matched cert's \"api.vapi.ai\"\n*  issuer: C=US; O=Google Trust Services; CN=WE1\n*  SSL certificate verify ok.\n* Using HTTP2, server supports multi-use\n* Connection state changed (HTTP\/2 confirmed)\n* Copying HTTP\/2 data in stream buffer to connection buffer after upgrade: len=0\n* Using Stream ID: 1 (easy handle 0x2d53d50)\n> POST \/analytics HTTP\/2\r\nHost: api.vapi.ai\r\nauthorization: Bearer a88d8eee-c2a2-4f4a-936d-676a6375ba20\r\ncontent-type: application\/json\r\naccept: application\/json\r\ncontent-length: 274\r\n\r\n* We are completely uploaded and fine\n* old SSL session ID is stale, removing\n< HTTP\/2 400 \r\n< date: Fri, 28 Feb 2025 01:37:12 GMT\r\n< content-type: application\/json; charset=utf-8\r\n< content-length: 601\r\n< x-powered-by: Express\r\n< access-control-allow-origin: *\r\n< etag: W\/\"259-MvPs2YiKvAYkYhFFydotsJbf72o\"\r\n< cf-cache-status: DYNAMIC\r\n< set-cookie: _cfuvid=kRPYTIXkmmzenTwrcnI2Z0eZwrXJWJdMJWR6.CY3FLQ-1740706632505-*******-604800000; path=\/; domain=.vapi.ai; HttpOnly; Secure; SameSite=None\r\n< server: cloudflare\r\n< cf-ray: 918cb0223e9cdcaa-FRA\r\n< alt-svc: h3=\":443\"; ma=86400\r\n< \r\n* Connection #0 to host api.vapi.ai left intact\n",
        "response_body": "{\"message\":[\"queries.0.property filters should not exist\",\"queries.0.operations.0.operation can't be `history` for column `*`. `history` supports concurrency, minutesUsed columns\",\"queries.0.operations.0.column must be one of the following values: id, cost, costBreakdown.llm, costBreakdown.stt, costBreakdown.tts, costBreakdown.vapi, costBreakdown.ttsCharacters, costBreakdown.llmPromptTokens, costBreakdown.llmCompletionTokens, duration, concurrency, minutesUsed\",\"queries.0.operations requires a 'step' parameter in timeRange when using 'history' operation\"],\"error\":\"Bad Request\",\"statusCode\":400}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:37:12",
    "message": "API Bad Request",
    "data": {
        "response": "{\"message\":[\"queries.0.property filters should not exist\",\"queries.0.operations.0.operation can't be `history` for column `*`. `history` supports concurrency, minutesUsed columns\",\"queries.0.operations.0.column must be one of the following values: id, cost, costBreakdown.llm, costBreakdown.stt, costBreakdown.tts, costBreakdown.vapi, costBreakdown.ttsCharacters, costBreakdown.llmPromptTokens, costBreakdown.llmCompletionTokens, duration, concurrency, minutesUsed\",\"queries.0.operations requires a 'step' parameter in timeRange when using 'history' operation\"],\"error\":\"Bad Request\",\"statusCode\":400}",
        "request_url": "https:\/\/api.vapi.ai\/analytics"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:37:12",
    "message": "Error in makeApiCall",
    "data": {
        "error": "Neplatný požadavek na API. Zkontrolujte formát dat.",
        "trace": "#0 \/home\/<USER>\/domains\/dentibot.eu\/public_html\/sync_vapi_data.php(72): makeApiCall()\n#1 \/home\/<USER>\/domains\/dentibot.eu\/public_html\/dashboard.php(77): syncVapiData()\n#2 {main}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:37:12",
    "message": "Sync error",
    "data": {
        "error": "Neplatný požadavek na API. Zkontrolujte formát dat.",
        "trace": "#0 \/home\/<USER>\/domains\/dentibot.eu\/public_html\/sync_vapi_data.php(72): makeApiCall()\n#1 \/home\/<USER>\/domains\/dentibot.eu\/public_html\/dashboard.php(77): syncVapiData()\n#2 {main}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:37:12",
    "message": "Starting makeApiCall",
    "data": {
        "endpoint": "analytics",
        "data_sample": "{\"queries\":[{\"table\":\"call\",\"name\":\"call_metrics\",\"operations\":[{\"operation\":\"count\",\"column\":\"id\",\"alias\":\"total_calls\"},{\"operation\":\"sum\",\"column\":\"duration\",\"alias\":\"total_duration\"},{\"operation\":\"sum\",\"column\":\"cost\",\"alias\":\"total_cost\"}],\"timeRange\":{\"step\":\"day\",\"start\":\"2025-01-29T02:37:12+01:00\",\"end\":\"2025-02-28T02:37:12+01:00\",\"timezone\":\"UTC\"},\"groupBy\":[\"assistantId\"]},{\"table\":\"call\",\"name\":\"peak_hours\",\"operations\":[{\"operation\":\"count\",\"column\":\"id\",\"alias\":\"total_calls\"}],\"groupBy\":[\"hour\",\"assistantId\"],\"timeRange\":{\"step\":\"hour\",\"start\":\"2025-01-29T02:37:12+01:00\",\"end\":\"2025-02-28T02:37:12+01:00\",\"timezone\":\"UTC\"}},{\"table\":\"call\",\"name\":\"end_reasons\",\"operations\":[{\"operation\":\"count\",\"column\":\"id\",\"alias\":\"reason_count\"}],\"groupBy\":[\"endedReason\",\"assistantId\"],\"timeRange\":{\"start\":\"2025-01-29T02:37:12+01:00\",\"end\":\"2025-02-28T02:37:12+01:00\",\"timezone\":\"UTC\"}}]}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:37:12",
    "message": "Checking usage limits",
    "data": {
        "usage": 0,
        "limit": 500
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:37:12",
    "message": "API configuration loaded",
    "data": {
        "url": "https:\/\/api.vapi.ai",
        "has_key": true
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:37:12",
    "message": "Preparing analytics API request",
    "data": {
        "url": "https:\/\/api.vapi.ai\/analytics",
        "request_body": "{\"queries\":[{\"table\":\"call\",\"name\":\"call_metrics\",\"operations\":[{\"operation\":\"count\",\"column\":\"id\",\"alias\":\"total_calls\"},{\"operation\":\"sum\",\"column\":\"duration\",\"alias\":\"total_duration\"},{\"operation\":\"sum\",\"column\":\"cost\",\"alias\":\"total_cost\"}],\"timeRange\":{\"step\":\"day\",\"start\":\"2025-01-29T02:37:12+01:00\",\"end\":\"2025-02-28T02:37:12+01:00\",\"timezone\":\"UTC\"},\"groupBy\":[\"assistantId\"]},{\"table\":\"call\",\"name\":\"peak_hours\",\"operations\":[{\"operation\":\"count\",\"column\":\"id\",\"alias\":\"total_calls\"}],\"groupBy\":[\"hour\",\"assistantId\"],\"timeRange\":{\"step\":\"hour\",\"start\":\"2025-01-29T02:37:12+01:00\",\"end\":\"2025-02-28T02:37:12+01:00\",\"timezone\":\"UTC\"}},{\"table\":\"call\",\"name\":\"end_reasons\",\"operations\":[{\"operation\":\"count\",\"column\":\"id\",\"alias\":\"reason_count\"}],\"groupBy\":[\"endedReason\",\"assistantId\"],\"timeRange\":{\"start\":\"2025-01-29T02:37:12+01:00\",\"end\":\"2025-02-28T02:37:12+01:00\",\"timezone\":\"UTC\"}}]}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:37:12",
    "message": "API Response Info",
    "data": {
        "http_code": 400,
        "headers": "HTTP\/2 400 \r\ndate: Fri, 28 Feb 2025 01:37:12 GMT\r\ncontent-type: application\/json; charset=utf-8\r\ncontent-length: 190\r\nx-powered-by: Express\r\naccess-control-allow-origin: *\r\netag: W\/\"be-DvYaTNjmcc0zIZ4AnDWuZ3fm6Mo\"\r\ncf-cache-status: DYNAMIC\r\nset-cookie: _cfuvid=UC6Iuj_uCcDouIibQ5druqtKA0lgSAeNahmKChbVrfc-1740706632721-*******-604800000; path=\/; domain=.vapi.ai; HttpOnly; Secure; SameSite=None\r\nserver: cloudflare\r\ncf-ray: 918cb0255af18f3e-FRA\r\nalt-svc: h3=\":443\"; ma=86400\r\n\r\n",
        "error": "",
        "verbose_log": "*   Trying 2606:4700::6812:1940:443...\n* Connected to api.vapi.ai (2606:4700::6812:1940) port 443 (#0)\n* ALPN, offering h2\n* ALPN, offering http\/1.1\n*  CAfile: \/etc\/pki\/tls\/certs\/ca-bundle.crt\n* SSL connection using TLSv1.3 \/ TLS_AES_256_GCM_SHA384\n* ALPN, server accepted to use h2\n* Server certificate:\n*  subject: CN=api.vapi.ai\n*  start date: Jan 12 19:44:51 2025 GMT\n*  expire date: Apr 12 20:44:49 2025 GMT\n*  subjectAltName: host \"api.vapi.ai\" matched cert's \"api.vapi.ai\"\n*  issuer: C=US; O=Google Trust Services; CN=WE1\n*  SSL certificate verify ok.\n* Using HTTP2, server supports multi-use\n* Connection state changed (HTTP\/2 confirmed)\n* Copying HTTP\/2 data in stream buffer to connection buffer after upgrade: len=0\n* Using Stream ID: 1 (easy handle 0x2e3c120)\n> POST \/analytics HTTP\/2\r\nHost: api.vapi.ai\r\nauthorization: Bearer a88d8eee-c2a2-4f4a-936d-676a6375ba20\r\ncontent-type: application\/json\r\naccept: application\/json\r\ncontent-length: 898\r\n\r\n* We are completely uploaded and fine\n* old SSL session ID is stale, removing\n< HTTP\/2 400 \r\n< date: Fri, 28 Feb 2025 01:37:12 GMT\r\n< content-type: application\/json; charset=utf-8\r\n< content-length: 190\r\n< x-powered-by: Express\r\n< access-control-allow-origin: *\r\n< etag: W\/\"be-DvYaTNjmcc0zIZ4AnDWuZ3fm6Mo\"\r\n< cf-cache-status: DYNAMIC\r\n< set-cookie: _cfuvid=UC6Iuj_uCcDouIibQ5druqtKA0lgSAeNahmKChbVrfc-1740706632721-*******-604800000; path=\/; domain=.vapi.ai; HttpOnly; Secure; SameSite=None\r\n< server: cloudflare\r\n< cf-ray: 918cb0255af18f3e-FRA\r\n< alt-svc: h3=\":443\"; ma=86400\r\n< \r\n* Connection #0 to host api.vapi.ai left intact\n",
        "response_body": "{\"message\":[\"queries.1.each value in groupBy must be one of the following values: type, assistantId, endedReason, analysis.successEvaluation, status\"],\"error\":\"Bad Request\",\"statusCode\":400}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:37:12",
    "message": "API Bad Request",
    "data": {
        "response": "{\"message\":[\"queries.1.each value in groupBy must be one of the following values: type, assistantId, endedReason, analysis.successEvaluation, status\"],\"error\":\"Bad Request\",\"statusCode\":400}",
        "request_url": "https:\/\/api.vapi.ai\/analytics"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:37:12",
    "message": "Error in makeApiCall",
    "data": {
        "error": "Neplatný požadavek na API. Zkontrolujte formát dat.",
        "trace": "#0 \/home\/<USER>\/domains\/dentibot.eu\/public_html\/dashboard.php(203): makeApiCall()\n#1 {main}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:38:27",
    "message": "Starting sync process",
    "data": [],
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:38:27",
    "message": "API Config loaded",
    "data": {
        "url": "https:\/\/api.vapi.ai"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:38:27",
    "message": "Fetching data from API",
    "data": {
        "startDate": "2025-01-29",
        "endDate": "2025-02-28",
        "api_url": "https:\/\/api.vapi.ai"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:38:27",
    "message": "Making API call with query",
    "data": {
        "query": "{\"queries\":[{\"table\":\"call\",\"name\":\"calls_data\",\"operations\":[{\"operation\":\"history\",\"column\":\"*\"}],\"timeRange\":{\"start\":\"2025-01-29T00:00:00Z\",\"end\":\"2025-02-28T23:59:59Z\"},\"filters\":[{\"field\":\"assistantId\",\"operator\":\"=\",\"value\":\"377ba32d-396b-4f6a-a91a-d45b12db11c7\"}]}]}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:38:27",
    "message": "Starting makeApiCall",
    "data": {
        "endpoint": "analytics",
        "data_sample": "{\"queries\":[{\"table\":\"call\",\"name\":\"calls_data\",\"operations\":[{\"operation\":\"history\",\"column\":\"*\"}],\"timeRange\":{\"start\":\"2025-01-29T00:00:00Z\",\"end\":\"2025-02-28T23:59:59Z\"},\"filters\":[{\"field\":\"assistantId\",\"operator\":\"=\",\"value\":\"377ba32d-396b-4f6a-a91a-d45b12db11c7\"}]}]}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:38:27",
    "message": "Checking usage limits",
    "data": {
        "usage": 0,
        "limit": 500
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:38:27",
    "message": "API configuration loaded",
    "data": {
        "url": "https:\/\/api.vapi.ai",
        "has_key": true
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:38:27",
    "message": "Preparing analytics API request",
    "data": {
        "url": "https:\/\/api.vapi.ai\/analytics",
        "request_body": "{\"queries\":[{\"table\":\"call\",\"name\":\"calls_data\",\"operations\":[{\"operation\":\"history\",\"column\":\"*\"}],\"timeRange\":{\"start\":\"2025-01-29T00:00:00Z\",\"end\":\"2025-02-28T23:59:59Z\"},\"filters\":[{\"field\":\"assistantId\",\"operator\":\"=\",\"value\":\"377ba32d-396b-4f6a-a91a-d45b12db11c7\"}]}]}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:38:28",
    "message": "API Response Info",
    "data": {
        "http_code": 400,
        "headers": "HTTP\/2 400 \r\ndate: Fri, 28 Feb 2025 01:38:28 GMT\r\ncontent-type: application\/json; charset=utf-8\r\ncontent-length: 601\r\nx-powered-by: Express\r\naccess-control-allow-origin: *\r\netag: W\/\"259-MvPs2YiKvAYkYhFFydotsJbf72o\"\r\ncf-cache-status: DYNAMIC\r\nset-cookie: _cfuvid=Z0e6s73AaCaxoQwU4LtSHGJJW0oE39DaD10yyl_kbes-1740706708327-*******-604800000; path=\/; domain=.vapi.ai; HttpOnly; Secure; SameSite=None\r\nserver: cloudflare\r\ncf-ray: 918cb1fd1b10e864-FRA\r\nalt-svc: h3=\":443\"; ma=86400\r\n\r\n",
        "error": "",
        "verbose_log": "*   Trying 2606:4700::6812:1940:443...\n* Connected to api.vapi.ai (2606:4700::6812:1940) port 443 (#0)\n* ALPN, offering h2\n* ALPN, offering http\/1.1\n*  CAfile: \/etc\/pki\/tls\/certs\/ca-bundle.crt\n* SSL connection using TLSv1.3 \/ TLS_AES_256_GCM_SHA384\n* ALPN, server accepted to use h2\n* Server certificate:\n*  subject: CN=api.vapi.ai\n*  start date: Jan 12 19:44:51 2025 GMT\n*  expire date: Apr 12 20:44:49 2025 GMT\n*  subjectAltName: host \"api.vapi.ai\" matched cert's \"api.vapi.ai\"\n*  issuer: C=US; O=Google Trust Services; CN=WE1\n*  SSL certificate verify ok.\n* Using HTTP2, server supports multi-use\n* Connection state changed (HTTP\/2 confirmed)\n* Copying HTTP\/2 data in stream buffer to connection buffer after upgrade: len=0\n* Using Stream ID: 1 (easy handle 0x2d53d50)\n> POST \/analytics HTTP\/2\r\nHost: api.vapi.ai\r\nauthorization: Bearer a88d8eee-c2a2-4f4a-936d-676a6375ba20\r\ncontent-type: application\/json\r\naccept: application\/json\r\ncontent-length: 274\r\n\r\n* We are completely uploaded and fine\n* old SSL session ID is stale, removing\n< HTTP\/2 400 \r\n< date: Fri, 28 Feb 2025 01:38:28 GMT\r\n< content-type: application\/json; charset=utf-8\r\n< content-length: 601\r\n< x-powered-by: Express\r\n< access-control-allow-origin: *\r\n< etag: W\/\"259-MvPs2YiKvAYkYhFFydotsJbf72o\"\r\n< cf-cache-status: DYNAMIC\r\n< set-cookie: _cfuvid=Z0e6s73AaCaxoQwU4LtSHGJJW0oE39DaD10yyl_kbes-1740706708327-*******-604800000; path=\/; domain=.vapi.ai; HttpOnly; Secure; SameSite=None\r\n< server: cloudflare\r\n< cf-ray: 918cb1fd1b10e864-FRA\r\n< alt-svc: h3=\":443\"; ma=86400\r\n< \r\n* Connection #0 to host api.vapi.ai left intact\n",
        "response_body": "{\"message\":[\"queries.0.property filters should not exist\",\"queries.0.operations.0.operation can't be `history` for column `*`. `history` supports concurrency, minutesUsed columns\",\"queries.0.operations.0.column must be one of the following values: id, cost, costBreakdown.llm, costBreakdown.stt, costBreakdown.tts, costBreakdown.vapi, costBreakdown.ttsCharacters, costBreakdown.llmPromptTokens, costBreakdown.llmCompletionTokens, duration, concurrency, minutesUsed\",\"queries.0.operations requires a 'step' parameter in timeRange when using 'history' operation\"],\"error\":\"Bad Request\",\"statusCode\":400}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:38:28",
    "message": "API Bad Request",
    "data": {
        "response": "{\"message\":[\"queries.0.property filters should not exist\",\"queries.0.operations.0.operation can't be `history` for column `*`. `history` supports concurrency, minutesUsed columns\",\"queries.0.operations.0.column must be one of the following values: id, cost, costBreakdown.llm, costBreakdown.stt, costBreakdown.tts, costBreakdown.vapi, costBreakdown.ttsCharacters, costBreakdown.llmPromptTokens, costBreakdown.llmCompletionTokens, duration, concurrency, minutesUsed\",\"queries.0.operations requires a 'step' parameter in timeRange when using 'history' operation\"],\"error\":\"Bad Request\",\"statusCode\":400}",
        "request_url": "https:\/\/api.vapi.ai\/analytics"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:38:28",
    "message": "Error in makeApiCall",
    "data": {
        "error": "Neplatný požadavek na API. Zkontrolujte formát dat.",
        "trace": "#0 \/home\/<USER>\/domains\/dentibot.eu\/public_html\/sync_vapi_data.php(72): makeApiCall()\n#1 \/home\/<USER>\/domains\/dentibot.eu\/public_html\/dashboard.php(77): syncVapiData()\n#2 {main}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:38:28",
    "message": "Sync error",
    "data": {
        "error": "Neplatný požadavek na API. Zkontrolujte formát dat.",
        "trace": "#0 \/home\/<USER>\/domains\/dentibot.eu\/public_html\/sync_vapi_data.php(72): makeApiCall()\n#1 \/home\/<USER>\/domains\/dentibot.eu\/public_html\/dashboard.php(77): syncVapiData()\n#2 {main}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:38:28",
    "message": "Starting makeApiCall",
    "data": {
        "endpoint": "analytics",
        "data_sample": "{\"queries\":[{\"table\":\"call\",\"name\":\"call_metrics\",\"operations\":[{\"operation\":\"count\",\"column\":\"id\",\"alias\":\"total_calls\"},{\"operation\":\"sum\",\"column\":\"duration\",\"alias\":\"total_duration\"},{\"operation\":\"sum\",\"column\":\"cost\",\"alias\":\"total_cost\"}],\"timeRange\":{\"step\":\"day\",\"start\":\"2025-01-29T02:38:28+01:00\",\"end\":\"2025-02-28T02:38:28+01:00\",\"timezone\":\"UTC\"},\"groupBy\":[\"assistantId\"]},{\"table\":\"call\",\"name\":\"peak_hours\",\"operations\":[{\"operation\":\"count\",\"column\":\"id\",\"alias\":\"total_calls\"}],\"groupBy\":[\"hour\",\"assistantId\"],\"timeRange\":{\"step\":\"hour\",\"start\":\"2025-01-29T02:38:28+01:00\",\"end\":\"2025-02-28T02:38:28+01:00\",\"timezone\":\"UTC\"}},{\"table\":\"call\",\"name\":\"end_reasons\",\"operations\":[{\"operation\":\"count\",\"column\":\"id\",\"alias\":\"reason_count\"}],\"groupBy\":[\"endedReason\",\"assistantId\"],\"timeRange\":{\"start\":\"2025-01-29T02:38:28+01:00\",\"end\":\"2025-02-28T02:38:28+01:00\",\"timezone\":\"UTC\"}}]}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:38:28",
    "message": "Checking usage limits",
    "data": {
        "usage": 0,
        "limit": 500
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:38:28",
    "message": "API configuration loaded",
    "data": {
        "url": "https:\/\/api.vapi.ai",
        "has_key": true
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:38:28",
    "message": "Preparing analytics API request",
    "data": {
        "url": "https:\/\/api.vapi.ai\/analytics",
        "request_body": "{\"queries\":[{\"table\":\"call\",\"name\":\"call_metrics\",\"operations\":[{\"operation\":\"count\",\"column\":\"id\",\"alias\":\"total_calls\"},{\"operation\":\"sum\",\"column\":\"duration\",\"alias\":\"total_duration\"},{\"operation\":\"sum\",\"column\":\"cost\",\"alias\":\"total_cost\"}],\"timeRange\":{\"step\":\"day\",\"start\":\"2025-01-29T02:38:28+01:00\",\"end\":\"2025-02-28T02:38:28+01:00\",\"timezone\":\"UTC\"},\"groupBy\":[\"assistantId\"]},{\"table\":\"call\",\"name\":\"peak_hours\",\"operations\":[{\"operation\":\"count\",\"column\":\"id\",\"alias\":\"total_calls\"}],\"groupBy\":[\"hour\",\"assistantId\"],\"timeRange\":{\"step\":\"hour\",\"start\":\"2025-01-29T02:38:28+01:00\",\"end\":\"2025-02-28T02:38:28+01:00\",\"timezone\":\"UTC\"}},{\"table\":\"call\",\"name\":\"end_reasons\",\"operations\":[{\"operation\":\"count\",\"column\":\"id\",\"alias\":\"reason_count\"}],\"groupBy\":[\"endedReason\",\"assistantId\"],\"timeRange\":{\"start\":\"2025-01-29T02:38:28+01:00\",\"end\":\"2025-02-28T02:38:28+01:00\",\"timezone\":\"UTC\"}}]}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:38:28",
    "message": "API Response Info",
    "data": {
        "http_code": 400,
        "headers": "HTTP\/2 400 \r\ndate: Fri, 28 Feb 2025 01:38:28 GMT\r\ncontent-type: application\/json; charset=utf-8\r\ncontent-length: 190\r\nx-powered-by: Express\r\naccess-control-allow-origin: *\r\netag: W\/\"be-DvYaTNjmcc0zIZ4AnDWuZ3fm6Mo\"\r\ncf-cache-status: DYNAMIC\r\nset-cookie: _cfuvid=TqJ5SaU0VPxzu21Xdc5qWdoi3kvwy6GdJYAH5KfP0bY-1740706708904-*******-604800000; path=\/; domain=.vapi.ai; HttpOnly; Secure; SameSite=None\r\nserver: cloudflare\r\ncf-ray: 918cb1ff3b35db0b-FRA\r\nalt-svc: h3=\":443\"; ma=86400\r\n\r\n",
        "error": "",
        "verbose_log": "*   Trying 2606:4700::6812:1940:443...\n* Connected to api.vapi.ai (2606:4700::6812:1940) port 443 (#0)\n* ALPN, offering h2\n* ALPN, offering http\/1.1\n*  CAfile: \/etc\/pki\/tls\/certs\/ca-bundle.crt\n* SSL connection using TLSv1.3 \/ TLS_AES_256_GCM_SHA384\n* ALPN, server accepted to use h2\n* Server certificate:\n*  subject: CN=api.vapi.ai\n*  start date: Jan 12 19:44:51 2025 GMT\n*  expire date: Apr 12 20:44:49 2025 GMT\n*  subjectAltName: host \"api.vapi.ai\" matched cert's \"api.vapi.ai\"\n*  issuer: C=US; O=Google Trust Services; CN=WE1\n*  SSL certificate verify ok.\n* Using HTTP2, server supports multi-use\n* Connection state changed (HTTP\/2 confirmed)\n* Copying HTTP\/2 data in stream buffer to connection buffer after upgrade: len=0\n* Using Stream ID: 1 (easy handle 0x2e3c120)\n> POST \/analytics HTTP\/2\r\nHost: api.vapi.ai\r\nauthorization: Bearer a88d8eee-c2a2-4f4a-936d-676a6375ba20\r\ncontent-type: application\/json\r\naccept: application\/json\r\ncontent-length: 898\r\n\r\n* We are completely uploaded and fine\n* old SSL session ID is stale, removing\n< HTTP\/2 400 \r\n< date: Fri, 28 Feb 2025 01:38:28 GMT\r\n< content-type: application\/json; charset=utf-8\r\n< content-length: 190\r\n< x-powered-by: Express\r\n< access-control-allow-origin: *\r\n< etag: W\/\"be-DvYaTNjmcc0zIZ4AnDWuZ3fm6Mo\"\r\n< cf-cache-status: DYNAMIC\r\n< set-cookie: _cfuvid=TqJ5SaU0VPxzu21Xdc5qWdoi3kvwy6GdJYAH5KfP0bY-1740706708904-*******-604800000; path=\/; domain=.vapi.ai; HttpOnly; Secure; SameSite=None\r\n< server: cloudflare\r\n< cf-ray: 918cb1ff3b35db0b-FRA\r\n< alt-svc: h3=\":443\"; ma=86400\r\n< \r\n* Connection #0 to host api.vapi.ai left intact\n",
        "response_body": "{\"message\":[\"queries.1.each value in groupBy must be one of the following values: type, assistantId, endedReason, analysis.successEvaluation, status\"],\"error\":\"Bad Request\",\"statusCode\":400}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:38:28",
    "message": "API Bad Request",
    "data": {
        "response": "{\"message\":[\"queries.1.each value in groupBy must be one of the following values: type, assistantId, endedReason, analysis.successEvaluation, status\"],\"error\":\"Bad Request\",\"statusCode\":400}",
        "request_url": "https:\/\/api.vapi.ai\/analytics"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:38:28",
    "message": "Error in makeApiCall",
    "data": {
        "error": "Neplatný požadavek na API. Zkontrolujte formát dat.",
        "trace": "#0 \/home\/<USER>\/domains\/dentibot.eu\/public_html\/dashboard.php(203): makeApiCall()\n#1 {main}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:38:30",
    "message": "Starting sync process",
    "data": [],
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:38:30",
    "message": "API Config loaded",
    "data": {
        "url": "https:\/\/api.vapi.ai"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:38:30",
    "message": "Fetching data from API",
    "data": {
        "startDate": "2025-01-29",
        "endDate": "2025-02-28",
        "api_url": "https:\/\/api.vapi.ai"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:38:30",
    "message": "Making API call with query",
    "data": {
        "query": "{\"queries\":[{\"table\":\"call\",\"name\":\"calls_data\",\"operations\":[{\"operation\":\"history\",\"column\":\"*\"}],\"timeRange\":{\"start\":\"2025-01-29T00:00:00Z\",\"end\":\"2025-02-28T23:59:59Z\"},\"filters\":[{\"field\":\"assistantId\",\"operator\":\"=\",\"value\":\"377ba32d-396b-4f6a-a91a-d45b12db11c7\"}]}]}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:38:30",
    "message": "Starting makeApiCall",
    "data": {
        "endpoint": "analytics",
        "data_sample": "{\"queries\":[{\"table\":\"call\",\"name\":\"calls_data\",\"operations\":[{\"operation\":\"history\",\"column\":\"*\"}],\"timeRange\":{\"start\":\"2025-01-29T00:00:00Z\",\"end\":\"2025-02-28T23:59:59Z\"},\"filters\":[{\"field\":\"assistantId\",\"operator\":\"=\",\"value\":\"377ba32d-396b-4f6a-a91a-d45b12db11c7\"}]}]}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:38:30",
    "message": "Checking usage limits",
    "data": {
        "usage": 0,
        "limit": 500
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:38:30",
    "message": "API configuration loaded",
    "data": {
        "url": "https:\/\/api.vapi.ai",
        "has_key": true
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:38:30",
    "message": "Preparing analytics API request",
    "data": {
        "url": "https:\/\/api.vapi.ai\/analytics",
        "request_body": "{\"queries\":[{\"table\":\"call\",\"name\":\"calls_data\",\"operations\":[{\"operation\":\"history\",\"column\":\"*\"}],\"timeRange\":{\"start\":\"2025-01-29T00:00:00Z\",\"end\":\"2025-02-28T23:59:59Z\"},\"filters\":[{\"field\":\"assistantId\",\"operator\":\"=\",\"value\":\"377ba32d-396b-4f6a-a91a-d45b12db11c7\"}]}]}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:38:30",
    "message": "API Response Info",
    "data": {
        "http_code": 400,
        "headers": "HTTP\/2 400 \r\ndate: Fri, 28 Feb 2025 01:38:30 GMT\r\ncontent-type: application\/json; charset=utf-8\r\ncontent-length: 601\r\nx-powered-by: Express\r\naccess-control-allow-origin: *\r\netag: W\/\"259-MvPs2YiKvAYkYhFFydotsJbf72o\"\r\ncf-cache-status: DYNAMIC\r\nset-cookie: _cfuvid=4Ru9KhY3e6ucR3AlPbt2_J7E6lzkl.22Zdrmg_Z5Dpk-1740706710467-*******-604800000; path=\/; domain=.vapi.ai; HttpOnly; Secure; SameSite=None\r\nserver: cloudflare\r\ncf-ray: 918cb20a0b3b66da-AMS\r\nalt-svc: h3=\":443\"; ma=86400\r\n\r\n",
        "error": "",
        "verbose_log": "*   Trying 2606:4700::6812:1940:443...\n* Connected to api.vapi.ai (2606:4700::6812:1940) port 443 (#0)\n* ALPN, offering h2\n* ALPN, offering http\/1.1\n*  CAfile: \/etc\/pki\/tls\/certs\/ca-bundle.crt\n* SSL connection using TLSv1.3 \/ TLS_AES_256_GCM_SHA384\n* ALPN, server accepted to use h2\n* Server certificate:\n*  subject: CN=api.vapi.ai\n*  start date: Jan 12 19:44:51 2025 GMT\n*  expire date: Apr 12 20:44:49 2025 GMT\n*  subjectAltName: host \"api.vapi.ai\" matched cert's \"api.vapi.ai\"\n*  issuer: C=US; O=Google Trust Services; CN=WE1\n*  SSL certificate verify ok.\n* Using HTTP2, server supports multi-use\n* Connection state changed (HTTP\/2 confirmed)\n* Copying HTTP\/2 data in stream buffer to connection buffer after upgrade: len=0\n* Using Stream ID: 1 (easy handle 0x2df4cc0)\n> POST \/analytics HTTP\/2\r\nHost: api.vapi.ai\r\nauthorization: Bearer a88d8eee-c2a2-4f4a-936d-676a6375ba20\r\ncontent-type: application\/json\r\naccept: application\/json\r\ncontent-length: 274\r\n\r\n* We are completely uploaded and fine\n* old SSL session ID is stale, removing\n< HTTP\/2 400 \r\n< date: Fri, 28 Feb 2025 01:38:30 GMT\r\n< content-type: application\/json; charset=utf-8\r\n< content-length: 601\r\n< x-powered-by: Express\r\n< access-control-allow-origin: *\r\n< etag: W\/\"259-MvPs2YiKvAYkYhFFydotsJbf72o\"\r\n< cf-cache-status: DYNAMIC\r\n< set-cookie: _cfuvid=4Ru9KhY3e6ucR3AlPbt2_J7E6lzkl.22Zdrmg_Z5Dpk-1740706710467-*******-604800000; path=\/; domain=.vapi.ai; HttpOnly; Secure; SameSite=None\r\n< server: cloudflare\r\n< cf-ray: 918cb20a0b3b66da-AMS\r\n< alt-svc: h3=\":443\"; ma=86400\r\n< \r\n* Connection #0 to host api.vapi.ai left intact\n",
        "response_body": "{\"message\":[\"queries.0.property filters should not exist\",\"queries.0.operations.0.operation can't be `history` for column `*`. `history` supports concurrency, minutesUsed columns\",\"queries.0.operations.0.column must be one of the following values: id, cost, costBreakdown.llm, costBreakdown.stt, costBreakdown.tts, costBreakdown.vapi, costBreakdown.ttsCharacters, costBreakdown.llmPromptTokens, costBreakdown.llmCompletionTokens, duration, concurrency, minutesUsed\",\"queries.0.operations requires a 'step' parameter in timeRange when using 'history' operation\"],\"error\":\"Bad Request\",\"statusCode\":400}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:38:30",
    "message": "API Bad Request",
    "data": {
        "response": "{\"message\":[\"queries.0.property filters should not exist\",\"queries.0.operations.0.operation can't be `history` for column `*`. `history` supports concurrency, minutesUsed columns\",\"queries.0.operations.0.column must be one of the following values: id, cost, costBreakdown.llm, costBreakdown.stt, costBreakdown.tts, costBreakdown.vapi, costBreakdown.ttsCharacters, costBreakdown.llmPromptTokens, costBreakdown.llmCompletionTokens, duration, concurrency, minutesUsed\",\"queries.0.operations requires a 'step' parameter in timeRange when using 'history' operation\"],\"error\":\"Bad Request\",\"statusCode\":400}",
        "request_url": "https:\/\/api.vapi.ai\/analytics"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:38:30",
    "message": "Error in makeApiCall",
    "data": {
        "error": "Neplatný požadavek na API. Zkontrolujte formát dat.",
        "trace": "#0 \/home\/<USER>\/domains\/dentibot.eu\/public_html\/sync_vapi_data.php(72): makeApiCall()\n#1 \/home\/<USER>\/domains\/dentibot.eu\/public_html\/dashboard.php(77): syncVapiData()\n#2 {main}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:38:30",
    "message": "Sync error",
    "data": {
        "error": "Neplatný požadavek na API. Zkontrolujte formát dat.",
        "trace": "#0 \/home\/<USER>\/domains\/dentibot.eu\/public_html\/sync_vapi_data.php(72): makeApiCall()\n#1 \/home\/<USER>\/domains\/dentibot.eu\/public_html\/dashboard.php(77): syncVapiData()\n#2 {main}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:38:30",
    "message": "Starting makeApiCall",
    "data": {
        "endpoint": "analytics",
        "data_sample": "{\"queries\":[{\"table\":\"call\",\"name\":\"call_metrics\",\"operations\":[{\"operation\":\"count\",\"column\":\"id\",\"alias\":\"total_calls\"},{\"operation\":\"sum\",\"column\":\"duration\",\"alias\":\"total_duration\"},{\"operation\":\"sum\",\"column\":\"cost\",\"alias\":\"total_cost\"}],\"timeRange\":{\"step\":\"day\",\"start\":\"2025-01-29T02:38:30+01:00\",\"end\":\"2025-02-28T02:38:30+01:00\",\"timezone\":\"UTC\"},\"groupBy\":[\"assistantId\"]},{\"table\":\"call\",\"name\":\"peak_hours\",\"operations\":[{\"operation\":\"count\",\"column\":\"id\",\"alias\":\"total_calls\"}],\"groupBy\":[\"hour\",\"assistantId\"],\"timeRange\":{\"step\":\"hour\",\"start\":\"2025-01-29T02:38:30+01:00\",\"end\":\"2025-02-28T02:38:30+01:00\",\"timezone\":\"UTC\"}},{\"table\":\"call\",\"name\":\"end_reasons\",\"operations\":[{\"operation\":\"count\",\"column\":\"id\",\"alias\":\"reason_count\"}],\"groupBy\":[\"endedReason\",\"assistantId\"],\"timeRange\":{\"start\":\"2025-01-29T02:38:30+01:00\",\"end\":\"2025-02-28T02:38:30+01:00\",\"timezone\":\"UTC\"}}]}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:38:30",
    "message": "Checking usage limits",
    "data": {
        "usage": 0,
        "limit": 500
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:38:30",
    "message": "API configuration loaded",
    "data": {
        "url": "https:\/\/api.vapi.ai",
        "has_key": true
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:38:30",
    "message": "Preparing analytics API request",
    "data": {
        "url": "https:\/\/api.vapi.ai\/analytics",
        "request_body": "{\"queries\":[{\"table\":\"call\",\"name\":\"call_metrics\",\"operations\":[{\"operation\":\"count\",\"column\":\"id\",\"alias\":\"total_calls\"},{\"operation\":\"sum\",\"column\":\"duration\",\"alias\":\"total_duration\"},{\"operation\":\"sum\",\"column\":\"cost\",\"alias\":\"total_cost\"}],\"timeRange\":{\"step\":\"day\",\"start\":\"2025-01-29T02:38:30+01:00\",\"end\":\"2025-02-28T02:38:30+01:00\",\"timezone\":\"UTC\"},\"groupBy\":[\"assistantId\"]},{\"table\":\"call\",\"name\":\"peak_hours\",\"operations\":[{\"operation\":\"count\",\"column\":\"id\",\"alias\":\"total_calls\"}],\"groupBy\":[\"hour\",\"assistantId\"],\"timeRange\":{\"step\":\"hour\",\"start\":\"2025-01-29T02:38:30+01:00\",\"end\":\"2025-02-28T02:38:30+01:00\",\"timezone\":\"UTC\"}},{\"table\":\"call\",\"name\":\"end_reasons\",\"operations\":[{\"operation\":\"count\",\"column\":\"id\",\"alias\":\"reason_count\"}],\"groupBy\":[\"endedReason\",\"assistantId\"],\"timeRange\":{\"start\":\"2025-01-29T02:38:30+01:00\",\"end\":\"2025-02-28T02:38:30+01:00\",\"timezone\":\"UTC\"}}]}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:38:30",
    "message": "API Response Info",
    "data": {
        "http_code": 400,
        "headers": "HTTP\/2 400 \r\ndate: Fri, 28 Feb 2025 01:38:30 GMT\r\ncontent-type: application\/json; charset=utf-8\r\ncontent-length: 190\r\nx-powered-by: Express\r\naccess-control-allow-origin: *\r\netag: W\/\"be-DvYaTNjmcc0zIZ4AnDWuZ3fm6Mo\"\r\ncf-cache-status: DYNAMIC\r\nset-cookie: _cfuvid=f8JwD0F4.3KbUxJIjrBq5VB48zq43tPYoV40hK1vCYc-1740706710968-*******-604800000; path=\/; domain=.vapi.ai; HttpOnly; Secure; SameSite=None\r\nserver: cloudflare\r\ncf-ray: 918cb20cbaa41cba-AMS\r\nalt-svc: h3=\":443\"; ma=86400\r\n\r\n",
        "error": "",
        "verbose_log": "*   Trying 2606:4700::6812:1940:443...\n* Connected to api.vapi.ai (2606:4700::6812:1940) port 443 (#0)\n* ALPN, offering h2\n* ALPN, offering http\/1.1\n*  CAfile: \/etc\/pki\/tls\/certs\/ca-bundle.crt\n* SSL connection using TLSv1.3 \/ TLS_AES_256_GCM_SHA384\n* ALPN, server accepted to use h2\n* Server certificate:\n*  subject: CN=api.vapi.ai\n*  start date: Jan 12 19:44:51 2025 GMT\n*  expire date: Apr 12 20:44:49 2025 GMT\n*  subjectAltName: host \"api.vapi.ai\" matched cert's \"api.vapi.ai\"\n*  issuer: C=US; O=Google Trust Services; CN=WE1\n*  SSL certificate verify ok.\n* Using HTTP2, server supports multi-use\n* Connection state changed (HTTP\/2 confirmed)\n* Copying HTTP\/2 data in stream buffer to connection buffer after upgrade: len=0\n* Using Stream ID: 1 (easy handle 0x2df4cc0)\n> POST \/analytics HTTP\/2\r\nHost: api.vapi.ai\r\nauthorization: Bearer a88d8eee-c2a2-4f4a-936d-676a6375ba20\r\ncontent-type: application\/json\r\naccept: application\/json\r\ncontent-length: 898\r\n\r\n* We are completely uploaded and fine\n* old SSL session ID is stale, removing\n< HTTP\/2 400 \r\n< date: Fri, 28 Feb 2025 01:38:30 GMT\r\n< content-type: application\/json; charset=utf-8\r\n< content-length: 190\r\n< x-powered-by: Express\r\n< access-control-allow-origin: *\r\n< etag: W\/\"be-DvYaTNjmcc0zIZ4AnDWuZ3fm6Mo\"\r\n< cf-cache-status: DYNAMIC\r\n< set-cookie: _cfuvid=f8JwD0F4.3KbUxJIjrBq5VB48zq43tPYoV40hK1vCYc-1740706710968-*******-604800000; path=\/; domain=.vapi.ai; HttpOnly; Secure; SameSite=None\r\n< server: cloudflare\r\n< cf-ray: 918cb20cbaa41cba-AMS\r\n< alt-svc: h3=\":443\"; ma=86400\r\n< \r\n* Connection #0 to host api.vapi.ai left intact\n",
        "response_body": "{\"message\":[\"queries.1.each value in groupBy must be one of the following values: type, assistantId, endedReason, analysis.successEvaluation, status\"],\"error\":\"Bad Request\",\"statusCode\":400}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:38:30",
    "message": "API Bad Request",
    "data": {
        "response": "{\"message\":[\"queries.1.each value in groupBy must be one of the following values: type, assistantId, endedReason, analysis.successEvaluation, status\"],\"error\":\"Bad Request\",\"statusCode\":400}",
        "request_url": "https:\/\/api.vapi.ai\/analytics"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:38:30",
    "message": "Error in makeApiCall",
    "data": {
        "error": "Neplatný požadavek na API. Zkontrolujte formát dat.",
        "trace": "#0 \/home\/<USER>\/domains\/dentibot.eu\/public_html\/dashboard.php(203): makeApiCall()\n#1 {main}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:38:31",
    "message": "Starting sync process",
    "data": [],
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:38:31",
    "message": "API Config loaded",
    "data": {
        "url": "https:\/\/api.vapi.ai"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:38:31",
    "message": "Fetching data from API",
    "data": {
        "startDate": "2025-01-29",
        "endDate": "2025-02-28",
        "api_url": "https:\/\/api.vapi.ai"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:38:31",
    "message": "Making API call with query",
    "data": {
        "query": "{\"queries\":[{\"table\":\"call\",\"name\":\"calls_data\",\"operations\":[{\"operation\":\"history\",\"column\":\"*\"}],\"timeRange\":{\"start\":\"2025-01-29T00:00:00Z\",\"end\":\"2025-02-28T23:59:59Z\"},\"filters\":[{\"field\":\"assistantId\",\"operator\":\"=\",\"value\":\"377ba32d-396b-4f6a-a91a-d45b12db11c7\"}]}]}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:38:31",
    "message": "Starting makeApiCall",
    "data": {
        "endpoint": "analytics",
        "data_sample": "{\"queries\":[{\"table\":\"call\",\"name\":\"calls_data\",\"operations\":[{\"operation\":\"history\",\"column\":\"*\"}],\"timeRange\":{\"start\":\"2025-01-29T00:00:00Z\",\"end\":\"2025-02-28T23:59:59Z\"},\"filters\":[{\"field\":\"assistantId\",\"operator\":\"=\",\"value\":\"377ba32d-396b-4f6a-a91a-d45b12db11c7\"}]}]}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:38:31",
    "message": "Checking usage limits",
    "data": {
        "usage": 0,
        "limit": 500
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:38:31",
    "message": "API configuration loaded",
    "data": {
        "url": "https:\/\/api.vapi.ai",
        "has_key": true
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:38:31",
    "message": "Preparing analytics API request",
    "data": {
        "url": "https:\/\/api.vapi.ai\/analytics",
        "request_body": "{\"queries\":[{\"table\":\"call\",\"name\":\"calls_data\",\"operations\":[{\"operation\":\"history\",\"column\":\"*\"}],\"timeRange\":{\"start\":\"2025-01-29T00:00:00Z\",\"end\":\"2025-02-28T23:59:59Z\"},\"filters\":[{\"field\":\"assistantId\",\"operator\":\"=\",\"value\":\"377ba32d-396b-4f6a-a91a-d45b12db11c7\"}]}]}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:38:31",
    "message": "API Response Info",
    "data": {
        "http_code": 400,
        "headers": "HTTP\/2 400 \r\ndate: Fri, 28 Feb 2025 01:38:31 GMT\r\ncontent-type: application\/json; charset=utf-8\r\ncontent-length: 601\r\nx-powered-by: Express\r\naccess-control-allow-origin: *\r\netag: W\/\"259-MvPs2YiKvAYkYhFFydotsJbf72o\"\r\ncf-cache-status: DYNAMIC\r\nset-cookie: _cfuvid=bgTPgCDm40BP9QIuRsZ9CJjle7wTBta_Pf58r_2JT4g-1740706711804-*******-604800000; path=\/; domain=.vapi.ai; HttpOnly; Secure; SameSite=None\r\nserver: cloudflare\r\ncf-ray: 918cb213a82e0c35-AMS\r\nalt-svc: h3=\":443\"; ma=86400\r\n\r\n",
        "error": "",
        "verbose_log": "*   Trying 2606:4700::6812:1940:443...\n* Connected to api.vapi.ai (2606:4700::6812:1940) port 443 (#0)\n* ALPN, offering h2\n* ALPN, offering http\/1.1\n*  CAfile: \/etc\/pki\/tls\/certs\/ca-bundle.crt\n* SSL connection using TLSv1.3 \/ TLS_AES_256_GCM_SHA384\n* ALPN, server accepted to use h2\n* Server certificate:\n*  subject: CN=api.vapi.ai\n*  start date: Jan 12 19:44:51 2025 GMT\n*  expire date: Apr 12 20:44:49 2025 GMT\n*  subjectAltName: host \"api.vapi.ai\" matched cert's \"api.vapi.ai\"\n*  issuer: C=US; O=Google Trust Services; CN=WE1\n*  SSL certificate verify ok.\n* Using HTTP2, server supports multi-use\n* Connection state changed (HTTP\/2 confirmed)\n* Copying HTTP\/2 data in stream buffer to connection buffer after upgrade: len=0\n* Using Stream ID: 1 (easy handle 0x2df4cc0)\n> POST \/analytics HTTP\/2\r\nHost: api.vapi.ai\r\nauthorization: Bearer a88d8eee-c2a2-4f4a-936d-676a6375ba20\r\ncontent-type: application\/json\r\naccept: application\/json\r\ncontent-length: 274\r\n\r\n* We are completely uploaded and fine\n* old SSL session ID is stale, removing\n< HTTP\/2 400 \r\n< date: Fri, 28 Feb 2025 01:38:31 GMT\r\n< content-type: application\/json; charset=utf-8\r\n< content-length: 601\r\n< x-powered-by: Express\r\n< access-control-allow-origin: *\r\n< etag: W\/\"259-MvPs2YiKvAYkYhFFydotsJbf72o\"\r\n< cf-cache-status: DYNAMIC\r\n< set-cookie: _cfuvid=bgTPgCDm40BP9QIuRsZ9CJjle7wTBta_Pf58r_2JT4g-1740706711804-*******-604800000; path=\/; domain=.vapi.ai; HttpOnly; Secure; SameSite=None\r\n< server: cloudflare\r\n< cf-ray: 918cb213a82e0c35-AMS\r\n< alt-svc: h3=\":443\"; ma=86400\r\n< \r\n* Connection #0 to host api.vapi.ai left intact\n",
        "response_body": "{\"message\":[\"queries.0.property filters should not exist\",\"queries.0.operations.0.operation can't be `history` for column `*`. `history` supports concurrency, minutesUsed columns\",\"queries.0.operations.0.column must be one of the following values: id, cost, costBreakdown.llm, costBreakdown.stt, costBreakdown.tts, costBreakdown.vapi, costBreakdown.ttsCharacters, costBreakdown.llmPromptTokens, costBreakdown.llmCompletionTokens, duration, concurrency, minutesUsed\",\"queries.0.operations requires a 'step' parameter in timeRange when using 'history' operation\"],\"error\":\"Bad Request\",\"statusCode\":400}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:38:31",
    "message": "API Bad Request",
    "data": {
        "response": "{\"message\":[\"queries.0.property filters should not exist\",\"queries.0.operations.0.operation can't be `history` for column `*`. `history` supports concurrency, minutesUsed columns\",\"queries.0.operations.0.column must be one of the following values: id, cost, costBreakdown.llm, costBreakdown.stt, costBreakdown.tts, costBreakdown.vapi, costBreakdown.ttsCharacters, costBreakdown.llmPromptTokens, costBreakdown.llmCompletionTokens, duration, concurrency, minutesUsed\",\"queries.0.operations requires a 'step' parameter in timeRange when using 'history' operation\"],\"error\":\"Bad Request\",\"statusCode\":400}",
        "request_url": "https:\/\/api.vapi.ai\/analytics"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:38:31",
    "message": "Error in makeApiCall",
    "data": {
        "error": "Neplatný požadavek na API. Zkontrolujte formát dat.",
        "trace": "#0 \/home\/<USER>\/domains\/dentibot.eu\/public_html\/sync_vapi_data.php(72): makeApiCall()\n#1 \/home\/<USER>\/domains\/dentibot.eu\/public_html\/dashboard.php(77): syncVapiData()\n#2 {main}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:38:31",
    "message": "Sync error",
    "data": {
        "error": "Neplatný požadavek na API. Zkontrolujte formát dat.",
        "trace": "#0 \/home\/<USER>\/domains\/dentibot.eu\/public_html\/sync_vapi_data.php(72): makeApiCall()\n#1 \/home\/<USER>\/domains\/dentibot.eu\/public_html\/dashboard.php(77): syncVapiData()\n#2 {main}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:38:31",
    "message": "Starting makeApiCall",
    "data": {
        "endpoint": "analytics",
        "data_sample": "{\"queries\":[{\"table\":\"call\",\"name\":\"call_metrics\",\"operations\":[{\"operation\":\"count\",\"column\":\"id\",\"alias\":\"total_calls\"},{\"operation\":\"sum\",\"column\":\"duration\",\"alias\":\"total_duration\"},{\"operation\":\"sum\",\"column\":\"cost\",\"alias\":\"total_cost\"}],\"timeRange\":{\"step\":\"day\",\"start\":\"2025-01-29T02:38:31+01:00\",\"end\":\"2025-02-28T02:38:31+01:00\",\"timezone\":\"UTC\"},\"groupBy\":[\"assistantId\"]},{\"table\":\"call\",\"name\":\"peak_hours\",\"operations\":[{\"operation\":\"count\",\"column\":\"id\",\"alias\":\"total_calls\"}],\"groupBy\":[\"hour\",\"assistantId\"],\"timeRange\":{\"step\":\"hour\",\"start\":\"2025-01-29T02:38:31+01:00\",\"end\":\"2025-02-28T02:38:31+01:00\",\"timezone\":\"UTC\"}},{\"table\":\"call\",\"name\":\"end_reasons\",\"operations\":[{\"operation\":\"count\",\"column\":\"id\",\"alias\":\"reason_count\"}],\"groupBy\":[\"endedReason\",\"assistantId\"],\"timeRange\":{\"start\":\"2025-01-29T02:38:31+01:00\",\"end\":\"2025-02-28T02:38:31+01:00\",\"timezone\":\"UTC\"}}]}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:38:31",
    "message": "Checking usage limits",
    "data": {
        "usage": 0,
        "limit": 500
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:38:31",
    "message": "API configuration loaded",
    "data": {
        "url": "https:\/\/api.vapi.ai",
        "has_key": true
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:38:31",
    "message": "Preparing analytics API request",
    "data": {
        "url": "https:\/\/api.vapi.ai\/analytics",
        "request_body": "{\"queries\":[{\"table\":\"call\",\"name\":\"call_metrics\",\"operations\":[{\"operation\":\"count\",\"column\":\"id\",\"alias\":\"total_calls\"},{\"operation\":\"sum\",\"column\":\"duration\",\"alias\":\"total_duration\"},{\"operation\":\"sum\",\"column\":\"cost\",\"alias\":\"total_cost\"}],\"timeRange\":{\"step\":\"day\",\"start\":\"2025-01-29T02:38:31+01:00\",\"end\":\"2025-02-28T02:38:31+01:00\",\"timezone\":\"UTC\"},\"groupBy\":[\"assistantId\"]},{\"table\":\"call\",\"name\":\"peak_hours\",\"operations\":[{\"operation\":\"count\",\"column\":\"id\",\"alias\":\"total_calls\"}],\"groupBy\":[\"hour\",\"assistantId\"],\"timeRange\":{\"step\":\"hour\",\"start\":\"2025-01-29T02:38:31+01:00\",\"end\":\"2025-02-28T02:38:31+01:00\",\"timezone\":\"UTC\"}},{\"table\":\"call\",\"name\":\"end_reasons\",\"operations\":[{\"operation\":\"count\",\"column\":\"id\",\"alias\":\"reason_count\"}],\"groupBy\":[\"endedReason\",\"assistantId\"],\"timeRange\":{\"start\":\"2025-01-29T02:38:31+01:00\",\"end\":\"2025-02-28T02:38:31+01:00\",\"timezone\":\"UTC\"}}]}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:38:32",
    "message": "API Response Info",
    "data": {
        "http_code": 400,
        "headers": "HTTP\/2 400 \r\ndate: Fri, 28 Feb 2025 01:38:32 GMT\r\ncontent-type: application\/json; charset=utf-8\r\ncontent-length: 190\r\nx-powered-by: Express\r\naccess-control-allow-origin: *\r\netag: W\/\"be-DvYaTNjmcc0zIZ4AnDWuZ3fm6Mo\"\r\ncf-cache-status: DYNAMIC\r\nset-cookie: _cfuvid=0oiygOambH_A_3Yf9F0.Kv2388JCeGIwB3JrpriGOzs-1740706712857-*******-604800000; path=\/; domain=.vapi.ai; HttpOnly; Secure; SameSite=None\r\nserver: cloudflare\r\ncf-ray: 918cb2150cfefead-AMS\r\nalt-svc: h3=\":443\"; ma=86400\r\n\r\n",
        "error": "",
        "verbose_log": "*   Trying 2606:4700::6812:1940:443...\n* Connected to api.vapi.ai (2606:4700::6812:1940) port 443 (#0)\n* ALPN, offering h2\n* ALPN, offering http\/1.1\n*  CAfile: \/etc\/pki\/tls\/certs\/ca-bundle.crt\n* SSL connection using TLSv1.3 \/ TLS_AES_256_GCM_SHA384\n* ALPN, server accepted to use h2\n* Server certificate:\n*  subject: CN=api.vapi.ai\n*  start date: Jan 12 19:44:51 2025 GMT\n*  expire date: Apr 12 20:44:49 2025 GMT\n*  subjectAltName: host \"api.vapi.ai\" matched cert's \"api.vapi.ai\"\n*  issuer: C=US; O=Google Trust Services; CN=WE1\n*  SSL certificate verify ok.\n* Using HTTP2, server supports multi-use\n* Connection state changed (HTTP\/2 confirmed)\n* Copying HTTP\/2 data in stream buffer to connection buffer after upgrade: len=0\n* Using Stream ID: 1 (easy handle 0x2e59100)\n> POST \/analytics HTTP\/2\r\nHost: api.vapi.ai\r\nauthorization: Bearer a88d8eee-c2a2-4f4a-936d-676a6375ba20\r\ncontent-type: application\/json\r\naccept: application\/json\r\ncontent-length: 898\r\n\r\n* We are completely uploaded and fine\n* old SSL session ID is stale, removing\n< HTTP\/2 400 \r\n< date: Fri, 28 Feb 2025 01:38:32 GMT\r\n< content-type: application\/json; charset=utf-8\r\n< content-length: 190\r\n< x-powered-by: Express\r\n< access-control-allow-origin: *\r\n< etag: W\/\"be-DvYaTNjmcc0zIZ4AnDWuZ3fm6Mo\"\r\n< cf-cache-status: DYNAMIC\r\n< set-cookie: _cfuvid=0oiygOambH_A_3Yf9F0.Kv2388JCeGIwB3JrpriGOzs-1740706712857-*******-604800000; path=\/; domain=.vapi.ai; HttpOnly; Secure; SameSite=None\r\n< server: cloudflare\r\n< cf-ray: 918cb2150cfefead-AMS\r\n< alt-svc: h3=\":443\"; ma=86400\r\n< \r\n* Connection #0 to host api.vapi.ai left intact\n",
        "response_body": "{\"message\":[\"queries.1.each value in groupBy must be one of the following values: type, assistantId, endedReason, analysis.successEvaluation, status\"],\"error\":\"Bad Request\",\"statusCode\":400}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:38:32",
    "message": "API Bad Request",
    "data": {
        "response": "{\"message\":[\"queries.1.each value in groupBy must be one of the following values: type, assistantId, endedReason, analysis.successEvaluation, status\"],\"error\":\"Bad Request\",\"statusCode\":400}",
        "request_url": "https:\/\/api.vapi.ai\/analytics"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:38:32",
    "message": "Error in makeApiCall",
    "data": {
        "error": "Neplatný požadavek na API. Zkontrolujte formát dat.",
        "trace": "#0 \/home\/<USER>\/domains\/dentibot.eu\/public_html\/dashboard.php(203): makeApiCall()\n#1 {main}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "POST",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:38:59",
    "message": "Starting makeApiCall",
    "data": {
        "endpoint": "analytics",
        "data_sample": "{\"queries\":[{\"table\":\"call\",\"name\":\"call_metrics\",\"operations\":[{\"operation\":\"count\",\"column\":\"id\",\"alias\":\"total_calls\"},{\"operation\":\"sum\",\"column\":\"duration\",\"alias\":\"total_duration\"},{\"operation\":\"sum\",\"column\":\"cost\",\"alias\":\"total_cost\"}],\"timeRange\":{\"step\":\"day\",\"start\":\"2025-01-29T02:38:59+01:00\",\"end\":\"2025-02-28T02:38:59+01:00\",\"timezone\":\"UTC\"},\"groupBy\":[\"assistantId\"]},{\"table\":\"call\",\"name\":\"peak_hours\",\"operations\":[{\"operation\":\"count\",\"column\":\"id\",\"alias\":\"total_calls\"}],\"groupBy\":[\"hour\",\"assistantId\"],\"timeRange\":{\"step\":\"hour\",\"start\":\"2025-01-29T02:38:59+01:00\",\"end\":\"2025-02-28T02:38:59+01:00\",\"timezone\":\"UTC\"}},{\"table\":\"call\",\"name\":\"end_reasons\",\"operations\":[{\"operation\":\"count\",\"column\":\"id\",\"alias\":\"reason_count\"}],\"groupBy\":[\"endedReason\",\"assistantId\"],\"timeRange\":{\"start\":\"2025-01-29T02:38:59+01:00\",\"end\":\"2025-02-28T02:38:59+01:00\",\"timezone\":\"UTC\"}}]}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "GET",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:38:59",
    "message": "Checking usage limits",
    "data": {
        "usage": 0,
        "limit": 500
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "GET",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:38:59",
    "message": "API configuration loaded",
    "data": {
        "url": "https:\/\/api.vapi.ai",
        "has_key": true
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "GET",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:38:59",
    "message": "Preparing analytics API request",
    "data": {
        "url": "https:\/\/api.vapi.ai\/analytics",
        "request_body": "{\"queries\":[{\"table\":\"call\",\"name\":\"call_metrics\",\"operations\":[{\"operation\":\"count\",\"column\":\"id\",\"alias\":\"total_calls\"},{\"operation\":\"sum\",\"column\":\"duration\",\"alias\":\"total_duration\"},{\"operation\":\"sum\",\"column\":\"cost\",\"alias\":\"total_cost\"}],\"timeRange\":{\"step\":\"day\",\"start\":\"2025-01-29T02:38:59+01:00\",\"end\":\"2025-02-28T02:38:59+01:00\",\"timezone\":\"UTC\"},\"groupBy\":[\"assistantId\"]},{\"table\":\"call\",\"name\":\"peak_hours\",\"operations\":[{\"operation\":\"count\",\"column\":\"id\",\"alias\":\"total_calls\"}],\"groupBy\":[\"hour\",\"assistantId\"],\"timeRange\":{\"step\":\"hour\",\"start\":\"2025-01-29T02:38:59+01:00\",\"end\":\"2025-02-28T02:38:59+01:00\",\"timezone\":\"UTC\"}},{\"table\":\"call\",\"name\":\"end_reasons\",\"operations\":[{\"operation\":\"count\",\"column\":\"id\",\"alias\":\"reason_count\"}],\"groupBy\":[\"endedReason\",\"assistantId\"],\"timeRange\":{\"start\":\"2025-01-29T02:38:59+01:00\",\"end\":\"2025-02-28T02:38:59+01:00\",\"timezone\":\"UTC\"}}]}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "GET",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:38:59",
    "message": "API Response Info",
    "data": {
        "http_code": 400,
        "headers": "HTTP\/2 400 \r\ndate: Fri, 28 Feb 2025 01:38:59 GMT\r\ncontent-type: application\/json; charset=utf-8\r\ncontent-length: 190\r\nx-powered-by: Express\r\naccess-control-allow-origin: *\r\netag: W\/\"be-DvYaTNjmcc0zIZ4AnDWuZ3fm6Mo\"\r\ncf-cache-status: DYNAMIC\r\nset-cookie: _cfuvid=UeCHQctrgk43zy8HIjdqJKFr6KisfW4UguluVBfbI.Q-1740706739744-*******-604800000; path=\/; domain=.vapi.ai; HttpOnly; Secure; SameSite=None\r\nserver: cloudflare\r\ncf-ray: 918cb2c0cd640a77-AMS\r\nalt-svc: h3=\":443\"; ma=86400\r\n\r\n",
        "error": "",
        "verbose_log": "*   Trying 2606:4700::6812:1940:443...\n* Connected to api.vapi.ai (2606:4700::6812:1940) port 443 (#0)\n* ALPN, offering h2\n* ALPN, offering http\/1.1\n*  CAfile: \/etc\/pki\/tls\/certs\/ca-bundle.crt\n* SSL connection using TLSv1.3 \/ TLS_AES_256_GCM_SHA384\n* ALPN, server accepted to use h2\n* Server certificate:\n*  subject: CN=api.vapi.ai\n*  start date: Jan 12 19:44:51 2025 GMT\n*  expire date: Apr 12 20:44:49 2025 GMT\n*  subjectAltName: host \"api.vapi.ai\" matched cert's \"api.vapi.ai\"\n*  issuer: C=US; O=Google Trust Services; CN=WE1\n*  SSL certificate verify ok.\n* Using HTTP2, server supports multi-use\n* Connection state changed (HTTP\/2 confirmed)\n* Copying HTTP\/2 data in stream buffer to connection buffer after upgrade: len=0\n* Using Stream ID: 1 (easy handle 0x2e11480)\n> POST \/analytics HTTP\/2\r\nHost: api.vapi.ai\r\nauthorization: Bearer a88d8eee-c2a2-4f4a-936d-676a6375ba20\r\ncontent-type: application\/json\r\naccept: application\/json\r\ncontent-length: 898\r\n\r\n* We are completely uploaded and fine\n* old SSL session ID is stale, removing\n< HTTP\/2 400 \r\n< date: Fri, 28 Feb 2025 01:38:59 GMT\r\n< content-type: application\/json; charset=utf-8\r\n< content-length: 190\r\n< x-powered-by: Express\r\n< access-control-allow-origin: *\r\n< etag: W\/\"be-DvYaTNjmcc0zIZ4AnDWuZ3fm6Mo\"\r\n< cf-cache-status: DYNAMIC\r\n< set-cookie: _cfuvid=UeCHQctrgk43zy8HIjdqJKFr6KisfW4UguluVBfbI.Q-1740706739744-*******-604800000; path=\/; domain=.vapi.ai; HttpOnly; Secure; SameSite=None\r\n< server: cloudflare\r\n< cf-ray: 918cb2c0cd640a77-AMS\r\n< alt-svc: h3=\":443\"; ma=86400\r\n< \r\n* Connection #0 to host api.vapi.ai left intact\n",
        "response_body": "{\"message\":[\"queries.1.each value in groupBy must be one of the following values: type, assistantId, endedReason, analysis.successEvaluation, status\"],\"error\":\"Bad Request\",\"statusCode\":400}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "GET",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:38:59",
    "message": "API Bad Request",
    "data": {
        "response": "{\"message\":[\"queries.1.each value in groupBy must be one of the following values: type, assistantId, endedReason, analysis.successEvaluation, status\"],\"error\":\"Bad Request\",\"statusCode\":400}",
        "request_url": "https:\/\/api.vapi.ai\/analytics"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "GET",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:38:59",
    "message": "Error in makeApiCall",
    "data": {
        "error": "Neplatný požadavek na API. Zkontrolujte formát dat.",
        "trace": "#0 \/home\/<USER>\/domains\/dentibot.eu\/public_html\/dashboard.php(203): makeApiCall()\n#1 {main}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "GET",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:39:09",
    "message": "Starting makeApiCall",
    "data": {
        "endpoint": "analytics",
        "data_sample": "{\"queries\":[{\"table\":\"call\",\"name\":\"call_metrics\",\"operations\":[{\"operation\":\"count\",\"column\":\"id\",\"alias\":\"total_calls\"},{\"operation\":\"sum\",\"column\":\"duration\",\"alias\":\"total_duration\"},{\"operation\":\"sum\",\"column\":\"cost\",\"alias\":\"total_cost\"}],\"timeRange\":{\"step\":\"day\",\"start\":\"2025-01-29T02:39:09+01:00\",\"end\":\"2025-02-28T02:39:09+01:00\",\"timezone\":\"UTC\"},\"groupBy\":[\"assistantId\"]},{\"table\":\"call\",\"name\":\"peak_hours\",\"operations\":[{\"operation\":\"count\",\"column\":\"id\",\"alias\":\"total_calls\"}],\"groupBy\":[\"hour\",\"assistantId\"],\"timeRange\":{\"step\":\"hour\",\"start\":\"2025-01-29T02:39:09+01:00\",\"end\":\"2025-02-28T02:39:09+01:00\",\"timezone\":\"UTC\"}},{\"table\":\"call\",\"name\":\"end_reasons\",\"operations\":[{\"operation\":\"count\",\"column\":\"id\",\"alias\":\"reason_count\"}],\"groupBy\":[\"endedReason\",\"assistantId\"],\"timeRange\":{\"start\":\"2025-01-29T02:39:09+01:00\",\"end\":\"2025-02-28T02:39:09+01:00\",\"timezone\":\"UTC\"}}]}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "GET",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:39:09",
    "message": "Checking usage limits",
    "data": {
        "usage": 0,
        "limit": 500
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "GET",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:39:09",
    "message": "API configuration loaded",
    "data": {
        "url": "https:\/\/api.vapi.ai",
        "has_key": true
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "GET",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:39:09",
    "message": "Preparing analytics API request",
    "data": {
        "url": "https:\/\/api.vapi.ai\/analytics",
        "request_body": "{\"queries\":[{\"table\":\"call\",\"name\":\"call_metrics\",\"operations\":[{\"operation\":\"count\",\"column\":\"id\",\"alias\":\"total_calls\"},{\"operation\":\"sum\",\"column\":\"duration\",\"alias\":\"total_duration\"},{\"operation\":\"sum\",\"column\":\"cost\",\"alias\":\"total_cost\"}],\"timeRange\":{\"step\":\"day\",\"start\":\"2025-01-29T02:39:09+01:00\",\"end\":\"2025-02-28T02:39:09+01:00\",\"timezone\":\"UTC\"},\"groupBy\":[\"assistantId\"]},{\"table\":\"call\",\"name\":\"peak_hours\",\"operations\":[{\"operation\":\"count\",\"column\":\"id\",\"alias\":\"total_calls\"}],\"groupBy\":[\"hour\",\"assistantId\"],\"timeRange\":{\"step\":\"hour\",\"start\":\"2025-01-29T02:39:09+01:00\",\"end\":\"2025-02-28T02:39:09+01:00\",\"timezone\":\"UTC\"}},{\"table\":\"call\",\"name\":\"end_reasons\",\"operations\":[{\"operation\":\"count\",\"column\":\"id\",\"alias\":\"reason_count\"}],\"groupBy\":[\"endedReason\",\"assistantId\"],\"timeRange\":{\"start\":\"2025-01-29T02:39:09+01:00\",\"end\":\"2025-02-28T02:39:09+01:00\",\"timezone\":\"UTC\"}}]}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "GET",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:39:09",
    "message": "API Response Info",
    "data": {
        "http_code": 400,
        "headers": "HTTP\/2 400 \r\ndate: Fri, 28 Feb 2025 01:39:09 GMT\r\ncontent-type: application\/json; charset=utf-8\r\ncontent-length: 190\r\nx-powered-by: Express\r\naccess-control-allow-origin: *\r\netag: W\/\"be-DvYaTNjmcc0zIZ4AnDWuZ3fm6Mo\"\r\ncf-cache-status: DYNAMIC\r\nset-cookie: _cfuvid=u4CjFiITxL9iU7dBjCf4K8z_XE3gtIN0Ti.U6y6CHBA-1740706749530-*******-604800000; path=\/; domain=.vapi.ai; HttpOnly; Secure; SameSite=None\r\nserver: cloudflare\r\ncf-ray: 918cb2ff5dd95223-FRA\r\nalt-svc: h3=\":443\"; ma=86400\r\n\r\n",
        "error": "",
        "verbose_log": "*   Trying 2606:4700::6812:1940:443...\n* Connected to api.vapi.ai (2606:4700::6812:1940) port 443 (#0)\n* ALPN, offering h2\n* ALPN, offering http\/1.1\n*  CAfile: \/etc\/pki\/tls\/certs\/ca-bundle.crt\n* SSL connection using TLSv1.3 \/ TLS_AES_256_GCM_SHA384\n* ALPN, server accepted to use h2\n* Server certificate:\n*  subject: CN=api.vapi.ai\n*  start date: Jan 12 19:44:51 2025 GMT\n*  expire date: Apr 12 20:44:49 2025 GMT\n*  subjectAltName: host \"api.vapi.ai\" matched cert's \"api.vapi.ai\"\n*  issuer: C=US; O=Google Trust Services; CN=WE1\n*  SSL certificate verify ok.\n* Using HTTP2, server supports multi-use\n* Connection state changed (HTTP\/2 confirmed)\n* Copying HTTP\/2 data in stream buffer to connection buffer after upgrade: len=0\n* Using Stream ID: 1 (easy handle 0x2e3a030)\n> POST \/analytics HTTP\/2\r\nHost: api.vapi.ai\r\nauthorization: Bearer a88d8eee-c2a2-4f4a-936d-676a6375ba20\r\ncontent-type: application\/json\r\naccept: application\/json\r\ncontent-length: 898\r\n\r\n* We are completely uploaded and fine\n* old SSL session ID is stale, removing\n< HTTP\/2 400 \r\n< date: Fri, 28 Feb 2025 01:39:09 GMT\r\n< content-type: application\/json; charset=utf-8\r\n< content-length: 190\r\n< x-powered-by: Express\r\n< access-control-allow-origin: *\r\n< etag: W\/\"be-DvYaTNjmcc0zIZ4AnDWuZ3fm6Mo\"\r\n< cf-cache-status: DYNAMIC\r\n< set-cookie: _cfuvid=u4CjFiITxL9iU7dBjCf4K8z_XE3gtIN0Ti.U6y6CHBA-1740706749530-*******-604800000; path=\/; domain=.vapi.ai; HttpOnly; Secure; SameSite=None\r\n< server: cloudflare\r\n< cf-ray: 918cb2ff5dd95223-FRA\r\n< alt-svc: h3=\":443\"; ma=86400\r\n< \r\n* Connection #0 to host api.vapi.ai left intact\n",
        "response_body": "{\"message\":[\"queries.1.each value in groupBy must be one of the following values: type, assistantId, endedReason, analysis.successEvaluation, status\"],\"error\":\"Bad Request\",\"statusCode\":400}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "GET",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:39:09",
    "message": "API Bad Request",
    "data": {
        "response": "{\"message\":[\"queries.1.each value in groupBy must be one of the following values: type, assistantId, endedReason, analysis.successEvaluation, status\"],\"error\":\"Bad Request\",\"statusCode\":400}",
        "request_url": "https:\/\/api.vapi.ai\/analytics"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "GET",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}
{
    "timestamp": "2025-02-28 02:39:09",
    "message": "Error in makeApiCall",
    "data": {
        "error": "Neplatný požadavek na API. Zkontrolujte formát dat.",
        "trace": "#0 \/home\/<USER>\/domains\/dentibot.eu\/public_html\/dashboard.php(203): makeApiCall()\n#1 {main}"
    },
    "user_id": 3,
    "request": {
        "url": "\/dashboard",
        "method": "GET",
        "ip": "2a00:1028:9942:bce:61f1:ccc9:2552:5fb8"
    }
}

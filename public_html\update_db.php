[V0_FILE]php:file="update_db.php"
<?php
require_once 'config.php';
require_once 'api_functions.php';

function updateAnalytics() {
    global $mysqli;
    
    $analytics = callVapiAPI('/analytics');
    if (!$analytics || empty($analytics['data'])) {
        writeErrorLog('Failed to fetch analytics data from API');
        return;
    }
    
    $query = "INSERT INTO analytics (date, total_calls, total_duration, successful_calls, cost) 
              VALUES (?, ?, ?, ?, ?) 
              ON DUPLICATE KEY UPDATE 
              total_calls = VALUES(total_calls), 
              total_duration = VALUES(total_duration), 
              successful_calls = VALUES(successful_calls), 
              cost = VALUES(cost)";
    
    $stmt = $mysqli->prepare($query);
    
    foreach ($analytics['data'] as $day) {
        $stmt->bind_param("siiii", 
            $day['date'], 
            $day['total_calls'], 
            $day['total_duration'], 
            $day['successful_calls'], 
            $day['cost']
        );
        $stmt->execute();
    }
    
    writeErrorLog('Analytics data updated in DB', ['count' => count($analytics['data'])]);
}

function updatePeakHours() {
    global $mysqli;
    
    $peakHours = callVapiAPI('/peak-hours');
    if (!$peakHours || empty($peakHours['data'])) {
        writeErrorLog('Failed to fetch peak hours data from API');
        return;
    }
    
    $query = "INSERT INTO peak_hours (hour, total_calls) 
              VALUES (?, ?) 
              ON DUPLICATE KEY UPDATE 
              total_calls = VALUES(total_calls)";
    
    $stmt = $mysqli->prepare($query);
    
    foreach ($peakHours['data'] as $hour) {
        $stmt->bind_param("ii", $hour['hour'], $hour['total_calls']);
        $stmt->execute();
    }
    
    writeErrorLog('Peak hours data updated in DB', ['count' => count($peakHours['data'])]);
}

function updateTopReasons() {
    global $mysqli;
    
    $topReasons = callVapiAPI('/top-reasons');
    if (!$topReasons || empty($topReasons['data'])) {
        writeErrorLog('Failed to fetch top reasons data from API');
        return;
    }
    
    $query = "INSERT INTO top_reasons (reason, count) 
              VALUES (?, ?) 
              ON DUPLICATE KEY UPDATE 
              count = VALUES(count)";
    
    $stmt = $mysqli->prepare($query);
    
    foreach ($topReasons['data'] as $reason) {
        $stmt->bind_param("si", $reason['reason'], $reason['count']);
        $stmt->execute();
    }
    
    writeErrorLog('Top reasons data updated in DB', ['count' => count($topReasons['data'])]);
}

// Aktualizace všech dat
updateAnalytics();
updatePeakHours();
updateTopReasons();

echo "Database update completed.";
<?php
// Include the configuration file
require_once '../config.php';

// Set headers to allow cross-origin requests if needed
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, PUT, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
header("Content-Type: application/json");

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Funkce pro získání user_id z požadavku
function getUserIdFromRequest() {
    // Pokud je v URL parametr user_id, použijeme ho
    if (isset($_GET['user_id']) && is_numeric($_GET['user_id'])) {
        return (int)$_GET['user_id'];
    }
    
    // Pokud je v hlavičce Authorization, můžeme extrahovat user_id z tokenu
    $headers = getallheaders();
    if (isset($headers['Authorization'])) {
        $token = str_replace('Bearer ', '', $headers['Authorization']);
        // Zde by byla logika pro ověření tokenu a získání user_id
        // Pro jednoduchost předpokládáme, že token je přímo user_id
        if (is_numeric($token)) {
            return (int)$token;
        }
    }
    
    // Pokud jsme přihlášeni v aplikaci, použijeme ID přihlášeného uživatele
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }
    
    if (isset($_SESSION['user_id'])) {
        return (int)$_SESSION['user_id'];
    }
    
    // Pokud nemůžeme určit user_id, vrátíme výchozí hodnotu
    return 1; // Výchozí ID pro zpětnou kompatibilitu
}



// Handle different HTTP methods
$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'GET':
        // Return messages for a specific user
        try {
            $user_id = getUserIdFromRequest();
            
            $mysqli = getDbConnection();
            
            // Check if the table has the new structure
            $result = $mysqli->query("SHOW COLUMNS FROM patient_messages LIKE 'name_iv'");
            $hasNewStructure = $result && $result->num_rows > 0;
            
            // Check if the table has user_id column
            $result = $mysqli->query("SHOW COLUMNS FROM patient_messages LIKE 'user_id'");
            $hasUserIdColumn = $result && $result->num_rows > 0;
            
            if ($hasNewStructure) {
                if ($hasUserIdColumn) {
                    $stmt = $mysqli->prepare("SELECT * FROM patient_messages WHERE user_id = ? ORDER BY timestamp DESC");
                    $stmt->bind_param("i", $user_id);
                } else {
                    // Fallback if user_id column doesn't exist yet
                    $stmt = $mysqli->prepare("SELECT * FROM patient_messages ORDER BY timestamp DESC");
                }
                
                $stmt->execute();
                $result = $stmt->get_result();
                $messages = [];
                
                while ($row = $result->fetch_assoc()) {
                    // Dešifrování dat s individuálními IV
                    $name = decryptPatientData($row['name_encrypted'], $row['name_iv']);
                    $phone = decryptPatientData($row['phone_encrypted'], $row['phone_iv']);
                    $message = decryptPatientData($row['message_encrypted'], $row['message_iv']);
                    
                    $messages[] = [
                        'id' => $row['id'],
                        'user_id' => $hasUserIdColumn ? $row['user_id'] : $user_id,
                        'name' => $name,
                        'phone' => $phone,
                        'message' => $message,
                        'timestamp' => strtotime($row['timestamp']),
                        'read' => (bool)$row['read']
                    ];
                }
            } else {
                // Fallback pro starou strukturu
                if ($hasUserIdColumn) {
                    $stmt = $mysqli->prepare("SELECT * FROM patient_messages WHERE user_id = ? ORDER BY timestamp DESC");
                    $stmt->bind_param("i", $user_id);
                } else {
                    $stmt = $mysqli->prepare("SELECT * FROM patient_messages ORDER BY timestamp DESC");
                }
                
                $stmt->execute();
                $result = $stmt->get_result();
                $messages = [];
                
                while ($row = $result->fetch_assoc()) {
                    // Dešifrování dat se společným IV
                    $name = decryptPatientData($row['name_encrypted'], $row['encryption_iv']);
                    $phone = decryptPatientData($row['phone_encrypted'], $row['encryption_iv']);
                    $message = decryptPatientData($row['message_encrypted'], $row['encryption_iv']);
                    
                    $messages[] = [
                        'id' => $row['id'],
                        'user_id' => $hasUserIdColumn ? $row['user_id'] : $user_id,
                        'name' => $name,
                        'phone' => $phone,
                        'message' => $message,
                        'timestamp' => strtotime($row['timestamp']),
                        'read' => (bool)$row['read']
                    ];
                }
            }
            
            echo json_encode(['messages' => $messages]);
            $stmt->close();
        } catch (Exception $e) {
            writeErrorLog("Error fetching messages", ['error' => $e->getMessage()]);
            http_response_code(500);
            echo json_encode(['error' => 'Database error']);
        }
        break;
        
    case 'POST':
        // Get the raw POST data
        $inputJSON = file_get_contents('php://input');
        $input = json_decode($inputJSON, true);
        
        // Validate input
        if (!$input || !isset($input['message'])) {
            http_response_code(400);
            echo json_encode(['error' => 'Invalid input. Message is required.']);
            exit;
        }
        
        try {
            // Získání user_id z požadavku nebo z dat
            $user_id = isset($input['user_id']) ? (int)$input['user_id'] : getUserIdFromRequest();
            
            // Šifrování dat
            $name = isset($input['name']) ? sanitizeInput($input['name']) : 'Neznámý pacient';
            $phone = isset($input['phone']) ? sanitizeInput($input['phone']) : '';
            $message = sanitizeInput($input['message']);
            
            $encryptedName = encryptPatientData($name);
            $encryptedPhone = encryptPatientData($phone);
            $encryptedMessage = encryptPatientData($message);
            
            $mysqli = getDbConnection();
            
            // Check if the table has the new structure
            $result = $mysqli->query("SHOW COLUMNS FROM patient_messages LIKE 'name_iv'");
            $hasNewStructure = $result && $result->num_rows > 0;
            
            // Check if the table has user_id column
            $result = $mysqli->query("SHOW COLUMNS FROM patient_messages LIKE 'user_id'");
            $hasUserIdColumn = $result && $result->num_rows > 0;
            
            if ($hasNewStructure) {
                if ($hasUserIdColumn) {
                    // Použití nové struktury s individuálními IV a user_id
                    $stmt = $mysqli->prepare("
                        INSERT INTO patient_messages 
                        (user_id, name_encrypted, name_iv, phone_encrypted, phone_iv, message_encrypted, message_iv) 
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    ");
                    
                    $stmt->bind_param("issssss", 
                        $user_id,
                        $encryptedName['data'],
                        $encryptedName['iv'],
                        $encryptedPhone['data'],
                        $encryptedPhone['iv'],
                        $encryptedMessage['data'],
                        $encryptedMessage['iv']
                    );
                } else {
                    // Použití nové struktury s individuálními IV bez user_id
                    $stmt = $mysqli->prepare("
                        INSERT INTO patient_messages 
                        (name_encrypted, name_iv, phone_encrypted, phone_iv, message_encrypted, message_iv) 
                        VALUES (?, ?, ?, ?, ?, ?)
                    ");
                    
                    $stmt->bind_param("ssssss", 
                        $encryptedName['data'],
                        $encryptedName['iv'],
                        $encryptedPhone['data'],
                        $encryptedPhone['iv'],
                        $encryptedMessage['data'],
                        $encryptedMessage['iv']
                    );
                }
            } else {
                if ($hasUserIdColumn) {
                    // Fallback pro starou strukturu se společným IV a user_id
                    $stmt = $mysqli->prepare("
                        INSERT INTO patient_messages 
                        (user_id, name_encrypted, phone_encrypted, message_encrypted, encryption_iv) 
                        VALUES (?, ?, ?, ?, ?)
                    ");
                    
                    $stmt->bind_param("issss", 
                        $user_id,
                        $encryptedName['data'],
                        $encryptedPhone['data'],
                        $encryptedMessage['data'],
                        $encryptedName['iv']
                    );
                } else {
                    // Fallback pro starou strukturu se společným IV bez user_id
                    $stmt = $mysqli->prepare("
                        INSERT INTO patient_messages 
                        (name_encrypted, phone_encrypted, message_encrypted, encryption_iv) 
                        VALUES (?, ?, ?, ?)
                    ");
                    
                    $stmt->bind_param("ssss", 
                        $encryptedName['data'],
                        $encryptedPhone['data'],
                        $encryptedMessage['data'],
                        $encryptedName['iv']
                    );
                }
            }
            
            $stmt->execute();
            $id = $mysqli->insert_id;
            $stmt->close();
            
            // Return the new message
            echo json_encode([
                'id' => $id,
                'user_id' => $user_id,
                'name' => $name,
                'phone' => $phone,
                'message' => $message,
                'timestamp' => time(),
                'read' => false
            ]);
            
            writeErrorLog("New message saved", ['id' => $id, 'user_id' => $user_id, 'name' => $name]);
        } catch (Exception $e) {
            writeErrorLog("Error saving message", ['error' => $e->getMessage()]);
            http_response_code(500);
            echo json_encode(['error' => 'Database error']);
        }
        break;
        
    case 'PUT':
        // Update message read status
        $inputJSON = file_get_contents('php://input');
        $input = json_decode($inputJSON, true);
        
        if (!$input || !isset($input['id'])) {
            http_response_code(400);
            echo json_encode(['error' => 'Invalid input. Message ID is required.']);
            exit;
        }
        
        try {
            $user_id = getUserIdFromRequest();
            $mysqli = getDbConnection();
            
            // Check if the table has user_id column
            $result = $mysqli->query("SHOW COLUMNS FROM patient_messages LIKE 'user_id'");
            $hasUserIdColumn = $result && $result->num_rows > 0;
            
            if ($hasUserIdColumn) {
                $stmt = $mysqli->prepare("UPDATE patient_messages SET `read` = ? WHERE id = ? AND user_id = ?");
                $read = isset($input['read']) ? (int)$input['read'] : 1;
                $id = (int)$input['id'];
                
                $stmt->bind_param("iii", $read, $id, $user_id);
            } else {
                $stmt = $mysqli->prepare("UPDATE patient_messages SET `read` = ? WHERE id = ?");
                $read = isset($input['read']) ? (int)$input['read'] : 1;
                $id = (int)$input['id'];
                
                $stmt->bind_param("ii", $read, $id);
            }
            
            $stmt->execute();
            $affected = $stmt->affected_rows;
            $stmt->close();
            
            if ($affected > 0) {
                echo json_encode(['success' => true, 'id' => $id, 'read' => (bool)$read]);
                writeErrorLog("Message status updated", ['id' => $id, 'user_id' => $user_id, 'read' => $read]);
            } else {
                http_response_code(404);
                echo json_encode(['error' => 'Message not found or not authorized to update']);
            }
        } catch (Exception $e) {
            writeErrorLog("Error updating message status", ['error' => $e->getMessage()]);
            http_response_code(500);
            echo json_encode(['error' => 'Database error']);
        }
        break;
        
    default:
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed']);
        break;
}
?>
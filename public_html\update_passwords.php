<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include the database configuration
require_once 'config.php';

// Function to update password for a specific user
function updateUserPassword($userId, $newPassword) {
    global $mysqli;
    
    // Hash the new password
    $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
    
    // Update the user's password_hash in the database
    $stmt = $mysqli->prepare("UPDATE users SET password_hash = ? WHERE id = ?");
    $stmt->bind_param("si", $hashedPassword, $userId);
    $stmt->execute();
    
    if ($stmt->affected_rows > 0) {
        echo "Updated password for User ID: $userId\n";
        return true;
    } else {
        echo "Failed to update password for User ID: $userId\n";
        return false;
    }
}

// Main script
try {
    // Check if password_hash column exists, if not, create it
    $result = $mysqli->query("SHOW COLUMNS FROM users LIKE 'password_hash'");
    if ($result->num_rows == 0) {
        $mysqli->query("ALTER TABLE users ADD COLUMN password_hash VARCHAR(255) NOT NULL");
        echo "Added password_hash column to users table.\n";
    }

    // Define the user IDs and their new passwords
    $userPasswords = [
        5 => "Janova",
        4 => "Trumpeta",
        3 => "sisina22"
    ];

    $updatedCount = 0;
    foreach ($userPasswords as $userId => $newPassword) {
        if (updateUserPassword($userId, $newPassword)) {
            $updatedCount++;
        }
    }

    echo "Updated passwords for $updatedCount users.\n";

    // Optionally, remove the old password column if it exists
    $result = $mysqli->query("SHOW COLUMNS FROM users LIKE 'password'");
    if ($result->num_rows > 0) {
        $mysqli->query("ALTER TABLE users DROP COLUMN password");
        echo "Removed old password column.\n";
    }

    echo "Password update process completed successfully.\n";

} catch (Exception $e) {
    echo "An error occurred: " . $e->getMessage() . "\n";
}

// Close the database connection
$mysqli->close();
?>
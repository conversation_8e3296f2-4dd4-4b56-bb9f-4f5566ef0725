const ContactDetailsComponent = (() => {
    function ContactDetails() {
        const { selectedConversation } = window.OmnichannelContext.useOmnichannel();

        if (!selectedConversation) {
            return null;
        }

        return React.createElement(
            'div',
            { className: "w-64 bg-white border-l p-4" },
            React.createElement('h3', { className: "font-medium mb-4" }, "Detaily kontaktu"),
            React.createElement(
                'div',
                { className: "space-y-4" },
                React.createElement(
                    'div',
                    null,
                    React.createElement(
                        'label',
                        { className: "text-sm text-gray-500 flex items-center gap-2" },
                        React.createElement(
                            'svg',
                            { className: "h-4 w-4", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" },
                            React.createElement('path', {
                                strokeLinecap: "round",
                                strokeLinejoin: "round",
                                strokeWidth: 2,
                                d: "M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                            })
                        ),
                        "Telefon"
                    ),
                    React.createElement('p', { className: "font-medium" }, selectedConversation.phone)
                ),
                React.createElement(
                    'div',
                    null,
                    React.createElement(
                        'label',
                        { className: "text-sm text-gray-500 flex items-center gap-2" },
                        React.createElement(
                            'svg',
                            { className: "h-4 w-4", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" },
                            React.createElement('path', {
                                strokeLinecap: "round",
                                strokeLinejoin: "round",
                                strokeWidth: 2,
                                d: "M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                            })
                        ),
                        "Email"
                    ),
                    React.createElement('p', { className: "font-medium" }, selectedConversation.email)
                ),
                React.createElement(
                    'div',
                    null,
                    React.createElement(
                        'label',
                        { className: "text-sm text-gray-500 flex items-center gap-2" },
                        React.createElement(
                            'svg',
                            { className: "h-4 w-4", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" },
                            React.createElement('path', {
                                strokeLinecap: "round",
                                strokeLinejoin: "round",
                                strokeWidth: 2,
                                d: "M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"
                            })
                        ),
                        "Štítky"
                    ),
                    React.createElement(
                        'div',
                        { className: "flex flex-wrap gap-2 mt-1" },
                        selectedConversation.tags.map((tag) =>
                            React.createElement(
                                'span',
                                {
                                    key: tag,
                                    className: "bg-gray-200 text-gray-700 text-xs px-2 py-1 rounded-full"
                                },
                                tag
                            )
                        )
                    )
                )
            ),
            React.createElement(
                'button',
                {
                    className: "w-full mt-4 bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 flex items-center justify-center gap-2"
                },
                React.createElement(
                    'svg',
                    { className: "h-4 w-4", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" },
                    React.createElement('path', {
                        strokeLinecap: "round",
                        strokeLinejoin: "round",
                        strokeWidth: 2,
                        d: "M12 6v6m0 0v6m0-6h6m-6 0H6"
                    })
                ),
                "Vytvořit příležitost"
            )
        );
    }

    return { ContactDetails };
})();

window.ContactDetailsComponent = ContactDetailsComponent;
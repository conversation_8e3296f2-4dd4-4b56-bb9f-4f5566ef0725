<?php
/**
 * CGM Časová razítka - API endpoint
 * REST API pro správu dokumentů a časových razítek
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../cgm_timestamps/DocumentManager.php';
require_once __DIR__ . '/../cgm_timestamps/TimestampManager.php';

// Kontrola přihlášení
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit;
}

$documentManager = new DocumentManager();
$timestampManager = new TimestampManager();

$method = $_SERVER['REQUEST_METHOD'];
$path = $_GET['path'] ?? '';

try {
    switch ($method) {
        case 'GET':
            handleGetRequest($path, $documentManager, $timestampManager);
            break;
            
        case 'POST':
            handlePostRequest($path, $documentManager, $timestampManager);
            break;
            
        case 'PUT':
            handlePutRequest($path, $documentManager, $timestampManager);
            break;
            
        case 'DELETE':
            handleDeleteRequest($path, $documentManager, $timestampManager);
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'error' => 'Method not allowed']);
            break;
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Internal server error']);
    error_log("CGM API Error: " . $e->getMessage());
}

function handleGetRequest($path, $documentManager, $timestampManager) {
    switch ($path) {
        case 'documents':
            $filters = [
                'category_id' => $_GET['category_id'] ?? null,
                'search' => $_GET['search'] ?? null,
                'limit' => $_GET['limit'] ?? 20,
                'offset' => $_GET['offset'] ?? 0
            ];
            
            $result = $documentManager->getUserDocuments($_SESSION['user_id'], $filters);
            echo json_encode($result);
            break;
            
        case 'document':
            $document_id = $_GET['id'] ?? 0;
            $result = $documentManager->getDocument($document_id, $_SESSION['user_id']);
            echo json_encode($result);
            break;
            
        case 'categories':
            $categories = $documentManager->getCategories();
            echo json_encode(['success' => true, 'categories' => $categories]);
            break;
            
        case 'timestamps':
            $document_id = $_GET['document_id'] ?? 0;
            $timestamps = $timestampManager->getDocumentTimestamps($document_id);
            echo json_encode(['success' => true, 'timestamps' => $timestamps]);
            break;
            
        case 'verify-timestamp':
            $timestamp_id = $_GET['id'] ?? 0;
            $result = $timestampManager->verifyTimestamp($timestamp_id);
            echo json_encode($result);
            break;
            
        default:
            http_response_code(404);
            echo json_encode(['success' => false, 'error' => 'Endpoint not found']);
            break;
    }
}

function handlePostRequest($path, $documentManager, $timestampManager) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    switch ($path) {
        case 'document':
            // Vytvoření nového dokumentu
            $data = [
                'title' => $input['title'] ?? '',
                'content' => $input['content'] ?? '',
                'category_id' => $input['category_id'] ?? 1,
                'patient_id' => $input['patient_id'] ?? null
            ];
            
            $result = $documentManager->createDocument($data);
            echo json_encode($result);
            break;
            
        case 'timestamp':
            // Vytvoření nového časového razítka
            $document_id = $input['document_id'] ?? 0;
            
            // Získání dokumentu pro hash
            $document_result = $documentManager->getDocument($document_id, $_SESSION['user_id']);
            if (!$document_result['success']) {
                echo json_encode($document_result);
                break;
            }
            
            $result = $timestampManager->createTimestamp($document_id, $document_result['document']['document_hash']);
            echo json_encode($result);
            break;
            
        case 'upload':
            // Nahrání souboru
            if (!isset($_FILES['file'])) {
                echo json_encode(['success' => false, 'error' => 'No file uploaded']);
                break;
            }
            
            $data = [
                'title' => $_POST['title'] ?? '',
                'content' => $_POST['content'] ?? '',
                'category_id' => $_POST['category_id'] ?? 1,
                'patient_id' => $_POST['patient_id'] ?? null
            ];
            
            $result = $documentManager->createDocument($data, $_FILES['file']);
            echo json_encode($result);
            break;
            
        default:
            http_response_code(404);
            echo json_encode(['success' => false, 'error' => 'Endpoint not found']);
            break;
    }
}

function handlePutRequest($path, $documentManager, $timestampManager) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    switch ($path) {
        case 'document':
            // Aktualizace dokumentu (bude implementováno později)
            echo json_encode(['success' => false, 'error' => 'Document update not implemented yet']);
            break;
            
        default:
            http_response_code(404);
            echo json_encode(['success' => false, 'error' => 'Endpoint not found']);
            break;
    }
}

function handleDeleteRequest($path, $documentManager, $timestampManager) {
    switch ($path) {
        case 'document':
            // Smazání dokumentu (bude implementováno později)
            echo json_encode(['success' => false, 'error' => 'Document deletion not implemented yet']);
            break;
            
        default:
            http_response_code(404);
            echo json_encode(['success' => false, 'error' => 'Endpoint not found']);
            break;
    }
}

/**
 * Pomocné funkce pro validaci
 */
function validateDocumentData($data) {
    $errors = [];
    
    if (empty($data['title'])) {
        $errors[] = 'Title is required';
    }
    
    if (strlen($data['title']) > 255) {
        $errors[] = 'Title is too long';
    }
    
    return $errors;
}

/**
 * Pomocná funkce pro logování API volání
 */
function logApiCall($method, $path, $user_id, $success = true, $error = null) {
    $log_data = [
        'timestamp' => date('Y-m-d H:i:s'),
        'method' => $method,
        'path' => $path,
        'user_id' => $user_id,
        'success' => $success,
        'error' => $error,
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
    ];
    
    error_log("CGM API: " . json_encode($log_data));
}
?>

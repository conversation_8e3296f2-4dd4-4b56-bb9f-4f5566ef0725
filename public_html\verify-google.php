<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "Starting Google Client verification...\n";

// Check composer autoload paths
$paths = [
    __DIR__ . '/vendor/autoload.php',
    __DIR__ . '/../vendor/autoload.php',
    __DIR__ . '/../../vendor/autoload.php'
];

foreach ($paths as $path) {
    echo "Checking path: $path\n";
    if (file_exists($path)) {
        echo "Found autoloader at: $path\n";
        require_once $path;
        break;
    }
}

// Try to load the Google Client class
try {
    echo "Attempting to load Google_Client class...\n";
    $client = new Google_Client();
    echo "Successfully created Google_Client instance\n";
} catch (Exception $e) {
    echo "Error creating Google_Client: " . $e->getMessage() . "\n";
}

// Check if specific Google classes exist
$classes = [
    'Google_Client',
    'Google\Client',
    'Google_Service_Calendar',
    'Google\Service\Calendar'
];

foreach ($classes as $class) {
    echo "Checking if class exists: $class... " . (class_exists($class) ? "Yes" : "No") . "\n";
}

// Print loaded extensions
echo "\nLoaded Extensions:\n";
print_r(get_loaded_extensions());

// Check composer installation
if (file_exists(__DIR__ . '/composer.json')) {
    echo "\nComposer.json contents:\n";
    echo file_get_contents(__DIR__ . '/composer.json');
}


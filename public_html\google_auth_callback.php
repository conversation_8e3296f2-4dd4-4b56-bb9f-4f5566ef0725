<?php
require_once 'config.php';
require_once 'GoogleCalendarSync.php';

session_start();

if (!isset($_GET['code'])) {
    die('No code provided');
}

$googleCalendarSync = new GoogleCalendarSync();

try {
    $result = $googleCalendarSync->handleAuthCallback($_GET['code'], $_SESSION['user_id']);
    if ($result === true) {
        $_SESSION['sync_message'] = "Autorizace úspěšná. Token byl uložen.";
    } else {
        $_SESSION['sync_message'] = "Chyba při autorizaci: " . $result;
    }
} catch (Exception $e) {
    error_log("Error in google_auth_callback.php: " . $e->getMessage());
    $_SESSION['sync_message'] = "Chyba při zpracování autorizace: " . $e->getMessage();
}

// Redirect back to reservations page
header('Location: reservations.php');
exit;





<?php
require_once 'config.php';
require_once 'cgm_timestamps/DocumentManager.php';
require_once 'cgm_timestamps/TimestampManager.php';

// Kontrola přihlášení
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

$documentManager = new DocumentManager();
$timestampManager = new TimestampManager();

$error = null;
$success = null;
$currentPage = 'cgm_documents';

// Zpracování akcí
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'create_document':
                $data = [
                    'title' => $_POST['title'] ?? '',
                    'content' => $_POST['content'] ?? '',
                    'category_id' => $_POST['category_id'] ?? 1,
                    'patient_id' => $_POST['patient_id'] ?? null
                ];
                
                $file = isset($_FILES['document_file']) ? $_FILES['document_file'] : null;
                $result = $documentManager->createDocument($data, $file);
                
                if ($result['success']) {
                    $success = 'Dokument byl úspěšně vytvořen a opatřen časovým razítkem.';
                } else {
                    $error = $result['error'];
                }
                break;
                
            case 'create_timestamp':
                $document_id = $_POST['document_id'] ?? 0;
                $document = $documentManager->getDocument($document_id, $_SESSION['user_id']);
                
                if ($document['success']) {
                    $result = $timestampManager->createTimestamp($document_id, $document['document']['document_hash']);
                    if ($result['success']) {
                        $success = 'Nové časové razítko bylo úspěšně vytvořeno.';
                    } else {
                        $error = $result['error'];
                    }
                } else {
                    $error = 'Dokument nebyl nalezen.';
                }
                break;
        }
    }
}

// Získání seznamu dokumentů
$filters = [
    'category_id' => $_GET['category'] ?? null,
    'search' => $_GET['search'] ?? null,
    'limit' => 20,
    'offset' => ($_GET['page'] ?? 1 - 1) * 20
];

$documents_result = $documentManager->getUserDocuments($_SESSION['user_id'], $filters);
$documents = $documents_result['success'] ? $documents_result['documents'] : [];

// Získání kategorií
$categories = $documentManager->getCategories();
?>

<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CGM Časová razítka - Elektronická dokumentace</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="css/cgm_documents.css" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
        .document-card {
            transition: all 0.2s ease;
            border: 1px solid #e5e7eb;
        }
        .document-card:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            border-color: #3b82f6;
        }
        .timestamp-badge {
            background: linear-gradient(135deg, #10b981, #059669);
        }
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        .modal.active {
            display: flex;
            align-items: center;
            justify-content: center;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="flex min-h-screen">
        <?php include 'sidebar.php'; ?>
        
        <div class="flex-1 ml-64">
            <div class="p-8">
                <!-- Header -->
                <div class="mb-8">
                    <div class="flex items-center justify-between">
                        <div>
                            <h1 class="text-3xl font-bold text-gray-900">CGM Časová razítka</h1>
                            <p class="text-gray-600 mt-2">Elektronická dokumentace bez kompromisů</p>
                        </div>
                        <button onclick="openCreateModal()" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                            <svg class="w-5 h-5 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                            </svg>
                            Nový dokument
                        </button>
                    </div>
                </div>

                <!-- Alerts -->
                <?php if ($error): ?>
                    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                        <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>

                <?php if ($success): ?>
                    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                        <?php echo htmlspecialchars($success); ?>
                    </div>
                <?php endif; ?>

                <!-- Filters -->
                <div class="bg-white rounded-lg shadow-sm border p-6 mb-6">
                    <form method="GET" class="flex flex-wrap gap-4">
                        <div class="flex-1 min-w-64">
                            <input type="text" name="search" placeholder="Hledat v dokumentech..." 
                                   value="<?php echo htmlspecialchars($_GET['search'] ?? ''); ?>"
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                        <div>
                            <select name="category" class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                <option value="">Všechny kategorie</option>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?php echo $category['id']; ?>" 
                                            <?php echo ($_GET['category'] ?? '') == $category['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($category['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <button type="submit" class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg transition-colors">
                            Filtrovat
                        </button>
                    </form>
                </div>

                <!-- Documents Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <?php foreach ($documents as $document): ?>
                        <div class="document-card bg-white rounded-lg p-6">
                            <div class="flex items-start justify-between mb-4">
                                <div class="flex-1">
                                    <h3 class="font-semibold text-gray-900 mb-2">
                                        <?php echo htmlspecialchars($document['title']); ?>
                                    </h3>
                                    <p class="text-sm text-gray-600 mb-2">
                                        <?php echo htmlspecialchars($document['category_name']); ?>
                                    </p>
                                    <p class="text-xs text-gray-500">
                                        <?php echo date('d.m.Y H:i', strtotime($document['created_at'])); ?>
                                    </p>
                                </div>
                                <?php if ($document['timestamp_count'] > 0): ?>
                                    <span class="timestamp-badge text-white text-xs px-2 py-1 rounded-full">
                                        <?php echo $document['timestamp_count']; ?> razítek
                                    </span>
                                <?php endif; ?>
                            </div>
                            
                            <?php if ($document['content']): ?>
                                <p class="text-sm text-gray-700 mb-4 line-clamp-3">
                                    <?php echo htmlspecialchars(substr($document['content'], 0, 150)) . '...'; ?>
                                </p>
                            <?php endif; ?>
                            
                            <div class="flex gap-2">
                                <button onclick="viewDocument(<?php echo $document['id']; ?>)" 
                                        class="flex-1 bg-blue-50 hover:bg-blue-100 text-blue-700 px-4 py-2 rounded text-sm font-medium transition-colors">
                                    Zobrazit
                                </button>
                                <button onclick="createTimestamp(<?php echo $document['id']; ?>)" 
                                        class="bg-green-50 hover:bg-green-100 text-green-700 px-4 py-2 rounded text-sm font-medium transition-colors">
                                    + Razítko
                                </button>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <?php if (empty($documents)): ?>
                    <div class="text-center py-12">
                        <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Žádné dokumenty</h3>
                        <p class="text-gray-600 mb-4">Zatím nemáte žádné dokumenty. Vytvořte svůj první dokument.</p>
                        <button onclick="openCreateModal()" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                            Vytvořit dokument
                        </button>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Create Document Modal -->
    <div id="createModal" class="modal">
        <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-screen overflow-y-auto">
            <div class="p-6 border-b">
                <h2 class="text-xl font-semibold text-gray-900">Nový dokument</h2>
            </div>
            <form method="POST" enctype="multipart/form-data" class="p-6">
                <input type="hidden" name="action" value="create_document">
                
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Název dokumentu *</label>
                    <input type="text" name="title" required 
                           class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                </div>
                
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Kategorie</label>
                    <select name="category_id" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                        <?php foreach ($categories as $category): ?>
                            <option value="<?php echo $category['id']; ?>">
                                <?php echo htmlspecialchars($category['name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Obsah dokumentu</label>
                    <textarea name="content" rows="6" 
                              class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"></textarea>
                </div>
                
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Soubor (volitelné)</label>
                    <input type="file" name="document_file" accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.gif,.txt"
                           class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    <p class="text-xs text-gray-500 mt-1">Podporované formáty: PDF, DOC, DOCX, JPG, PNG, GIF, TXT (max 10MB)</p>
                </div>
                
                <div class="flex gap-4">
                    <button type="submit" class="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-lg font-medium transition-colors">
                        Vytvořit dokument
                    </button>
                    <button type="button" onclick="closeCreateModal()" class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                        Zrušit
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        function openCreateModal() {
            document.getElementById('createModal').classList.add('active');
        }
        
        function closeCreateModal() {
            document.getElementById('createModal').classList.remove('active');
        }
        
        function viewDocument(documentId) {
            window.location.href = 'cgm_document_detail.php?id=' + documentId;
        }
        
        function createTimestamp(documentId) {
            if (confirm('Opravdu chcete vytvořit nové časové razítko pro tento dokument?')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="create_timestamp">
                    <input type="hidden" name="document_id" value="${documentId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }
        
        // Close modal when clicking outside
        document.getElementById('createModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeCreateModal();
            }
        });
    </script>
    <script src="js/cgm_documents.js"></script>
</body>
</html>

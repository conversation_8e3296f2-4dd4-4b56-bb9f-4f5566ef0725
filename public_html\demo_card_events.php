<?php
/**
 * Demo simulátor pro testování automatických událostí karty pacienta
 */

require_once 'config.php';
require_once 'patient_card/CardEventHandler.php';

// Kontrola přihlášení
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

$message = null;
$error = null;

// Zpracování akcí
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $patient_id = $_POST['patient_id'] ?? 1;
    
    switch ($action) {
        case 'simulate_document':
            $result = onDocumentSaved($patient_id, [
                'title' => $_POST['title'] ?? 'Testovací záznam',
                'content' => $_POST['content'] ?? 'Obsah testovacího záznamu vytvořeného simulátorem.',
                'category' => 'general',
                'visit_id' => 'visit_' . uniqid()
            ]);
            break;
            
        case 'simulate_image':
            $result = onImageUploaded($patient_id, [
                'title' => 'RTG snímek',
                'description' => 'Panoramatický RTG snímek',
                'image_type' => 'rtg',
                'file_path' => 'uploads/demo/rtg_' . uniqid() . '.jpg',
                'file_size' => 245760,
                'mime_type' => 'image/jpeg',
                'visit_id' => 'visit_' . uniqid()
            ]);
            break;
            
        case 'simulate_procedure':
            $result = onProcedureCompleted($patient_id, [
                'procedure_code' => '001',
                'procedure_name' => 'Preventivní prohlídka',
                'description' => 'Rutinní preventivní prohlídka s čištěním',
                'tooth_number' => null,
                'duration_minutes' => 30,
                'price' => 500,
                'insurance_covered' => true,
                'visit_id' => 'visit_' . uniqid()
            ]);
            break;
            
        case 'simulate_consent':
            $result = onConsentSigned($patient_id, [
                'consent_type' => 'treatment',
                'consent_text' => 'Souhlasím s provedením preventivní prohlídky a souvisejících výkonů.',
                'signed_by' => 'patient',
                'visit_id' => 'visit_' . uniqid()
            ]);
            break;
            
        case 'simulate_payment':
            $result = onPaymentReceived($patient_id, [
                'amount' => 500,
                'currency' => 'CZK',
                'payment_method' => 'card',
                'transaction_id' => 'tx_' . uniqid(),
                'description' => 'Platba za preventivní prohlídku',
                'visit_id' => 'visit_' . uniqid()
            ]);
            break;
            
        case 'simulate_call':
            $result = onCallCompleted($patient_id, [
                'summary' => 'Pacient volal ohledně změny termínu návštěvy.',
                'duration' => 120,
                'call_type' => 'incoming',
                'result' => 'completed',
                'phone_number' => '+420123456789'
            ]);
            break;
            
        default:
            $result = ['success' => false, 'error' => 'Neznámá akce'];
    }
    
    if ($result['success']) {
        $message = 'Událost byla úspěšně zpracována a přidána do karty pacienta!';
    } else {
        $error = $result['error'];
    }
}

$currentPage = 'cgm_documents';
?>

<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo - Automatické události karty pacienta</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
    </style>
</head>
<body class="bg-gray-50">
    <div class="flex min-h-screen">
        <?php include 'sidebar.php'; ?>
        
        <div class="flex-1 ml-64">
            <div class="p-8">
                <!-- Header -->
                <div class="mb-8">
                    <h1 class="text-3xl font-bold text-gray-900">Demo - Automatické události</h1>
                    <p class="text-gray-600 mt-2">Simulátor pro testování automatického naplňování karty pacienta</p>
                </div>

                <!-- Alerts -->
                <?php if ($error): ?>
                    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                        <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>

                <?php if ($message): ?>
                    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                        <?php echo htmlspecialchars($message); ?>
                        <a href="patient_card_view.php?patient_id=1" class="ml-4 underline">Zobrazit kartu pacienta</a>
                    </div>
                <?php endif; ?>

                <!-- Info Box -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
                    <h2 class="text-lg font-semibold text-blue-900 mb-2">Jak to funguje</h2>
                    <div class="text-blue-800 space-y-2">
                        <p>• Každá aktivita v CRM automaticky vyvolá událost</p>
                        <p>• CardEventHandler zachytí událost a přidá položku do karty pacienta</p>
                        <p>• Každá položka dostane okamžité časové razítko (TST)</p>
                        <p>• Celá karta se denně agregovaně razítkuje</p>
                        <p>• Lékař nemusí dělat nic - vše běží automaticky na pozadí</p>
                    </div>
                </div>

                <!-- Patient Selection -->
                <div class="bg-white rounded-lg shadow-sm border p-6 mb-8">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">Výběr pacienta</h2>
                    <form method="GET" class="flex items-center gap-4">
                        <label class="text-sm font-medium text-gray-700">Pacient ID:</label>
                        <select name="patient_id" class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                            <option value="1" <?php echo ($_GET['patient_id'] ?? 1) == 1 ? 'selected' : ''; ?>>1 - Jan Novák</option>
                            <option value="2" <?php echo ($_GET['patient_id'] ?? 1) == 2 ? 'selected' : ''; ?>>2 - Marie Svobodová</option>
                            <option value="3" <?php echo ($_GET['patient_id'] ?? 1) == 3 ? 'selected' : ''; ?>>3 - Pavel Dvořák</option>
                            <option value="4" <?php echo ($_GET['patient_id'] ?? 1) == 4 ? 'selected' : ''; ?>>4 - Anna Procházková</option>
                        </select>
                        <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                            Vybrat
                        </button>
                        <a href="patient_card_view.php?patient_id=<?php echo $_GET['patient_id'] ?? 1; ?>" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors">
                            Zobrazit kartu
                        </a>
                    </form>
                </div>

                <!-- Event Simulators -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- Document Event -->
                    <div class="bg-white rounded-lg shadow-sm border p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">📝 Uložení záznamu</h3>
                        <form method="POST">
                            <input type="hidden" name="action" value="simulate_document">
                            <input type="hidden" name="patient_id" value="<?php echo $_GET['patient_id'] ?? 1; ?>">
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Název</label>
                                <input type="text" name="title" value="Preventivní prohlídka" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Obsah</label>
                                <textarea name="content" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">Provedena rutinní preventivní prohlídka. Stav chrupu dobrý.</textarea>
                            </div>
                            <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 rounded-lg transition-colors">
                                Simulovat uložení
                            </button>
                        </form>
                    </div>

                    <!-- Image Event -->
                    <div class="bg-white rounded-lg shadow-sm border p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">📷 Nahrání snímku</h3>
                        <form method="POST">
                            <input type="hidden" name="action" value="simulate_image">
                            <input type="hidden" name="patient_id" value="<?php echo $_GET['patient_id'] ?? 1; ?>">
                            <p class="text-sm text-gray-600 mb-4">Simuluje nahrání RTG snímku do karty pacienta.</p>
                            <button type="submit" class="w-full bg-green-600 hover:bg-green-700 text-white py-2 rounded-lg transition-colors">
                                Simulovat RTG snímek
                            </button>
                        </form>
                    </div>

                    <!-- Procedure Event -->
                    <div class="bg-white rounded-lg shadow-sm border p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">🦷 Dokončený výkon</h3>
                        <form method="POST">
                            <input type="hidden" name="action" value="simulate_procedure">
                            <input type="hidden" name="patient_id" value="<?php echo $_GET['patient_id'] ?? 1; ?>">
                            <p class="text-sm text-gray-600 mb-4">Simuluje dokončení preventivní prohlídky.</p>
                            <button type="submit" class="w-full bg-yellow-600 hover:bg-yellow-700 text-white py-2 rounded-lg transition-colors">
                                Simulovat výkon
                            </button>
                        </form>
                    </div>

                    <!-- Consent Event -->
                    <div class="bg-white rounded-lg shadow-sm border p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">✍️ Podepsaný souhlas</h3>
                        <form method="POST">
                            <input type="hidden" name="action" value="simulate_consent">
                            <input type="hidden" name="patient_id" value="<?php echo $_GET['patient_id'] ?? 1; ?>">
                            <p class="text-sm text-gray-600 mb-4">Simuluje podepsání souhlasu s léčbou.</p>
                            <button type="submit" class="w-full bg-purple-600 hover:bg-purple-700 text-white py-2 rounded-lg transition-colors">
                                Simulovat souhlas
                            </button>
                        </form>
                    </div>

                    <!-- Payment Event -->
                    <div class="bg-white rounded-lg shadow-sm border p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">💳 Přijatá platba</h3>
                        <form method="POST">
                            <input type="hidden" name="action" value="simulate_payment">
                            <input type="hidden" name="patient_id" value="<?php echo $_GET['patient_id'] ?? 1; ?>">
                            <p class="text-sm text-gray-600 mb-4">Simuluje platbu kartou za výkon.</p>
                            <button type="submit" class="w-full bg-red-600 hover:bg-red-700 text-white py-2 rounded-lg transition-colors">
                                Simulovat platbu
                            </button>
                        </form>
                    </div>

                    <!-- Call Event -->
                    <div class="bg-white rounded-lg shadow-sm border p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">📞 Dokončený hovor</h3>
                        <form method="POST">
                            <input type="hidden" name="action" value="simulate_call">
                            <input type="hidden" name="patient_id" value="<?php echo $_GET['patient_id'] ?? 1; ?>">
                            <p class="text-sm text-gray-600 mb-4">Simuluje hovor s pacientem přes voicebot.</p>
                            <button type="submit" class="w-full bg-indigo-600 hover:bg-indigo-700 text-white py-2 rounded-lg transition-colors">
                                Simulovat hovor
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>

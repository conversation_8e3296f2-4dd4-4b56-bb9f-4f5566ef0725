<?php
header('Content-Type: application/json');

// Základní konfigurace
$config = [
    'storage_file' => __DIR__ . '/../data/settings.json',
    'storage_dir' => __DIR__ . '/../data'
];

// Vytvoření složky pro data, pokud neexistuje
if (!file_exists($config['storage_dir'])) {
    mkdir($config['storage_dir'], 0777, true);
}

try {
    // Načtení dat z POST requestu
    $data = json_decode(file_get_contents('php://input'), true);
    
    if (!$data) {
        throw new Exception('Invalid JSON data');
    }
    
    // Uložení nastavení do souboru
    if (file_put_contents($config['storage_file'], json_encode($data, JSON_PRETTY_PRINT))) {
        echo json_encode(['success' => true]);
    } else {
        throw new Exception('Failed to save settings');
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}


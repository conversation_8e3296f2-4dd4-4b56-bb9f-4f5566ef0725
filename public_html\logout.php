<?php
require_once 'config.php';
require_once 'error_log.php';

// Ensure session is started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

writeErrorLog('Logout process started');

writeErrorLog("Logout.php accessed. SESSION data: " . print_r($_SESSION, true));

// Set security headers
set_security_headers();

// Set Content Security Policy
set_content_security_policy();

// Ensure the user is logged in
if (!isset($_SESSION['user_id'])) {
    writeErrorLog('Logout attempted without active session');
    header('Location: login.php');
    exit;
}

// Generate CSRF token if not exists
$csrf_token = generateCSRFToken();

// Check if it's a POST request (for AJAX calls) or a direct link
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    writeErrorLog('POST request received for logout');
    // For AJAX requests, we still want to verify CSR<PERSON> token
    if (!isset($_POST['csrf_token']) || !verifyCSRFToken($_POST['csrf_token'])) {
        writeErrorLog('CSRF token verification failed for AJAX logout');
        header('HTTP/1.1 403 Forbidden');
        echo json_encode(['success' => false, 'message' => 'Invalid request']);
        exit;
    }
} elseif ($_SERVER['REQUEST_METHOD'] === 'GET') {
    writeErrorLog('Direct access to logout.php');
    // For GET requests, we'll show a confirmation page with a form
    ?>
    <!DOCTYPE html>
    <html lang="cs">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Odhlášení - Dentibot</title>
        <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
        <style>
            body {
                font-family: 'Inter', sans-serif;
                background-color: #0A0A0A;
                color: #fff;
            }
            .logout-container {
                background-color: #1E1E2E;
                border-radius: 8px;
                width: 100%;
                max-width: 400px;
                padding: 2rem;
            }
            .gradient-text {
                background: linear-gradient(90deg, #4fd1c5 0%, #38b2ac 100%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
            }
            .submit-button {
                width: 100%;
                padding: 0.75rem;
                background: linear-gradient(90deg, #4fd1c5 0%, #38b2ac 100%);
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: 500;
                cursor: pointer;
                transition: opacity 0.2s;
            }
            .submit-button:hover {
                opacity: 0.9;
            }
            .cancel-link {
                color: #94A3B8;
                text-decoration: none;
                font-size: 0.875rem;
                transition: color 0.2s;
            }
            .cancel-link:hover {
                color: #4fd1c5;
            }
        </style>
    </head>
    <body class="min-h-screen flex items-center justify-center p-4">
        <div class="logout-container">
            <h1 class="text-2xl font-bold mb-8 gradient-text text-center">Odhlášení z Dentibot</h1>
            <p class="mb-6 text-center">Opravdu se chcete odhlásit?</p>
            <form method="POST" action="" class="space-y-4">
                <input type="hidden" name="csrf_token" value="<?php echo htmlspecialchars($csrf_token, ENT_QUOTES, 'UTF-8'); ?>">
                <div class="pt-2">
                    <button type="submit" class="submit-button">
                        Odhlásit se
                    </button>
                </div>
            </form>
            <div class="mt-6 text-center">
                <a href="dashboard.php" class="cancel-link">
                    Zrušit a vrátit se na dashboard
                </a>
            </div>
        </div>
    </body>
    </html>
    <?php
    exit;
} else {
    writeErrorLog('Invalid request method for logout');
    header('HTTP/1.1 405 Method Not Allowed');
    exit;
}

// Perform logout
$username = $_SESSION['username'] ?? 'Unknown';
writeErrorLog("Logging out user: $username");

logoutUser();

writeErrorLog("Session after logout: " . print_r($_SESSION, true));

// Clear any output buffers
while (ob_get_level()) {
    ob_end_clean();
}

// Determine if it's an AJAX request
$isAjax = !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest';

if ($isAjax) {
    writeErrorLog('Sending AJAX response for logout');
    header('Content-Type: application/json');
    echo json_encode(['success' => true, 'message' => 'Logout successful']);
} else {
    writeErrorLog('Redirecting to login page after logout');
    header("Location: login.php?logout=success");
}

writeErrorLog('Logout process completed');
exit();
?>


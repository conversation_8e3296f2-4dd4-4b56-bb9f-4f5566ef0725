<?php
require_once 'config.php';

function loadGoogleCredentials() {
    $mysqli = getDatabaseConnection();
    
    $stmt = $mysqli->prepare("SELECT * FROM google_api_credentials WHERE id = 1");
    $stmt->execute();
    $result = $stmt->get_result();
    $credentials = $result->fetch_assoc();
    $stmt->close();

    if (!$credentials) {
        throw new Exception("Google API credentials nebyly nalezeny v databázi");
    }

    return [
        'client_id' => $credentials['client_id'],
        'client_secret' => $credentials['client_secret'],
        'redirect_uri' => $credentials['redirect_uri'],
        'developer_key' => $credentials['developer_key'],
        'project_id' => $credentials['project_id']
    ];
}

$googleCredentials = loadGoogleCredentials();

define('GOOGLE_CLIENT_ID', $googleCredentials['client_id']);
define('GOOGLE_CLIENT_SECRET', $googleCredentials['client_secret']);
define('GOOGLE_REDIRECT_URI', $googleCredentials['redirect_uri']);
<?php
require_once 'config.php';

$mysqli = getDbConnection();

$query = "DESCRIBE users";
$result = $mysqli->query($query);

if ($result) {
    $subscriptionPlanExists = false;
    while ($row = $result->fetch_assoc()) {
        if ($row['Field'] == 'subscription_plan') {
            $subscriptionPlanExists = true;
            break;
        }
    }
    
    if ($subscriptionPlanExists) {
        echo "Sloupec 'subscription_plan' existuje v tabulce 'users'.";
    } else {
        echo "Sloupec 'subscription_plan' neexistuje v tabulce 'users'. Přidejte ho pomocí následujícího SQL příkazu:";
        echo "\n\nALTER TABLE users ADD COLUMN subscription_plan VARCHAR(50) DEFAULT 'Základní';";
    }
} else {
    echo "Chyba při získávání struktury tabulky: " . $mysqli->error;
}

$mysqli->close();


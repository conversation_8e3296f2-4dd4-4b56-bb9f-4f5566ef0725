<?php
require_once 'config.php';
require_once 'api_functions.php';
require_once 'error_log.php';
require_once 'includes/credit_system.php';

// Zpracování požadavků souvisejících s platbami za SMS
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Kontrola, zda je uživatel přihlášen
    session_start();
    if (!isset($_SESSION['user_id'])) {
        echo json_encode(['success' => false, 'message' => 'Uživatel není přihlášen']);
        exit;
    }
    
    $userId = $_SESSION['user_id'];
    
    // Zpracování různých typů akcí
    $action = isset($_POST['action']) ? $_POST['action'] : '';
    
    switch ($action) {
        case 'recharge_credit':
            // Dobití kreditu
            $amount = isset($_POST['amount']) ? floatval($_POST['amount']) : 0;
            $paymentMethod = isset($_POST['payment_method']) ? $_POST['payment_method'] : '';
            
            if ($amount <= 0) {
                echo json_encode(['success' => false, 'message' => 'Neplatná částka']);
                exit;
            }
            
            try {
                // Přidání kreditů na účet uživatele
                $description = "Dobití kreditu: $amount Kč";
                $reference_id = 'PAYMENT_' . time();
                
                // Převod peněz na kredity (1 Kč = 1 kredit)
                $credits = $amount;
                
                addUserCredits($userId, $credits, 'purchase', $description, $reference_id);
                
                // Získání aktuálního zůstatku
                $newBalance = getUserCreditBalance($userId);
                
                echo json_encode([
                    'success' => true, 
                    'message' => 'Kredit byl úspěšně dobit',
                    'new_balance' => $newBalance
                ]);
            } catch (Exception $e) {
                writeErrorLog('Error recharging credit', [
                    'user_id' => $userId,
                    'amount' => $amount,
                    'error' => $e->getMessage()
                ]);
                
                echo json_encode([
                    'success' => false, 
                    'message' => 'Při dobíjení kreditu došlo k chybě: ' . $e->getMessage()
                ]);
            }
            break;
            
        case 'get_credit':
            // Získání aktuálního zůstatku
            try {
                $credit = getUserCreditBalance($userId);
                echo json_encode([
                    'success' => true,
                    'credit' => $credit
                ]);
            } catch (Exception $e) {
                writeErrorLog('Error getting credit balance', [
                    'user_id' => $userId,
                    'error' => $e->getMessage()
                ]);
                
                echo json_encode([
                    'success' => false,
                    'message' => 'Nepodařilo se načíst zůstatek kreditu: ' . $e->getMessage()
                ]);
            }
            break;
            
        case 'get_usage_history':
            // Získání historie využití
            $period = isset($_POST['period']) ? $_POST['period'] : 'month';
            $limit = isset($_POST['limit']) ? intval($_POST['limit']) : 10;
            $offset = isset($_POST['offset']) ? intval($_POST['offset']) : 0;
            
            try {
                $transactions = getUserCreditHistory($userId, $limit, $offset);
                
                // Filtrování transakcí podle typu a období
                $filteredTransactions = [];
                $startDate = null;
                
                switch ($period) {
                    case 'week':
                        $startDate = strtotime('-1 week');
                        break;
                    case 'month':
                        $startDate = strtotime('-1 month');
                        break;
                    case 'year':
                        $startDate = strtotime('-1 year');
                        break;
                    case 'all':
                    default:
                        $startDate = null;
                        break;
                }
                
                foreach ($transactions as $transaction) {
                    // Filtrování podle období
                    if ($startDate !== null) {
                        $transactionDate = strtotime($transaction['created_at']);
                        if ($transactionDate < $startDate) {
                            continue;
                        }
                    }
                    
                    // Filtrování pouze transakcí typu 'usage'
                    if ($transaction['type'] === 'usage') {
                        $filteredTransactions[] = [
                            'date' => $transaction['created_at'],
                            'description' => $transaction['description'],
                            'amount' => abs($transaction['amount']),
                            'reference_id' => $transaction['reference_id']
                        ];
                    }
                }
                
                echo json_encode([
                    'success' => true,
                    'history' => $filteredTransactions
                ]);
            } catch (Exception $e) {
                writeErrorLog('Error getting usage history', [
                    'user_id' => $userId,
                    'period' => $period,
                    'error' => $e->getMessage()
                ]);
                
                echo json_encode([
                    'success' => false,
                    'message' => 'Nepodařilo se načíst historii využití: ' . $e->getMessage()
                ]);
            }
            break;
            
        case 'get_price':
            // Získání ceny za SMS
            try {
                $pricing = getSmsPrice($userId);
                echo json_encode([
                    'success' => true,
                    'price' => $pricing['price_per_sms']
                ]);
            } catch (Exception $e) {
                writeErrorLog('Error getting SMS price', [
                    'user_id' => $userId,
                    'error' => $e->getMessage()
                ]);
                
                echo json_encode([
                    'success' => false,
                    'message' => 'Nepodařilo se načíst cenu SMS: ' . $e->getMessage()
                ]);
            }
            break;
            
        default:
            echo json_encode(['success' => false, 'message' => 'Neznámá akce']);
            break;
    }
} else {
    // Metoda není povolena
    header('HTTP/1.1 405 Method Not Allowed');
    echo json_encode(['success' => false, 'message' => 'Metoda není povolena']);
}
?>


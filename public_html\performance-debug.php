<?php
// Performance Debug Tool for Dentibot Landing Page
// Place this file in your root directory and access it via browser

// Display all PHP errors
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Start timing
$start_time = microtime(true);
$timing_points = [];

// Function to record timing points
function record_timing($label) {
    global $start_time, $timing_points;
    $timing_points[$label] = round((microtime(true) - $start_time) * 1000, 2);
}

// Function to get file size in human-readable format
function format_size($size) {
    $units = ['B', 'KB', 'MB', 'GB'];
    $i = 0;
    while ($size >= 1024 && $i < count($units) - 1) {
        $size /= 1024;
        $i++;
    }
    return round($size, 2) . ' ' . $units[$i];
}

// Function to check if URL exists and get its size
function get_remote_file_info($url) {
    $start = microtime(true);
    $headers = get_headers($url, 1);
    $time = round((microtime(true) - $start) * 1000, 2);
    
    if ($headers === false) {
        return [
            'exists' => false,
            'size' => 'N/A',
            'time' => $time
        ];
    }
    
    $status = substr($headers[0], 9, 3);
    $exists = $status >= 200 && $status < 400;
    
    $size = 'Unknown';
    if (isset($headers['Content-Length'])) {
        $size = format_size((int)$headers['Content-Length']);
    }
    
    return [
        'exists' => $exists,
        'size' => $size,
        'time' => $time
    ];
}

// Record initial timing
record_timing('start');

// Check PHP configuration
$memory_limit = ini_get('memory_limit');
$max_execution_time = ini_get('max_execution_time');
$post_max_size = ini_get('post_max_size');
$upload_max_filesize = ini_get('upload_max_filesize');

// Check server information
$server_software = $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown';
$php_version = phpversion();

// Check if OPCache is enabled
$opcache_enabled = function_exists('opcache_get_status') && opcache_get_status(false) !== false;

// Check if mod_deflate/gzip is enabled
$gzip_enabled = isset($_SERVER['HTTP_ACCEPT_ENCODING']) && strpos($_SERVER['HTTP_ACCEPT_ENCODING'], 'gzip') !== false;

// Record timing after configuration checks
record_timing('config_check');

// Analyze directory structure
$directories = [
    'landing-page',
    'landing-page/config',
    'landing-page/includes',
    'landing-page/sections',
    'landing-page/css',
    'landing-page/js',
    'seo'
];

$directory_stats = [];
foreach ($directories as $dir) {
    if (is_dir($dir)) {
        $files = glob("$dir/*");
        $total_size = 0;
        foreach ($files as $file) {
            if (is_file($file)) {
                $total_size += filesize($file);
            }
        }
        
        $directory_stats[$dir] = [
            'exists' => true,
            'file_count' => count($files),
            'total_size' => format_size($total_size)
        ];
    } else {
        $directory_stats[$dir] = [
            'exists' => false,
            'file_count' => 0,
            'total_size' => 'N/A'
        ];
    }
}

// Record timing after directory analysis
record_timing('directory_analysis');

// Analyze CSS files
$css_files = [
    'landing-page/css/styles.css',
    'landing-page/css/animations.css',
    'landing-page/css/combined.min.css' // Check if this exists from previous optimization
];

$css_stats = [];
foreach ($css_files as $file) {
    if (file_exists($file)) {
        $size = filesize($file);
        $content = file_get_contents($file);
        $selectors_count = substr_count($content, '{');
        
        $css_stats[$file] = [
            'exists' => true,
            'size' => format_size($size),
            'selectors' => $selectors_count
        ];
    } else {
        $css_stats[$file] = [
            'exists' => false,
            'size' => 'N/A',
            'selectors' => 0
        ];
    }
}

// Record timing after CSS analysis
record_timing('css_analysis');

// Analyze JS files
$js_files = [
    'landing-page/js/main.js',
    'landing-page/js/slider.js',
    'landing-page/js/cost-comparison.js',
    'landing-page/js/form-handler.js',
    'landing-page/js/combined.min.js' // Check if this exists from previous optimization
];

$js_stats = [];
foreach ($js_files as $file) {
    if (file_exists($file)) {
        $size = filesize($file);
        $js_stats[$file] = [
            'exists' => true,
            'size' => format_size($size)
        ];
    } else {
        $js_stats[$file] = [
            'exists' => false,
            'size' => 'N/A'
        ];
    }
}

// Record timing after JS analysis
record_timing('js_analysis');

// Check external resources
$external_resources = [
    'Tailwind CSS' => 'https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css',
    'Google Fonts' => 'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap',
    'Cal.com Embed' => 'https://app.cal.com/embed/embed.js'
];

$external_stats = [];
foreach ($external_resources as $name => $url) {
    $external_stats[$name] = get_remote_file_info($url);
}

// Record timing after external resource checks
record_timing('external_resources');

// Check image files
$image_files = glob('images/*.{jpg,jpeg,png,gif,webp,svg}', GLOB_BRACE);
$image_stats = [];

foreach ($image_files as $image) {
    if (file_exists($image)) {
        $size = filesize($image);
        $dimensions = getimagesize($image);
        
        $image_stats[$image] = [
            'exists' => true,
            'size' => format_size($size),
            'dimensions' => $dimensions[0] . 'x' . $dimensions[1]
        ];
    }
}

// Record timing after image analysis
record_timing('image_analysis');

// Check landing.php performance
$landing_file = 'landing.php';
$landing_stats = [];

if (file_exists($landing_file)) {
    $size = filesize($landing_file);
    $content = file_get_contents($landing_file);
    $include_count = substr_count($content, 'include') + substr_count($content, 'require');
    
    $landing_stats = [
        'exists' => true,
        'size' => format_size($size),
        'include_count' => $include_count
    ];
} else {
    $landing_stats = [
        'exists' => false,
        'size' => 'N/A',
        'include_count' => 0
    ];
}

// Record timing after landing.php analysis
record_timing('landing_analysis');

// Check for .htaccess optimizations
$htaccess_file = '.htaccess';
$htaccess_stats = [];

if (file_exists($htaccess_file)) {
    $content = file_get_contents($htaccess_file);
    $has_expires = strpos($content, 'ExpiresActive') !== false;
    $has_deflate = strpos($content, 'DEFLATE') !== false;
    $has_cache_control = strpos($content, 'Cache-Control') !== false;
    
    $htaccess_stats = [
        'exists' => true,
        'has_expires' => $has_expires,
        'has_deflate' => $has_deflate,
        'has_cache_control' => $has_cache_control
    ];
} else {
    $htaccess_stats = [
        'exists' => false,
        'has_expires' => false,
        'has_deflate' => false,
        'has_cache_control' => false
    ];
}

// Record timing after .htaccess analysis
record_timing('htaccess_analysis');

// Simulate page load time
$page_url = 'http' . (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 's' : '') . '://' . $_SERVER['HTTP_HOST'] . '/landing.php';
$page_load_start = microtime(true);
$page_content = @file_get_contents($page_url);
$page_load_time = round((microtime(true) - $page_load_start) * 1000, 2);

// Record final timing
record_timing('end');

// Calculate total execution time
$total_execution_time = $timing_points['end'];

// Generate recommendations based on findings
$recommendations = [];

// Check CSS optimization
if (!$css_stats['landing-page/css/combined.min.css']['exists']) {
    $recommendations[] = 'Combine and minify CSS files to reduce HTTP requests and file size.';
}

// Check JS optimization
if (!$js_stats['landing-page/js/combined.min.js']['exists']) {
    $recommendations[] = 'Combine and minify JavaScript files to reduce HTTP requests and file size.';
}

// Check .htaccess optimizations
if (!$htaccess_stats['exists'] || !$htaccess_stats['has_expires'] || !$htaccess_stats['has_deflate']) {
    $recommendations[] = 'Add or update .htaccess file with proper caching and compression directives.';
}

// Check image optimization
$large_images = [];
foreach ($image_stats as $image => $stats) {
    if (strpos($stats['size'], 'MB') !== false || (strpos($stats['size'], 'KB') !== false && (float)$stats['size'] > 200)) {
        $large_images[] = $image;
    }
}

if (!empty($large_images)) {
    $recommendations[] = 'Optimize large images: ' . implode(', ', $large_images);
}

// Check include count
if ($landing_stats['include_count'] > 10) {
    $recommendations[] = 'Consider reducing the number of includes in landing.php to improve performance.';
}

// Check for OPCache
if (!$opcache_enabled) {
    $recommendations[] = 'Enable OPCache to improve PHP performance.';
}

// Check for Gzip compression
if (!$gzip_enabled) {
    $recommendations[] = 'Enable Gzip compression to reduce transfer size.';
}

// Output HTML report
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dentibot Performance Debug</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        .timing-bar {
            height: 20px;
            background: linear-gradient(90deg, #4fd1c5 0%, #38b2ac 100%);
        }
    </style>
</head>
<body class="bg-gray-100 text-gray-800">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold mb-6">Dentibot Performance Debug</h1>
        
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-2xl font-bold mb-4">Summary</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div class="bg-gray-100 p-4 rounded">
                    <h3 class="font-bold text-lg mb-2">Total Execution Time</h3>
                    <p class="text-3xl font-bold text-teal-600"><?php echo $total_execution_time; ?> ms</p>
                </div>
                <div class="bg-gray-100 p-4 rounded">
                    <h3 class="font-bold text-lg mb-2">Page Load Time</h3>
                    <p class="text-3xl font-bold <?php echo $page_load_time > 1000 ? 'text-red-600' : 'text-teal-600'; ?>">
                        <?php echo $page_load_time; ?> ms
                    </p>
                </div>
                <div class="bg-gray-100 p-4 rounded">
                    <h3 class="font-bold text-lg mb-2">PHP Version</h3>
                    <p class="text-3xl font-bold text-teal-600"><?php echo $php_version; ?></p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-2xl font-bold mb-4">Timing Analysis</h2>
            <div class="overflow-x-auto">
                <table class="min-w-full bg-white">
                    <thead>
                        <tr>
                            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                Operation
                            </th>
                            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                Time (ms)
                            </th>
                            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                Visualization
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php 
                        $prev_time = 0;
                        foreach ($timing_points as $label => $time): 
                            if ($label === 'end') continue;
                            $duration = $label === 'start' ? $time : $time - $prev_time;
                            $prev_time = $time;
                            $percentage = ($duration / $total_execution_time) * 100;
                        ?>
                        <tr>
                            <td class="py-2 px-4 border-b border-gray-200"><?php echo ucfirst(str_replace('_', ' ', $label)); ?></td>
                            <td class="py-2 px-4 border-b border-gray-200"><?php echo $duration; ?> ms</td>
                            <td class="py-2 px-4 border-b border-gray-200 w-1/2">
                                <div class="timing-bar" style="width: <?php echo $percentage; ?>%"></div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-2xl font-bold mb-4">Recommendations</h2>
            <?php if (empty($recommendations)): ?>
                <p class="text-green-600">No performance issues detected. Your site is well optimized!</p>
            <?php else: ?>
                <ul class="list-disc pl-5 space-y-2">
                    <?php foreach ($recommendations as $recommendation): ?>
                        <li class="text-red-600"><?php echo $recommendation; ?></li>
                    <?php endforeach; ?>
                </ul>
            <?php endif; ?>
        </div>
        
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-2xl font-bold mb-4">Server Configuration</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <h3 class="font-bold text-lg mb-2">PHP Settings</h3>
                    <ul class="list-disc pl-5 space-y-1">
                        <li>Memory Limit: <?php echo $memory_limit; ?></li>
                        <li>Max Execution Time: <?php echo $max_execution_time; ?> seconds</li>
                        <li>Post Max Size: <?php echo $post_max_size; ?></li>
                        <li>Upload Max Filesize: <?php echo $upload_max_filesize; ?></li>
                        <li>OPCache Enabled: <?php echo $opcache_enabled ? 'Yes' : 'No'; ?></li>
                    </ul>
                </div>
                <div>
                    <h3 class="font-bold text-lg mb-2">Server Information</h3>
                    <ul class="list-disc pl-5 space-y-1">
                        <li>Server Software: <?php echo $server_software; ?></li>
                        <li>PHP Version: <?php echo $php_version; ?></li>
                        <li>Gzip Compression: <?php echo $gzip_enabled ? 'Enabled' : 'Disabled'; ?></li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-2xl font-bold mb-4">File Analysis</h2>
            
            <h3 class="font-bold text-lg mb-2">CSS Files</h3>
            <div class="overflow-x-auto mb-4">
                <table class="min-w-full bg-white">
                    <thead>
                        <tr>
                            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                File
                            </th>
                            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                Status
                            </th>
                            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                Size
                            </th>
                            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                Selectors
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($css_stats as $file => $stats): ?>
                        <tr>
                            <td class="py-2 px-4 border-b border-gray-200"><?php echo $file; ?></td>
                            <td class="py-2 px-4 border-b border-gray-200">
                                <?php if ($stats['exists']): ?>
                                    <span class="text-green-600">Exists</span>
                                <?php else: ?>
                                    <span class="text-red-600">Missing</span>
                                <?php endif; ?>
                            </td>
                            <td class="py-2 px-4 border-b border-gray-200"><?php echo $stats['size']; ?></td>
                            <td class="py-2 px-4 border-b border-gray-200"><?php echo $stats['selectors']; ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <h3 class="font-bold text-lg mb-2">JavaScript Files</h3>
            <div class="overflow-x-auto mb-4">
                <table class="min-w-full bg-white">
                    <thead>
                        <tr>
                            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                File
                            </th>
                            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                Status
                            </th>
                            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                Size
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($js_stats as $file => $stats): ?>
                        <tr>
                            <td class="py-2 px-4 border-b border-gray-200"><?php echo $file; ?></td>
                            <td class="py-2 px-4 border-b border-gray-200">
                                <?php if ($stats['exists']): ?>
                                    <span class="text-green-600">Exists</span>
                                <?php else: ?>
                                    <span class="text-red-600">Missing</span>
                                <?php endif; ?>
                            </td>
                            <td class="py-2 px-4 border-b border-gray-200"><?php echo $stats['size']; ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <h3 class="font-bold text-lg mb-2">External Resources</h3>
            <div class="overflow-x-auto mb-4">
                <table class="min-w-full bg-white">
                    <thead>
                        <tr>
                            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                Resource
                            </th>
                            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                Status
                            </th>
                            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                Size
                            </th>
                            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                Load Time
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($external_stats as $name => $stats): ?>
                        <tr>
                            <td class="py-2 px-4 border-b border-gray-200"><?php echo $name; ?></td>
                            <td class="py-2 px-4 border-b border-gray-200">
                                <?php if ($stats['exists']): ?>
                                    <span class="text-green-600">Available</span>
                                <?php else: ?>
                                    <span class="text-red-600">Unavailable</span>
                                <?php endif; ?>
                            </td>
                            <td class="py-2 px-4 border-b border-gray-200"><?php echo $stats['size']; ?></td>
                            <td class="py-2 px-4 border-b border-gray-200"><?php echo $stats['time']; ?> ms</td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <h3 class="font-bold text-lg mb-2">Image Files (Top 10 by size)</h3>
            <div class="overflow-x-auto">
                <table class="min-w-full bg-white">
                    <thead>
                        <tr>
                            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                File
                            </th>
                            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                Size
                            </th>
                            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                Dimensions
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php 
                        // Sort images by size (largest first)
                        uasort($image_stats, function($a, $b) {
                            if (!$a['exists'] || !$b['exists']) return 0;
                            
                            $a_size = $a['size'];
                            $b_size = $b['size'];
                            
                            // Convert to bytes for comparison
                            $a_unit = substr($a_size, -2);
                            $b_unit = substr($b_size, -2);
                            
                            $a_value = (float)$a_size;
                            $b_value = (float)$b_size;
                            
                            $units = ['B', 'KB', 'MB', 'GB'];
                            $a_index = array_search($a_unit, $units);
                            $b_index = array_search($b_unit, $units);
                            
                            if ($a_index !== $b_index) {
                                return $b_index - $a_index;
                            }
                            
                            return $b_value - $a_value;
                        });
                        
                        // Display top 10
                        $count = 0;
                        foreach ($image_stats as $image => $stats): 
                            if ($count++ >= 10) break;
                        ?>
                        <tr>
                            <td class="py-2 px-4 border-b border-gray-200"><?php echo $image; ?></td>
                            <td class="py-2 px-4 border-b border-gray-200 <?php echo (strpos($stats['size'], 'MB') !== false || (strpos($stats['size'], 'KB') !== false && (float)$stats['size'] > 200)) ? 'text-red-600 font-bold' : ''; ?>">
                                <?php echo $stats['size']; ?>
                            </td>
                            <td class="py-2 px-4 border-b border-gray-200"><?php echo $stats['dimensions']; ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-2xl font-bold mb-4">.htaccess Analysis</h2>
            <?php if ($htaccess_stats['exists']): ?>
                <ul class="list-disc pl-5 space-y-1">
                    <li>
                        Expires Directives: 
                        <?php if ($htaccess_stats['has_expires']): ?>
                            <span class="text-green-600">Enabled</span>
                        <?php else: ?>
                            <span class="text-red-600">Missing</span>
                        <?php endif; ?>
                    </li>
                    <li>
                        Deflate/Compression: 
                        <?php if ($htaccess_stats['has_deflate']): ?>
                            <span class="text-green-600">Enabled</span>
                        <?php else: ?>
                            <span class="text-red-600">Missing</span>
                        <?php endif; ?>
                    </li>
                    <li>
                        Cache Control: 
                        <?php if ($htaccess_stats['has_cache_control']): ?>
                            <span class="text-green-600">Enabled</span>
                        <?php else: ?>
                            <span class="text-red-600">Missing</span>
                        <?php endif; ?>
                    </li>
                </ul>
            <?php else: ?>
                <p class="text-red-600">No .htaccess file found. Adding one with proper caching and compression directives can significantly improve performance.</p>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>


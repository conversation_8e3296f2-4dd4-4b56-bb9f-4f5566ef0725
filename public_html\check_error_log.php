<?php
require_once 'config.php';
require_once 'error_log.php';

// Zobrazení posledních 50 řádků logu
$logFile = ERROR_LOG_FILE;
if (file_exists($logFile)) {
    $logs = file($logFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    $lastLogs = array_slice($logs, -50);
    echo "<h2>Posledních 50 záznamů z logu:</h2>";
    echo "<pre>";
    foreach ($lastLogs as $log) {
        echo htmlspecialchars($log) . "\n";
    }
    echo "</pre>";
} else {
    echo "Log soubor nenalezen.";
}

// Kontrola PHP verze a nastavení
echo "<h2>PHP Informace:</h2>";
echo "<pre>";
echo "PHP Verze: " . phpversion() . "\n";
echo "Zobrazování chyb: " . (ini_get('display_errors') ? 'Zapnuto' : 'Vypnuto') . "\n";
echo "Úroveň chybových hlášení: " . ini_get('error_reporting') . "\n";
echo "</pre>";

// Kontrola připojení k databázi
echo "<h2>Test připojení k databázi:</h2>";
try {
    $mysqli = getDbConnection();
    echo "Připojení k databázi úspěšné.";
    $mysqli->close();
} catch (Exception $e) {
    echo "Chyba při připojování k databázi: " . $e->getMessage();
}
?>




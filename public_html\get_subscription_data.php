<?php
// Include necessary files
require_once 'config.php';
require_once 'db_connection.php';
require_once 'error_log.php';

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Set content type to JSON
header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'error' => 'User not logged in']);
    exit;
}

try {
    // Get user ID from session
    $user_id = $_SESSION['user_id'];
    
    // Get database connection
    $db = getDbConnection();
    
    // Get subscription data from database (plan and limit only)
    $stmt = $db->prepare("SELECT subscription_plan, minute_limit FROM users WHERE id = ?");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $userData = $result->fetch_assoc();
    $stmt->close();
    
    if (!$userData) {
        throw new Exception('Failed to retrieve user data');
    }
    
    // Get metrics to get accurate minutes usage
    $metrics = getDefaultMetrics(); // Make sure this function is included/available
    $minutesUsed = 0;
    foreach ($metrics as $metric) {
        if ($metric['label'] === 'Spotřeba minut') {
            $minutesUsed = intval($metric['value']);
            break;
        }
    }
    
    // Extract subscription data
    $plan = $userData['subscription_plan'];
    $limit = $userData['minute_limit'];
    
    // Check if the plan is unlimited
    $isUnlimited = ($limit == -1);
    
    // Return the subscription data
    echo json_encode([
        'success' => true,
        'plan' => $plan,
        'limit' => $limit,
        'minutesUsed' => $minutesUsed,
        'isUnlimited' => $isUnlimited
    ]);
    
} catch (Exception $e) {
    // Log error
    writeErrorLog('Subscription Data Error', [
        'user_id' => $_SESSION['user_id'] ?? 'unknown',
        'error' => $e->getMessage()
    ]);
    
    // Return error response
    echo json_encode([
        'success' => false,
        'error' => 'Failed to retrieve subscription data: ' . $e->getMessage()
    ]);
}
?>


<?php
require_once __DIR__ . '/../config.php';

function debugUsers() {
    $conn = getDbConnection();
    if (!$conn) {
        error_log('Failed to establish database connection in debugUsers');
        return false;
    }
    
    try {
        $sql = "
            SELECT id, username, subscription_plan, minute_limit, minutes_remaining, used_minutes,
                   (minutes_remaining / minute_limit * 100) as percentage_remaining
            FROM users
            WHERE subscription_plan IN ('Základní', 'Pro')
              AND minute_limit > 0
            ORDER BY percentage_remaining ASC
        ";
        
        $stmt = $conn->prepare($sql);
        
        if (!$stmt) {
            throw new Exception("Failed to prepare statement: " . $conn->error);
        }
        
        $stmt->execute();
        $result = $stmt->get_result();
        
        $users = [];
        while ($user = $result->fetch_assoc()) {
            $users[] = $user;
        }
        
        return $users;
    } catch (Exception $e) {
        error_log('Debug users error: ' . $e->getMessage());
        return false;
    } finally {
        if (isset($stmt)) {
            $stmt->close();
        }
        if ($conn) {
            $conn->close();
        }
    }
}

// Spuštění debugovací funkce
if (php_sapi_name() === 'cli' || isset($_GET['debug'])) {
    $users = debugUsers();
    if ($users !== false) {
        echo json_encode($users, JSON_PRETTY_PRINT);
    } else {
        echo "Error occurred while debugging users.";
    }
}


<?php
// Main landing page file that includes all components

// Load configuration - make sure this file exists
require_once 'landing-page/config/config.php';

// Check if SEO functions file exists before including it
if (file_exists('seo/index.php')) {
    require_once 'seo/index.php';
} else {
    // Define fallback functions if the SEO file doesn't exist
    function get_seo_head($page) {
        return '<meta name="description" content="Dentibot - Moderní řešení pro zubní ordinace">';
    }
    
    function get_seo_structured_data($type) {
        return '';
    }
}

// Define $expires if it's not set in config
if (!isset($expires)) {
    $expires = 60*60*24*14; // 14 days
}

// Add HTTP headers for performance and security
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: SAMEORIGIN');
header('X-XSS-Protection: 1; mode=block');
header('Referrer-Policy: strict-origin-when-cross-origin');
header('Permissions-Policy: camera=(), microphone=(), geolocation=()');

// Add cache headers
header("Pragma: public");
header("Cache-Control: max-age=$expires");
header('Expires: ' . gmdate('D, d M Y H:i:s', time() + $expires) . ' GMT');

// Detect mobile devices
$isMobile = preg_match('/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i', $_SERVER['HTTP_USER_AGENT']);

// Set a variable that can be used in included files
$deviceType = $isMobile ? 'mobile' : 'desktop';

// Generate sitemap.xml if it doesn't exist - moved after headers to avoid output issues
if (!file_exists('sitemap.xml')) {
    try {
        $sitemap = new SimpleXMLElement('<?xml version="1.0" encoding="UTF-8"?><urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"></urlset>');
        
        $urls = [
            '' => '1.0',
            'sluzby' => '0.8',
            'o-nas' => '0.8',
            'kontakt' => '0.8',
            'gdpr' => '0.6'
        ];

        foreach ($urls as $url => $priority) {
            $urlElement = $sitemap->addChild('url');
            $urlElement->addChild('loc', 'https://www.dentibot.eu/' . $url);
            $urlElement->addChild('lastmod', date('Y-m-d'));
            $urlElement->addChild('changefreq', 'weekly');
            $urlElement->addChild('priority', $priority);
        }
        
        $sitemap->asXML('sitemap.xml');
    } catch (Exception $e) {
        // Silently fail if sitemap generation fails
    }
}

// Include header - use a safer include method
include_once 'landing-page/includes/header.php';
?>

<body>
    <!-- Background Glow Effects -->
    <div class="background-glow top-0 left-0"></div>
    <div class="background-glow bottom-0 right-0"></div>
    <div class="mouse-glow"></div>

    <?php 
    // Include the appropriate navigation based on device type
    if ($isMobile) {
        include_once 'landing-page/includes/navigation_mobile.php';
    } else {
        include_once 'landing-page/includes/navigation.php';
    }
    
    // Use safer include methods with error checking for other sections
    $sections = [
        'landing-page/sections/hero.php',
        'landing-page/sections/statistics.php',
        'landing-page/sections/sliding-panel.php',
        'landing-page/sections/features.php',
        'landing-page/sections/cost-comparison.php',
        'landing-page/sections/benefits.php',
        'landing-page/sections/why-choose.php',
        'landing-page/sections/case-studies.php',
        'landing-page/sections/implementation.php',
        'landing-page/sections/faq.php',
        'landing-page/sections/help-section.php',
        'landing-page/sections/demo-form.php',
        'landing-page/sections/app-showcase.php',
        'landing-page/sections/community.php',
        'landing-page/sections/calendar.php',
        'landing-page/sections/footer.php'
    ];
    
    foreach ($sections as $section) {
        if (file_exists($section)) {
            include_once $section;
        }
    }
    ?>

    <!-- Load JavaScript files -->
    <script src="landing-page/js/main.js"></script>
    <script src="landing-page/js/slider.js"></script>
    <script src="landing-page/js/cost-comparison.js"></script>
    <script src="landing-page/js/form-handler.js"></script>
    
    <?php if ($isMobile): ?>
    <!-- Mobile-specific JavaScript -->
    <script src="landing-page/js/mobile-nav.js"></script>
    <?php endif; ?>
    
    <!-- Cal floating-popup embed code -->
    <script type="text/javascript">
      (function (C, A, L) { let p = function (a, ar) { a.q.push(ar); }; let d = C.document; C.Cal = C.Cal || function () { let cal = C.Cal; let ar = arguments; if (!cal.loaded) { cal.ns = {}; cal.q = cal.q || []; d.head.appendChild(d.createElement("script")).src = A; cal.loaded = true; } if (ar[0] === L) { const api = function () { p(api, arguments); }; const namespace = ar[1]; api.q = api.q || []; if(typeof namespace === "string"){cal.ns[namespace] = cal.ns[namespace] || api;p(cal.ns[namespace], ar);p(cal, ["initNamespace", namespace]);} else p(cal, ar); return;} p(cal, ar); }; })(window, "https://app.cal.com/embed/embed.js", "init");
    Cal("init", "hovor-s-tknurture", {origin:"https://cal.com"});

      Cal.ns["hovor-s-tknurture"]("floatingButton", {"calLink":"tknurture/hovor-s-tknurture","config":{"layout":"month_view"},"buttonColor":"#0cf4ff","buttonTextColor":"#000000","buttonText":"Zarezervovat online schůzku"}); 
      Cal.ns["hovor-s-tknurture"]("ui", {"cssVarsPerTheme":{"light":{"cal-brand":"#000000"},"dark":{"cal-brand":"#199da3"}},"hideEventTypeDetails":false,"layout":"month_view"});
    </script>
</body>
</html>
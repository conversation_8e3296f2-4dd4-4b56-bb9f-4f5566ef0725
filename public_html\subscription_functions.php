<?php
require_once 'subscription_constants.php';

// Function to get subscription limit
function getSubscriptionLimit($subscription_type, $custom_limit = null) {
    global $SUBSCRIPTION_TYPES;
    
    switch ($subscription_type) {
        case SUBSCRIPTION_BASIC:
            return $SUBSCRIPTION_TYPES[SUBSCRIPTION_BASIC]['limit'];
        case SUBSCRIPTION_ADVANCED:
            return $SUBSCRIPTION_TYPES[SUBSCRIPTION_ADVANCED]['limit'];
        case SUBSCRIPTION_ENTERPRISE:
            return $custom_limit ?? 1000;
        case SUBSCRIPTION_PAY_AS_YOU_GO:
            return PHP_INT_MAX;
        default:
            return 0;
    }
}

function isUnlimitedPlan($plan) {
    return $plan === SUBSCRIPTION_PAY_AS_YOU_GO;
}

function getUserSubscriptionData($user_id) {
    global $mysqli;
    
    $stmt = $mysqli->prepare("SELECT subscription_plan, custom_minute_limit, used_minutes FROM users WHERE id = ?");
    if (!$stmt) {
        error_log("Error preparing statement: " . $mysqli->error);
        return false;
    }
    
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();
    $stmt->close();
    
    if (!$row) {
        return false;
    }
    
    return [
        'plan' => $row['subscription_plan'],
        'limit' => getSubscriptionLimit($row['subscription_plan'], $row['custom_minute_limit']),
        'used_minutes' => $row['used_minutes']
    ];
}

function updateUserSubscription($user_id, $new_plan, $custom_limit = null) {
    global $mysqli;
    
    $stmt = $mysqli->prepare("UPDATE users SET subscription_plan = ?, custom_minute_limit = ? WHERE id = ?");
    if (!$stmt) {
        error_log("Error preparing statement: " . $mysqli->error);
        return false;
    }
    
    $stmt->bind_param("sii", $new_plan, $custom_limit, $user_id);
    $success = $stmt->execute();
    $stmt->close();
    
    if ($success) {
        // Update session variables if they exist
        if (isset($_SESSION['subscription_plan'])) {
            $_SESSION['subscription_plan'] = $new_plan;
        }
        if (isset($_SESSION['custom_minute_limit'])) {
            $_SESSION['custom_minute_limit'] = $custom_limit;
        }
    }
    
    return $success;
}

function getSubscriptionTypes() {
    global $mysqli;
    $types = [];
    $result = $mysqli->query("SELECT name, minutes_limit, is_unlimited FROM subscription_types");
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $types[$row['name']] = [
                'limit' => $row['is_unlimited'] ? 'Unlimited' : $row['minutes_limit'],
                'price' => 'Custom' // You might want to add a price field to the subscription_types table
            ];
        }
    }
    return $types;
}

function validateSubscriptionType($subscription_type) {
    return in_array($subscription_type, [
        SUBSCRIPTION_BASIC,
        SUBSCRIPTION_ADVANCED,
        SUBSCRIPTION_ENTERPRISE,
        SUBSCRIPTION_PAY_AS_YOU_GO
    ]);
}

// Wrapper function for backward compatibility
function getMinutesLimit($subscription_type) {
    return getSubscriptionLimit($subscription_type);
}

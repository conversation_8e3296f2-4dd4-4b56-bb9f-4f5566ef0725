<?php
/**
 * Dynamická karta pacienta - <PERSON>lavn<PERSON> s<PERSON>r<PERSON>
 * Event-driven automatická karta s agregovanými časovými razí<PERSON>ky
 */

require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../cgm_timestamps/TimestampManager.php';

class PatientCardManager {
    private $db;
    private $timestampManager;
    
    public function __construct() {
        $this->db = getDbConnection();
        $this->timestampManager = new TimestampManager();
    }
    
    /**
     * Získá aktuální kartu pacienta
     */
    public function getPatientCard($patient_id) {
        try {
            $stmt = $this->db->prepare("
                SELECT pc.*, p.name as patient_name, p.phone, p.email 
                FROM patient_cards pc
                LEFT JOIN patients p ON pc.patient_id = p.id
                WHERE pc.patient_id = ? AND pc.is_current = TRUE
                ORDER BY pc.card_version DESC
                LIMIT 1
            ");
            $stmt->bind_param("i", $patient_id);
            $stmt->execute();
            $result = $stmt->get_result();
            $card = $result->fetch_assoc();
            
            if (!$card) {
                // Vytvoř novou kartu pokud neexistuje
                return $this->createNewCard($patient_id);
            }
            
            // Načti položky karty
            $card['items'] = $this->getCardItems($card['id']);
            $card['card_data'] = json_decode($card['card_data'], true);
            
            return ['success' => true, 'card' => $card];
            
        } catch (Exception $e) {
            error_log("PatientCard get error: " . $e->getMessage());
            return ['success' => false, 'error' => 'Chyba při načítání karty pacienta'];
        }
    }
    
    /**
     * Vytvoří novou kartu pacienta
     */
    private function createNewCard($patient_id) {
        try {
            $card_data = [
                'patientId' => $patient_id,
                'cardVersion' => 1,
                'snapTime' => date('c'),
                'visits' => [],
                'metadata' => [
                    'created' => date('c'),
                    'lastActivity' => null
                ]
            ];
            
            $card_json = json_encode($card_data);
            $card_hash = hash('sha256', $card_json);
            
            $stmt = $this->db->prepare("
                INSERT INTO patient_cards (patient_id, card_version, card_data, card_hash) 
                VALUES (?, 1, ?, ?)
            ");
            $stmt->bind_param("iss", $patient_id, $card_json, $card_hash);
            $stmt->execute();
            
            $card_id = $this->db->insert_id;
            
            return [
                'success' => true,
                'card' => [
                    'id' => $card_id,
                    'patient_id' => $patient_id,
                    'card_version' => 1,
                    'card_data' => $card_data,
                    'card_hash' => $card_hash,
                    'items' => []
                ]
            ];
            
        } catch (Exception $e) {
            error_log("PatientCard create error: " . $e->getMessage());
            return ['success' => false, 'error' => 'Chyba při vytváření karty pacienta'];
        }
    }
    
    /**
     * Přidá novou položku do karty (automaticky voláno z eventů)
     */
    public function addCardItem($patient_id, $item_data) {
        try {
            $this->db->begin_transaction();
            
            // Získej aktuální kartu
            $card_result = $this->getPatientCard($patient_id);
            if (!$card_result['success']) {
                throw new Exception($card_result['error']);
            }
            
            $card = $card_result['card'];
            
            // Vytvoř okamžité časové razítko pro položku
            $content_hash = hash('sha256', json_encode($item_data));
            $tst_result = $this->timestampManager->createTimestamp(0, $content_hash); // 0 = no document_id
            
            // Generuj UUID pro položku
            $version_id = $this->generateUUID();
            $visit_id = $item_data['visit_id'] ?? $this->generateUUID();
            
            // Vlož položku do card_items
            $stmt = $this->db->prepare("
                INSERT INTO card_items 
                (card_id, visit_id, item_type, version_id, content_hash, tst_id, item_data, file_path) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $item_json = json_encode($item_data);
            $tst_id = $tst_result['success'] ? $tst_result['timestamp_id'] : null;
            
            $stmt->bind_param("issssiis", 
                $card['id'],
                $visit_id,
                $item_data['type'],
                $version_id,
                $content_hash,
                $tst_id,
                $item_json,
                $item_data['file_path'] ?? null
            );
            $stmt->execute();
            
            // Aktualizuj JSON karty
            $this->updateCardData($card['id'], $visit_id, $item_data, $version_id, $content_hash, $tst_id);
            
            $this->db->commit();
            
            // Zařaď do fronty pro denní razítkování
            $this->queueForDailyStamp($patient_id);
            
            return [
                'success' => true,
                'item_id' => $this->db->insert_id,
                'version_id' => $version_id,
                'tst_created' => $tst_result['success']
            ];
            
        } catch (Exception $e) {
            $this->db->rollback();
            error_log("PatientCard addItem error: " . $e->getMessage());
            return ['success' => false, 'error' => 'Chyba při přidávání položky do karty'];
        }
    }
    
    /**
     * Aktualizuje JSON data karty
     */
    private function updateCardData($card_id, $visit_id, $item_data, $version_id, $content_hash, $tst_id) {
        // Získej aktuální data karty
        $stmt = $this->db->prepare("SELECT card_data, card_version FROM patient_cards WHERE id = ?");
        $stmt->bind_param("i", $card_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $card_row = $result->fetch_assoc();
        
        $card_data = json_decode($card_row['card_data'], true);
        
        // Najdi nebo vytvoř návštěvu
        $visit_index = null;
        foreach ($card_data['visits'] as $index => $visit) {
            if ($visit['visitId'] === $visit_id) {
                $visit_index = $index;
                break;
            }
        }
        
        if ($visit_index === null) {
            // Nová návštěva
            $card_data['visits'][] = [
                'visitId' => $visit_id,
                'dateTime' => $item_data['created_at'] ?? date('c'),
                'items' => []
            ];
            $visit_index = count($card_data['visits']) - 1;
        }
        
        // Přidej položku do návštěvy
        $card_data['visits'][$visit_index]['items'][] = [
            'type' => $item_data['type'],
            'versionId' => $version_id,
            'contentHash' => $content_hash,
            'tstId' => $tst_id,
            'data' => $item_data
        ];
        
        // Aktualizuj metadata
        $card_data['cardVersion'] = $card_row['card_version'] + 1;
        $card_data['snapTime'] = date('c');
        $card_data['metadata']['lastActivity'] = date('c');
        
        $new_card_json = json_encode($card_data);
        $new_card_hash = hash('sha256', $new_card_json);
        
        // Ulož aktualizovanou kartu
        $stmt = $this->db->prepare("
            UPDATE patient_cards 
            SET card_data = ?, card_hash = ?, card_version = card_version + 1, updated_at = NOW()
            WHERE id = ?
        ");
        $stmt->bind_param("ssi", $new_card_json, $new_card_hash, $card_id);
        $stmt->execute();
    }
    
    /**
     * Získá položky karty
     */
    private function getCardItems($card_id) {
        $stmt = $this->db->prepare("
            SELECT ci.*, dt.created_at as tst_created, dt.is_valid as tst_valid
            FROM card_items ci
            LEFT JOIN document_timestamps dt ON ci.tst_id = dt.id
            WHERE ci.card_id = ?
            ORDER BY ci.created_at DESC
        ");
        $stmt->bind_param("i", $card_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $items = [];
        while ($row = $result->fetch_assoc()) {
            $row['item_data'] = json_decode($row['item_data'], true);
            $items[] = $row;
        }
        
        return $items;
    }
    
    /**
     * Zařadí pacienta do fronty pro denní razítkování
     */
    private function queueForDailyStamp($patient_id) {
        $today = date('Y-m-d');
        
        $stmt = $this->db->prepare("
            INSERT IGNORE INTO daily_card_stamps (stamp_date, patient_id, card_version, aggregated_hash, status)
            SELECT ?, pc.patient_id, pc.card_version, pc.card_hash, 'pending'
            FROM patient_cards pc
            WHERE pc.patient_id = ? AND pc.is_current = TRUE
        ");
        $stmt->bind_param("si", $today, $patient_id);
        $stmt->execute();
    }
    
    /**
     * Denní job pro agregované razítkování
     */
    public function processDailyStamps($date = null) {
        if (!$date) $date = date('Y-m-d');
        
        try {
            $stmt = $this->db->prepare("
                SELECT dcs.*, pc.card_data 
                FROM daily_card_stamps dcs
                JOIN patient_cards pc ON dcs.patient_id = pc.patient_id AND pc.is_current = TRUE
                WHERE dcs.stamp_date = ? AND dcs.status = 'pending'
            ");
            $stmt->bind_param("s", $date);
            $stmt->execute();
            $result = $stmt->get_result();
            
            $processed = 0;
            while ($stamp = $result->fetch_assoc()) {
                // Vytvoř agregované časové razítko
                $tst_result = $this->timestampManager->createTimestamp(0, $stamp['aggregated_hash']);
                
                if ($tst_result['success']) {
                    $update_stmt = $this->db->prepare("
                        UPDATE daily_card_stamps 
                        SET tst_id = ?, status = 'completed', completed_at = NOW()
                        WHERE id = ?
                    ");
                    $update_stmt->bind_param("ii", $tst_result['timestamp_id'], $stamp['id']);
                    $update_stmt->execute();
                    $processed++;
                } else {
                    $update_stmt = $this->db->prepare("
                        UPDATE daily_card_stamps 
                        SET status = 'failed', completed_at = NOW()
                        WHERE id = ?
                    ");
                    $update_stmt->bind_param("i", $stamp['id']);
                    $update_stmt->execute();
                }
            }
            
            return ['success' => true, 'processed' => $processed];
            
        } catch (Exception $e) {
            error_log("Daily stamps processing error: " . $e->getMessage());
            return ['success' => false, 'error' => 'Chyba při zpracování denních razítek'];
        }
    }
    
    /**
     * Generuje UUID
     */
    private function generateUUID() {
        return sprintf('%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff), mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
        );
    }
}

<?php
// Funkce pro generování meta tagů

// Funkce pro generování základních meta tagů
function generate_basic_meta_tags($meta) {
    $output = '';
    
    $output .= '<title>' . htmlspecialchars($meta['title']) . '</title>' . PHP_EOL;
    $output .= '<meta name="description" content="' . htmlspecialchars($meta['description']) . '">' . PHP_EOL;
    $output .= '<meta name="keywords" content="' . htmlspecialchars($meta['keywords']) . '">' . PHP_EOL;
    $output .= '<meta name="author" content="' . htmlspecialchars($meta['author']) . '">' . PHP_EOL;
    $output .= '<meta name="robots" content="' . htmlspecialchars($meta['robots']) . '">' . PHP_EOL;
    $output .= '<link rel="canonical" href="' . htmlspecialchars($meta['canonical']) . '">' . PHP_EOL;
    
    return $output;
}

// Funkce pro generování Open Graph meta tagů
function generate_og_meta_tags($og) {
    $output = '';
    
    $output .= '<meta property="og:title" content="' . htmlspecialchars($og['title']) . '">' . PHP_EOL;
    $output .= '<meta property="og:description" content="' . htmlspecialchars($og['description']) . '">' . PHP_EOL;
    $output .= '<meta property="og:image" content="' . htmlspecialchars($og['image']) . '">' . PHP_EOL;
    $output .= '<meta property="og:url" content="' . htmlspecialchars($og['url']) . '">' . PHP_EOL;
    $output .= '<meta property="og:type" content="website">' . PHP_EOL;
    
    return $output;
}

// Funkce pro generování Twitter Card meta tagů
function generate_twitter_meta_tags($og) {
    $output = '';
    
    $output .= '<meta name="twitter:card" content="summary_large_image">' . PHP_EOL;
    $output .= '<meta name="twitter:title" content="' . htmlspecialchars($og['title']) . '">' . PHP_EOL;
    $output .= '<meta name="twitter:description" content="' . htmlspecialchars($og['description']) . '">' . PHP_EOL;
    $output .= '<meta name="twitter:image" content="' . htmlspecialchars($og['image']) . '">' . PHP_EOL;
    
    return $output;
}

// Funkce pro generování preload tagů
function generate_preload_tags() {
    $output = '';
    
    $output .= '<link rel="preload" href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" as="style">' . PHP_EOL;
    $output .= '<link rel="preload" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" as="style">' . PHP_EOL;
    $output .= '<link rel="preload" href="/images/DENTIBOT LOGO.png" as="image">' . PHP_EOL;
    
    return $output;
}

// Funkce pro generování favicon tagů
function generate_favicon_tags() {
    $output = '';
    
    $output .= '<link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">' . PHP_EOL;
    $output .= '<link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">' . PHP_EOL;
    $output .= '<link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">' . PHP_EOL;
    $output .= '<link rel="manifest" href="/site.webmanifest">' . PHP_EOL;
    
    return $output;
}

// Funkce pro generování hreflang tagů
function generate_hreflang_tags() {
    $output = '';
    
    $output .= '<link rel="alternate" hreflang="cs" href="https://www.dentibot.eu/">' . PHP_EOL;
    $output .= '<link rel="alternate" hreflang="x-default" href="https://www.dentibot.eu/">' . PHP_EOL;
    
    return $output;
}

// Funkce pro generování všech meta tagů
function generate_all_meta_tags($meta, $og) {
    $output = '';
    
    $output .= generate_basic_meta_tags($meta);
    $output .= generate_og_meta_tags($og);
    $output .= generate_twitter_meta_tags($og);
    $output .= generate_preload_tags();
    $output .= generate_favicon_tags();
    $output .= generate_hreflang_tags();
    
    return $output;
}
?>


<?php
require_once 'config.php';
require_once 'airtable_functions.php';
require_once 'error_log.php';

// Kontrola přihlášení
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['id'])) {
    try {
        $patientId = $_GET['id'];
        
        // Delete patient from Airtable
        deletePatient($patientId);

        $success = "Pacient byl ú<PERSON>ěšně smazán";
        header("Location: patients.php?success=" . urlencode($success));
        exit;
    } catch (Exception $e) {
        $error = $e->getMessage();
        header("Location: patients.php?error=" . urlencode($error));
        exit;
    }
} else {
    header("Location: patients.php");
    exit;
}

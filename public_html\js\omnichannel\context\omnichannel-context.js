const OmnichannelContext = React.createContext();

function OmnichannelProvider({ children }) {
    const [conversations, setConversations] = React.useState([]);
    const [selectedId, setSelectedId] = React.useState(null);

    const selectedConversation = React.useMemo(() => 
        conversations.find(c => c.id === selectedId) || null,
        [conversations, selectedId]
    );

    const selectConversation = React.useCallback((id) => {
        setSelectedId(id);
    }, []);

    const sendMessage = React.useCallback(async (text) => {
        if (!selectedConversation) return;

        try {
            const response = await fetch('/api/omnichannel/send_message.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    conversationId: selectedConversation.id,
                    text,
                    channel: selectedConversation.channel
                }),
            });

            if (!response.ok) {
                throw new Error('Failed to send message');
            }

            const newMessage = {
                id: Date.now().toString(),
                text,
                sender: 'dentist',
                timestamp: new Date().toISOString(),
                channel: selectedConversation.channel
            };

            setConversations(prev => prev.map(conv => {
                if (conv.id === selectedConversation.id) {
                    return {
                        ...conv,
                        lastMessage: text,
                        lastMessageTime: new Date().toISOString(),
                        messages: [...conv.messages, newMessage]
                    };
                }
                return conv;
            }));
        } catch (error) {
            console.error('Failed to send message:', error);
        }
    }, [selectedConversation]);

    React.useEffect(() => {
        fetch('/api/omnichannel/get_conversations.php')
            .then(response => response.json())
            .then(data => setConversations(data))
            .catch(error => console.error('Error fetching conversations:', error));
    }, []);

    const value = React.useMemo(() => ({
        conversations,
        selectedConversation,
        selectConversation,
        sendMessage
    }), [conversations, selectedConversation, selectConversation, sendMessage]);

    return React.createElement(
        OmnichannelContext.Provider,
        { value: value },
        children
    );
}

function useOmnichannel() {
    const context = React.useContext(OmnichannelContext);
    if (context === undefined) {
        throw new Error('useOmnichannel must be used within an OmnichannelProvider');
    }
    return context;
}

window.OmnichannelContext = {
    OmnichannelProvider: OmnichannelProvider,
    useOmnichannel: useOmnichannel
};
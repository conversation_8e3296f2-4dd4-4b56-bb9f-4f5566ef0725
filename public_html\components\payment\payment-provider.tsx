"use client"

import { create<PERSON>ontext, useContext, useState, useEffect, type ReactNode } from "react"
import { useToast } from "@/components/ui/use-toast"

// Types
export type PaymentMethod = {
  id: string
  type: "card" | "paypal" | "bank_transfer"
  details: {
    last4?: string
    brand?: string
    expiryMonth?: string
    expiryYear?: string
    email?: string
    bankName?: string
    accountLast4?: string
  }
  isDefault: boolean
}

export type Subscription = {
  id: string
  planId: string
  planName: string
  price: number
  interval: "month" | "year"
  status: "active" | "canceled" | "past_due" | "trialing"
  currentPeriodEnd: string
  cancelAtPeriodEnd: boolean
}

export type Transaction = {
  id: string
  date: string
  amount: number
  description: string
  status: "completed" | "pending" | "failed" | "refunded"
  paymentMethod: string
  type: "subscription" | "one-time"
  invoiceUrl?: string
}

export type SubscriptionPlan = {
  id: string
  name: string
  description: string
  price: {
    monthly: number
    yearly: number
  }
  features: string[]
  popular?: boolean
}

export type OneTimeService = {
  id: string
  name: string
  description: string
  price: number
}

// Context type
type PaymentContextType = {
  paymentMethods: PaymentMethod[]
  subscription: Subscription | null
  transactions: Transaction[]
  subscriptionPlans: SubscriptionPlan[]
  oneTimeServices: OneTimeService[]
  isLoading: boolean
  addPaymentMethod: (method: Omit<PaymentMethod, "id">) => Promise<void>
  removePaymentMethod: (id: string) => Promise<void>
  setDefaultPaymentMethod: (id: string) => Promise<void>
  updateSubscription: (planId: string) => Promise<void>
  cancelSubscription: () => Promise<void>
  processOneTimePayment: (serviceId: string, paymentMethodId: string) => Promise<void>
}

// Create context
const PaymentContext = createContext<PaymentContextType | undefined>(undefined)

// Mock data
const mockPaymentMethods: PaymentMethod[] = [
  {
    id: "pm_1",
    type: "card",
    details: {
      last4: "4242",
      brand: "Visa",
      expiryMonth: "12",
      expiryYear: "2025",
    },
    isDefault: true,
  },
  {
    id: "pm_2",
    type: "paypal",
    details: {
      email: "<EMAIL>",
    },
    isDefault: false,
  },
]

const mockSubscription: Subscription = {
  id: "sub_1",
  planId: "plan_advanced",
  planName: "Pokročilý tarif",
  price: 5000,
  interval: "month",
  status: "active",
  currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
  cancelAtPeriodEnd: false,
}

const mockTransactions: Transaction[] = [
  {
    id: "tx_1",
    date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
    amount: 5000,
    description: "Pokročilý tarif - Měsíční předplatné",
    status: "completed",
    paymentMethod: "Visa •••• 4242",
    type: "subscription",
    invoiceUrl: "#",
  },
  {
    id: "tx_2",
    date: new Date(Date.now() - 32 * 24 * 60 * 60 * 1000).toISOString(),
    amount: 5000,
    description: "Pokročilý tarif - Měsíční předplatné",
    status: "completed",
    paymentMethod: "Visa •••• 4242",
    type: "subscription",
    invoiceUrl: "#",
  },
  {
    id: "tx_3",
    date: new Date(Date.now() - 40 * 24 * 60 * 60 * 1000).toISOString(),
    amount: 2500,
    description: "Dodatečné SMS zprávy (500 ks)",
    status: "completed",
    paymentMethod: "PayPal",
    type: "one-time",
    invoiceUrl: "#",
  },
]

const mockSubscriptionPlans: SubscriptionPlan[] = [
  {
    id: "plan_basic",
    name: "Základní tarif",
    description: "Ideální pro malé ordinace",
    price: {
      monthly: 3000,
      yearly: 30000,
    },
    features: ["500 minut hovorů", "1000 SMS zpráv", "Základní statistiky", "E-mailová podpora"],
  },
  {
    id: "plan_advanced",
    name: "Pokročilý tarif",
    description: "Nejoblíbenější volba pro středně velké ordinace",
    price: {
      monthly: 5000,
      yearly: 50000,
    },
    features: ["1000 minut hovorů", "2000 SMS zpráv", "Pokročilé statistiky", "Prioritní podpora", "Vlastní nastavení"],
    popular: true,
  },
  {
    id: "plan_premium",
    name: "Prémiový tarif",
    description: "Pro velké ordinace s vysokými nároky",
    price: {
      monthly: 10000,
      yearly: 100000,
    },
    features: [
      "Neomezené minuty",
      "5000 SMS zpráv",
      "Kompletní analytika",
      "24/7 podpora",
      "Vlastní integrace",
      "Dedikovaný account manager",
    ],
  },
]

const mockOneTimeServices: OneTimeService[] = [
  {
    id: "service_sms_500",
    name: "Dodatečné SMS zprávy",
    description: "Balíček 500 SMS zpráv",
    price: 2500,
  },
  {
    id: "service_minutes_200",
    name: "Dodatečné minuty hovorů",
    description: "Balíček 200 minut hovorů",
    price: 3000,
  },
  {
    id: "service_custom_integration",
    name: "Vlastní integrace",
    description: "Integrace s vaším stávajícím systémem",
    price: 15000,
  },
]

// Provider component
export function PaymentProvider({ children }: { children: ReactNode }) {
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([])
  const [subscription, setSubscription] = useState<Subscription | null>(null)
  const [transactions, setTransactions] = useState<Transaction[]>([])
  const [subscriptionPlans, setSubscriptionPlans] = useState<SubscriptionPlan[]>([])
  const [oneTimeServices, setOneTimeServices] = useState<OneTimeService[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const { toast } = useToast()

  // Simulate API calls
  useEffect(() => {
    const loadData = async () => {
      try {
        // Simulate API delay
        await new Promise((resolve) => setTimeout(resolve, 1000))

        // Set mock data
        setPaymentMethods(mockPaymentMethods)
        setSubscription(mockSubscription)
        setTransactions(mockTransactions)
        setSubscriptionPlans(mockSubscriptionPlans)
        setOneTimeServices(mockOneTimeServices)
      } catch (error) {
        toast({
          title: "Chyba při načítání dat",
          description: "Nepodařilo se načíst platební údaje. Zkuste to prosím později.",
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    }

    loadData()
  }, [toast])

  // Add payment method
  const addPaymentMethod = async (method: Omit<PaymentMethod, "id">) => {
    setIsLoading(true)
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000))

      const newMethod: PaymentMethod = {
        ...method,
        id: `pm_${Date.now()}`,
      }

      // If this is the first payment method or marked as default, update other methods
      if (method.isDefault || paymentMethods.length === 0) {
        setPaymentMethods((prev) =>
          prev.map((pm) => ({ ...pm, isDefault: false })).concat({ ...newMethod, isDefault: true }),
        )
      } else {
        setPaymentMethods((prev) => [...prev, newMethod])
      }

      toast({
        title: "Platební metoda přidána",
        description: "Vaše nová platební metoda byla úspěšně přidána.",
      })
    } catch (error) {
      toast({
        title: "Chyba při přidávání platební metody",
        description: "Nepodařilo se přidat platební metodu. Zkuste to prosím později.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Remove payment method
  const removePaymentMethod = async (id: string) => {
    setIsLoading(true)
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000))

      const methodToRemove = paymentMethods.find((pm) => pm.id === id)

      if (!methodToRemove) {
        throw new Error("Platební metoda nenalezena")
      }

      // Check if this is the default method
      if (methodToRemove.isDefault && paymentMethods.length > 1) {
        throw new Error("Nelze odstranit výchozí platební metodu. Nejprve nastavte jinou metodu jako výchozí.")
      }

      setPaymentMethods((prev) => prev.filter((pm) => pm.id !== id))

      toast({
        title: "Platební metoda odstraněna",
        description: "Vaše platební metoda byla úspěšně odstraněna.",
      })
    } catch (error) {
      toast({
        title: "Chyba při odstraňování platební metody",
        description: error instanceof Error ? error.message : "Nepodařilo se odstranit platební metodu.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Set default payment method
  const setDefaultPaymentMethod = async (id: string) => {
    setIsLoading(true)
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000))

      setPaymentMethods((prev) =>
        prev.map((pm) => ({
          ...pm,
          isDefault: pm.id === id,
        })),
      )

      toast({
        title: "Výchozí platební metoda aktualizována",
        description: "Vaše výchozí platební metoda byla úspěšně změněna.",
      })
    } catch (error) {
      toast({
        title: "Chyba při aktualizaci výchozí platební metody",
        description: "Nepodařilo se aktualizovat výchozí platební metodu.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Update subscription
  const updateSubscription = async (planId: string) => {
    setIsLoading(true)
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000))

      const selectedPlan = subscriptionPlans.find((plan) => plan.id === planId)

      if (!selectedPlan) {
        throw new Error("Vybraný tarif nebyl nalezen")
      }

      // Update subscription
      const updatedSubscription: Subscription = {
        id: subscription ? subscription.id : `sub_${Date.now()}`,
        planId: selectedPlan.id,
        planName: selectedPlan.name,
        price: selectedPlan.price.monthly,
        interval: "month",
        status: "active",
        currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        cancelAtPeriodEnd: false,
      }

      setSubscription(updatedSubscription)

      // Add transaction
      const newTransaction: Transaction = {
        id: `tx_${Date.now()}`,
        date: new Date().toISOString(),
        amount: selectedPlan.price.monthly,
        description: `${selectedPlan.name} - Měsíční předplatné`,
        status: "completed",
        paymentMethod:
          paymentMethods.find((pm) => pm.isDefault)?.type === "card"
            ? `${paymentMethods.find((pm) => pm.isDefault)?.details.brand} •••• ${paymentMethods.find((pm) => pm.isDefault)?.details.last4}`
            : "PayPal",
        type: "subscription",
        invoiceUrl: "#",
      }

      setTransactions((prev) => [newTransaction, ...prev])

      toast({
        title: "Předplatné aktualizováno",
        description: `Vaše předplatné bylo úspěšně změněno na ${selectedPlan.name}.`,
      })
    } catch (error) {
      toast({
        title: "Chyba při aktualizaci předplatného",
        description: error instanceof Error ? error.message : "Nepodařilo se aktualizovat předplatné.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Cancel subscription
  const cancelSubscription = async () => {
    setIsLoading(true)
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000))

      if (!subscription) {
        throw new Error("Žádné aktivní předplatné")
      }

      // Update subscription to cancel at period end
      setSubscription((prev) => (prev ? { ...prev, cancelAtPeriodEnd: true } : null))

      toast({
        title: "Předplatné zrušeno",
        description: "Vaše předplatné bude zrušeno na konci aktuálního období.",
      })
    } catch (error) {
      toast({
        title: "Chyba při rušení předplatného",
        description: error instanceof Error ? error.message : "Nepodařilo se zrušit předplatné.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Process one-time payment
  const processOneTimePayment = async (serviceId: string, paymentMethodId: string) => {
    setIsLoading(true)
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000))

      const selectedService = oneTimeServices.find((service) => service.id === serviceId)
      const selectedMethod = paymentMethods.find((method) => method.id === paymentMethodId)

      if (!selectedService) {
        throw new Error("Vybraná služba nebyla nalezena")
      }

      if (!selectedMethod) {
        throw new Error("Vybraná platební metoda nebyla nalezena")
      }

      // Add transaction
      const newTransaction: Transaction = {
        id: `tx_${Date.now()}`,
        date: new Date().toISOString(),
        amount: selectedService.price,
        description: selectedService.name,
        status: "completed",
        paymentMethod:
          selectedMethod.type === "card"
            ? `${selectedMethod.details.brand} •••• ${selectedMethod.details.last4}`
            : "PayPal",
        type: "one-time",
        invoiceUrl: "#",
      }

      setTransactions((prev) => [newTransaction, ...prev])

      toast({
        title: "Platba zpracována",
        description: `Vaše platba za ${selectedService.name} byla úspěšně zpracována.`,
      })
    } catch (error) {
      toast({
        title: "Chyba při zpracování platby",
        description: error instanceof Error ? error.message : "Nepodařilo se zpracovat platbu.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const value = {
    paymentMethods,
    subscription,
    transactions,
    subscriptionPlans,
    oneTimeServices,
    isLoading,
    addPaymentMethod,
    removePaymentMethod,
    setDefaultPaymentMethod,
    updateSubscription,
    cancelSubscription,
    processOneTimePayment,
  }

  return <PaymentContext.Provider value={value}>{children}</PaymentContext.Provider>
}

// Hook to use the payment context
export function usePayment() {
  const context = useContext(PaymentContext)
  if (context === undefined) {
    throw new Error("usePayment must be used within a PaymentProvider")
  }
  return context
}


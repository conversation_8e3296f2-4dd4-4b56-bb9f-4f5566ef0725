<?php
require_once 'config.php';

$payload = file_get_contents('php://input');
$signature = $_SERVER['HTTP_X_VAPI_SIGNATURE'] ?? '';

$computedSignature = hash_hmac('sha256', $payload, VAPI_WEBHOOK_SECRET);

if (!hash_equals($computedSignature, $signature)) {
    http_response_code(401);
    exit('Neplatný podpis');
}

$data = json_decode($payload, true);

if (isset($data['event_type'])) {
    switch ($data['event_type']) {
        case 'call_completed':
            processCallCompleted($data);
            break;
        case 'metric_update':
            updateMetrics($data);
            break;
        // Přidejte dalš<PERSON> případy podle potřeby
    }
}

http_response_code(200);
echo json_encode(['status' => 'success']);

function processCallCompleted($data) {
    global $conn;
    
    $callId = $conn->real_escape_string($data['call_id']);
    $duration = $conn->real_escape_string($data['duration']);
    $status = $data['success'] ? 'successful' : 'failed';

    $sql = "INSERT INTO calls (call_id, duration, status, timestamp) VALUES (?, ?, ?, NOW())";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("sis", $callId, $duration, $status);
    $stmt->execute();
    $stmt->close();
    
    log_action('call_completed', "Hovor dokončen: ID $callId, Délka: $duration, Status: $status");
}

function updateMetrics($data) {
    global $conn;
    
    $metricName = $conn->real_escape_string($data['metric_name']);
    $value = $conn->real_escape_string($data['value']);

    $sql = "INSERT INTO metrics (name, value, timestamp) VALUES (?, ?, NOW()) 
            ON DUPLICATE KEY UPDATE value = ?, timestamp = NOW()";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("sss", $metricName, $value, $value);
    $stmt->execute();
    $stmt->close();
    
    log_action('metric_update', "Metrika aktualizována: $metricName = $value");
}


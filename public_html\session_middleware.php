<?php
function validateSession() {
    // Start session if not already started
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    // Debug log current session state
    writeErrorLog('Session State', [
        'session_id' => session_id(),
        'session_status' => session_status(),
        'session_data' => $_SESSION,
        'request_uri' => $_SERVER['REQUEST_URI']
    ]);

    // Check if user is logged in
    if (!isset($_SESSION['user_id'])) {
        writeErrorLog('Session Validation Failed', [
            'reason' => 'No user_id in session'
        ]);
        return false;
    }

    try {
        $mysqli = getDbConnection();
        
        // Verify session in database
        $stmt = $mysqli->prepare("SELECT 1 FROM user_sessions 
                                 WHERE user_id = ? 
                                 AND session_id = ? 
                                 AND active = 1");
        
        $sessionId = session_id();
        $stmt->bind_param("is", $_SESSION['user_id'], $sessionId);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows === 0) {
            writeErrorLog('Session Validation Failed', [
                'reason' => 'Session not found in database',
                'user_id' => $_SESSION['user_id'],
                'session_id' => $sessionId
            ]);
            return false;
        }

        // Update last activity
        $stmt = $mysqli->prepare("UPDATE user_sessions 
                                SET last_activity = NOW() 
                                WHERE user_id = ? AND session_id = ?");
        $stmt->bind_param("is", $_SESSION['user_id'], $sessionId);
        $stmt->execute();

        return true;

    } catch (Exception $e) {
        writeErrorLog('Session Validation Error', [
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
        return false;
    }
}
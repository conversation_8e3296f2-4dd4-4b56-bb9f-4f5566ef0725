<?php
require_once 'config.php';
require_once 'subscription_functions/check_minute_limits.php';

function testWebhookNotification($thresholdPercentage = 10, $notificationInterval = 7, $forceNotify = false) {
    $conn = getDbConnection();
    if (!$conn) {
        error_log('Failed to establish database connection in testWebhookNotification');
        return false;
    }
    
    $webhook_url = MAKE_WEBHOOK_URL;
    
    try {
        $sql = "
            SELECT id, username, subscription_plan, minute_limit, minutes_remaining, used_minutes,
                   (minutes_remaining / minute_limit * 100) as percentage_remaining,
                   last_notification_sent
            FROM users
            WHERE subscription_plan IN ('Základní', 'Pro')
              AND minute_limit > 0
        ";
        
        if (!$forceNotify) {
            $sql .= " AND (minutes_remaining / minute_limit * 100) <= ?";
        }
        
        $sql .= " AND (last_notification_sent IS NULL OR last_notification_sent < DATE_SUB(NOW(), INTERVAL ? DAY))";
        
        $stmt = $conn->prepare($sql);
        
        if (!$stmt) {
            throw new Exception("Failed to prepare statement: " . $conn->error);
        }
        
        if (!$forceNotify) {
            $stmt->bind_param("di", $thresholdPercentage, $notificationInterval);
        } else {
            $stmt->bind_param("i", $notificationInterval);
        }
        
        $stmt->execute();
        $result = $stmt->get_result();

        $notificationsSent = 0;
        $users = [];
        
        while ($user = $result->fetch_assoc()) {
            $percentageRemaining = floatval($user['percentage_remaining']);
            $users[] = $user;
            
            $notificationMessage = sprintf(
                "TEST NOTIFICATION: Zbývá pouze %.1f%% minut (zbývá %d z %d minut). Prosím, zvažte navýšení vašeho plánu.",
                $percentageRemaining,
                $user['minutes_remaining'],
                $user['minute_limit']
            );
            
            $data = array(
                'notification_type' => 'low_minutes_alert',
                'user' => array(
                    'username' => $user['username'],
                    'subscription_plan' => $user['subscription_plan'],
                    'minutes_remaining' => $user['minutes_remaining'],
                    'minute_limit' => $user['minute_limit'],
                    'percentage_remaining' => $percentageRemaining
                ),
                'message' => $notificationMessage,
                'timestamp' => date('Y-m-d H:i:s'),
                'source' => 'DentiBot Minute Limit Test',
                'test' => true
            );
            
            $ch = curl_init($webhook_url);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                'Content-Type: application/json',
                'Accept: application/json'
            ));
            
            $response = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($http_code >= 200 && $http_code < 300) {
                $notificationsSent++;
                error_log("Test webhook sent successfully for user: " . $user['username']);
            } else {
                error_log("Failed to send test webhook for user: " . $user['username'] . ". HTTP Code: " . $http_code);
            }
        }
        
        return [
            'success' => true,
            'notifications_sent' => $notificationsSent,
            'users_found' => count($users),
            'users' => $users
        ];
    } catch (Exception $e) {
        error_log('Test webhook notification error: ' . $e->getMessage());
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    } finally {
        if (isset($stmt)) {
            $stmt->close();
        }
        if ($conn) {
            $conn->close();
        }
    }
}

// Only process API request if specifically requested
if (isset($_GET['run_test'])) {
    header('Content-Type: application/json');
    $thresholdPercentage = isset($_GET['threshold']) ? floatval($_GET['threshold']) : 10;
    $notificationInterval = isset($_GET['interval']) ? intval($_GET['interval']) : 7;
    $forceNotify = isset($_GET['force']) && $_GET['force'] === 'true';
    $result = testWebhookNotification($thresholdPercentage, $notificationInterval, $forceNotify);
    echo json_encode($result, JSON_PRETTY_PRINT);
    exit;
}
?>
<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Webhook Notification a Limity Minut</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
</head>
<body class="bg-gray-100 min-h-screen p-8">
    <div class="max-w-2xl mx-auto">
        <div class="bg-white rounded-lg shadow-md p-6">
            <h1 class="text-2xl font-bold mb-6">Test Webhook Notification a Limity Minut</h1>
            
            <div class="mb-6">
                <p class="text-gray-600">
                    Tento test zkontroluje uživatele s nízkým počtem zbývajících minut a odešle testovací webhook notifikaci.
                </p>
            </div>
            
            <div class="space-y-4">
                <div>
                    <label for="threshold" class="block text-sm font-medium text-gray-700">Práh procent (výchozí: 10%)</label>
                    <input type="number" 
                           id="threshold" 
                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                           value="10"
                           min="1"
                           max="100">
                </div>
                
                <div>
                    <label for="interval" class="block text-sm font-medium text-gray-700">Interval notifikací (dny, výchozí: 7)</label>
                    <input type="number" 
                           id="interval" 
                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                           value="7"
                           min="1">
                </div>
                
                <div class="flex items-center">
                    <input type="checkbox" 
                           id="force" 
                           class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                    <label for="force" class="ml-2 block text-sm text-gray-900">
                        Vynutit notifikace (ignorovat práh)
                    </label>
                </div>
                
                <button onclick="runTest()" 
                        class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Spustit test
                </button>
            </div>
            
            <div id="result" class="mt-6 hidden">
                <h2 class="text-lg font-semibold mb-2">Výsledek:</h2>
                <pre id="resultContent" class="bg-gray-50 rounded-md p-4 text-sm overflow-auto"></pre>
            </div>
        </div>
    </div>

    <script>
    async function runTest() {
        const threshold = document.getElementById('threshold').value;
        const interval = document.getElementById('interval').value;
        const force = document.getElementById('force').checked;
        const resultDiv = document.getElementById('result');
        const resultContent = document.getElementById('resultContent');
        
        try {
            resultDiv.classList.remove('hidden');
            resultContent.textContent = 'Probíhá test...';
            
            const response = await fetch(`?run_test&threshold=${threshold}&interval=${interval}&force=${force}`);
            const data = await response.json();
            
            resultContent.textContent = JSON.stringify(data, null, 2);
        } catch (error) {
            resultContent.textContent = `Error: ${error.message}`;
        }
    }
    </script>
</body>
</html>


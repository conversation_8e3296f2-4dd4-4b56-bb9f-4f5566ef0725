*   Trying **************:443...
* Connected to api.baserow.io (**************) port 443 (#0)
* ALPN, offering h2
* ALPN, offering http/1.1
*  CAfile: /etc/pki/tls/certs/ca-bundle.crt
* SSL connection using TLSv1.3 / TLS_AES_256_GCM_SHA384
* ALPN, server accepted to use h2
* Server certificate:
*  subject: CN=api.baserow.io
*  start date: Jan 31 09:59:15 2025 GMT
*  expire date: May  1 09:59:14 2025 GMT
*  subjectAltName: host "api.baserow.io" matched cert's "api.baserow.io"
*  issuer: C=US; O=Let's Encrypt; CN=R11
*  SSL certificate verify ok.
* Using HTTP2, server supports multi-use
* Connection state changed (HTTP/2 confirmed)
* Copying HTTP/2 data in stream buffer to connection buffer after upgrade: len=0
* Using Stream ID: 1 (easy handle 0x2792b80)
> GET /api/database/rows/table/402582/?user_field_names=true&size=1 HTTP/2
Host: api.baserow.io
accept: */*
authorization: Token jBZBMRZ1OL6E8MkNYzCnwhSbelEDsyJv
content-type: application/json

* old SSL session ID is stale, removing
* Connection state changed (MAX_CONCURRENT_STREAMS == 128)!
< HTTP/2 200 
< date: Wed, 05 Feb 2025 23:21:09 GMT
< content-type: application/json
< content-length: 637
< allow: GET, POST, HEAD, OPTIONS
< x-frame-options: DENY
< x-content-type-options: nosniff
< referrer-policy: same-origin
< cross-origin-opener-policy: same-origin
< vary: origin
< strict-transport-security: max-age=31536000; includeSubDomains
< 
* Connection #0 to host api.baserow.io left intact

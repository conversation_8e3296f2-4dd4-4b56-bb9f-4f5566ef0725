<?php
require_once __DIR__ . '/../config.php';

function addPhoneNumberToVapiAssistant($userId, $phoneNumber) {
    $vapiApiKey = VAPI_API_KEY;
    $assistantId = VAPI_ASSISTANT_ID;

    $ch = curl_init("https://api.vapi.ai/assistants/{$assistantId}/phone-numbers");
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode(['phone_number' => $phoneNumber]));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        "Authorization: Bearer {$vapiApiKey}",
        "Content-Type: application/json"
    ]);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($httpCode == 200 || $httpCode == 201) {
        $conn = getDbConnection();
        if (!$conn) {
            error_log("Failed to establish database connection in addPhoneNumberToVapiAssistant");
            return false;
        }

        try {
            $stmt = $conn->prepare("UPDATE users SET vapi_number_active = TRUE WHERE id = ?");
            $stmt->bind_param("i", $userId);
            $result = $stmt->execute();
            $stmt->close();

            if ($result) {
                error_log("Successfully added phone number to vapi assistant and updated database for user: " . $userId);
                return true;
            } else {
                error_log("Failed to update database after adding phone number to vapi assistant for user: " . $userId);
                return false;
            }
        } catch (Exception $e) {
            error_log("Database error in addPhoneNumberToVapiAssistant: " . $e->getMessage());
            return false;
        } finally {
            $conn->close();
        }
    } else {
        error_log("Failed to add phone number to vapi assistant for user: " . $userId . ". HTTP Code: " . $httpCode);
        return false;
    }
}
<?php
require_once '../stripe-php/init.php';
require_once '../config.php';
require_once '../stripe_functions.php';

// Webhook handler pro Stripe události
function handleStripeWebhook() {
    $payload = @file_get_contents('php://input');
    $sig_header = $_SERVER['HTTP_STRIPE_SIGNATURE'];
    $event = null;

    try {
        $event = \Stripe\Webhook::constructEvent(
            $payload, $sig_header, STRIPE_WEBHOOK_SECRET
        );
    } catch(\UnexpectedValueException $e) {
        // Invalid payload
        http_response_code(400);
        exit();
    } catch(\Stripe\Exception\SignatureVerificationException $e) {
        // Invalid signature
        http_response_code(400);
        exit();
    }

    // Handle the event
    switch ($event->type) {
        case 'customer.subscription.created':
        case 'customer.subscription.updated':
        case 'customer.subscription.deleted':
            $subscription = $event->data->object;
            handleSubscriptionWebhook($event);
            break;
        default:
            echo 'Received unknown event type ' . $event->type;
    }

    http_response_code(200);
}

// Spuštění webhook handleru
handleStripeWebhook();
?>


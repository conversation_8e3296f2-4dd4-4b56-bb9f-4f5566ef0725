<?php
require_once 'config.php';
require_once 'db_connection.php';
require_once 'error_log.php';

// Allow BulkGate's IP addresses
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Log incoming webhook request for debugging
writeErrorLog('Webhook Request', [
    'method' => $_SERVER['REQUEST_METHOD'],
    'headers' => getallheaders(),
    'raw_input' => file_get_contents('php://input')
]);

// Verify that this is a POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    exit('Method Not Allowed');
}

// Get the raw POST data
$rawData = file_get_contents('php://input');
$data = json_decode($rawData, true);

if (!$data) {
    logApiError("Invalid webhook data received", ['raw_data' => $rawData]);
    http_response_code(400);
    exit('Invalid Data');
}

try {
    $db = getDatabaseConnection();
    
    // Log the delivery report
    $stmt = $db->prepare("
        INSERT INTO sms_delivery_reports 
        (message_id, status, timestamp, error_code, error_message) 
        VALUES (?, ?, NOW(), ?, ?)
    ");

    foreach ($data['messages'] as $message) {
        $messageId = $message['id'] ?? null;
        $status = $message['status'] ?? 'unknown';
        $errorCode = $message['error']['code'] ?? null;
        $errorMessage = $message['error']['message'] ?? null;

        $stmt->bind_param("ssss", $messageId, $status, $errorCode, $errorMessage);
        $stmt->execute();

        // Log for debugging
        writeErrorLog("Delivery Report Received", [
            'message_id' => $messageId,
            'status' => $status,
            'error_code' => $errorCode,
            'error_message' => $errorMessage
        ]);

        // Update campaign statistics if message_id exists
        if ($messageId) {
            updateCampaignStats($messageId, $status);
        }
    }

    // Send success response
    http_response_code(200);
    echo json_encode(['status' => 'success']);

} catch (Exception $e) {
    logApiError("Error processing webhook", [
        'error' => $e->getMessage(),
        'data' => $data
    ]);
    http_response_code(500);
    echo json_encode(['status' => 'error', 'message' => 'Internal Server Error']);
}

// Ensure database connection is closed
if (isset($db) && $db->ping()) {
    $db->close();
}

function updateCampaignStats($messageId, $status) {
    $db = getDatabaseConnection();
    
    // First, find the campaign_id for this message
    $stmt = $db->prepare("
        SELECT campaign_id 
        FROM sms_campaign_logs 
        WHERE message_id = ?
    ");
    $stmt->bind_param("s", $messageId);
    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();
    
    if ($row) {
        $campaignId = $row['campaign_id'];
        
        // Update campaign statistics
        $stmt = $db->prepare("
            UPDATE sms_campaigns 
            SET 
                delivered_messages = (
                    SELECT COUNT(*) 
                    FROM sms_campaign_logs 
                    WHERE campaign_id = ? AND status = 'delivered'
                ),
                failed_messages = (
                    SELECT COUNT(*) 
                    FROM sms_campaign_logs 
                    WHERE campaign_id = ? AND status IN ('failed', 'error')
                ),
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        ");
        
        $stmt->bind_param("iii", $campaignId, $campaignId, $campaignId);
        $stmt->execute();
    }
}


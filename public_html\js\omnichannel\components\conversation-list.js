const ConversationListComponent = (() => {
    function ConversationList() {
        const [activeTab, setActiveTab] = React.useState('unread');
        const { conversations, selectConversation } = window.OmnichannelContext.useOmnichannel();

        const getChannelIcon = (channel) => {
            const iconProps = {
                className: "h-4 w-4",
                fill: "none",
                stroke: "currentColor",
                viewBox: "0 0 24 24"
            };

            const paths = {
                email: "M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z",
                sms: "M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z",
                whatsapp: "M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
            };

            return React.createElement(
                'svg',
                iconProps,
                React.createElement('path', {
                    strokeLinecap: "round",
                    strokeLinejoin: "round",
                    strokeWidth: 2,
                    d: paths[channel] || paths.sms
                })
            );
        };

        return React.createElement(
            'div',
            { className: "flex flex-col h-full w-80 bg-white border-r" },
            React.createElement(
                'div',
                { className: "p-4 border-b" },
                React.createElement(
                    'div',
                    { className: "flex space-x-4 mb-4" },
                    ['unread', 'recent', 'all'].map(tab => 
                        React.createElement(
                            'button',
                            {
                                key: tab,
                                className: `${activeTab === tab ? 'text-indigo-600 font-medium' : 'text-gray-600'}`,
                                onClick: () => setActiveTab(tab)
                            },
                            tab.charAt(0).toUpperCase() + tab.slice(1)
                        )
                    )
                ),
                React.createElement(
                    'div',
                    { className: "relative" },
                    React.createElement('input', {
                        type: "text",
                        placeholder: "Hledat konverzace...",
                        className: "w-full px-4 py-2 pl-10 border rounded-lg focus:outline-none focus:border-indigo-500"
                    }),
                    React.createElement(
                        'svg',
                        {
                            className: "absolute left-3 top-3 h-4 w-4 text-gray-400",
                            fill: "none",
                            stroke: "currentColor",
                            viewBox: "0 0 24 24"
                        },
                        React.createElement('path', {
                            strokeLinecap: "round",
                            strokeLinejoin: "round",
                            strokeWidth: 2,
                            d: "M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                        })
                    )
                )
            ),
            React.createElement(
                'div',
                { className: "flex-1 overflow-y-auto" },
                conversations.map((conv) =>
                    React.createElement(
                        'div',
                        {
                            key: conv.id,
                            className: "p-4 border-b hover:bg-gray-50 cursor-pointer",
                            onClick: () => selectConversation(conv.id)
                        },
                        React.createElement(
                            'div',
                            { className: "flex items-center justify-between mb-1" },
                            React.createElement('span', { className: "font-medium" }, conv.patientName),
                            React.createElement(
                                'span',
                                { className: "text-sm text-gray-500" },
                                new Date(conv.lastMessageTime).toLocaleTimeString()
                            )
                        ),
                        React.createElement(
                            'div',
                            { className: "text-sm text-gray-600 truncate" },
                            conv.lastMessage
                        ),
                        React.createElement(
                            'div',
                            { className: "flex items-center gap-2 mt-2" },
                            getChannelIcon(conv.channel),
                            React.createElement(
                                'span',
                                { className: "text-xs bg-gray-200 px-2 py-1 rounded-full" },
                                conv.channel
                            ),
                            conv.unread > 0 && React.createElement(
                                'span',
                                { className: "ml-auto bg-red-500 text-white text-xs px-2 py-1 rounded-full" },
                                conv.unread
                            )
                        )
                    )
                )
            )
        );
    }

    return { ConversationList };
})();

window.ConversationListComponent = ConversationListComponent;
<?php
require_once 'api_functions.php';

/**
 * Payment Processor - Zpracování plateb
 */
class PaymentProcessor {
    
    /**
     * Zpracuje platbu za dobití SMS kreditu
     * @param int $userId ID uživatele
     * @param float $amount Částka k dobití
     * @param string $paymentMethod Platební metoda
     * @return array Výsledek operace
     */
    public function processSmsRecharge($userId, $amount, $paymentMethod) {
        // Zde by byla logika pro zpracování platby přes platební bránu
        // Pro účely demonstrace použijeme simulaci
        
        // Simulace úspěšné platby
        $paymentResult = [
            'success' => true,
            'transaction_id' => 'SMS_' . uniqid(),
            'amount' => $amount,
            'payment_method' => $paymentMethod,
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        // Pokud byla platba úspěšná, dobijeme kredit přes API
        if ($paymentResult['success']) {
            $rechargeResult = makeApiCall('POST', "users/{$userId}/sms-credit/recharge", [
                'amount' => $amount,
                'payment_method' => $paymentMethod,
                'transaction_id' => $paymentResult['transaction_id']
            ]);
            
            return [
                'success' => $rechargeResult['success'] ?? false,
                'message' => $rechargeResult['message'] ?? 'Kredit byl úspěšně dobit',
                'new_balance' => $rechargeResult['new_balance'] ?? 0,
                'transaction_id' => $paymentResult['transaction_id']
            ];
        } else {
            return [
                'success' => false,
                'message' => 'Platba nebyla úspěšná',
                'error' => $paymentResult['error'] ?? 'Neznámá chyba'
            ];
        }
    }
    
    /**
     * Zpracuje platbu za předplatné
     * @param int $userId ID uživatele
     * @param string $plan Plán předplatného
     * @param string $interval Interval platby (monthly, yearly)
     * @return array Výsledek operace
     */
    public function processSubscriptionPayment($userId, $plan, $interval) {
        // Zde by byla logika pro zpracování platby za předplatné
        // Pro účely demonstrace použijeme simulaci
        
        // Simulace úspěšné platby
        $paymentResult = [
            'success' => true,
            'transaction_id' => 'SUB_' . uniqid(),
            'plan' => $plan,
            'interval' => $interval,
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        // Pokud byla platba úspěšná, aktivujeme předplatné přes API
        if ($paymentResult['success']) {
            $subscriptionResult = makeApiCall('POST', "users/{$userId}/subscription/activate", [
                'plan' => $plan,
                'interval' => $interval,
                'transaction_id' => $paymentResult['transaction_id']
            ]);
            
            return [
                'success' => $subscriptionResult['success'] ?? false,
                'message' => $subscriptionResult['message'] ?? 'Předplatné bylo úspěšně aktivováno',
                'subscription_id' => $subscriptionResult['subscription_id'] ?? '',
                'transaction_id' => $paymentResult['transaction_id']
            ];
        } else {
            return [
                'success' => false,
                'message' => 'Platba nebyla úspěšná',
                'error' => $paymentResult['error'] ?? 'Neznámá chyba'
            ];
        }
    }
}
?>


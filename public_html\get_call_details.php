<?php
require_once 'config.php';
require_once 'api_functions.php';
require_once 'error_log.php';

// Kontrola přihlášení
if (!isset($_SESSION['user_id'])) {
    header('HTTP/1.1 401 Unauthorized');
    exit;
}

// Kontrola ID hovoru
if (!isset($_GET['id'])) {
    header('HTTP/1.1 400 Bad Request');
    exit;
}

try {
    $apiConfig = getCurrentUserApiKey();
    if (!$apiConfig) {
        throw new Exception("API klíč není nastaven");
    }

    // Make API call to get call details
    $url = rtrim($apiConfig['url'], '/') . '/call/' . $_GET['id'];
    $headers = [
        'Authorization: Bearer ' . $apiConfig['key'],
        'Accept: application/json'
    ];

    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_HTTPHEADER => $headers,
        CURLOPT_SSL_VERIFYPEER => true
    ]);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    
    if (curl_errno($ch)) {
        throw new Exception(curl_error($ch));
    }
    
    curl_close($ch);

    if ($httpCode !== 200) {
        throw new Exception("API vrátila chybový kód: " . $httpCode);
    }

    header('Content-Type: application/json');
    echo $response;

} catch (Exception $e) {
    header('HTTP/1.1 500 Internal Server Error');
    echo json_encode(['error' => $e->getMessage()]);
}
?>
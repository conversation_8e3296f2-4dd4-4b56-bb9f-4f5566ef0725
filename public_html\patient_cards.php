<?php
/**
 * Automatické karty pacientů - integrace s Baserow
 */

require_once 'config_optimized.php';
require_once 'baserow_functions.php';
require_once 'patient_card/BaserowCardManager.php';

// Kontrola přihlášení
requireLogin();

// Parametry
$page = max(1, (int)($_GET['page'] ?? 1));
$limit = 10;
$search = $_GET['search'] ?? '';

// Načtení pacientů s kartami
$patientsResult = getPatients($page, $limit, $search);
$patients = $patientsResult['patients'] ?? [];
$totalRecords = $patientsResult['totalRecords'] ?? 0;
$totalPages = ceil($totalRecords / $limit);
$error = $patientsResult['success'] ? null : ($patientsResult['error'] ?? 'Neznámá chyba');

// Statistiky karet
$cardStats = ['total' => 0, 'active' => 0, 'with_tst' => 0];
$manager = new BaserowCardManager();

foreach ($patients as &$patient) {
    $baserow_id = $patient['row_id'] ?? $patient['id'];
    $cardResult = $manager->getPatientCardFromBaserow($baserow_id);
    
    if ($cardResult['success']) {
        $patient['has_card'] = true;
        $patient['card_info'] = $cardResult['card'];
        $cardStats['total']++;
        if ($cardResult['card']['tst_count'] > 0) {
            $cardStats['with_tst']++;
        }
        $cardStats['active']++;
    } else {
        $patient['has_card'] = false;
        $patient['card_error'] = $cardResult['error'] ?? 'Neznámá chyba';
    }
}

$currentPage = 'patient_cards';
?>

<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Automatické karty pacientů - Dentibot</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
        .sidebar {
            width: 16rem;
            background: linear-gradient(135deg, #1e3a8a, #1e40af);
            color: white;
            position: fixed;
            height: 100vh;
            left: 0;
            top: 0;
            z-index: 100;
            overflow-y: auto;
        }
        .main-content {
            margin-left: 16rem;
            min-height: 100vh;
        }
        .nav-link {
            display: flex;
            align-items: center;
            padding: 0.75rem 1.5rem;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.2s;
            cursor: pointer;
        }
        .nav-link:hover, .nav-link.active {
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }
        .nav-icon {
            width: 1.25rem;
            height: 1.25rem;
            margin-right: 0.75rem;
        }
        .auto-badge {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
            border-radius: 9999px;
            font-weight: 500;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="p-6 border-b border-blue-600">
            <div class="flex items-center">
                <div class="w-8 h-8 bg-white rounded mr-3 flex items-center justify-center">
                    <span class="text-blue-600 font-bold text-sm">D</span>
                </div>
                <span class="text-xl font-bold">Dentibot</span>
            </div>
            <div class="mt-3 text-sm text-blue-200">
                <div><?php echo htmlspecialchars($_SESSION['username'] ?? 'Demo User'); ?></div>
                <div class="text-xs"><?php echo ucfirst(getUserSubscriptionPlan($_SESSION['user_id'])); ?> plán</div>
            </div>
        </div>
        <nav class="mt-6">
            <a href="main_dashboard.php" class="nav-link">
                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <rect x="3" y="3" width="18" height="18" rx="2"/><path d="M3 9h18"/><path d="M9 21V9"/>
                </svg>
                Dashboard
            </a>
            <a href="call_history.php" class="nav-link">
                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
                </svg>
                Historie hovorů
            </a>
            <a href="sms_campaigns.php" class="nav-link">
                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
                </svg>
                Hromadné SMS
            </a>
            <a href="patients_list.php" class="nav-link">
                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/><circle cx="9" cy="7" r="4"/><path d="M23 21v-2a4 4 0 0 0-3-3.87"/><path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                </svg>
                Seznam pacientů
            </a>
            <a href="patient_cards.php" class="nav-link active">
                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/><polyline points="14,2 14,8 20,8"/><line x1="16" y1="13" x2="8" y2="13"/><line x1="16" y1="17" x2="8" y2="17"/><polyline points="10,9 9,9 8,9"/><circle cx="12" cy="15" r="2"/>
                </svg>
                Karty pacientů
                <span class="ml-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full">auto</span>
            </a>
            <a href="demo_events.html" class="nav-link">
                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                </svg>
                Demo události
                <span class="ml-2 bg-orange-500 text-white text-xs px-2 py-1 rounded-full">test</span>
            </a>
            <a href="settings.php" class="nav-link">
                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <circle cx="12" cy="12" r="3"/><path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
                </svg>
                Nastavení
            </a>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <div class="p-8">
            <!-- Header -->
            <div class="mb-8">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">Automatické karty pacientů</h1>
                        <p class="text-gray-600 mt-2">Event-driven systém pro automatické generování karet</p>
                    </div>
                    <div class="auto-badge">
                        Zero-click administrace
                    </div>
                </div>
            </div>

            <!-- Statistics -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <div class="bg-white rounded-lg shadow-sm border p-6">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Celkem karet</p>
                            <p class="text-2xl font-bold text-gray-900"><?php echo $cardStats['total']; ?></p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-sm border p-6">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Aktivní karty</p>
                            <p class="text-2xl font-bold text-gray-900"><?php echo $cardStats['active']; ?></p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-sm border p-6">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">S časovými razítky</p>
                            <p class="text-2xl font-bold text-gray-900"><?php echo $cardStats['with_tst']; ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Error Alert -->
            <?php if ($error): ?>
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                    <strong class="font-bold">Chyba při načítání pacientů:</strong>
                    <span class="block sm:inline"><?php echo htmlspecialchars($error); ?></span>
                </div>
            <?php endif; ?>

            <!-- Search -->
            <div class="bg-white rounded-lg shadow-sm border p-6 mb-6">
                <form method="GET" class="flex gap-4">
                    <div class="flex-1">
                        <input type="text" name="search" value="<?php echo htmlspecialchars($search); ?>" 
                               placeholder="Hledat pacienta..." 
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors">
                        Hledat
                    </button>
                    <?php if ($search): ?>
                        <a href="patient_cards.php" class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg transition-colors">
                            Zrušit
                        </a>
                    <?php endif; ?>
                </form>
            </div>

            <!-- Patient Cards -->
            <?php if (!empty($patients)): ?>
                <div class="space-y-6 mb-8">
                    <?php foreach ($patients as $patient): ?>
                        <div class="bg-white rounded-lg shadow-sm border p-6">
                            <div class="flex items-start justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center gap-3 mb-2">
                                        <h3 class="text-lg font-semibold text-gray-900">
                                            <?php echo htmlspecialchars($patient['jmeno_pacienta'] ?? $patient['Jmeno_pacienta'] ?? 'Neznámé jméno'); ?>
                                        </h3>
                                        <?php if ($patient['has_card']): ?>
                                            <span class="auto-badge">Automatická karta</span>
                                        <?php else: ?>
                                            <span class="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full">Bez karty</span>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                                        <div>
                                            <strong>Telefon:</strong> 
                                            <?php echo htmlspecialchars($patient['telefonni_cislo'] ?? $patient['Telefonni_cislo'] ?? 'Bez telefonu'); ?>
                                        </div>
                                        <div>
                                            <strong>Email:</strong> 
                                            <?php echo htmlspecialchars($patient['emailova_adresa'] ?? $patient['Emailova_adresa'] ?? 'Bez emailu'); ?>
                                        </div>
                                        <div>
                                            <strong>Pojišťovna:</strong> 
                                            <?php echo htmlspecialchars($patient['zdravotni_pojistovna'] ?? $patient['Zdravotni_pojistovna'] ?? 'N/A'); ?>
                                        </div>
                                    </div>

                                    <?php if ($patient['has_card']): ?>
                                        <div class="mt-4 grid grid-cols-2 md:grid-cols-5 gap-4 text-sm">
                                            <div>
                                                <span class="text-gray-600">Verze karty:</span>
                                                <span class="font-semibold"><?php echo $patient['card_info']['card_version']; ?></span>
                                            </div>
                                            <div>
                                                <span class="text-gray-600">TST:</span>
                                                <span class="font-semibold"><?php echo $patient['card_info']['tst_count']; ?></span>
                                            </div>
                                            <div>
                                                <span class="text-gray-600">Záznamy:</span>
                                                <span class="font-semibold"><?php echo $patient['card_info']['stats']['notes']; ?></span>
                                            </div>
                                            <div>
                                                <span class="text-gray-600">Snímky:</span>
                                                <span class="font-semibold"><?php echo $patient['card_info']['stats']['images']; ?></span>
                                            </div>
                                            <div>
                                                <span class="text-gray-600">Výkony:</span>
                                                <span class="font-semibold"><?php echo $patient['card_info']['stats']['procedures']; ?></span>
                                            </div>
                                        </div>
                                    <?php else: ?>
                                        <div class="mt-4 text-sm text-red-600">
                                            <strong>Chyba:</strong> <?php echo htmlspecialchars($patient['card_error']); ?>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <div class="flex flex-col gap-2 ml-6">
                                    <?php if ($patient['has_card']): ?>
                                        <a href="patient_card_view.php?baserow_id=<?php echo urlencode($patient['row_id'] ?? $patient['id']); ?>" 
                                           class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors text-center">
                                            Zobrazit kartu
                                        </a>
                                        <a href="baserow_card_demo.php?phone=<?php echo urlencode($patient['telefonni_cislo'] ?? $patient['Telefonni_cislo'] ?? ''); ?>" 
                                           class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors text-center">
                                            Demo události
                                        </a>
                                    <?php else: ?>
                                        <button onclick="createCard('<?php echo htmlspecialchars($patient['row_id'] ?? $patient['id']); ?>')" 
                                                class="bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                                            Vytvořit kartu
                                        </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <!-- Pagination -->
                <?php if ($totalPages > 1): ?>
                    <div class="flex items-center justify-between">
                        <div class="text-sm text-gray-700">
                            Zobrazeno <?php echo count($patients); ?> z <?php echo number_format($totalRecords); ?> pacientů
                        </div>
                        <div class="flex items-center gap-2">
                            <?php if ($page > 1): ?>
                                <a href="?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($search); ?>" 
                                   class="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors">
                                    Předchozí
                                </a>
                            <?php endif; ?>
                            
                            <span class="bg-blue-600 text-white px-4 py-2 rounded-lg">
                                <?php echo $page; ?> / <?php echo $totalPages; ?>
                            </span>
                            
                            <?php if ($page < $totalPages): ?>
                                <a href="?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($search); ?>" 
                                   class="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors">
                                    Další
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>
            <?php else: ?>
                <div class="text-center py-12">
                    <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                    </svg>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Žádné karty pacientů</h3>
                    <p class="text-gray-600 mb-4">Zatím nejsou načteny žádné karty pacientů.</p>
                    <a href="demo_events.html" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors">
                        Vyzkoušet demo události
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script>
        function createCard(patientId) {
            if (confirm('Vytvořit automatickou kartu pro pacienta #' + patientId + '?')) {
                // Implementace vytvoření karty
                alert('Funkce vytvoření karty bude implementována');
            }
        }
    </script>
</body>
</html>

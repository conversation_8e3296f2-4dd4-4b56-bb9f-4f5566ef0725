<?php
require_once 'config.php';
require_once 'api_functions.php';
require_once 'error_log.php';
require_once 'sync_vapi_data.php';
require_once 'stripe_functions.php';
require_once 'db_connection.php'; // Make sure this is included

// Ensure session is started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Kontrola přihlášení
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

// Generate CSRF token
$csrf_token = generateCSRFToken();

$error = null;
$success = null;

// Zpracování odhlášení
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['logout'])) {
    if (verifyCSRFToken($_POST['csrf_token'])) {
        logoutUser();
        header("Location: landing.php");
        exit();
    } else {
        $error = "Neplatný CSRF token. Akce byla zru<PERSON>.";
    }
}

// Automatická synchronizace dat po přihlášení
if (!isset($_SESSION['data_synced']) || $_SESSION['data_synced'] !== true) {
    $syncResult = syncVapiData();
    if ($syncResult === true) {
        $_SESSION['last_sync'] = time();
        $_SESSION['data_synced'] = true;
        $success = "Data byla úspěšně synchronizována po přihlášení.";
    } else {
        $error = "Nepodařilo se synchronizovat data po přihlášení: " . $syncResult;
    }
}

// Get the selected time range (default to 30 days if not set)
$timeRange = isset($_GET['timeRange']) ? sanitizeInput($_GET['timeRange']) : '30d';

// Zpracování manuální synchronizace
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['manual_sync'])) {
    if (verifyCSRFToken($_POST['csrf_token'])) {
        if (!isset($_SESSION['last_sync']) || (time() - $_SESSION['last_sync'] > 300)) {
            $syncResult = syncVapiData();
            if ($syncResult === true) {
                $_SESSION['last_sync'] = time();
                $success = "Data byla úspěšně synchronizována.";
            } else {
                $error = "Nepodařilo se synchronizovat data: " . $syncResult;
            }
        } else {
            $error = "Data byla nedávno synchronizována. Prosím, počkejte před další synchronizací.";
        }
    } else {
        $error = "Neplatný CSRF token. Akce byla zrušena.";
    }
}

// Inicializace proměnných s výchozími hodnotami
$metrics = getDefaultMetrics();
$graphData = [
    'calls' => [],
    'dates' => [],
    'peakHours' => [],
    'hours' => [],
    'peakDates' => []
];
$topReasons = []; 

// Získání dat
try {
    // Modify the date range based on selection
    $dateRange = '30 DAY'; // default
    switch($timeRange) {
        case '1d':
            $dateRange = '1 DAY';
            break;
        case '6m':
            $dateRange = '6 MONTH';
            break;
        case '30d':
        default:
            $dateRange = '30 DAY';
            break;
    }
    
    $apiConfig = getCurrentUserApiKey();
    if ($apiConfig) {
        // Get data directly from session (populated by syncVapiData)
        $analytics = getAnalytics($dateRange, $apiConfig['assistant_id']);
        $peakHours = getPeakHours($dateRange, $apiConfig['assistant_id']);
        $metrics = getMetrics($analytics);

        if (!empty($analytics['data'])) {
            $graphData['calls'] = array_reverse(array_column($analytics['data'], 'total_calls'));
            $graphData['dates'] = array_reverse(array_column($analytics['data'], 'date'));
        }

        if (!empty($peakHours['data'])) {
            $graphData['peakHours'] = array_column($peakHours['data'], 'total_calls');
            $graphData['hours'] = array_column($peakHours['data'], 'hour');
            $graphData['peakDates'] = array_column($peakHours['data'], 'date');
        }

        $topReasons = getTopReasons($apiConfig['assistant_id']);
    } else {
        $error = 'Nepodařilo se získat konfiguraci API.';
    }
} catch (Exception $e) {
    $error = 'Došlo k chybě při načítání dat.';
    writeErrorLog('Dashboard Error', [
        'message' => $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ]);
}

// Get minutes used from metrics array as it's more accurate
$minutes_used = 0;
foreach ($metrics as $metric) {
    if ($metric['label'] === 'Spotřeba minut') {
        $minutes_used = intval($metric['value']);
        break;
    }
}

// Get subscription data directly from the database (but only for plan and limit)
$user_id = $_SESSION['user_id'];
$db = getDbConnection();
$stmt = $db->prepare("SELECT subscription_plan, minute_limit FROM users WHERE id = ?");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$userData = $result->fetch_assoc();
$stmt->close();

if ($userData) {
    $current_plan = $userData['subscription_plan'];
    $limit = $userData['minute_limit'];
    
    // Check if the plan is unlimited
    $is_unlimited = ($limit == -1);
} else {
    // Fallback values if query fails
    $current_plan = 'Základní';
    $limit = 500;
    $is_unlimited = false;
}

$currentPage = 'dashboard';
?>
<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width-device-width, initial-scale=1.0">
    <title>Dentibot Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>
    <script src="subscription_updater.js"></script>
    <style>
        /* Base styles */
        body {
            font-family: 'Inter', sans-serif;
        }
        
        /* Light theme */
        .light-theme {
            background-color: #f8fafc;
            color: #334155;
        }
        
        .light-theme .bg-gray-900 {
            background-color: #ffffff;
            border: 1px solid #e2e8f0;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .light-theme .metric-card {
            background: #ffffff;
            border: 1px solid #e2e8f0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .light-theme .stat-value {
            color: #0d9488;
            font-weight: 600;
        }

        .light-theme .stat-label {
            color: #475569;
        }

        .light-theme .chart-container {
            background: #ffffff;
            border: 1px solid #e2e8f0;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }

        .light-theme .time-range-button {
            background: #f1f5f9;
            color: #475569;
            border: 1px solid #e2e8f0;
        }

        .light-theme .time-range-button:hover {
            background: #e2e8f0;
            border-color: #cbd5e1;
        }

        .light-theme .time-range-button.active {
            background: #0d9488;
            color: white;
            border-color: #0d9488;
        }

        .light-theme .button-primary {
            background: #0d9488;
            color: white;
            border: 1px solid #0d9488;
        }

        .light-theme .button-primary:hover {
            background: #0f766e;
            border-color: #0f766e;
        }

        .light-theme .button-danger {
            background: #ef4444;
            color: white;
            border: 1px solid #ef4444;
        }

        .light-theme .button-danger:hover {
            background: #dc2626;
            border-color: #dc2626;
        }

        /* Dark theme */
        body:not(.light-theme) {
            background-color: #0f172a;
            color: #f1f5f9;
        }

        body:not(.light-theme) .bg-gray-900 {
            background-color: #1e293b;
            border: 1px solid #334155;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3);
        }

        body:not(.light-theme) .metric-card {
            background-color: #1e293b;
            border: 1px solid #334155;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3);
        }

        body:not(.light-theme) .stat-label {
            color: #cbd5e1;
        }

        body:not(.light-theme) .stat-value {
            color: #f8fafc;
        }

        body:not(.light-theme) .chart-container {
            background-color: #1e293b;
            border: 1px solid #334155;
        }

        body:not(.light-theme) .time-range-button {
            background-color: #334155;
            color: #cbd5e1;
            border: 1px solid #475569;
        }

        body:not(.light-theme) .time-range-button:hover {
            background-color: #475569;
            border-color: #64748b;
        }

        body:not(.light-theme) .time-range-button.active {
            background-color: #0d9488;
            color: #f8fafc;
            border-color: #0d9488;
        }

        body:not(.light-theme) .button-primary {
            background: #0d9488;
            color: #f8fafc;
            border: 1px solid #0d9488;
        }

        body:not(.light-theme) .button-primary:hover {
            background: #0f766e;
            border-color: #0f766e;
        }

        body:not(.light-theme) .button-danger {
            background: #ef4444;
            color: #f8fafc;
            border: 1px solid #ef4444;
        }

        body:not(.light-theme) .button-danger:hover {
            background: #dc2626;
            border-color: #dc2626;
        }

        /* Common improvements */
        .chart-container {
            padding: 1.5rem;
            border-radius: 0.75rem;
        }

        .metric-card {
            border-radius: 0.75rem;
            transition: all 0.2s ease;
        }

        .metric-card:hover {
            transform: translateY(-2px);
        }

        .time-range-button {
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            transition: all 0.2s ease;
        }

        /* Progress bar improvements */
        .progress-bg {
            height: 0.5rem;
            border-radius: 0.25rem;
            overflow: hidden;
        }

        .light-theme .progress-bg {
            background: #e2e8f0;
        }

        .light-theme .progress-fill {
            background: #0d9488;
        }

        body:not(.light-theme) .progress-bg {
            background: #334155;
        }

        body:not(.light-theme) .progress-fill {
            background: #0d9488;
        }

        /* Button styles */
        .button {
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .button:disabled {
            opacity: 0.7;
            cursor: not-allowed;
        }

        /* Alert styles */
        .alert {
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
        }

        .alert-error {
            background-color: #fee2e2;
            border: 1px solid #ef4444;
            color: #b91c1c;
        }

        .alert-success {
            background-color: #dcfce7;
            border: 1px solid #22c55e;
            color: #15803d;
        }

        /* Dashboard title */
        .dashboard-title {
            font-size: 1.875rem;
            font-weight: 700;
            background: linear-gradient(90deg, #0d9488 0%, #14b8a6 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 1.5rem;
        }

        /* Popup styles */
        .modal-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 50;
            align-items: center;
            justify-content: center;
        }

        .modal-container {
            background-color: #1e293b;
            border-radius: 0.5rem;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            width: 90%;
            max-width: 1000px;
            max-height: 90vh;
            position: relative;
        }

        .modal-close {
            position: absolute;
            top: 1rem;
            right: 1rem;
            color: #9ca3af;
            cursor: pointer;
            z-index: 51;
        }

        .modal-close:hover {
            color: #f9fafb;
        }

        .modal-iframe {
            width: 100%;
            height: 80vh;
            border: none;
            border-radius: 0.5rem;
        }

        @media (max-width: 640px) {
            .modal-container {
                width: 95%;
                margin: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="flex h-screen">
        <?php include 'sidebar.php'; ?>
        <div class="flex-1 overflow-y-auto">
            <div class="p-6">
                <div class="flex justify-between items-center mb-6">
                    <h1 class="dashboard-title">Přehledy</h1>
                    <div class="flex gap-2">
                        <form method="POST">
                            <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                            <button type="submit" name="manual_sync" 
                                    class="button button-primary"
                                    <?php echo (isset($_SESSION['last_sync']) && (time() - $_SESSION['last_sync'] <= 300)) ? 'disabled' : ''; ?>>
                                <?php
                                if (isset($_SESSION['last_sync']) && (time() - $_SESSION['last_sync'] <= 300)) {
                                    $remainingTime = 300 - (time() - $_SESSION['last_sync']);
                                    echo "Synchronizovat (" . htmlspecialchars($remainingTime) . " s)";
                                } else {
                                    echo "Synchronizovat data";
                                }
                                ?>
                            </button>
                        </form>
                        <button id="themeToggle" class="button button-primary">
                            Přepnout motiv
                        </button>
                        <form method="POST" class="inline-block">
                            <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                            <button type="submit" name="logout" class="button button-danger">
                                Odhlásit se
                            </button>
                        </form>
                    </div>
                </div>

                <?php if ($error): ?>
                <div class="alert alert-error">
                    <?php echo htmlspecialchars($error, ENT_QUOTES, 'UTF-8'); ?>
                </div>
                <?php endif; ?>

                <?php if ($success): ?>
                <div class="alert alert-success">
                    <?php echo htmlspecialchars($success, ENT_QUOTES, 'UTF-8'); ?>
                </div>
                <?php endif; ?>

                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                    <?php foreach ($metrics as $metric): ?>
                    <div class="metric-card p-6">
                        <h2 class="stat-label text-sm"><?php echo htmlspecialchars($metric['label'], ENT_QUOTES, 'UTF-8'); ?></h2>
                        <p class="stat-value text-2xl mt-2"><?php echo htmlspecialchars($metric['value'], ENT_QUOTES, 'UTF-8'); ?></p>
                        <div class="flex justify-between items-center mt-2">
                            <span class="stat-label text-sm"><?php echo htmlspecialchars($metric['subValue'], ENT_QUOTES, 'UTF-8'); ?></span>
                            <span class="text-teal-600 text-sm"><?php echo htmlspecialchars($metric['trend'], ENT_QUOTES, 'UTF-8'); ?></span>
                        </div>
                    </div>
                    <?php endforeach; ?>

                    <!-- Subscription card -->
                    <div class="metric-card p-6" id="subscription-card">
                        <div class="flex justify-between items-start">
                            <div>
                                <h2 class="stat-label text-sm">Aktuální předplatné</h2>
                                <p class="stat-value text-2xl mt-2" id="current-plan"><?php echo htmlspecialchars(ucfirst($current_plan), ENT_QUOTES, 'UTF-8'); ?></p>
                            </div>
                            <button id="openSubscriptionPopup" class="button button-primary text-sm">
                                Změnit plán
                            </button>
                        </div>
                        <div class="mt-4">
                            <div class="flex justify-between items-center text-sm">
                                <span class="stat-label">Využito minut:</span>
                                <span class="stat-value" id="minutes-usage">
                                    <?php echo $minutes_used; ?> 
                                    <?php if ($is_unlimited): ?>
                                        / ∞
                                    <?php else: ?>
                                        / <?php echo $limit; ?>
                                    <?php endif; ?>
                                </span>
                            </div>
                            <div class="progress-bg mt-2" id="progress-container">
                                <div class="progress-fill h-full" id="minutes-progress-bar"
                                     style="width: <?php echo $is_unlimited ? min(($minutes_used / 1000) * 100, 100) : ($limit > 0 ? min(($minutes_used / $limit) * 100, 100) : 0); ?>%">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                    <div class="bg-gray-900 p-6 rounded-lg shadow-lg">
                        <div class="flex flex-col gap-4">
                            <h2 class="text-2xl font-bold gradient-text">Počet hovorů</h2>
                            <div class="flex gap-2">
                                <a href="?timeRange=1d" class="time-range-button <?php echo $timeRange === '1d' ? 'active' : ''; ?>">1 den</a>
                                <a href="?timeRange=30d" class="time-range-button <?php echo $timeRange === '30d' ? 'active' : ''; ?>">30 dní</a>
                                <a href="?timeRange=6m" class="time-range-button <?php echo $timeRange === '6m' ? 'active' : ''; ?>">6 měsíců</a>
                            </div>
                        </div>
                        <div class="chart-container mt-4">
                            <canvas id="callsChart"></canvas>
                        </div>
                    </div>
                    <div class="bg-gray-900 p-6 rounded-lg shadow-lg">
                        <h2 class="text-2xl font-bold gradient-text mb-4">Špičky v provozu</h2>
                        <div class="chart-container">
                            <canvas id="peakHoursChart"></canvas>
                        </div>
                    </div>
                </div>

                <div class="bg-gray-900 p-6 rounded-lg shadow-lg mb-6">
                    <h2 class="text-2xl font-bold gradient-text mb-4">Nejčastější ukončení hovoru</h2>
                    <ul class="list-disc pl-5">
                        <?php foreach ($topReasons as $reason => $count): ?>
                        <li><?php echo htmlspecialchars($reason, ENT_QUOTES, 'UTF-8') . ': ' . $count; ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Subscription Popup -->
    <div id="subscriptionPopupOverlay" class="modal-overlay">
        <div class="modal-container">
            <button id="closeSubscriptionPopup" class="modal-close">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
            <iframe id="subscriptionIframe" class="modal-iframe" src="about:blank"></iframe>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const isLightTheme = document.body.classList.contains('light-theme');

            const commonOptions = {
                responsive: true,
                maintainAspectRatio: false,
                plugins: { 
                    legend: { display: false }
                },
                scales: { 
                    y: { 
                        beginAtZero: true,
                        grid: {
                            color: isLightTheme ? 'rgba(226, 232, 240, 0.5)' : 'rgba(51, 65, 85, 0.5)',
                        },
                        ticks: { 
                            color: isLightTheme ? '#475569' : '#cbd5e1',
                            stepSize: 5,
                            maxTicksLimit: 5
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            color: isLightTheme ? '#475569' : '#cbd5e1'
                        }
                    }
                }
            };

            const formatDate = (dateStr) => {
                try {
                    const date = new Date(dateStr);
                    return new Intl.DateTimeFormat('cs-CZ', {
                        day: '2-digit',
                        month: '2-digit'
                    }).format(date);
                } catch (e) {
                    return '';
                }
            };

            const dates = <?php echo json_encode($graphData['dates']); ?>;
            const calls = <?php echo json_encode($graphData['calls']); ?>;
            
            if (dates && dates.length > 0) {
                new Chart(document.getElementById('callsChart'), {
                    type: 'line',
                    data: {
                        labels: dates.map(formatDate),
                        datasets: [{
                            label: 'Počet hovorů',
                            data: calls,
                            borderColor: '#0d9488',
                            backgroundColor: 'rgba(13, 148, 136, 0.1)',
                            tension: 0.1,
                            fill: true
                        }]
                    },
                    options: {
                        ...commonOptions,
                        elements: { 
                            point: { radius: 0 },
                            line: { borderWidth: 2 }
                        }
                    }
                });
            }

            const hours = <?php echo json_encode($graphData['hours']); ?>;
            const peakDates = <?php echo json_encode($graphData['peakDates']); ?>;
            const peakCounts = <?php echo json_encode($graphData['peakHours']); ?>;
            
            if (hours && hours.length > 0) {
                new Chart(document.getElementById('peakHoursChart'), {
                    type: 'bar',
                    data: {
                        labels: hours.map((hour, index) => {
                            const date = peakDates[index];
                            let formattedDate = '';
                            try {
                                if (date) {
                                    const dateObj = new Date(date.replace(' ', 'T'));
                                    formattedDate = new Intl.DateTimeFormat('cs-CZ', {
                                        day: '2-digit',
                                        month: '2-digit'
                                    }).format(dateObj);
                                }
                            } catch (e) {
                                formattedDate = '';
                            }
                            return `${String(hour).padStart(2, '0')}:00${formattedDate ? ` (${formattedDate})` : ''}`;
                        }),
                        datasets: [{
                            label: 'Počet hovorů',
                            data: peakCounts,
                            backgroundColor: 'rgba(13, 148, 136, 0.5)',
                            borderColor: '#0d9488',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        ...commonOptions,
                        scales: {
                            x: {
                                ticks: {
                                    maxRotation: 45,
                                    minRotation: 45
                                }
                            },
                            y: commonOptions.scales.y
                        }
                    }
                });
            }

            // Theme toggle functionality
            const themeToggle = document.getElementById('themeToggle');
            
            function updateChartTheme() {
                const isLightTheme = document.body.classList.contains('light-theme');
                Chart.helpers.each(Chart.instances, function(instance) {
                    instance.options.scales.y.grid.color = isLightTheme ? 'rgba(226, 232, 240, 0.5)' : 'rgba(51, 65, 85, 0.5)';
                    instance.options.scales.y.ticks.color = isLightTheme ? '#475569' : '#cbd5e1';
                    instance.options.scales.x.ticks.color = isLightTheme ? '#475569' : '#cbd5e1';
                    instance.update();
                });
            }

            themeToggle.addEventListener('click', function() {
                document.body.classList.toggle('light-theme');
                const isDark = !document.body.classList.contains('light-theme');
                localStorage.setItem('theme', isDark ? 'dark' : 'light');
                updateChartTheme();
                
                // Dispatch event for sidebar
                window.dispatchEvent(new CustomEvent('themeChanged', { 
                    detail: { isDark: isDark }
                }));
            });

            // Initialize theme
            const savedTheme = localStorage.getItem('theme');
            if (savedTheme === 'light') {
                document.body.classList.add('light-theme');
            }
            updateChartTheme();

            // Subscription popup functionality
            const openButton = document.getElementById('openSubscriptionPopup');
            const overlay = document.getElementById('subscriptionPopupOverlay');
            const closeButton = document.getElementById('closeSubscriptionPopup');
            const iframe = document.getElementById('subscriptionIframe');
            
            function openSubscriptionPopup() {
                iframe.src = 'subscription.php';
                overlay.style.display = 'flex';
                document.body.style.overflow = 'hidden';
            }
            
            function closeSubscriptionPopup() {
                overlay.style.display = 'none';
                document.body.style.overflow = '';
                iframe.src = 'about:blank';
            }
            
            openButton.addEventListener('click', openSubscriptionPopup);
            closeButton.addEventListener('click', closeSubscriptionPopup);
            
            // Close when clicking outside the popup
            overlay.addEventListener('click', function(e) {
                if (e.target === overlay) {
                    closeSubscriptionPopup();
                }
            });
            
            // Listen for messages from the iframe
            window.addEventListener('message', function(event) {
                if (event.data.type === 'closeSubscriptionPopup') {
                    closeSubscriptionPopup();
                } else if (event.data.type === 'planSelected') {
                    console.log('Plan selected:', event.data.plan, event.data.interval);
                    closeSubscriptionPopup();
                } else if (event.data.type === 'updateDashboard') {
                    // Refresh the subscription card with new data
                    updateSubscriptionCard(event.data.data);
                }
            });
            
            // Function to update subscription card with new data
            function updateSubscriptionCard(data) {
                if (!data) return;
                
                const planElement = document.getElementById('current-plan');
                const usageElement = document.getElementById('minutes-usage');
                const progressBar = document.getElementById('minutes-progress-bar');
                
                if (planElement && data.plan) {
                    planElement.textContent = data.plan.charAt(0).toUpperCase() + data.plan.slice(1);
                }
                
                if (usageElement) {
                    if (data.isUnlimited) {
                        usageElement.textContent = `${data.minutesUsed} / ∞`;
                    } else {
                        usageElement.textContent = `${data.minutesUsed} / ${data.limit}`;
                    }
                }
                
                if (progressBar) {
                    if (data.isUnlimited) {
                        // For unlimited plans, show progress relative to a large number
                        const percentage = Math.min((data.minutesUsed / 1000) * 100, 100);
                        progressBar.style.width = `${percentage}%`;
                    } else {
                        // For limited plans, show actual percentage
                        const percentage = data.limit > 0 ? Math.min((data.minutesUsed / data.limit) * 100, 100) : 0;
                        progressBar.style.width = `${percentage}%`;
                    }
                }
            }
            
            // Check for subscription updates every minute
            setInterval(function() {
                fetch('get_subscription_data.php')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            updateSubscriptionCard({
                                plan: data.plan,
                                minutesUsed: data.minutesUsed,
                                limit: data.limit,
                                isUnlimited: data.isUnlimited
                            });
                        }
                    })
                    .catch(error => console.error('Error fetching subscription data:', error));
            }, 60000); // Check every minute
        });
    </script>
<!-- Message Widget -->
<link rel="stylesheet" href="/assets/css/message-widget.css">
<script src="/assets/js/message-widget.js"></script>
<script>
    // Inicializace widgetu s ID přihlášeného uživatele
    const messageWidget = new MessageWidget({
        apiEndpoint: '/api/messages.php',
        refreshInterval: 30000,
        userId: <?php echo isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 'null'; ?>
    });
</script>
    </body>
</html>


const ChatWindowComponent = (() => {
    function ChatWindow() {
        const [message, setMessage] = React.useState('');
        const { selectedConversation, sendMessage } = window.OmnichannelContext.useOmnichannel();

        const handleSend = () => {
            if (!message.trim()) return;
            sendMessage(message);
            setMessage('');
        };

        if (!selectedConversation) {
            return React.createElement(
                'div',
                { className: "flex-1 flex items-center justify-center bg-gray-50" },
                React.createElement(
                    'p',
                    { className: "text-gray-500" },
                    "Vyberte konverzaci pro zobrazení zpráv"
                )
            );
        }

        return React.createElement(
            'div',
            { className: "flex-1 flex flex-col" },
            React.createElement(
                'div',
                { className: "h-16 bg-white border-b flex items-center justify-between px-6" },
                React.createElement(
                    'div',
                    { className: "flex items-center gap-2" },
                    React.createElement('span', { className: "font-medium" }, selectedConversation.patientName),
                    React.createElement('span', { className: "text-sm bg-gray-200 px-2 py-1 rounded-full" }, selectedConversation.channel)
                )
            ),
            React.createElement(
                'div',
                { className: "flex-1 overflow-y-auto p-6 bg-gray-50" },
                selectedConversation.messages.map((msg) =>
                    React.createElement(
                        'div',
                        {
                            key: msg.id,
                            className: `flex ${msg.sender === 'patient' ? 'justify-end' : 'justify-start'} mb-4`
                        },
                        React.createElement(
                            'div',
                            {
                                className: `max-w-xl rounded-lg p-3 shadow ${
                                    msg.sender === 'patient' ? 'bg-indigo-100' : 'bg-white'
                                }`
                            },
                            React.createElement('div', { className: "text-sm" }, msg.text),
                            React.createElement(
                                'div',
                                { className: "text-xs text-gray-500 mt-1" },
                                `${new Date(msg.timestamp).toLocaleTimeString()} · ${msg.channel}`
                            )
                        )
                    )
                )
            ),
            React.createElement(
                'div',
                { className: "bg-white border-t p-4" },
                React.createElement(
                    'div',
                    { className: "flex items-center gap-4" },
                    React.createElement(
                        'select',
                        {
                            className: "border rounded-lg px-3 py-2 focus:outline-none focus:border-indigo-500",
                            defaultValue: selectedConversation.channel
                        },
                        React.createElement('option', { value: "email" }, "Email"),
                        React.createElement('option', { value: "sms" }, "SMS"),
                        React.createElement('option', { value: "whatsapp" }, "WhatsApp"),
                        React.createElement('option', { value: "messenger" }, "Messenger")
                    ),
                    React.createElement(
                        'div',
                        { className: "flex-1 relative" },
                        React.createElement('input', {
                            type: "text",
                            value: message,
                            onChange: (e) => setMessage(e.target.value),
                            placeholder: "Napište zprávu...",
                            className: "w-full px-4 py-2 border rounded-lg focus:outline-none focus:border-indigo-500",
                            onKeyPress: (e) => e.key === 'Enter' && handleSend()
                        })
                    ),
                    React.createElement(
                        'button',
                        {
                            onClick: handleSend,
                            className: "bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 flex items-center gap-2"
                        },
                        "Odeslat"
                    )
                )
            )
        );
    }

    return { ChatWindow };
})();

window.ChatWindowComponent = ChatWindowComponent;
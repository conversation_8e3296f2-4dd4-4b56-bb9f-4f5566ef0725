<?php
require_once 'config.php';
require_once 'db_connection.php';

// Kontrola přihlášení a oprávnění
if (!isLoggedIn()) {
    header('Location: index.php');
    exit;
}

if (!isAdmin()) {
    header('Location: dashboard.php?error=unauthorized');
    exit;
}

$currentPage = 'error_log_view';

// Získání parametrů pro filtrování a řazení
$search = isset($_GET['search']) ? sanitizeInput($_GET['search']) : '';
$sortBy = isset($_GET['sort']) ? sanitizeInput($_GET['sort']) : 'timestamp';
$sortOrder = isset($_GET['order']) && $_GET['order'] === 'asc' ? 'ASC' : 'DESC';
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$perPage = 50; // Počet záznamů na stránku

// Připojení k databázi
$db = getDbConnection();
if (!$db || !$db->ping()) {
    die("Nepodařilo se připojit k databázi. Prosím zkuste to později.");
}

// Sestavení SQL dotazu
$sql = "SELECT SQL_CALC_FOUND_ROWS * FROM error_logs WHERE 1=1";
if (!empty($search)) {
    $sql .= " AND (message LIKE ? OR context LIKE ?)";
}
$sql .= " ORDER BY timestamp DESC LIMIT ? OFFSET ?";

// Příprava a provedení dotazu
$stmt = $db->prepare($sql);
$offset = ($page - 1) * $perPage;
if (!empty($search)) {
    $searchParam = "%$search%";
    $stmt->bind_param("ssii", $searchParam, $searchParam, $perPage, $offset);
} else {
    $stmt->bind_param("ii", $perPage, $offset);
}
$stmt->execute();
$result = $stmt->get_result();
$errors = $result->fetch_all(MYSQLI_ASSOC);

// Získání celkového počtu záznamů pro stránkování
$countSql = "SELECT FOUND_ROWS() as total";
$countStmt = $db->prepare($countSql);
$countStmt->execute();
$totalRecords = $countStmt->get_result()->fetch_assoc()['total'];
$totalPages = ceil($totalRecords / $perPage);


// Funkce pro generování URL pro řazení
function sortUrl($field) {
    global $sortBy, $sortOrder, $search;
    $newOrder = ($sortBy === $field && $sortOrder === 'DESC') ? 'asc' : 'desc';
    return "?sort=$field&order=$newOrder" . (!empty($search) ? "&search=$search" : "");
}

// Funkce pro generování URL pro stránkování
function pageUrl($page) {
    global $sortBy, $sortOrder, $search;
    return "?page=$page&sort=$sortBy&order=$sortOrder" . (!empty($search) ? "&search=$search" : "");
}
?>

<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Error Log - Dentibot</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
</head>
<body class="bg-gray-100">
    <div class="flex h-screen">
        <?php include 'sidebar.php'; ?>
        <div class="flex-1 overflow-auto">
            <div class="p-8">
                <h1 class="text-2xl font-bold text-indigo-600 mb-6">Error Log</h1>

                <!-- Vyhledávací formulář -->
                <form method="GET" class="mb-4">
                    <div class="flex items-center">
                        <input type="text" name="search" value="<?php echo htmlspecialchars($search); ?>" 
                               placeholder="Hledat v chybových záznamech..." 
                               class="flex-grow p-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-indigo-500">
                        <button type="submit" class="bg-indigo-500 text-white p-2 rounded-r-md hover:bg-indigo-600 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                            Hledat
                        </button>
                    </div>
                </form>

                <!-- Tabulka s chybovými záznamy -->
                <div class="bg-white shadow-md rounded-lg overflow-hidden">
                    <table class="min-w-full divide-y divide-gray-200">
                        <?php
                        if (empty($errors)) {
                            echo '<tr><td colspan="3" class="px-6 py-4 text-center text-gray-500">
                                Počet záznamů: ' . $totalRecords . '<br>
                                SQL Query: ' . htmlspecialchars($sql) . '<br>
                                Search term: ' . htmlspecialchars($search) . '
                            </td></tr>';
                        }
                        ?>
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <a href="<?php echo sortUrl('timestamp'); ?>" class="hover:text-indigo-500">
                                        Čas
                                        <?php if ($sortBy === 'timestamp'): ?>
                                            <span class="ml-1"><?php echo $sortOrder === 'DESC' ? '▼' : '▲'; ?></span>
                                        <?php endif; ?>
                                    </a>
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <a href="<?php echo sortUrl('message'); ?>" class="hover:text-indigo-500">
                                        Zpráva
                                        <?php if ($sortBy === 'message'): ?>
                                            <span class="ml-1"><?php echo $sortOrder === 'DESC' ? '▼' : '▲'; ?></span>
                                        <?php endif; ?>
                                    </a>
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Kontext
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php foreach ($errors as $error): ?>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <?php echo htmlspecialchars($error['timestamp']); ?>
                                </td>
                                <td class="px-6 py-4 text-sm text-gray-900">
                                    <?php echo htmlspecialchars($error['message']); ?>
                                </td>
                                <td class="px-6 py-4 text-sm text-gray-500">
                                    <?php 
                                        $contextData = json_decode($error['context'], true);
                                        if (json_last_error() === JSON_ERROR_NONE && is_array($contextData)) {
                                            echo '<pre class="whitespace-pre-wrap">' . htmlspecialchars(print_r($contextData, true)) . '</pre>';
                                        } else {
                                            echo htmlspecialchars($error['context']);
                                        }
                                    ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Stránkování -->
                <?php if ($totalPages > 1): ?>
                <div class="mt-4 flex justify-center">
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                        <?php if ($page > 1): ?>
                        <a href="<?php echo pageUrl($page - 1); ?>" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            Předchozí
                        </a>
                        <?php endif; ?>
                        
                        <?php
                        $start = max(1, $page - 2);
                        $end = min($totalPages, $start + 4);
                        for ($i = $start; $i <= $end; $i++):
                        ?>
                        <a href="<?php echo pageUrl($i); ?>" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium <?php echo $i === $page ? 'text-indigo-600 bg-indigo-50' : 'text-gray-700 hover:bg-gray-50'; ?>">
                            <?php echo $i; ?>
                        </a>
                        <?php endfor; ?>
                        
                        <?php if ($page < $totalPages): ?>
                        <a href="<?php echo pageUrl($page + 1); ?>" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            Další
                        </a>
                        <?php endif; ?>
                    </nav>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</body>
</html>
<?php
// Close the database connection at the very end
$db->close();
?>






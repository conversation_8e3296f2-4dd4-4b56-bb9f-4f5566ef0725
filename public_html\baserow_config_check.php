<?php
require_once 'config.php';
require_once 'error_log.php';

// Enable error reporting for debugging
ini_set('display_errors', 1);
error_reporting(E_ALL);

echo "<h1>Baserow Configuration Fix</h1>";

try {
    global $mysqli;
    
    if (!$mysqli) {
        echo "<p style='color:red;'>Error: Database connection not available</p>";
        exit;
    }
    
    // Check if any users have Baserow configuration
    $query = "SELECT id, username, email, baserow_api_token, baserow_database_id, baserow_table_id FROM users WHERE baserow_api_token IS NOT NULL OR baserow_database_id IS NOT NULL OR baserow_table_id IS NOT NULL";
    $result = $mysqli->query($query);
    
    if ($result->num_rows === 0) {
        echo "<p style='color:orange;'>No users found with Baserow configuration</p>";
        echo "<p>We need to add Baserow configuration to at least one user.</p>";
    } else {
        echo "<h2>Users with Baserow Configuration:</h2>";
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>ID</th><th>Username</th><th>Email</th><th>API Token</th><th>Database ID</th><th>Table ID</th></tr>";
        
        while ($user = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $user['id'] . "</td>";
            echo "<td>" . ($user['username'] ?? 'N/A') . "</td>";
            echo "<td>" . ($user['email'] ?? 'N/A') . "</td>";
            echo "<td>" . (!empty($user['baserow_api_token']) ? "✅ Present" : "❌ Missing") . "</td>";
            echo "<td>" . (!empty($user['baserow_database_id']) ? $user['baserow_database_id'] : "❌ Missing") . "</td>";
            echo "<td>" . (!empty($user['baserow_table_id']) ? $user['baserow_table_id'] : "❌ Missing") . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    }
    
    // Get the current user ID
    $currentUserId = $_SESSION['user_id'] ?? 0;
    echo "<p>Current logged-in user ID: " . $currentUserId . "</p>";
    
    // Check if the current user has Baserow configuration
    if ($currentUserId > 0) {
        $stmt = $mysqli->prepare("SELECT baserow_api_token, baserow_database_id, baserow_table_id FROM users WHERE id = ?");
        $stmt->bind_param("i", $currentUserId);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows === 0) {
            echo "<p style='color:red;'>Error: User with ID $currentUserId not found</p>";
        } else {
            $user = $result->fetch_assoc();
            $hasApiToken = !empty($user['baserow_api_token']);
            $hasDatabaseId = !empty($user['baserow_database_id']);
            $hasTableId = !empty($user['baserow_table_id']);
            
            echo "<h2>Current User's Baserow Configuration:</h2>";
            echo "<ul>";
            echo "<li>API Token: " . ($hasApiToken ? "✅ Present" : "❌ Missing") . "</li>";
            echo "<li>Database ID: " . ($hasDatabaseId ? $user['baserow_database_id'] : "❌ Missing") . "</li>";
            echo "<li>Table ID: " . ($hasTableId ? $user['baserow_table_id'] : "❌ Missing") . "</li>";
            echo "</ul>";
            
            if (!$hasApiToken || !$hasDatabaseId || !$hasTableId) {
                echo "<p style='color:orange;'>The current user is missing some Baserow configuration values</p>";
            } else {
                echo "<p style='color:green;'>The current user has all required Baserow configuration values</p>";
            }
        }
    }
    
    // Check the getBaserowConfig function in baserow_functions.php
    echo "<h2>Fixing getBaserowConfig Function:</h2>";
    
    if (file_exists('baserow_functions.php')) {
        $baserowFunctions = file_get_contents('baserow_functions.php');
        
        // Look for getBaserowConfig function
        if (preg_match('/function\s+getBaserowConfig\s*$$\s*$$\s*\{(.+?)\}/s', $baserowFunctions, $matches)) {
            echo "<p>Found getBaserowConfig function in baserow_functions.php</p>";
            
            // Create a fixed version of the function
            $fixedFunction = "function getBaserowConfig() {
   global \$mysqli;
   
   if (!isset(\$_SESSION['user_id'])) {
       throw new Exception(\"Uživatel není přihlášen\");
   }
   
   \$stmt = \$mysqli->prepare(\"SELECT baserow_api_token, baserow_database_id, baserow_table_id FROM users WHERE id = ?\");
   \$stmt->bind_param(\"i\", \$_SESSION['user_id']);
   \$stmt->execute();
   \$result = \$stmt->get_result();
   \$config = \$result->fetch_assoc();
   
   if (!isset(\$config['baserow_api_token']) || empty(\$config['baserow_api_token']) || 
       !isset(\$config['baserow_database_id']) || empty(\$config['baserow_database_id']) || 
       !isset(\$config['baserow_table_id']) || empty(\$config['baserow_table_id'])) {
       throw new Exception(\"Chybí konfigurace Baserow. Prosím, nastavte API token, Database ID a Table ID v nastavení.\");
   }
   
   writeErrorLog('Baserow Config Retrieved', [
       'database_id' => \$config['baserow_database_id'],
       'table_id' => \$config['baserow_table_id'],
       'api_token_length' => strlen(\$config['baserow_api_token'])
   ]);
   
   return \$config;
}";
            
            // Create a backup of the original file
            copy('baserow_functions.php', 'baserow_functions.php.bak');
            
            // Replace the function in the file
            $updatedContent = preg_replace('/function\s+getBaserowConfig\s*$$\s*$$\s*\{(.+?)\}/s', $fixedFunction, $baserowFunctions);
            file_put_contents('baserow_functions.php', $updatedContent);
            
            echo "<p style='color:green;'>Updated getBaserowConfig function in baserow_functions.php</p>";
            echo "<p>A backup of the original file was created as baserow_functions.php.bak</p>";
        } else {
            echo "<p style='color:orange;'>Could not find getBaserowConfig function in baserow_functions.php</p>";
            
            // Create a new function at the beginning of the file
            $fixedFunction = "
function getBaserowConfig() {
   global \$mysqli;
   
   if (!isset(\$_SESSION['user_id'])) {
       throw new Exception(\"Uživatel není přihlášen\");
   }
   
   \$stmt = \$mysqli->prepare(\"SELECT baserow_api_token, baserow_database_id, baserow_table_id FROM users WHERE id = ?\");
   \$stmt->bind_param(\"i\", \$_SESSION['user_id']);
   \$stmt->execute();
   \$result = \$stmt->get_result();
   \$config = \$result->fetch_assoc();
   
   if (!isset(\$config['baserow_api_token']) || empty(\$config['baserow_api_token']) || 
       !isset(\$config['baserow_database_id']) || empty(\$config['baserow_database_id']) || 
       !isset(\$config['baserow_table_id']) || empty(\$config['baserow_table_id'])) {
       throw new Exception(\"Chybí konfigurace Baserow. Prosím, nastavte API token, Database ID a Table ID v nastavení.\");
   }
   
   writeErrorLog('Baserow Config Retrieved', [
       'database_id' => \$config['baserow_database_id'],
       'table_id' => \$config['baserow_table_id'],
       'api_token_length' => strlen(\$config['baserow_api_token'])
   ]);
   
   return \$config;
}
";
            
            // Create a backup of the original file
            copy('baserow_functions.php', 'baserow_functions.php.bak');
            
            // Add the function to the beginning of the file
            $updatedContent = "<?php\n" . $fixedFunction . substr($baserowFunctions, 5);
            file_put_contents('baserow_functions.php', $updatedContent);
            
            echo "<p style='color:green;'>Added getBaserowConfig function to baserow_functions.php</p>";
            echo "<p>A backup of the original file was created as baserow_functions.php.bak</p>";
        }
    } else {
        echo "<p style='color:red;'>baserow_functions.php file not found</p>";
    }
    
    // Fix the update_patient_field function in patients.php
    echo "<h2>Fixing update_patient_field Function:</h2>";
    
    if (file_exists('patients.php')) {
        $patientsFile = file_get_contents('patients.php');
        
        // Look for update_patient_field case
        if (preg_match('/case\s+\'update_patient_field\':(.*?)break;/s', $patientsFile, $matches)) {
            echo "<p>Found update_patient_field case in patients.php</p>";
            
            // Create a fixed version of the case
            $fixedCase = "case 'update_patient_field':
    // Enhanced logging for debugging
    error_log(\"Update patient field request method: \" . \$_SERVER['REQUEST_METHOD']);
    error_log(\"Content-Type: \" . (\$_SERVER['CONTENT_TYPE'] ?? 'Not set'));
    
    // Get patient ID from multiple possible sources
    \$patientId = 0;
    
    // Try GET parameters first (most reliable)
    if (isset(\$_GET['patient_id']) && !empty(\$_GET['patient_id'])) {
        \$patientId = intval(\$_GET['patient_id']);
        error_log(\"Patient ID from GET['patient_id']: \" . \$patientId);
    }
    else if (isset(\$_GET['id']) && !empty(\$_GET['id'])) {
        \$patientId = intval(\$_GET['id']);
        error_log(\"Patient ID from GET['id']: \" . \$patientId);
    }
    // Then try POST data
    else if (isset(\$_POST['id']) && !empty(\$_POST['id'])) {
        \$patientId = intval(\$_POST['id']);
        error_log(\"Patient ID from POST['id']: \" . \$patientId);
    }
    // Finally try JSON data
    else {
        \$rawPostData = file_get_contents('php://input');
        \$jsonData = json_decode(\$rawPostData, true);
        if (isset(\$jsonData['id']) && !empty(\$jsonData['id'])) {
            \$patientId = intval(\$jsonData['id']);
            error_log(\"Patient ID from JSON data: \" . \$patientId);
        }
    }
    
    // Get field and value
    \$field = \$_GET['field'] ?? \$_POST['field'] ?? \$jsonData['field'] ?? '';
    \$value = \$_GET['value'] ?? \$_POST['value'] ?? \$jsonData['value'] ?? '';
    
    // Log the final extracted values
    error_log(\"Final extracted values: patientId={\$patientId}, field={\$field}, value={\$value}\");
    
    // Field mapping
    \$fieldMapping = [
        'examination_date' => 'Datum_prohlidky',
        'akutni' => 'Akutní',
        'brouseni' => 'Broušení',
        'endo' => 'Endo',
        'extrakce_chirurgie' => 'Extrakce, chirurgie',
        'postendo' => 'Postendo',
        'predni_protetiky' => 'Předání protetiky',
        'sanace_dite' => 'Sanace - dítě',
        'sanace_dospely' => 'Sanace - dospělý',
        'snimatelna_protetika' => 'Snímatelná protetika - otisky'
    ];
    
    try {
        // Validate patient ID - must be greater than 0
        if (\$patientId <= 0) {
            throw new Exception(\"Neplatné ID pacienta: \" . \$patientId);
        }
        
        if (!isset(\$fieldMapping[\$field])) {
            throw new Exception(\"Neplatné pole: \" . \$field);
        }
        
        \$baserowField = \$fieldMapping[\$field];
        
        // Special handling for examination date
        if (\$field === 'examination_date') {
            \$success = updateExaminationDateInBaserow(\$patientId, \$value);
            
            if (!\$success) {
                throw new Exception(\"Nepodařilo se aktualizovat datum prohlídky v Baserow\");
            }
            
            echo json_encode(['success' => true, 'message' => \"Datum prohlídky bylo úspěšně aktualizováno\"]);
        } else {
            // For other fields, use standard update
            \$updateData = [];
            \$updateData[\$baserowField] = \$value;
            
            error_log(\"Updating patient field: \" . json_encode([
                'patient_id' => \$patientId,
                'field' => \$field,
                'baserow_field' => \$baserowField,
                'value' => \$value,
                'update_data' => \$updateData
            ]));
            
            // Make the Baserow API request
            \$response = baserowRequest('PATCH', \$patientId . '/?user_field_names=true', \$updateData);
            
            if (!is_array(\$response)) {
                throw new Exception(\"Neočekávaná odpověď při aktualizaci pacienta v Baserow\");
            }
            
            echo json_encode(['success' => true, 'message' => \"Pole bylo úspěšně aktualizováno\"]);
        }
    } catch (Exception \$e) {
        error_log(\"Error updating patient field: \" . \$e->getMessage());
        echo json_encode(['success' => false, 'message' => \$e->getMessage()]);
    }
    break;";
            
            // Create a backup of the original file
            copy('patients.php', 'patients.php.bak');
            
            // Replace the case in the file
            $updatedContent = preg_replace('/case\s+\'update_patient_field\':(.*?)break;/s', $fixedCase, $patientsFile);
            file_put_contents('patients.php', $updatedContent);
            
            echo "<p style='color:green;'>Updated update_patient_field case in patients.php</p>";
            echo "<p>A backup of the original file was created as patients.php.bak</p>";
        } else {
            echo "<p style='color:orange;'>Could not find update_patient_field case in patients.php</p>";
        }
    } else {
        echo "<p style='color:red;'>patients.php file not found</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color:red;'>Error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>

<h2>Add Baserow Configuration</h2>

<p>Use the form below to add Baserow configuration to a user:</p>

<form method="post" action="update_baserow_config.php">
    <div style="margin-bottom: 10px;">
        <label for="user_id">User ID:</label>
        <input type="number" id="user_id" name="user_id" value="<?php echo $_SESSION['user_id'] ?? 1; ?>" required>
    </div>
    
    <div style="margin-bottom: 10px;">
        <label for="api_token">Baserow API Token:</label>
        <input type="text" id="api_token" name="api_token" required style="width: 300px;">
    </div>
    
    <div style="margin-bottom: 10px;">
        <label for="database_id">Baserow Database ID:</label>
        <input type="number" id="database_id" name="database_id" required>
    </div>
    
    <div style="margin-bottom: 10px;">
        <label for="table_id">Baserow Table ID:</label>
        <input type="number" id="table_id" name="table_id" required>
    </div>
    
    <button type="submit">Update Configuration</button>
</form>
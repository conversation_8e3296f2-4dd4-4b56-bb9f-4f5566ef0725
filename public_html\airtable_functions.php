<?php
require_once 'config.php';
require_once 'cache_helper.php';
require_once 'error_log.php';

function getDefaultMetrics() {
    return [
        [
            'label' => 'Spotřeba minut',
            'value' => '0',
            'subValue' => 'Celkem: 0 minut',
            'trend' => '+0%',
            'highlight' => false
        ],
        [
            'label' => 'Průměrná délka hovoru',
            'value' => '0:00',
            'subValue' => 'minut',
            'trend' => '+0%',
            'highlight' => false
        ],
        [
            'label' => 'Úspěšnost hovorů',
            'value' => '0%',
            'subValue' => 'Z celkových 0 hovorů',
            'trend' => '+0%',
            'highlight' => false
        ],
        [
            'label' => 'Počet hovorů',
            'value' => '0',
            'subValue' => 'Za posledních 30 dní',
            'trend' => '+0%',
            'highlight' => false
        ]
    ];
}

function getReasonTranslation($reason) {
    $translations = [
        'customer-ended-call' => 'Pacient ukončil hovor',
        'customer-busy' => 'Pacient byl nedostupný',
        'assistant-ended-call' => 'Dentibot ukončil hovor',
        'customer-did-not-give-microphone-permission' => 'Pacient nepovolil mikrofon',
        'twilio-failed-to-connect-call' => 'Chyba připojení hovoru',
        'silence-timed-out' => 'Pacient moc dlouho mlčel',
        'unknown' => 'Neznámý důvod',
        'assistant-forwarded-call' => 'Dentibot přepojil hovor do ordinace'
    ];
    
    return $translations[$reason] ?? $reason;
}

function getCallHistory($limit = 1000, $startDate = null) {
    try {
        $apiConfig = getCurrentUserApiKey();
        if (!$apiConfig) {
            throw new Exception('API configuration not found');
        }

        $url = 'https://api.vapi.ai/call/api/v1/calls';
        $params = [
            'limit' => $limit
        ];

        if ($startDate) {
            $params['from'] = date('c', strtotime($startDate));
        }

        $headers = [
            'Authorization: Bearer ' . $apiConfig['api_key'],
            'Content-Type: application/json',
            'Accept: application/json'
        ];

        $ch = curl_init($url . '?' . http_build_query($params));
        curl_setopt_array($ch, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPHEADER => $headers
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode !== 200) {
            throw new Exception("API returned status code: $httpCode");
        }

        $data = json_decode($response, true);
        if (!isset($data['data'])) {
            throw new Exception('Invalid API response format');
        }

        return $data['data'];
    } catch (Exception $e) {
        writeErrorLog('Call History API Error', [
            'message' => $e->getMessage(),
            'limit' => $limit,
            'startDate' => $startDate
        ]);
        return [];
    }
}

function getAnalytics($dateRange = '30 DAY', $assistantId = null) {
    try {
        $cache = new CacheHelper();
        $cacheKey = 'analytics';
        $params = [
            'date_range' => $dateRange,
            'assistant_id' => $assistantId
        ];

        $cachedData = $cache->get($cacheKey, $params);
        if ($cachedData !== null) {
            return $cachedData;
        }

        $startDate = date('Y-m-d', strtotime("-$dateRange"));
        $calls = getCallHistory(1000, $startDate);

        if (empty($calls)) {
            return ['data' => []];
        }

        $analytics = [];
        foreach ($calls as $call) {
            $date = substr($call['createdAt'], 0, 10);
            if (!isset($analytics[$date])) {
                $analytics[$date] = [
                    'date' => $date,
                    'total_calls' => 0,
                    'total_duration' => 0
                ];
            }
            $analytics[$date]['total_calls']++;
            $analytics[$date]['total_duration'] += isset($call['duration']) ? $call['duration'] : 0;
        }

        $result = ['data' => array_values($analytics)];
        $cache->set($cacheKey, $result, $params);

        return $result;
    } catch (Exception $e) {
        writeErrorLog('Analytics Error', [
            'message' => $e->getMessage(),
            'dateRange' => $dateRange
        ]);
        return ['data' => []];
    }
}

function getPeakHours($dateRange = '30 DAY', $assistantId = null) {
    try {
        $cache = new CacheHelper();
        $cacheKey = 'peak_hours';
        $params = [
            'date_range' => $dateRange,
            'assistant_id' => $assistantId
        ];

        $cachedData = $cache->get($cacheKey, $params);
        if ($cachedData !== null) {
            return $cachedData;
        }

        $startDate = date('Y-m-d', strtotime("-$dateRange"));
        $calls = getCallHistory(1000, $startDate);

        if (empty($calls)) {
            return ['data' => []];
        }

        $hourlyData = [];
        foreach ($calls as $call) {
            $datetime = new DateTime($call['createdAt']);
            $hour = $datetime->format('H');
            $date = $datetime->format('Y-m-d');
            
            $key = "$date-$hour";
            if (!isset($hourlyData[$key])) {
                $hourlyData[$key] = [
                    'date' => $date,
                    'hour' => (int)$hour,
                    'total_calls' => 0
                ];
            }
            $hourlyData[$key]['total_calls']++;
        }

        // Sort by number of calls
        usort($hourlyData, function($a, $b) {
            return $b['total_calls'] - $a['total_calls'];
        });

        $result = ['data' => array_slice($hourlyData, 0, 10)];
        $cache->set($cacheKey, $result, $params);

        return $result;
    } catch (Exception $e) {
        writeErrorLog('Peak Hours Error', [
            'message' => $e->getMessage(),
            'dateRange' => $dateRange
        ]);
        return ['data' => []];
    }
}

function getTopReasons($assistantId = null) {
    try {
        $cache = new CacheHelper();
        $cacheKey = 'top_reasons';
        $params = ['assistant_id' => $assistantId];

        $cachedData = $cache->get($cacheKey, $params);
        if ($cachedData !== null) {
            return $cachedData;
        }

        $calls = getCallHistory(1000);
        if (empty($calls)) {
            return [];
        }

        $reasons = [];
        foreach ($calls as $call) {
            if (!isset($call['endedReason'])) continue;
            
            $reason = getReasonTranslation($call['endedReason']);
            if (!isset($reasons[$reason])) {
                $reasons[$reason] = 0;
            }
            $reasons[$reason]++;
        }

        arsort($reasons);
        $cache->set($cacheKey, $reasons, $params);

        return $reasons;
    } catch (Exception $e) {
        writeErrorLog('Get Top Reasons Error', ['error' => $e->getMessage()]);
        return [];
    }
}

function getMetrics($analytics) {
    try {
        if (empty($analytics['data'])) {
            return getDefaultMetrics();
        }

        $totalCalls = 0;
        $totalDuration = 0;
        $successfulCalls = 0;

        foreach ($analytics['data'] as $day) {
            $totalCalls += $day['total_calls'];
            $totalDuration += $day['total_duration'];
        }

        // Get success rate from API data
        $calls = getCallHistory(1000);
        foreach ($calls as $call) {
            if (isset($call['analysis']['successEvaluation']) && 
                ($call['analysis']['successEvaluation'] === 'successful' || 
                 $call['analysis']['successEvaluation'] === 'partially-successful')) {
                $successfulCalls++;
            }
        }

        $avgDuration = $totalCalls > 0 ? floor($totalDuration / $totalCalls) : 0;
        $successRate = $totalCalls > 0 ? floor(($successfulCalls / $totalCalls) * 100) : 0;

        return [
            [
                'label' => 'Spotřeba minut',
                'value' => (string)ceil($totalDuration / 60),
                'subValue' => 'Celkem: ' . ceil($totalDuration / 60) . ' minut',
                'trend' => '+0%',
                'highlight' => false
            ],
            [
                'label' => 'Průměrná délka hovoru',
                'value' => sprintf('%d:%02d', floor($avgDuration / 60), $avgDuration % 60),
                'subValue' => 'minut',
                'trend' => '+0%',
                'highlight' => false
            ],
            [
                'label' => 'Úspěšnost hovorů',
                'value' => $successRate . '%',
                'subValue' => 'Z celkových ' . $totalCalls . ' hovorů',
                'trend' => '+0%',
                'highlight' => false
            ],
            [
                'label' => 'Počet hovorů',
                'value' => (string)$totalCalls,
                'subValue' => 'Za posledních 30 dní',
                'trend' => '+0%',
                'highlight' => false
            ]
        ];
    } catch (Exception $e) {
        writeErrorLog('Metrics Calculation Error', ['error' => $e->getMessage()]);
        return getDefaultMetrics();
    }
}
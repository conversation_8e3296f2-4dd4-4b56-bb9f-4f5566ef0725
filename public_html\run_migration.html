<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Spu<PERSON><PERSON><PERSON><PERSON><PERSON> migrace - Dentibot</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
    </style>
</head>
<body class="bg-gray-50">
    <div class="min-h-screen flex items-center justify-center">
        <div class="max-w-md w-full bg-white rounded-lg shadow-lg p-8">
            <div class="text-center mb-6">
                <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"/>
                    </svg>
                </div>
                <h1 class="text-2xl font-bold text-gray-900">Migrace databáze</h1>
                <p class="text-gray-600 mt-2">Vytvoření tabulek pro automatickou kartu pacienta</p>
            </div>

            <div class="space-y-4">
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h3 class="font-semibold text-blue-900 mb-2">Co migrace udělá:</h3>
                    <ul class="text-blue-800 text-sm space-y-1">
                        <li>✅ Vytvoří tabulky pro automatické karty</li>
                        <li>✅ Přidá kategorie dokumentů</li>
                        <li>✅ Vytvoří testovací pacienty</li>
                        <li>✅ Zachová existující data</li>
                    </ul>
                </div>

                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <h3 class="font-semibold text-yellow-800 mb-2">⚠️ Důležité:</h3>
                    <p class="text-yellow-700 text-sm">
                        Migrace je bezpečná a neovlivní existující data. 
                        Pouze přidá nové tabulky a funkce.
                    </p>
                </div>

                <button onclick="runMigration()" id="migrateBtn" 
                        class="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-lg font-medium transition-colors">
                    🚀 Spustit migraci
                </button>

                <div id="migrationResult" class="hidden">
                    <div class="bg-gray-100 rounded-lg p-4">
                        <h3 class="font-semibold text-gray-900 mb-2">Výsledek migrace:</h3>
                        <div id="migrationOutput" class="text-sm text-gray-700 font-mono whitespace-pre-wrap"></div>
                    </div>
                </div>

                <div class="text-center">
                    <a href="main_dashboard.php" class="text-blue-600 hover:text-blue-800 text-sm">
                        ← Zpět na dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script>
        async function runMigration() {
            const btn = document.getElementById('migrateBtn');
            const result = document.getElementById('migrationResult');
            const output = document.getElementById('migrationOutput');

            btn.disabled = true;
            btn.innerHTML = '⏳ Spouštím migraci...';

            try {
                const response = await fetch('migrate_patient_cards.php');
                const text = await response.text();
                
                output.innerHTML = text;
                result.classList.remove('hidden');
                
                if (response.ok) {
                    btn.innerHTML = '✅ Migrace dokončena';
                    btn.classList.remove('bg-blue-600', 'hover:bg-blue-700');
                    btn.classList.add('bg-green-600', 'hover:bg-green-700');
                    
                    setTimeout(() => {
                        if (confirm('Migrace byla úspěšná! Chcete přejít na automatické karty pacientů?')) {
                            window.location.href = 'patient_cards.php';
                        }
                    }, 2000);
                } else {
                    btn.innerHTML = '❌ Chyba při migraci';
                    btn.classList.remove('bg-blue-600', 'hover:bg-blue-700');
                    btn.classList.add('bg-red-600', 'hover:bg-red-700');
                }
            } catch (error) {
                output.innerHTML = 'Chyba: ' + error.message;
                result.classList.remove('hidden');
                
                btn.innerHTML = '❌ Chyba při migraci';
                btn.classList.remove('bg-blue-600', 'hover:bg-blue-700');
                btn.classList.add('bg-red-600', 'hover:bg-red-700');
            }
            
            btn.disabled = false;
        }
    </script>
</body>
</html>

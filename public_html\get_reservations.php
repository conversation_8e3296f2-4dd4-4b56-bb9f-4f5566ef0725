<?php
require_once 'config.php';
require_once 'functions.php';

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

header('Content-Type: application/json');

if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

$date = isset($_GET['date']) ? $_GET['date'] : date('Y-m-d');

try {
    error_log("Attempting to get reservations for date: " . $date);
    $reservations = getReservations($date);
    error_log("Successfully retrieved reservations: " . json_encode($reservations));
    echo json_encode($reservations);
} catch (Exception $e) {
    error_log("Error in get_reservations.php: " . $e->getMessage() . "\nStack trace: " . $e->getTraceAsString());
    http_response_code(500);
    echo json_encode(['error' => 'Internal Server Error: ' . $e->getMessage()]);
}



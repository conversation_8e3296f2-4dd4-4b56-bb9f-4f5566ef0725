"use client"
import { cn } from "@/lib/utils"

interface CalendarGridProps {
  currentDate: Date
  events?: Array<{
    id: string
    title: string
    start: Date
    end: Date
    type: "kontrola" | "osetreni" | "konzultace" | "operace"
  }>
}

export function CalendarGrid({ currentDate, events = [] }: CalendarGridProps) {
  const daysInMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0).getDate()

  const firstDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1).getDay()

  const adjustedFirstDay = firstDayOfMonth === 0 ? 6 : firstDayOfMonth - 1

  const previousMonthDays = new Array(adjustedFirstDay)
    .fill(null)
    .map((_, index) => {
      const day = new Date(currentDate.getFullYear(), currentDate.getMonth(), 0 - index)
      return {
        date: day,
        isCurrentMonth: false,
        dayNumber: day.getDate(),
      }
    })
    .reverse()

  const currentMonthDays = new Array(daysInMonth).fill(null).map((_, index) => {
    const day = new Date(currentDate.getFullYear(), currentDate.getMonth(), index + 1)
    return {
      date: day,
      isCurrentMonth: true,
      dayNumber: index + 1,
    }
  })

  const nextMonthDays = new Array(42 - (previousMonthDays.length + currentMonthDays.length))
    .fill(null)
    .map((_, index) => {
      const day = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, index + 1)
      return {
        date: day,
        isCurrentMonth: false,
        dayNumber: index + 1,
      }
    })

  const days = [...previousMonthDays, ...currentMonthDays, ...nextMonthDays]
  const weekDays = ["po", "út", "st", "čt", "pá", "so", "ne"]

  return (
    <div className="rounded-lg border border-teal-500/20 bg-white dark:bg-gray-800">
      <div className="grid grid-cols-7 gap-px border-b border-teal-500/20">
        {weekDays.map((day) => (
          <div key={day} className="py-2 text-center text-sm font-medium text-gray-500 dark:text-gray-400">
            {day}
          </div>
        ))}
      </div>
      <div className="grid grid-cols-7 gap-px">
        {days.map((day, idx) => (
          <div
            key={idx}
            className={cn(
              "min-h-[100px] p-2 text-sm",
              day.isCurrentMonth
                ? "bg-white dark:bg-gray-800"
                : "bg-gray-50 dark:bg-gray-900 text-gray-400 dark:text-gray-600",
              "relative hover:bg-teal-50 dark:hover:bg-teal-900/10 transition-colors",
            )}
          >
            <span className="absolute top-2 right-2">{day.dayNumber}</span>
            {events
              .filter((event) => event.start.toDateString() === day.date.toDateString())
              .map((event) => (
                <div
                  key={event.id}
                  className={cn("mt-6 truncate rounded px-2 py-1 text-xs", {
                    "bg-teal-500 text-white": event.type === "kontrola",
                    "bg-blue-500 text-white": event.type === "osetreni",
                    "bg-indigo-500 text-white": event.type === "konzultace",
                    "bg-pink-500 text-white": event.type === "operace",
                  })}
                >
                  {event.title}
                </div>
              ))}
          </div>
        ))}
      </div>
    </div>
  )
}


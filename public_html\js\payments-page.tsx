"use client"

import React from "react"
import { <PERSON>, Card<PERSON>ontent, CardDescription, <PERSON><PERSON>oot<PERSON>, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { CreditCard, Calendar, AlertCircle, CheckCircle, XCircle } from "lucide-react"
import ReactDOM from "react-dom/client"

interface PaymentPageProps {
  subscription: {
    status: string
    plan_name: string
    price: number
    description: string
    start_date: string
    end_date: string
  } | null
  payments: Array<{
    id: number
    amount: number
    date: string
    status: string
  }>
  user: {
    id: number
    name: string
  }
}

export default function PaymentsPage({ subscription, payments, user }: PaymentPageProps) {
  const [isLoading, setIsLoading] = React.useState(false)

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("cs-CZ", {
      day: "numeric",
      month: "long",
      year: "numeric",
    })
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat("cs-CZ", {
      style: "currency",
      currency: "CZK",
    }).format(price)
  }

  const getStatusBadge = (status: string) => {
    switch (status.toLowerCase()) {
      case "active":
        return (
          <Badge variant="success" className="flex items-center gap-1">
            <CheckCircle className="h-4 w-4" />
            Aktivní
          </Badge>
        )
      case "inactive":
        return (
          <Badge variant="destructive" className="flex items-center gap-1">
            <XCircle className="h-4 w-4" />
            Neaktivní
          </Badge>
        )
      default:
        return (
          <Badge variant="secondary" className="flex items-center gap-1">
            <AlertCircle className="h-4 w-4" />
            {status}
          </Badge>
        )
    }
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Platby a předplatné</h1>
        <p className="text-gray-500">Správa vašeho předplatného a přehled plateb</p>
      </div>

      {isLoading ? (
        <div className="space-y-6">
          <Skeleton className="h-[200px] w-full" />
          <Skeleton className="h-[400px] w-full" />
        </div>
      ) : (
        <>
          <div className="grid gap-6 md:grid-cols-2">
            {/* Subscription Card */}
            <Card>
              <CardHeader>
                <CardTitle>Aktuální předplatné</CardTitle>
                <CardDescription>Informace o vašem předplatném</CardDescription>
              </CardHeader>
              <CardContent>
                {subscription ? (
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Status:</span>
                      {getStatusBadge(subscription.status)}
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Plán:</span>
                      <span className="text-sm">{subscription.plan_name}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Cena:</span>
                      <span className="text-sm font-bold">{formatPrice(subscription.price)}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Platné do:</span>
                      <span className="text-sm flex items-center gap-2">
                        <Calendar className="h-4 w-4" />
                        {formatDate(subscription.end_date)}
                      </span>
                    </div>
                    <div className="mt-2 text-sm text-gray-500">{subscription.description}</div>
                  </div>
                ) : (
                  <Alert>
                    <AlertCircle className="h-4 w-4" />
                    <AlertTitle>Žádné aktivní předplatné</AlertTitle>
                    <AlertDescription>
                      Nemáte žádné aktivní předplatné. Pro využívání služeb Dentibot si prosím aktivujte předplatné.
                    </AlertDescription>
                  </Alert>
                )}
              </CardContent>
              <CardFooter>
                <Button className="w-full" onClick={() => setIsLoading(true)}>
                  <CreditCard className="mr-2 h-4 w-4" />
                  {subscription ? "Spravovat předplatné" : "Aktivovat předplatné"}
                </Button>
              </CardFooter>
            </Card>

            {/* Payment History Card */}
            <Card>
              <CardHeader>
                <CardTitle>Historie plateb</CardTitle>
                <CardDescription>Přehled vašich posledních plateb</CardDescription>
              </CardHeader>
              <CardContent>
                {payments.length > 0 ? (
                  <div className="space-y-4">
                    {payments.map((payment) => (
                      <div key={payment.id} className="flex items-center justify-between py-2 border-b last:border-b-0">
                        <div className="flex flex-col">
                          <span className="text-sm font-medium">{formatDate(payment.date)}</span>
                          <span className="text-sm text-gray-500">{formatPrice(payment.amount)}</span>
                        </div>
                        {getStatusBadge(payment.status)}
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-6 text-gray-500">Zatím nemáte žádné platby</div>
                )}
              </CardContent>
              {payments.length > 0 && (
                <CardFooter>
                  <Button variant="outline" className="w-full">
                    Zobrazit všechny platby
                  </Button>
                </CardFooter>
              )}
            </Card>
          </div>

          {/* Support Section */}
          <div className="mt-8">
            <Alert>
              <AlertCircle className="h"use client"

import type React from "react"
import { useState, useEffect } from "react"

interface Subscription {
  status: string
  plan_name: string
  price: number
  end_date: string
  description: string
}

interface Payment {
  id: number
  date: string
  status: string
  amount: number
}

interface User {
  id: number
  name: string
}

interface PageData {
  subscription: Subscription | null
  payments: Payment[]
  user: User
}

declare global {
  interface Window {
    pageData: PageData
  }
}

export function PaymentsPage() {
  const [isLoading, setIsLoading] = useState(false)
  const { subscription, payments, user } = window.pageData

  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (isLoading) {
        e.preventDefault()
        e.returnValue = ""
      }
    }

    window.addEventListener("beforeunload", handleBeforeUnload)

    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload)
    }
  }, [isLoading])

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("cs-CZ", {
      day: "numeric",
      month: "long",
      year: "numeric",
    })
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat("cs-CZ", {
      style: "currency",
      currency: "CZK",
    }).format(price)
  }

  const getStatusBadge = (status: string) => {
    switch (status.toLowerCase()) {
      case "active":
        return (
          <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
            Aktivní
          </span>
        )
      case "inactive":
        return (
          <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
            Neaktivní
          </span>
        )
      default:
        return (
          <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
            {status}
          </span>
        )
    }
  }

  const handleManageSubscription = (e: React.MouseEvent) => {
    e.preventDefault()
    setIsLoading(true)
    // Here you would typically make an API call or redirect to a subscription management page
    // For now, we'll just simulate a delay
    setTimeout(() => {
      setIsLoading(false)
      alert("Subscription management functionality not implemented yet.")
    }, 1000)
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* ... (rest of the component remains the same) */}
      <div className="px-4 py-3 bg-gray-50 text-right sm:px-6">
        <button
          onClick={handleManageSubscription}
          className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          {subscription ? "Spravovat předplatné" : "Aktivovat předplatné"}
        </button>
      </div>
      {/* ... (rest of the component remains the same) */}
    </div>
  )
}

-4 w-4" />
              <AlertTitle>Potřebujete pomoc?</AlertTitle>
              <AlertDescription>
                V případě jakýchkoliv dotazů ohledně plateb nebo předplatného nás neváhejte kontaktovat na{" "}
                <a href="mailto:<EMAIL>" className="font-medium underline">
                  <EMAIL>
                </a>
              </AlertDescription>
            </Alert>
          </div>
        </>
      )}
    </div>
  )
}

// Render the component with the page data
const root = document.getElementById("root")
if (root) {
  ReactDOM.createRoot(root).render(<PaymentsPage {...(window.pageData as PaymentPageProps)} />)
}

// Přidejte tento kód do vašeho hlavního JavaScript souboru
document.addEventListener("DOMContentLoaded", () => {
  // Kontrola meta tagu pro zabránění přesměrování
  const preventRedirectMeta = document.querySelector('meta[name="prevent-dashboard-redirect"]')
  if (preventRedirectMeta && preventRedirectMeta.getAttribute("content") === "true") {
    // Zabránění přesměrování na dashboard
    window.preventDefaultRedirect = () => {
      console.log("Přesměrování na dashboard bylo zabráněno")
      // Zde můžete přidat další logiku, pokud je potřeba
    }
  }
})


<?php
require_once __DIR__ . '/vendor/autoload.php';

try {
    // Try to create a new Google Client
    $client = new Google_Client();
    echo "Google Client library is properly installed and loaded.\n";
    
    // Check if credentials file exists
    if (file_exists(__DIR__ . '/credentials.json')) {
        echo "credentials.json file found.\n";
    } else {
        echo "Warning: credentials.json file not found.\n";
    }
    
    // Check vendor directory
    if (is_dir(__DIR__ . '/vendor')) {
        echo "Vendor directory exists.\n";
    } else {
        echo "Warning: Vendor directory not found.\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}


<?php
require_once __DIR__ . '/../config.php';

function createUser($username, $email, $password, $role = 'user') {
    global $mysqli;

    try {
        $mysqli->begin_transaction();

        // Generate the hash
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
        if ($hashedPassword === false) {
            throw new Exception("Failed to generate password hash");
        }

        writeErrorLog("Password hash generated", [
            'username' => $username,
            'hash_length' => strlen($hashedPassword)
        ]);

        writeErrorLog("Inserting user", [
            'username' => $username,
            'email' => $email,
            'hashed_password_length' => strlen($hashedPassword)
        ]);

        $query = "INSERT INTO users (
            username, 
            email, 
            role,
            vapi_api_key,
            vapi_api_url,
            subscription_plan,
            password_hash,
            subscription_status,
            subscription_interval
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";

        $stmt = $mysqli->prepare($query);

        if (!$stmt) {
            throw new Exception("Error preparing statement: " . $mysqli->error);
        }

        $vapi_api_key = '';
        $vapi_api_url = 'https://api.vapi.ai';
        $subscription_plan = 'Základní';
        $subscription_status = 'active';
        $subscription_interval = 'monthly';

        $stmt->bind_param("sssssssss", 
            $username, 
            $email, 
            $role,
            $vapi_api_key,
            $vapi_api_url,
            $subscription_plan,
            $hashedPassword,
            $subscription_status,
            $subscription_interval
        );

        if (!$stmt->execute()) {
            throw new Exception("Error executing statement: " . $stmt->error);
        }

        $userId = $mysqli->insert_id;

        // Verify the stored hash immediately after insertion
        $verifyQuery = "SELECT password_hash FROM users WHERE id = ?";
        $verifyStmt = $mysqli->prepare($verifyQuery);
        $verifyStmt->bind_param("i", $userId);
        $verifyStmt->execute();
        $result = $verifyStmt->get_result();
        $user = $result->fetch_assoc();
        $verifyStmt->close();

        $verifyResult = password_verify($password, $user['password_hash']);
        
        writeErrorLog("User creation verification", [
            'user_id' => $userId,
            'username' => $username,
            'stored_hash_length' => strlen($user['password_hash']),
            'verification_result' => $verifyResult
        ]);

        if (!$verifyResult) {
            throw new Exception("Password verification failed after user creation");
        }

        $mysqli->commit();
        return $userId;

    } catch (Exception $e) {
        $mysqli->rollback();
        writeErrorLog("User Creation Error", [
            'username' => $username,
            'error' => $e->getMessage()
        ]);
        throw $e;
    } finally {
        if (isset($stmt)) {
            $stmt->close();
        }
    }
}

// Zpracování formuláře
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $username = $_POST['username'] ?? '';
    $email = $_POST['email'] ?? '';
    $password = $_POST['password'] ?? '';
    $role = $_POST['role'] ?? 'user';

    if (empty($username) || empty($email) || empty($password)) {
        $error = "Všechna pole jsou povinná.";
    } else {
        try {
            $newUserId = createUser($username, $email, $password, $role);
            $success = "Nový uživatel byl úspěšně vytvořen s ID: " . $newUserId;
        } catch (Exception $e) {
            $error = "Chyba při vytváření uživatele: " . $e->getMessage();
        }
    }
}
?>

<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Přidat nového uživatele</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
</head>
<body class="bg-gray-100 min-h-screen flex items-center justify-center">
    <div class="bg-white p-8 rounded-lg shadow-md w-96">
        <h1 class="text-2xl font-bold mb-6 text-center">Přidat nového uživatele</h1>
        
        <?php if (isset($error)): ?>
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
                <span class="block sm:inline"><?php echo htmlspecialchars($error); ?></span>
            </div>
        <?php endif; ?>

        <?php if (isset($success)): ?>
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
                <span class="block sm:inline"><?php echo htmlspecialchars($success); ?></span>
            </div>
        <?php endif; ?>

        <form method="POST" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" class="space-y-4">
            <div>
                <label for="username" class="block text-sm font-medium text-gray-700">Uživatelské jméno</label>
                <input type="text" id="username" name="username" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
            </div>
            <div>
                <label for="email" class="block text-sm font-medium text-gray-700">E-mail</label>
                <input type="email" id="email" name="email" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
            </div>
            <div>
                <label for="password" class="block text-sm font-medium text-gray-700">Heslo</label>
                <input type="password" id="password" name="password" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
            </div>
            <div>
                <label for="role" class="block text-sm font-medium text-gray-700">Role</label>
                <select id="role" name="role" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                    <option value="user">Uživatel</option>
                    <option value="admin">Admin</option>
                </select>
            </div>
            <div>
                <button type="submit" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Přidat uživatele
                </button>
            </div>
        </form>
    </div>
</body>
</html>


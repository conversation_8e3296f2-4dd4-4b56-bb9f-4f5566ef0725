<?php
// Funkce pro generování sitemap.xml

function generate_sitemap() {
    // Kontrola, zda sitemap.xml již existuje
    if (!file_exists('sitemap.xml')) {
        $sitemap = new SimpleXMLElement('<?xml version="1.0" encoding="UTF-8"?><urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"></urlset>');
        
        $urls = [
            '' => '1.0',
            'sluzby' => '0.8',
            'o-nas' => '0.8',
            'kontakt' => '0.8',
            'gdpr' => '0.6'
        ];

        foreach ($urls as $url => $priority) {
            $urlElement = $sitemap->addChild('url');
            $urlElement->addChild('loc', 'https://www.dentibot.eu/' . $url);
            $urlElement->addChild('lastmod', date('Y-m-d'));
            $urlElement->addChild('changefreq', 'weekly');
            $urlElement->addChild('priority', $priority);
        }
        
        $sitemap->asXML('sitemap.xml');
    }
}
?>


<?php
// Funkce pro nastavení HTTP hlaviček pro výkon a bezpečnost

// Funkce pro nastavení bezpečnostních hlavi<PERSON>ek
function set_security_headers() {
    header('X-Content-Type-Options: nosniff');
    header('X-Frame-Options: SAMEORIGIN');
    header('X-XSS-Protection: 1; mode=block');
    header('Referrer-Policy: strict-origin-when-cross-origin');
    header('Permissions-Policy: camera=(), microphone=(), geolocation=()');
}

// Funkce pro nastavení cache hlaviček
function set_cache_headers($expires_days = 14) {
    $expires = 60 * 60 * 24 * $expires_days; // Převod dnů na sekundy
    header("Pragma: public");
    header("Cache-Control: max-age=$expires");
    header('Expires: ' . gmdate('D, d M Y H:i:s', time() + $expires) . ' GMT');
}

// Funkce pro nastavení všech hlaviček
function set_all_headers() {
    set_security_headers();
    set_cache_headers();
}
?>


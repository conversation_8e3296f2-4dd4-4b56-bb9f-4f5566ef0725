"use client"

import { ChevronLeft, ChevronRight } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"

interface CalendarHeaderProps {
  currentDate: Date
  onPrevMonth: () => void
  onNextMonth: () => void
  onToday: () => void
  onViewChange: (view: "month" | "week" | "day") => void
  currentView: "month" | "week" | "day"
}

export function CalendarHeader({
  currentDate,
  onPrevMonth,
  onNextMonth,
  onToday,
  onViewChange,
  currentView,
}: CalendarHeaderProps) {
  const monthName = currentDate.toLocaleString("cs-CZ", { month: "long", year: "numeric" })

  return (
    <div className="flex items-center justify-between mb-4">
      <div className="flex items-center space-x-2">
        <Button
          variant="outline"
          size="icon"
          onClick={onPrevMonth}
          className="h-8 w-8 border-teal-500/20 hover:bg-teal-500/10"
        >
          <ChevronLeft className="h-4 w-4 text-teal-500" />
        </Button>
        <Button
          variant="outline"
          size="icon"
          onClick={onNextMonth}
          className="h-8 w-8 border-teal-500/20 hover:bg-teal-500/10"
        >
          <ChevronRight className="h-4 w-4 text-teal-500" />
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={onToday}
          className="ml-2 border-teal-500/20 hover:bg-teal-500/10 text-teal-500"
        >
          Dnes
        </Button>
      </div>

      <h2 className="text-lg font-medium text-gray-900 dark:text-gray-100">{monthName}</h2>

      <div className="flex items-center space-x-2">
        <Button
          variant={currentView === "month" ? "default" : "outline"}
          size="sm"
          onClick={() => onViewChange("month")}
          className={
            currentView === "month"
              ? "bg-teal-600 hover:bg-teal-700"
              : "border-teal-500/20 hover:bg-teal-500/10 text-teal-500"
          }
        >
          Měsíc
        </Button>
        <Button
          variant={currentView === "week" ? "default" : "outline"}
          size="sm"
          onClick={() => onViewChange("week")}
          className={
            currentView === "week"
              ? "bg-teal-600 hover:bg-teal-700"
              : "border-teal-500/20 hover:bg-teal-500/10 text-teal-500"
          }
        >
          Týden
        </Button>
        <Button
          variant={currentView === "day" ? "default" : "outline"}
          size="sm"
          onClick={() => onViewChange("day")}
          className={
            currentView === "day"
              ? "bg-teal-600 hover:bg-teal-700"
              : "border-teal-500/20 hover:bg-teal-500/10 text-teal-500"
          }
        >
          Den
        </Button>
      </div>
    </div>
  )
}


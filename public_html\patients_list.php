<?php
/**
 * Seznam pacientů - zachovává původní Baserow funkcionalitu
 */

require_once 'config_optimized.php';
require_once 'baserow_functions.php';

// Kontrola přihlášení
requireLogin();

// Parametry pro stránkování a filtrování
$page = max(1, (int)($_GET['page'] ?? 1));
$limit = 12;
$search = $_GET['search'] ?? '';
$insurance = $_GET['insurance'] ?? '';

// Načtení pacientů z Baserow - zachováváme původní funkcionalitu
$patientsResult = getPatients($page, $limit, $search, $insurance);
$patients = $patientsResult['patients'] ?? [];
$totalRecords = $patientsResult['totalRecords'] ?? 0;
$totalPages = ceil($totalRecords / $limit);
$error = $patientsResult['success'] ? null : ($patientsResult['error'] ?? 'Neznámá chyba');

$currentPage = 'patients';
?>

<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Seznam pacientů - Dentibot</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
        .sidebar {
            width: 16rem;
            background: linear-gradient(135deg, #1e3a8a, #1e40af);
            color: white;
            position: fixed;
            height: 100vh;
            left: 0;
            top: 0;
            z-index: 100;
            overflow-y: auto;
        }
        .main-content {
            margin-left: 16rem;
            min-height: 100vh;
        }
        .nav-link {
            display: flex;
            align-items: center;
            padding: 0.75rem 1.5rem;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.2s;
            cursor: pointer;
        }
        .nav-link:hover, .nav-link.active {
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }
        .nav-icon {
            width: 1.25rem;
            height: 1.25rem;
            margin-right: 0.75rem;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="p-6 border-b border-blue-600">
            <div class="flex items-center">
                <div class="w-8 h-8 bg-white rounded mr-3 flex items-center justify-center">
                    <span class="text-blue-600 font-bold text-sm">D</span>
                </div>
                <span class="text-xl font-bold">Dentibot</span>
            </div>
            <div class="mt-3 text-sm text-blue-200">
                <div><?php echo htmlspecialchars($_SESSION['username'] ?? 'Demo User'); ?></div>
                <div class="text-xs"><?php echo ucfirst(getUserSubscriptionPlan($_SESSION['user_id'])); ?> plán</div>
            </div>
        </div>
        <nav class="mt-6">
            <a href="main_dashboard.php" class="nav-link">
                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <rect x="3" y="3" width="18" height="18" rx="2"/><path d="M3 9h18"/><path d="M9 21V9"/>
                </svg>
                Dashboard
            </a>
            <a href="call_history.php" class="nav-link">
                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
                </svg>
                Historie hovorů
            </a>
            <a href="sms_campaigns.php" class="nav-link">
                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
                </svg>
                Hromadné SMS
            </a>
            <a href="patients_list.php" class="nav-link active">
                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/><circle cx="9" cy="7" r="4"/><path d="M23 21v-2a4 4 0 0 0-3-3.87"/><path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                </svg>
                Seznam pacientů
                <?php if ($totalRecords > 0): ?>
                    <span class="ml-2 bg-blue-500 text-white text-xs px-2 py-1 rounded-full"><?php echo $totalRecords; ?></span>
                <?php endif; ?>
            </a>
            <a href="patient_cards.php" class="nav-link">
                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/><polyline points="14,2 14,8 20,8"/><line x1="16" y1="13" x2="8" y2="13"/><line x1="16" y1="17" x2="8" y2="17"/><polyline points="10,9 9,9 8,9"/><circle cx="12" cy="15" r="2"/>
                </svg>
                Karty pacientů
                <span class="ml-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full">auto</span>
            </a>
            <a href="demo_events.html" class="nav-link">
                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                </svg>
                Demo události
                <span class="ml-2 bg-orange-500 text-white text-xs px-2 py-1 rounded-full">test</span>
            </a>
            <a href="settings.php" class="nav-link">
                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <circle cx="12" cy="12" r="3"/><path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
                </svg>
                Nastavení
            </a>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <div class="p-8">
            <!-- Header -->
            <div class="mb-8">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">Seznam pacientů</h1>
                        <p class="text-gray-600 mt-2">Správa kartotéky pacientů z Baserow</p>
                    </div>
                    <div class="flex items-center gap-4">
                        <span class="text-sm text-gray-600">
                            Celkem: <strong><?php echo number_format($totalRecords); ?></strong> pacientů
                        </span>
                        <a href="patient_cards.php" class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                            Automatické karty
                        </a>
                    </div>
                </div>
            </div>

            <!-- Error Alert -->
            <?php if ($error): ?>
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                    <strong class="font-bold">Chyba při načítání pacientů:</strong>
                    <span class="block sm:inline"><?php echo htmlspecialchars($error); ?></span>
                    <div class="mt-2 text-sm">
                        <a href="settings.php" class="underline">Zkontrolujte nastavení Baserow</a> nebo 
                        <a href="?refresh=1" class="underline">zkuste obnovit stránku</a>.
                    </div>
                </div>
            <?php endif; ?>

            <!-- Filters -->
            <div class="bg-white rounded-lg shadow-sm border p-6 mb-6">
                <form method="GET" class="flex flex-wrap gap-4">
                    <div class="flex-1 min-w-64">
                        <input type="text" name="search" value="<?php echo htmlspecialchars($search); ?>" 
                               placeholder="Hledat pacienta..." 
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    <div>
                        <select name="insurance" class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                            <option value="">Všechny pojišťovny</option>
                            <option value="VZP" <?php echo $insurance === 'VZP' ? 'selected' : ''; ?>>VZP</option>
                            <option value="ČPZP" <?php echo $insurance === 'ČPZP' ? 'selected' : ''; ?>>ČPZP</option>
                            <option value="VoZP" <?php echo $insurance === 'VoZP' ? 'selected' : ''; ?>>VoZP</option>
                            <option value="OZP" <?php echo $insurance === 'OZP' ? 'selected' : ''; ?>>OZP</option>
                        </select>
                    </div>
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors">
                        Filtrovat
                    </button>
                    <?php if ($search || $insurance): ?>
                        <a href="patients_list.php" class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg transition-colors">
                            Zrušit filtry
                        </a>
                    <?php endif; ?>
                </form>
            </div>

            <!-- Patients Grid -->
            <?php if (!empty($patients)): ?>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                    <?php foreach ($patients as $patient): ?>
                        <div class="bg-white rounded-lg shadow-sm border p-6 hover:shadow-md transition-shadow">
                            <div class="flex items-start justify-between mb-4">
                                <div class="flex-1">
                                    <h3 class="font-semibold text-gray-900 mb-1">
                                        <?php echo htmlspecialchars($patient['jmeno_pacienta'] ?? $patient['Jmeno_pacienta'] ?? 'Neznámé jméno'); ?>
                                    </h3>
                                    <p class="text-sm text-gray-600">
                                        <?php echo htmlspecialchars($patient['telefonni_cislo'] ?? $patient['Telefonni_cislo'] ?? 'Bez telefonu'); ?>
                                    </p>
                                    <p class="text-sm text-gray-600">
                                        <?php echo htmlspecialchars($patient['emailova_adresa'] ?? $patient['Emailova_adresa'] ?? 'Bez emailu'); ?>
                                    </p>
                                </div>
                                <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                                    <?php echo htmlspecialchars($patient['zdravotni_pojistovna'] ?? $patient['Zdravotni_pojistovna'] ?? 'N/A'); ?>
                                </span>
                            </div>
                            
                            <?php if (!empty($patient['datum_narozeni']) || !empty($patient['Datum_narozeni'])): ?>
                                <div class="text-sm text-gray-600 mb-4">
                                    <strong>Narozen:</strong> 
                                    <?php echo htmlspecialchars($patient['datum_narozeni'] ?? $patient['Datum_narozeni']); ?>
                                </div>
                            <?php endif; ?>
                            
                            <div class="flex gap-2">
                                <a href="patient_card_view.php?baserow_id=<?php echo urlencode($patient['row_id'] ?? $patient['id']); ?>" 
                                   class="flex-1 bg-blue-50 hover:bg-blue-100 text-blue-700 px-4 py-2 rounded text-sm font-medium transition-colors text-center">
                                    Karta pacienta
                                </a>
                                <button onclick="editPatient('<?php echo htmlspecialchars($patient['row_id'] ?? $patient['id']); ?>')" 
                                        class="bg-gray-50 hover:bg-gray-100 text-gray-700 px-4 py-2 rounded text-sm font-medium transition-colors">
                                    Detail
                                </button>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <!-- Pagination -->
                <?php if ($totalPages > 1): ?>
                    <div class="flex items-center justify-between">
                        <div class="text-sm text-gray-700">
                            Zobrazeno <?php echo count($patients); ?> z <?php echo number_format($totalRecords); ?> pacientů
                        </div>
                        <div class="flex items-center gap-2">
                            <?php if ($page > 1): ?>
                                <a href="?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($search); ?>&insurance=<?php echo urlencode($insurance); ?>" 
                                   class="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors">
                                    Předchozí
                                </a>
                            <?php endif; ?>
                            
                            <span class="bg-blue-600 text-white px-4 py-2 rounded-lg">
                                <?php echo $page; ?> / <?php echo $totalPages; ?>
                            </span>
                            
                            <?php if ($page < $totalPages): ?>
                                <a href="?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($search); ?>&insurance=<?php echo urlencode($insurance); ?>" 
                                   class="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors">
                                    Další
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>
            <?php else: ?>
                <div class="text-center py-12">
                    <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2M23 21v-2a4 4 0 0 0-3-3.87M16 3.13a4 4 0 0 1 0 7.75"/>
                    </svg>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Žádní pacienti</h3>
                    <p class="text-gray-600 mb-4">
                        <?php if ($search || $insurance): ?>
                            Nebyli nalezeni žádní pacienti odpovídající vašim kritériím.
                        <?php else: ?>
                            Zatím nejsou načteni žádní pacienti z Baserow.
                        <?php endif; ?>
                    </p>
                    <?php if ($search || $insurance): ?>
                        <a href="patients_list.php" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors">
                            Zobrazit všechny pacienty
                        </a>
                    <?php else: ?>
                        <a href="settings.php" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors">
                            Zkontrolovat nastavení Baserow
                        </a>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script>
        function editPatient(patientId) {
            alert('Detail pacienta #' + patientId + ' - funkce bude implementována');
        }
    </script>
</body>
</html>

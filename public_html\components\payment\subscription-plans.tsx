"use client"

import { useState } from "react"
import { usePayment } from "@/components/payment/payment-provider"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Skeleton } from "@/components/ui/skeleton"
import { CheckCircle2, CreditCard, CheckIcon } from "lucide-react"

export function SubscriptionPlans() {
  const { subscription, subscriptionPlans, paymentMethods, isLoading, updateSubscription } = usePayment()
  const [billingInterval, setBillingInterval] = useState<"month" | "year">("month")
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null)
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false)
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<string | null>(null)
  const [isProcessing, setIsProcessing] = useState(false)

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-semibold">Tarify předplatného</h2>
          <Skeleton className="h-10 w-48" />
        </div>
        <div className="grid gap-8 md:grid-cols-3">
          <Skeleton className="h-96" />
          <Skeleton className="h-96" />
          <Skeleton className="h-96" />
        </div>
      </div>
    )
  }

  const handlePlanSelect = (planId: string) => {
    if (subscription && subscription.planId === planId) {
      return // Already subscribed to this plan
    }

    setSelectedPlan(planId)
    setConfirmDialogOpen(true)

    // Set default payment method if available
    const defaultMethod = paymentMethods.find((pm) => pm.isDefault)
    if (defaultMethod) {
      setSelectedPaymentMethod(defaultMethod.id)
    } else if (paymentMethods.length > 0) {
      setSelectedPaymentMethod(paymentMethods[0].id)
    }
  }

  const handleConfirmSubscription = async () => {
    if (!selectedPlan) return

    setIsProcessing(true)

    try {
      await updateSubscription(selectedPlan)
      setConfirmDialogOpen(false)
    } catch (error) {
      console.error("Subscription update failed:", error)
    } finally {
      setIsProcessing(false)
    }
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">Tarify předplatného</h2>
        <Tabs
          value={billingInterval}
          onValueChange={(value) => setBillingInterval(value as "month" | "year")}
          className="w-48"
        >
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="month">Měsíčně</TabsTrigger>
            <TabsTrigger value="year">Ročně</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      <div className="grid gap-8 md:grid-cols-3">
        {subscriptionPlans.map((plan) => {
          const price = billingInterval === "month" ? plan.price.monthly : plan.price.yearly
          const isCurrentPlan = subscription && subscription.planId === plan.id

          return (
            <Card
              key={plan.id}
              className={`flex flex-col ${plan.popular ? "border-primary" : ""} ${isCurrentPlan ? "bg-muted" : ""}`}
            >
              {plan.popular && (
                <div className="absolute top-0 right-4 -translate-y-1/2 rounded-full bg-primary px-3 py-1 text-xs font-semibold text-primary-foreground">
                  Nejoblíbenější
                </div>
              )}
              <CardHeader>
                <CardTitle>{plan.name}</CardTitle>
                <CardDescription>{plan.description}</CardDescription>
              </CardHeader>
              <CardContent className="flex-grow">
                <div className="mb-4">
                  <span className="text-3xl font-bold">{price.toLocaleString()}</span>
                  <span className="text-muted-foreground"> Kč / {billingInterval === "month" ? "měsíc" : "rok"}</span>
                  {billingInterval === "year" && (
                    <div className="mt-1 text-xs text-primary">
                      Ušetříte {(plan.price.monthly * 12 - plan.price.yearly).toLocaleString()} Kč ročně
                    </div>
                  )}
                </div>
                <ul className="space-y-2">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-start">
                      <CheckIcon className="mr-2 h-4 w-4 text-primary" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
              <CardFooter>
                <Button
                  variant={isCurrentPlan ? "outline" : plan.popular ? "default" : "outline"}
                  className="w-full"
                  disabled={isCurrentPlan}
                  onClick={() => handlePlanSelect(plan.id)}
                >
                  {isCurrentPlan ? (
                    <>
                      <CheckCircle2 className="mr-2 h-4 w-4" />
                      Aktuální tarif
                    </>
                  ) : (
                    "Vybrat tarif"
                  )}
                </Button>
              </CardFooter>
            </Card>
          )
        })}
      </div>

      <Dialog open={confirmDialogOpen} onOpenChange={setConfirmDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Potvrdit změnu tarifu</DialogTitle>
            <DialogDescription>Vyberte platební metodu pro dokončení změny tarifu.</DialogDescription>
          </DialogHeader>

          <div className="py-4">
            <div className="mb-4 p-4 bg-muted rounded-lg">
              <div className="flex justify-between items-center">
                <div>
                  <p className="font-medium">{subscriptionPlans.find((p) => p.id === selectedPlan)?.name}</p>
                  <p className="text-sm text-muted-foreground">
                    {billingInterval === "month" ? "Měsíční" : "Roční"} předplatné
                  </p>
                </div>
                <p className="font-bold">
                  {billingInterval === "month"
                    ? subscriptionPlans.find((p) => p.id === selectedPlan)?.price.monthly.toLocaleString()
                    : subscriptionPlans.find((p) => p.id === selectedPlan)?.price.yearly.toLocaleString()}{" "}
                  Kč
                </p>
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <Label>Vyberte platební metodu</Label>
                {paymentMethods.length > 0 ? (
                  <RadioGroup
                    value={selectedPaymentMethod || ""}
                    onValueChange={setSelectedPaymentMethod}
                    className="mt-2 space-y-2"
                  >
                    {paymentMethods.map((method) => (
                      <div key={method.id} className="flex items-center space-x-2 rounded-md border p-3">
                        <RadioGroupItem value={method.id} id={method.id} />
                        <Label htmlFor={method.id} className="flex-grow cursor-pointer">
                          <div className="flex items-center">
                            {method.type === "card" ? (
                              <>
                                <CreditCard className="mr-2 h-4 w-4" />
                                {method.details.brand} •••• {method.details.last4}
                              </>
                            ) : method.type === "paypal" ? (
                              <>
                                <span className="mr-2 font-bold text-blue-600">P</span>
                                PayPal ({method.details.email})
                              </>
                            ) : (
                              <>
                                <CreditCard className="mr-2 h-4 w-4" />
                                {method.details.bankName} •••• {method.details.accountLast4}
                              </>
                            )}
                            {method.isDefault && <span className="ml-2 text-xs text-muted-foreground">(Výchozí)</span>}
                          </div>
                        </Label>
                      </div>
                    ))}
                  </RadioGroup>
                ) : (
                  <div className="mt-2 rounded-md border border-dashed p-4 text-center">
                    <p className="text-sm text-muted-foreground">
                      Nemáte přidané žádné platební metody. Přidejte platební metodu pro dokončení změny tarifu.
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setConfirmDialogOpen(false)} disabled={isProcessing}>
              Zrušit
            </Button>
            <Button onClick={handleConfirmSubscription} disabled={!selectedPaymentMethod || isProcessing}>
              {isProcessing ? "Zpracování..." : "Potvrdit změnu"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}


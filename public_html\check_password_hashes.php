<?php
require_once 'config.php';

function get_all_users() {
    global $conn;
    $sql = "SELECT id, username, password FROM users";
    $result = $conn->query($sql);
    return $result->fetch_all(MYSQLI_ASSOC);
}

$users = get_all_users();

echo "<h1>Kontrola hashů hesel</h1>";

foreach ($users as $user) {
    echo "<p>Uživatel: " . htmlspecialchars($user['username']) . "<br>";
    echo "Hash hesla: " . htmlspecialchars($user['password']) . "<br>";
    echo "Je hash platný: " . (password_get_info($user['password'])['algo'] !== 0 ? 'Ano' : 'Ne') . "</p>";
}


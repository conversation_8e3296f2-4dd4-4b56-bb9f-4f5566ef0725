import type React from "react"
import { useState, useEffect, useCallback } from "react"
import type { Patient } from "../types/patient"

const PatientList: React.FC = () => {
  const [patients, setPatients] = useState<Patient[]>([])
  const [totalPatients, setTotalPatients] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [perPage, setPerPage] = useState(20)
  const [totalPages, setTotalPages] = useState(1)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedPatients, setSelectedPatients] = useState<number[]>([])
  const [allSelected, setAllSelected] = useState(false)

  const fetchPatients = useCallback(async () => {
    try {
      const response = await fetch(
        `patients.php?action=get_patients&page=${currentPage}&perPage=${perPage}&search=${searchTerm}`,
      )
      const data = await response.json()
      setPatients(data.patients)
      setTotalPatients(data.totalRecords)
      setTotalPages(Math.ceil(data.totalRecords / perPage))
      setCurrentPage((prev) => Math.max(1, Math.min(prev, Math.ceil(data.totalRecords / perPage))))
      updateAllSelected(data.patients)
    } catch (error) {
      console.error("Error fetching patients:", error)
    }
  }, [currentPage, perPage, searchTerm])

  useEffect(() => {
    fetchPatients()
  }, [fetchPatients])

  const updateAllSelected = (patientList: Patient[]) => {
    setAllSelected(patientList.length > 0 && selectedPatients.length === patientList.length)
  }

  const toggleAllPatients = () => {
    if (allSelected) {
      setSelectedPatients([])
    } else {
      setSelectedPatients(patients.map((patient) => patient.id))
    }
    setAllSelected(!allSelected)
  }

  const togglePatient = (patientId: number) => {
    setSelectedPatients((prev) => {
      const newSelected = prev.includes(patientId) ? prev.filter((id) => id !== patientId) : [...prev, patientId]
      setAllSelected(newSelected.length === patients.length)
      return newSelected
    })
  }

  const handleSearch = () => {
    setCurrentPage(1)
    fetchPatients()
  }

  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages && page !== currentPage) {
      setCurrentPage(page)
    }
  }

  const deletePatient = async (id: number) => {
    if (window.confirm("Opravdu chcete smazat tohoto pacienta?")) {
      try {
        const response = await fetch(`delete_patient.php?id=${id}`, { method: "POST" })
        if (response.ok) {
          await fetchPatients()
        } else {
          alert("Chyba při mazání pacienta")
        }
      } catch (error) {
        console.error("Error deleting patient:", error)
        alert("Chyba při mazání pacienta")
      }
    }
  }

  const pageNumbers = () => {
    const totalPageButtons = 5
    const pageNumbers = []
    let startPage = Math.max(1, currentPage - Math.floor(totalPageButtons / 2))
    const endPage = Math.min(totalPages, startPage + totalPageButtons - 1)

    if (endPage - startPage + 1 < totalPageButtons) {
      startPage = Math.max(1, endPage - totalPageButtons + 1)
    }

    for (let i = startPage; i <= endPage; i++) {
      pageNumbers.push(i)
    }

    return pageNumbers
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">Seznam pacientů</h1>
          <p className="text-sm text-gray-500 mt-1">
            Celkem: {totalPatients} pacientů
            {searchTerm && <span> (filtrováno)</span>}
          </p>
        </div>
        <div className="flex gap-3">
          <a
            href="sync_patients.php"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Synchronizovat s Baserow
          </a>
          <a
            href="add_patient.php"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
          >
            Přidat pacienta
          </a>
        </div>
      </div>

      <div className="bg-white shadow overflow-hidden sm:rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="flex flex-col sm:flex-row justify-between items-center mb-4">
            <div className="w-full sm:w-1/2 mb-4 sm:mb-0">
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyPress={(e) => e.key === "Enter" && handleSearch()}
                placeholder="Vyhledat pacienty..."
                className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
              />
            </div>
            <div className="w-full sm:w-auto flex items-center">
              <span className="mr-2">Zobrazit:</span>
              <select
                value={perPage}
                onChange={(e) => {
                  setPerPage(Number(e.target.value))
                  setCurrentPage(1)
                  fetchPatients()
                }}
                className="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
              >
                <option>10</option>
                <option>20</option>
                <option>50</option>
                <option>100</option>
              </select>
            </div>
          </div>

          <div className="mt-4">
            <div className="flex items-center justify-between mb-2">
              <button
                onClick={toggleAllPatients}
                className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-full shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                {allSelected ? "Odznačit vše" : "Vybrat vše"}
              </button>
              <span className="text-sm text-gray-700">
                Vybráno: {selectedPatients.length} z {totalPatients} pacientů
              </span>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      <input
                        type="checkbox"
                        checked={allSelected}
                        onChange={toggleAllPatients}
                        className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded"
                      />
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      Jméno
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      Email
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      Telefon
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      Akce
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {patients.map((patient) => (
                    <tr key={patient.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <input
                          type="checkbox"
                          checked={selectedPatients.includes(patient.id)}
                          onChange={() => togglePatient(patient.id)}
                          className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded"
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{patient.name}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-500">{patient.email}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-500">{patient.phone}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <a
                          href={`edit_patient.php?id=${patient.id}`}
                          className="text-blue-600 hover:text-blue-900 mr-3"
                        >
                          Upravit
                        </a>
                        <button onClick={() => deletePatient(patient.id)} className="text-red-600 hover:text-red-900">
                          Smazat
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          <div className="mt-4 flex justify-between items-center">
            <div>
              <p className="text-sm text-gray-700">
                Zobrazeno {(currentPage - 1) * perPage + 1} až {Math.min(currentPage * perPage, totalPatients)} z{" "}
                {totalPatients} pacientů
              </p>
            </div>
            <div>
              <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                <button
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                  className={`relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 ${currentPage === 1 ? "opacity-50 cursor-not-allowed" : ""}`}
                >
                  Předchozí
                </button>
                {pageNumbers().map((page) => (
                  <button
                    key={page}
                    onClick={() => handlePageChange(page)}
                    className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${page === currentPage ? "z-10 bg-blue-50 border-blue-500 text-blue-600" : "bg-white border-gray-300 text-gray-500 hover:bg-gray-50"}`}
                  >
                    {page}
                  </button>
                ))}
                <button
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className={`relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 ${currentPage === totalPages ? "opacity-50 cursor-not-allowed" : ""}`}
                >
                  Další
                </button>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default PatientList


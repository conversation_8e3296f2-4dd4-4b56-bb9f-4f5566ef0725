CREATE DATABASE dentibot_stats;
USE dentibot_stats;

CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    name <PERSON><PERSON><PERSON><PERSON>(100),
    role <PERSON><PERSON><PERSON>('admin', 'doctor', 'assistant') DEFAULT 'doctor',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE calls (
    id INT AUTO_INCREMENT PRIMARY KEY,
    patient_phone VARCHAR(20) NOT NULL,
    duration INT NOT NULL,
    status ENUM('successful', 'failed', 'needs_attention') NOT NULL,
    type ENUM('appointment', 'cancellation', 'info') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    notes TEXT
);

CREATE TABLE appointments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    call_id INT,
    patient_phone VARCHAR(20) NOT NULL,
    appointment_date DATETIME NOT NULL,
    status ENUM('scheduled', 'cancelled', 'completed') DEFAULT 'scheduled',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (call_id) REFERENCES calls(id)
);

CREATE TABLE notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    type VARCHAR(50) NOT NULL,
    message TEXT NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- CGM Časová razítka - Elektronická dokumentace
CREATE TABLE document_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE medical_documents (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    patient_id INT,
    category_id INT,
    title VARCHAR(255) NOT NULL,
    content LONGTEXT,
    file_path VARCHAR(500),
    file_type VARCHAR(50),
    file_size INT,
    document_hash VARCHAR(64) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (category_id) REFERENCES document_categories(id),
    INDEX idx_user_id (user_id),
    INDEX idx_patient_id (patient_id),
    INDEX idx_created_at (created_at)
);

CREATE TABLE document_timestamps (
    id INT AUTO_INCREMENT PRIMARY KEY,
    document_id INT NOT NULL,
    timestamp_data LONGTEXT NOT NULL,
    timestamp_hash VARCHAR(64) NOT NULL,
    tsa_url VARCHAR(255),
    certificate_info TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP,
    is_valid BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (document_id) REFERENCES medical_documents(id) ON DELETE CASCADE,
    INDEX idx_document_id (document_id),
    INDEX idx_created_at (created_at)
);

CREATE TABLE document_signatures (
    id INT AUTO_INCREMENT PRIMARY KEY,
    document_id INT NOT NULL,
    user_id INT NOT NULL,
    signature_data LONGTEXT NOT NULL,
    signature_hash VARCHAR(64) NOT NULL,
    certificate_data TEXT,
    signature_algorithm VARCHAR(50) DEFAULT 'SHA256withRSA',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP,
    is_valid BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (document_id) REFERENCES medical_documents(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id),
    INDEX idx_document_id (document_id),
    INDEX idx_user_id (user_id)
);

CREATE TABLE document_audit_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    document_id INT NOT NULL,
    user_id INT NOT NULL,
    action ENUM('created', 'viewed', 'updated', 'signed', 'timestamped', 'exported', 'deleted') NOT NULL,
    details TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (document_id) REFERENCES medical_documents(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id),
    INDEX idx_document_id (document_id),
    INDEX idx_user_id (user_id),
    INDEX idx_created_at (created_at)
);

-- Vložení základních kategorií dokumentů
INSERT INTO document_categories (name, description) VALUES
('Zdravotnická dokumentace', 'Základní zdravotnická dokumentace pacienta'),
('Vyšetření', 'Výsledky vyšetření a diagnostiky'),
('Terapie', 'Terapeutické plány a postupy'),
('Recepty', 'Elektronické recepty a předpisy'),
('Zprávy', 'Lékařské zprávy a posudky'),
('Preventivní péče', 'Dokumentace preventivních prohlídek'),
('Administrativní', 'Administrativní dokumenty a formuláře');

-- Dynamická karta pacienta - nový model
CREATE TABLE patient_cards (
    id INT AUTO_INCREMENT PRIMARY KEY,
    patient_id INT NOT NULL,
    card_version INT NOT NULL DEFAULT 1,
    card_data JSON NOT NULL,
    card_hash VARCHAR(64) NOT NULL,
    snap_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    aggregated_tst_id INT,
    is_current BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_patient_id (patient_id),
    INDEX idx_card_version (card_version),
    INDEX idx_snap_time (snap_time),
    INDEX idx_is_current (is_current)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE card_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    card_id INT NOT NULL,
    visit_id VARCHAR(36),
    item_type ENUM('note', 'image', 'file', 'procedure', 'consent', 'payment') NOT NULL,
    version_id VARCHAR(36) NOT NULL,
    content_hash VARCHAR(64) NOT NULL,
    tst_id INT,
    item_data JSON,
    file_path VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (card_id) REFERENCES patient_cards(id) ON DELETE CASCADE,
    FOREIGN KEY (tst_id) REFERENCES document_timestamps(id),
    INDEX idx_card_id (card_id),
    INDEX idx_visit_id (visit_id),
    INDEX idx_item_type (item_type),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE card_events (
    id INT AUTO_INCREMENT PRIMARY KEY,
    patient_id INT NOT NULL,
    event_type VARCHAR(50) NOT NULL,
    event_data JSON,
    processed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP NULL,
    INDEX idx_patient_id (patient_id),
    INDEX idx_event_type (event_type),
    INDEX idx_processed (processed),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE daily_card_stamps (
    id INT AUTO_INCREMENT PRIMARY KEY,
    stamp_date DATE NOT NULL,
    patient_id INT NOT NULL,
    card_version INT NOT NULL,
    aggregated_hash VARCHAR(64) NOT NULL,
    tst_id INT,
    status ENUM('pending', 'completed', 'failed') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,
    FOREIGN KEY (tst_id) REFERENCES document_timestamps(id),
    UNIQUE KEY unique_daily_stamp (stamp_date, patient_id),
    INDEX idx_stamp_date (stamp_date),
    INDEX idx_patient_id (patient_id),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


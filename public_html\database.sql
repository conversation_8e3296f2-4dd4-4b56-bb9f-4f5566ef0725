CREATE DATABASE dentibot_stats;
USE dentibot_stats;

CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    name <PERSON><PERSON><PERSON><PERSON>(100),
    role <PERSON><PERSON><PERSON>('admin', 'doctor', 'assistant') DEFAULT 'doctor',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE calls (
    id INT AUTO_INCREMENT PRIMARY KEY,
    patient_phone VARCHAR(20) NOT NULL,
    duration INT NOT NULL,
    status ENUM('successful', 'failed', 'needs_attention') NOT NULL,
    type ENUM('appointment', 'cancellation', 'info') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    notes TEXT
);

CREATE TABLE appointments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    call_id INT,
    patient_phone VARCHAR(20) NOT NULL,
    appointment_date DATETIME NOT NULL,
    status ENUM('scheduled', 'cancelled', 'completed') DEFAULT 'scheduled',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (call_id) REFERENCES calls(id)
);

CREATE TABLE notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    type VARCHAR(50) NOT NULL,
    message TEXT NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- CGM Časová razítka - Elektronická dokumentace
CREATE TABLE document_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE medical_documents (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    patient_id INT,
    category_id INT,
    title VARCHAR(255) NOT NULL,
    content LONGTEXT,
    file_path VARCHAR(500),
    file_type VARCHAR(50),
    file_size INT,
    document_hash VARCHAR(64) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (category_id) REFERENCES document_categories(id),
    INDEX idx_user_id (user_id),
    INDEX idx_patient_id (patient_id),
    INDEX idx_created_at (created_at)
);

CREATE TABLE document_timestamps (
    id INT AUTO_INCREMENT PRIMARY KEY,
    document_id INT NOT NULL,
    timestamp_data LONGTEXT NOT NULL,
    timestamp_hash VARCHAR(64) NOT NULL,
    tsa_url VARCHAR(255),
    certificate_info TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP,
    is_valid BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (document_id) REFERENCES medical_documents(id) ON DELETE CASCADE,
    INDEX idx_document_id (document_id),
    INDEX idx_created_at (created_at)
);

CREATE TABLE document_signatures (
    id INT AUTO_INCREMENT PRIMARY KEY,
    document_id INT NOT NULL,
    user_id INT NOT NULL,
    signature_data LONGTEXT NOT NULL,
    signature_hash VARCHAR(64) NOT NULL,
    certificate_data TEXT,
    signature_algorithm VARCHAR(50) DEFAULT 'SHA256withRSA',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP,
    is_valid BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (document_id) REFERENCES medical_documents(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id),
    INDEX idx_document_id (document_id),
    INDEX idx_user_id (user_id)
);

CREATE TABLE document_audit_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    document_id INT NOT NULL,
    user_id INT NOT NULL,
    action ENUM('created', 'viewed', 'updated', 'signed', 'timestamped', 'exported', 'deleted') NOT NULL,
    details TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (document_id) REFERENCES medical_documents(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id),
    INDEX idx_document_id (document_id),
    INDEX idx_user_id (user_id),
    INDEX idx_created_at (created_at)
);

-- Vložení základních kategorií dokumentů
INSERT INTO document_categories (name, description) VALUES
('Zdravotnická dokumentace', 'Základní zdravotnická dokumentace pacienta'),
('Vyšetření', 'Výsledky vyšetření a diagnostiky'),
('Terapie', 'Terapeutické plány a postupy'),
('Recepty', 'Elektronické recepty a předpisy'),
('Zprávy', 'Lékařské zprávy a posudky'),
('Preventivní péče', 'Dokumentace preventivních prohlídek'),
('Administrativní', 'Administrativní dokumenty a formuláře');


<?php
// Tento soubor by m<PERSON><PERSON> b<PERSON><PERSON> vložen na konec vš<PERSON> stránek
?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Vytvoření elementů pro widget
    const widgetContainer = document.createElement('div');
    widgetContainer.className = 'messages-widget fixed bottom-4 right-4 z-50';
    document.body.appendChild(widgetContainer);
    
    // Inicializace stavu
    let messages = [];
    let unreadCount = 0;
    let isOpen = false;
    
    // Funkce pro vykreslení widgetu
    function renderWidget() {
        widgetContainer.innerHTML = `
            <button id="messages-button" class="rounded-full h-14 w-14 shadow-lg bg-indigo-600 text-white flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-6 w-6">
                    <path d="M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9"></path>
                    <path d="M10.3 21a1.94 1.94 0 0 0 3.4 0"></path>
                </svg>
                ${unreadCount > 0 ? `<span class="absolute -top-1 -right-1 bg-red-500 text-white rounded-full h-6 w-6 flex items-center justify-center text-xs font-bold">${unreadCount}</span>` : ''}
            </button>
            
            ${isOpen ? `
            <div id="messages-panel" class="fixed bottom-20 right-4 bg-white rounded-lg shadow-xl border w-full max-w-md max-h-[80vh] flex flex-col">
                <div class="p-4 border-b flex justify-between items-center">
                    <h3 class="font-bold text-lg">Zprávy od pacientů</h3>
                    ${unreadCount > 0 ? `
                    <button id="mark-all-read" class="text-xs flex items-center gap-1 text-indigo-600 hover:text-indigo-800">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                            <path d="M18 6 7 17l-5-5"></path>
                            <path d="m22 10-7.5 7.5L13 16"></path>
                        </svg>
                        Označit vše jako přečtené
                    </button>
                    ` : ''}
                    <button id="close-messages" class="ml-2 text-gray-500 hover:text-gray-700">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M18 6 6 18"></path>
                            <path d="m6 6 12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="overflow-y-auto p-4 space-y-4 flex-grow">
                    ${messages.length === 0 ? `
                        <div class="text-center py-8 text-gray-500">
                            Žádné zprávy k zobrazení
                        </div>
                    ` : messages.map(message => `
                        <div class="p-4 rounded-lg border ${message.read ? 'bg-white' : 'bg-gray-50 border-indigo-200'}">
                            <div class="flex justify-between items-start mb-2">
                                <h3 class="font-medium">${escapeHtml(message.name)}</h3>
                                <span class="text-xs text-gray-500">
                                    ${formatTimestamp(message.timestamp)}
                                </span>
                            </div>
                            <p class="text-sm mb-2">${escapeHtml(message.content)}</p>
                            ${!message.read ? `
                                <button class="mark-read text-xs flex items-center gap-1 text-indigo-600 hover:text-indigo-800" data-id="${message.id}">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M20 6 9 17l-5-5"></path>
                                    </svg>
                                    Označit jako přečtené
                                </button>
                            ` : ''}
                        </div>
                    `).join('')}
                </div>
            </div>
            ` : ''}
        `;
        
        // Přidání event listenerů
        document.getElementById('messages-button').addEventListener('click', togglePanel);
        
        if (isOpen) {
            document.getElementById('close-messages').addEventListener('click', closePanel);
            
            if (unreadCount > 0) {
                document.getElementById('mark-all-read').addEventListener('click', markAllAsRead);
            }
            
            document.querySelectorAll('.mark-read').forEach(button => {
                button.addEventListener('click', function() {
                    markAsRead(this.getAttribute('data-id'));
                });
            });
        }
    }
    
    // Funkce pro přepínání panelu
    function togglePanel() {
        isOpen = !isOpen;
        renderWidget();
        
        if (isOpen && unreadCount > 0) {
            markAllAsRead();
        }
    }
    
    // Funkce pro zavření panelu
    function closePanel() {
        isOpen = false;
        renderWidget();
    }
    
    // Funkce pro načtení zpráv
    async function fetchMessages() {
        try {
            const response = await fetch('api_messages.php');
            const data = await response.json();
            
            messages = data.messages || [];
            unreadCount = messages.filter(msg => !msg.read).length;
            
            renderWidget();
        } catch (error) {
            console.error('Chyba při načítání zpráv:', error);
        }
    }
    
    // Funkce pro označení zprávy jako přečtené
    async function markAsRead(id) {
        try {
            await fetch('api_messages.php', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ id, read: true }),
            });
            
            // Aktualizace lokálního stavu
            messages = messages.map(msg => 
                msg.id === id ? { ...msg, read: true } : msg
            );
            unreadCount = Math.max(0, unreadCount - 1);
            
            renderWidget();
        } catch (error) {
            console.error('Chyba při označování zprávy jako přečtené:', error);
        }
    }
    
    // Funkce pro označení všech zpráv jako přečtené
    async function markAllAsRead() {
        try {
            const unreadMessages = messages.filter(msg => !msg.read);
            
            for (const msg of unreadMessages) {
                await fetch('api_messages.php', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ id: msg.id, read: true }),
                });
            }
            
            // Aktualizace lokálního stavu
            messages = messages.map(msg => ({ ...msg, read: true }));
            unreadCount = 0;
            
            renderWidget();
        } catch (error) {
            console.error('Chyba při označování všech zpráv jako přečtených:', error);
        }
    }
    
    // Pomocné funkce
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    function formatTimestamp(timestamp) {
        const date = new Date(timestamp);
        const now = new Date();
        const diffMs = now - date;
        const diffSec = Math.floor(diffMs / 1000);
        const diffMin = Math.floor(diffSec / 60);
        const diffHour = Math.floor(diffMin / 60);
        const diffDay = Math.floor(diffHour / 24);
        
        if (diffDay > 0) {
            return `${diffDay} ${diffDay === 1 ? 'den' : diffDay < 5 ? 'dny' : 'dní'} zpět`;
        } else if (diffHour > 0) {
            return `${diffHour} ${diffHour === 1 ? 'hodinu' : diffHour < 5 ? 'hodiny' : 'hodin'} zpět`;
        } else if (diffMin > 0) {
            return `${diffMin} ${diffMin === 1 ? 'minutu' : diffMin < 5 ? 'minuty' : 'minut'} zpět`;
        } else {
            return 'právě teď';
        }
    }
    
    // Inicializace
    fetchMessages();
    
    // Pravidelná aktualizace zpráv (každých 30 sekund)
    setInterval(fetchMessages, 30000);
});
</script>
<style>
    .messages-widget {
        z-index: 9999;
    }
    
    #messages-panel {
        z-index: 10000;
    }
</style>


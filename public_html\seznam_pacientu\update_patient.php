<?php
require_once '../config.php';
require_once '../baserow_functions.php';
require_once '../error_log.php';

// Initialize session and check for errors
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Kontrola přihlášení
if (!isset($_SESSION['user_id'])) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Uživatel není přihl<PERSON>en']);
    exit;
}

// Zpracování požadavku na aktualizaci pacienta
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');
    
    $patientId = isset($_POST['id']) ? intval($_POST['id']) : 0;
    $field = isset($_POST['field']) ? $_POST['field'] : '';
    $value = isset($_POST['value']) ? $_POST['value'] : '';
    
    error_log("Update Patient Request: ID=$patientId, field=$field, value=$value");
    
    // Mapování polí z frontendu na Baserow
    $fieldMapping = [
        'examination_date' => 'Datum_prohlidky',
        'akutni' => 'akutní',
        'brouseni' => 'broušení',
        'endo' => 'endo',
        'extrakce_chirurgie' => 'extrakce, chirurgie',
        'postendo' => 'postendo',
        'predni_protetiky' => 'předání protetiky',
        'sanace_dite' => 'sanace - dítě',
        'sanace_dospely' => 'sanace - dospělý',
        'snimatelna_protetika' => 'snímatelná protetika - otisky'
    ];
    
    try {
        if (!$patientId || !isset($fieldMapping[$field])) {
            throw new Exception("Neplatné ID pacienta nebo pole");
        }
        
        $baserowField = $fieldMapping[$field];
        $updateData = [$baserowField => $value];
        
        error_log("Sending to Baserow: " . json_encode($updateData));
        
        $response = baserowRequest('PATCH', '/' . $patientId . '/?user_field_names=true', $updateData);
        
        if (!is_array($response)) {
            throw new Exception("Neočekávaná odpověď při aktualizaci pacienta v Baserow");
        }
        
        error_log("Baserow response: " . json_encode($response));
        
        echo json_encode(['success' => true, 'message' => "Pole bylo úspěšně aktualizováno"]);
    } catch (Exception $e) {
        error_log("Error updating patient: " . $e->getMessage());
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
    exit;
}

// Pokud není POST požadavek
header('Content-Type: application/json');
echo json_encode(['success' => false, 'message' => 'Neplatný požadavek']);
exit;
?>


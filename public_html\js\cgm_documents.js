/**
 * CGM Časová razítka - Frontend JavaScript
 * Interaktivní funkcionalita pro správu dokumentů
 */

class CGMDocuments {
    constructor() {
        this.apiBase = 'api/cgm_documents_api.php';
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.loadDocuments();
    }
    
    bindEvents() {
        // Modal events
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-action="open-create-modal"]')) {
                this.openCreateModal();
            }
            
            if (e.target.matches('[data-action="close-modal"]')) {
                this.closeModal(e.target.closest('.modal'));
            }
            
            if (e.target.matches('[data-action="create-timestamp"]')) {
                const documentId = e.target.dataset.documentId;
                this.createTimestamp(documentId);
            }
            
            if (e.target.matches('[data-action="verify-timestamp"]')) {
                const timestampId = e.target.dataset.timestampId;
                this.verifyTimestamp(timestampId);
            }
        });
        
        // Form submissions
        document.addEventListener('submit', (e) => {
            if (e.target.matches('#createDocumentForm')) {
                e.preventDefault();
                this.createDocument(e.target);
            }
            
            if (e.target.matches('#uploadDocumentForm')) {
                e.preventDefault();
                this.uploadDocument(e.target);
            }
        });
        
        // Search and filter
        const searchInput = document.getElementById('documentSearch');
        if (searchInput) {
            let searchTimeout;
            searchInput.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    this.filterDocuments();
                }, 300);
            });
        }
        
        const categoryFilter = document.getElementById('categoryFilter');
        if (categoryFilter) {
            categoryFilter.addEventListener('change', () => {
                this.filterDocuments();
            });
        }
    }
    
    async loadDocuments(filters = {}) {
        try {
            this.showLoading();
            
            const params = new URLSearchParams({
                path: 'documents',
                ...filters
            });
            
            const response = await fetch(`${this.apiBase}?${params}`);
            const result = await response.json();
            
            if (result.success) {
                this.renderDocuments(result.documents);
            } else {
                this.showError(result.error);
            }
        } catch (error) {
            this.showError('Chyba při načítání dokumentů');
            console.error('Load documents error:', error);
        } finally {
            this.hideLoading();
        }
    }
    
    async createDocument(form) {
        try {
            const formData = new FormData(form);
            const data = {
                title: formData.get('title'),
                content: formData.get('content'),
                category_id: formData.get('category_id'),
                patient_id: formData.get('patient_id')
            };
            
            const response = await fetch(`${this.apiBase}?path=document`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showSuccess('Dokument byl úspěšně vytvořen');
                this.closeModal(form.closest('.modal'));
                this.loadDocuments();
                form.reset();
            } else {
                this.showError(result.error);
            }
        } catch (error) {
            this.showError('Chyba při vytváření dokumentu');
            console.error('Create document error:', error);
        }
    }
    
    async uploadDocument(form) {
        try {
            const formData = new FormData(form);
            
            const response = await fetch(`${this.apiBase}?path=upload`, {
                method: 'POST',
                body: formData
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showSuccess('Dokument byl úspěšně nahrán');
                this.closeModal(form.closest('.modal'));
                this.loadDocuments();
                form.reset();
            } else {
                this.showError(result.error);
            }
        } catch (error) {
            this.showError('Chyba při nahrávání dokumentu');
            console.error('Upload document error:', error);
        }
    }
    
    async createTimestamp(documentId) {
        if (!confirm('Opravdu chcete vytvořit nové časové razítko pro tento dokument?')) {
            return;
        }
        
        try {
            const response = await fetch(`${this.apiBase}?path=timestamp`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ document_id: documentId })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showSuccess('Časové razítko bylo úspěšně vytvořeno');
                // Refresh document detail if on detail page
                if (window.location.pathname.includes('cgm_document_detail.php')) {
                    window.location.reload();
                } else {
                    this.loadDocuments();
                }
            } else {
                this.showError(result.error);
            }
        } catch (error) {
            this.showError('Chyba při vytváření časového razítka');
            console.error('Create timestamp error:', error);
        }
    }
    
    async verifyTimestamp(timestampId) {
        try {
            const response = await fetch(`${this.apiBase}?path=verify-timestamp&id=${timestampId}`);
            const result = await response.json();
            
            if (result.success) {
                const status = result.is_valid ? 'platné' : 'neplatné';
                const message = `Časové razítko je ${status}`;
                
                if (result.is_valid) {
                    this.showSuccess(message);
                } else {
                    this.showWarning(message);
                }
            } else {
                this.showError(result.error);
            }
        } catch (error) {
            this.showError('Chyba při ověřování časového razítka');
            console.error('Verify timestamp error:', error);
        }
    }
    
    filterDocuments() {
        const search = document.getElementById('documentSearch')?.value || '';
        const category = document.getElementById('categoryFilter')?.value || '';
        
        const filters = {};
        if (search) filters.search = search;
        if (category) filters.category_id = category;
        
        this.loadDocuments(filters);
    }
    
    renderDocuments(documents) {
        const container = document.getElementById('documentsContainer');
        if (!container) return;
        
        if (documents.length === 0) {
            container.innerHTML = this.getEmptyState();
            return;
        }
        
        const html = documents.map(doc => this.getDocumentCard(doc)).join('');
        container.innerHTML = html;
    }
    
    getDocumentCard(document) {
        const timestampBadge = document.timestamp_count > 0 
            ? `<span class="timestamp-badge text-white text-xs px-2 py-1 rounded-full">${document.timestamp_count} razítek</span>`
            : '';
            
        return `
            <div class="document-card bg-white rounded-lg p-6">
                <div class="flex items-start justify-between mb-4">
                    <div class="flex-1">
                        <h3 class="font-semibold text-gray-900 mb-2">
                            ${this.escapeHtml(document.title)}
                        </h3>
                        <p class="text-sm text-gray-600 mb-2">
                            ${this.escapeHtml(document.category_name)}
                        </p>
                        <p class="text-xs text-gray-500">
                            ${this.formatDate(document.created_at)}
                        </p>
                    </div>
                    ${timestampBadge}
                </div>
                
                ${document.content ? `
                    <p class="text-sm text-gray-700 mb-4 line-clamp-3">
                        ${this.escapeHtml(document.content.substring(0, 150))}...
                    </p>
                ` : ''}
                
                <div class="flex gap-2">
                    <a href="cgm_document_detail.php?id=${document.id}" 
                       class="flex-1 bg-blue-50 hover:bg-blue-100 text-blue-700 px-4 py-2 rounded text-sm font-medium transition-colors text-center">
                        Zobrazit
                    </a>
                    <button data-action="create-timestamp" data-document-id="${document.id}"
                            class="bg-green-50 hover:bg-green-100 text-green-700 px-4 py-2 rounded text-sm font-medium transition-colors">
                        + Razítko
                    </button>
                </div>
            </div>
        `;
    }
    
    getEmptyState() {
        return `
            <div class="text-center py-12">
                <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                <h3 class="text-lg font-medium text-gray-900 mb-2">Žádné dokumenty</h3>
                <p class="text-gray-600 mb-4">Zatím nemáte žádné dokumenty. Vytvořte svůj první dokument.</p>
                <button data-action="open-create-modal" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                    Vytvořit dokument
                </button>
            </div>
        `;
    }
    
    openCreateModal() {
        const modal = document.getElementById('createModal');
        if (modal) {
            modal.classList.add('active');
        }
    }
    
    closeModal(modal) {
        if (modal) {
            modal.classList.remove('active');
        }
    }
    
    showLoading() {
        // Implementation for loading state
    }
    
    hideLoading() {
        // Implementation for hiding loading state
    }
    
    showSuccess(message) {
        this.showAlert(message, 'success');
    }
    
    showError(message) {
        this.showAlert(message, 'error');
    }
    
    showWarning(message) {
        this.showAlert(message, 'warning');
    }
    
    showAlert(message, type) {
        // Simple alert implementation - can be enhanced with toast notifications
        const alertClass = {
            success: 'bg-green-100 border-green-400 text-green-700',
            error: 'bg-red-100 border-red-400 text-red-700',
            warning: 'bg-yellow-100 border-yellow-400 text-yellow-700'
        }[type];
        
        const alertHtml = `
            <div class="${alertClass} px-4 py-3 rounded mb-6 alert-message">
                ${this.escapeHtml(message)}
                <button onclick="this.parentElement.remove()" class="float-right font-bold">&times;</button>
            </div>
        `;
        
        const container = document.querySelector('.alerts-container') || document.querySelector('main');
        if (container) {
            container.insertAdjacentHTML('afterbegin', alertHtml);
            
            // Auto-remove after 5 seconds
            setTimeout(() => {
                const alert = container.querySelector('.alert-message');
                if (alert) alert.remove();
            }, 5000);
        }
    }
    
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('cs-CZ', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    if (window.location.pathname.includes('cgm_documents') || 
        window.location.pathname.includes('cgm_document_detail')) {
        new CGMDocuments();
    }
});

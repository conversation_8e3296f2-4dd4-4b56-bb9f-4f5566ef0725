<?php
require_once 'config.php';
require_once 'deduct_minutes.php';

// Receive incoming webhook from Twilio
$callSid = $_POST['CallSid'];
$callDuration = $_POST['CallDuration'];
$from = $_POST['From'];

// Get user ID based on phone number (you'll need to implement this function)
$userId = getUserIdByPhoneNumber($from);

// Calculate minutes to deduct (round up to nearest minute)
$minutesToDeduct = ceil($callDuration / 60);

if (deductMinutes($userId, $minutesToDeduct)) {
    header("Content-type: text/xml");
    echo "<Response><Say>Váš hovor byl zahájen.</Say></Response>";
} else {
    header("Content-type: text/xml");
    echo "<Response><Say>Vaše minuty jsou vyčerpány. Dokupte si kredit a zkuste to znovu.</Say></Response>";
}
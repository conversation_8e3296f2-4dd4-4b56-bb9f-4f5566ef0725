<?php
function writeApiLog($message, $data = []) {
    try {
        $logEntry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'message' => $message,
            'data' => $data
        ];

        // Add session info if available
        if (session_status() === PHP_SESSION_ACTIVE && isset($_SESSION['user_id'])) {
            $logEntry['user_id'] = $_SESSION['user_id'];
        }

        // Add request info
        $logEntry['request'] = [
            'url' => $_SERVER['REQUEST_URI'] ?? 'unknown',
            'method' => $_SERVER['REQUEST_METHOD'] ?? 'unknown',
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
        ];

        $logMessage = json_encode($logEntry, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        
        // Log to file
        $logDir = __DIR__ . '/logs';
        $logFile = $logDir . '/vapi_api_' . date('Y-m-d') . '.log';
        
        // Create logs directory if it doesn't exist
        if (!is_dir($logDir)) {
            if (!mkdir($logDir, 0755, true)) {
                error_log("Failed to create log directory: " . $logDir);
                return;
            }
        }

        // Check if directory is writable
        if (!is_writable($logDir)) {
            error_log("Log directory is not writable: " . $logDir);
            return;
        }
        
        // Append to log file
        if (file_put_contents($logFile, $logMessage . "\n", FILE_APPEND) === false) {
            error_log("Failed to write to log file: " . $logFile);
            return;
        }
        
        // Also log to error_log for server logs
        error_log($message . ': ' . json_encode($data, JSON_UNESCAPED_UNICODE));
        
    } catch (Exception $e) {
        error_log("Error in writeApiLog: " . $e->getMessage());
    }
}


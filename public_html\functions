<?php

/**
 * Updates the minute limit for a specific user
 * 
 * @param int $user_id The ID of the user
 * @param int $limit The new minute limit to set
 * @return bool Returns true if update was successful, false otherwise
 */
function updateUserMinuteLimit($user_id, $limit) {
    global $conn;
    
    try {
        // Prepare the SQL statement
        $stmt = $conn->prepare("UPDATE users SET minute_limit = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
        
        // Bind parameters and execute
        $stmt->bind_param("ii", $limit, $user_id);
        $result = $stmt->execute();
        
        if ($result) {
            // Log the update for tracking
            writeLog('Minute limit updated', [
                'user_id' => $user_id,
                'new_limit' => $limit,
                'timestamp' => date('Y-m-d H:i:s')
            ]);
            return true;
        }
        
        return false;
    } catch (Exception $e) {
        writeErrorLog('Update minute limit error', [
            'message' => $e->getMessage(),
            'user_id' => $user_id,
            'limit' => $limit
        ]);
        return false;
    }
}

/**
 * Gets the current minute limit for a user
 * 
 * @param int $user_id The ID of the user
 * @return int|null Returns the minute limit or null if not found
 */
function getUserMinuteLimit($user_id) {
    global $conn;
    
    try {
        $stmt = $conn->prepare("SELECT minute_limit FROM users WHERE id = ?");
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        
        $result = $stmt->get_result();
        if ($row = $result->fetch_assoc()) {
            return $row['minute_limit'];
        }
        
        return null;
    } catch (Exception $e) {
        writeErrorLog('Get minute limit error', [
            'message' => $e->getMessage(),
            'user_id' => $user_id
        ]);
        return null;
    }
}


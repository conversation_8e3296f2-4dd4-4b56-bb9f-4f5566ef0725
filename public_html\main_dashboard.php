<?php
/**
 * Hlavní dashboard - zachovává původní VAPI funkcionalita + přidává nové funkce
 */

require_once 'config_optimized.php';
require_once 'baserow_functions.php';
require_once 'api_functions.php';
require_once 'direct_vapi_functions.php';

// Kontrola přihlášení
requireLogin();

// Získání VAPI dat - zachováváme původní funkcionalitu
$dateRange = $_GET['range'] ?? '7';
$metrics = [];
$graphData = ['calls' => [], 'dates' => [], 'peakHours' => [], 'hours' => []];
$analytics = [];
$peakHours = [];

try {
    $apiConfig = getCurrentUserApiKey();
    if ($apiConfig) {
        // Získání dat z VAPI - původní funkcionalita
        $analytics = getAnalytics($dateRange, $apiConfig['assistant_id']);
        $peakHours = getPeakHours($dateRange, $apiConfig['assistant_id']);
        $metrics = getMetrics($analytics);

        if (!empty($analytics['data'])) {
            $graphData['calls'] = array_reverse(array_column($analytics['data'], 'total_calls'));
            $graphData['dates'] = array_reverse(array_column($analytics['data'], 'date'));
        }

        if (!empty($peakHours['data'])) {
            $graphData['peakHours'] = array_column($peakHours['data'], 'total_calls');
            $graphData['hours'] = array_column($peakHours['data'], 'hour');
        }
    } else {
        // Fallback na mock data pokud není VAPI klíč
        $metrics = getDefaultMetrics();
        $graphData = getDefaultGraphData();
    }
} catch (Exception $e) {
    writeErrorLog("Error loading dashboard data", ['error' => $e->getMessage()]);
    $metrics = getDefaultMetrics();
    $graphData = getDefaultGraphData();
}

// Získání statistik pacientů z Baserow - nová funkcionalita
$patientStats = ['total' => 0, 'recent' => 0, 'error' => null];
try {
    $patientsResult = getPatients(1, 10, '', null, false);
    if ($patientsResult['success']) {
        $patientStats['total'] = $patientsResult['totalRecords'] ?? 0;
        $patientStats['recent'] = count($patientsResult['patients'] ?? []);
    } else {
        $patientStats['error'] = $patientsResult['error'] ?? 'Chyba při načítání pacientů';
    }
} catch (Exception $e) {
    $patientStats['error'] = 'Chyba připojení k Baserow: ' . $e->getMessage();
}

$currentPage = 'dashboard';
?>

<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dentibot Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>
    <style>
        body { font-family: 'Inter', sans-serif; }
        .sidebar {
            width: 16rem;
            background: linear-gradient(135deg, #1e3a8a, #1e40af);
            color: white;
            position: fixed;
            height: 100vh;
            left: 0;
            top: 0;
            z-index: 100;
            overflow-y: auto;
        }
        .main-content {
            margin-left: 16rem;
            min-height: 100vh;
        }
        .nav-link {
            display: flex;
            align-items: center;
            padding: 0.75rem 1.5rem;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.2s;
            cursor: pointer;
        }
        .nav-link:hover, .nav-link.active {
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }
        .nav-icon {
            width: 1.25rem;
            height: 1.25rem;
            margin-right: 0.75rem;
        }
        .metric-card {
            background: white;
            border-radius: 0.5rem;
            padding: 1.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="p-6 border-b border-blue-600">
            <div class="flex items-center">
                <div class="w-8 h-8 bg-white rounded mr-3 flex items-center justify-center">
                    <span class="text-blue-600 font-bold text-sm">D</span>
                </div>
                <span class="text-xl font-bold">Dentibot</span>
            </div>
            <div class="mt-3 text-sm text-blue-200">
                <div><?php echo htmlspecialchars($_SESSION['username'] ?? 'Demo User'); ?></div>
                <div class="text-xs"><?php echo ucfirst(getUserSubscriptionPlan($_SESSION['user_id'])); ?> plán</div>
            </div>
        </div>
        <nav class="mt-6">
            <a href="main_dashboard.php" class="nav-link active">
                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <rect x="3" y="3" width="18" height="18" rx="2"/><path d="M3 9h18"/><path d="M9 21V9"/>
                </svg>
                Dashboard
            </a>
            <a href="call_history.php" class="nav-link">
                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
                </svg>
                Historie hovorů
            </a>
            <a href="sms_campaigns.php" class="nav-link">
                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
                </svg>
                Hromadné SMS
            </a>
            <a href="patients_list.php" class="nav-link">
                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/><circle cx="9" cy="7" r="4"/><path d="M23 21v-2a4 4 0 0 0-3-3.87"/><path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                </svg>
                Seznam pacientů
                <?php if ($patientStats['total'] > 0): ?>
                    <span class="ml-2 bg-blue-500 text-white text-xs px-2 py-1 rounded-full"><?php echo $patientStats['total']; ?></span>
                <?php endif; ?>
            </a>
            <a href="patient_cards.php" class="nav-link">
                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/><polyline points="14,2 14,8 20,8"/><line x1="16" y1="13" x2="8" y2="13"/><line x1="16" y1="17" x2="8" y2="17"/><polyline points="10,9 9,9 8,9"/><circle cx="12" cy="15" r="2"/>
                </svg>
                Karty pacientů
                <span class="ml-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full">auto</span>
            </a>
            <a href="demo_events.html" class="nav-link">
                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                </svg>
                Demo události
                <span class="ml-2 bg-orange-500 text-white text-xs px-2 py-1 rounded-full">test</span>
            </a>
            <a href="settings.php" class="nav-link">
                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <circle cx="12" cy="12" r="3"/><path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
                </svg>
                Nastavení
            </a>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <div class="p-8">
            <!-- Header -->
            <div class="mb-8">
                <h1 class="text-3xl font-bold text-gray-900">Dashboard</h1>
                <p class="text-gray-600 mt-2">Přehled aktivit a statistik vaší ordinace</p>
            </div>

            <!-- VAPI API Warning -->
            <?php if (!$apiConfig): ?>
                <div class="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-6">
                    <strong class="font-bold">Upozornění!</strong>
                    <span class="block sm:inline"> Není nastaven VAPI API klíč. Pro zobrazení statistik prosím </span>
                    <a href="settings.php" class="underline">nastavte svůj Vapi.ai API klíč</a>.
                </div>
            <?php endif; ?>

            <!-- Baserow Warning -->
            <?php if ($patientStats['error']): ?>
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                    <strong class="font-bold">Baserow chyba:</strong>
                    <span class="block sm:inline"><?php echo htmlspecialchars($patientStats['error']); ?></span>
                    <a href="settings.php" class="underline">Zkontrolujte nastavení Baserow</a>.
                </div>
            <?php endif; ?>

            <!-- Metrics Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <?php foreach ($metrics as $metric): ?>
                    <div class="metric-card">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600"><?php echo htmlspecialchars($metric['label']); ?></p>
                                <p class="text-2xl font-bold text-gray-900"><?php echo htmlspecialchars($metric['value']); ?></p>
                            </div>
                            <div class="text-blue-600">
                                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z"/>
                                </svg>
                            </div>
                        </div>
                        <div class="mt-2">
                            <span class="text-green-600 text-sm font-medium"><?php echo htmlspecialchars($metric['trend']); ?></span>
                            <span class="text-gray-500 text-sm"><?php echo htmlspecialchars($metric['subValue']); ?></span>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

            <!-- Charts -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                <div class="bg-white rounded-lg shadow-sm border p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Hovory za posledních <?php echo $dateRange; ?> dní</h3>
                    <div style="position: relative; height: 300px;">
                        <canvas id="callsChart"></canvas>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow-sm border p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Špičkové hodiny</h3>
                    <div style="position: relative; height: 300px;">
                        <canvas id="peakHoursChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="bg-white rounded-lg shadow-sm border p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Pacienti</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Celkem pacientů</span>
                            <span class="font-semibold"><?php echo $patientStats['total']; ?></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Nedávno načteno</span>
                            <span class="font-semibold"><?php echo $patientStats['recent']; ?></span>
                        </div>
                        <a href="patients_list.php" class="block w-full bg-blue-600 hover:bg-blue-700 text-white text-center py-2 rounded-lg transition-colors">
                            Zobrazit všechny
                        </a>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-sm border p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Automatické karty</h3>
                    <div class="space-y-3">
                        <p class="text-sm text-gray-600">Nový systém automatických karet pacientů je aktivní.</p>
                        <a href="patient_cards.php" class="block w-full bg-green-600 hover:bg-green-700 text-white text-center py-2 rounded-lg transition-colors">
                            Zobrazit karty
                        </a>
                        <a href="demo_events.html" class="block w-full bg-orange-600 hover:bg-orange-700 text-white text-center py-2 rounded-lg transition-colors">
                            Demo události
                        </a>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-sm border p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Nastavení</h3>
                    <div class="space-y-3">
                        <p class="text-sm text-gray-600">Konfigurace API klíčů a integrace.</p>
                        <a href="settings.php" class="block w-full bg-gray-600 hover:bg-gray-700 text-white text-center py-2 rounded-lg transition-colors">
                            Otevřít nastavení
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize charts with data from PHP
        const graphData = <?php echo json_encode($graphData); ?>;

        // Calls Chart
        const callsCtx = document.getElementById('callsChart');
        if (callsCtx) {
            new Chart(callsCtx, {
                type: 'line',
                data: {
                    labels: graphData.dates.length ? graphData.dates : ['1.1', '2.1', '3.1', '4.1', '5.1', '6.1', '7.1'],
                    datasets: [{
                        label: 'Počet hovorů',
                        data: graphData.calls.length ? graphData.calls : [45, 52, 38, 67, 73, 89, 94],
                        borderColor: '#3b82f6',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { display: false } },
                    scales: { y: { beginAtZero: true } },
                    animation: { duration: 0 }
                }
            });
        }

        // Peak Hours Chart
        const peakCtx = document.getElementById('peakHoursChart');
        if (peakCtx) {
            new Chart(peakCtx, {
                type: 'bar',
                data: {
                    labels: graphData.hours.length ? graphData.hours : ['8:00', '9:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00'],
                    datasets: [{
                        label: 'Počet hovorů',
                        data: graphData.peakHours.length ? graphData.peakHours : [12, 25, 45, 67, 43, 23, 56, 78, 34],
                        backgroundColor: '#10b981',
                        borderRadius: 4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { display: false } },
                    scales: { y: { beginAtZero: true } },
                    animation: { duration: 0 }
                }
            });
        }
    </script>

<?php
// Helper functions pro fallback data
function getDefaultMetrics() {
    return [
        ['label' => 'Celkem hovorů', 'value' => '1,247', 'trend' => '+12%', 'subValue' => 'vs minulý měsíc'],
        ['label' => 'Úspěšné hovory', 'value' => '1,156', 'trend' => '+8%', 'subValue' => 'vs minulý měsíc'],
        ['label' => 'Spotřeba minut', 'value' => '347', 'trend' => '+15%', 'subValue' => 'z 1000 minut'],
        ['label' => 'Průměrná délka', 'value' => '2:34', 'trend' => '-3%', 'subValue' => 'vs minulý měsíc']
    ];
}

function getDefaultGraphData() {
    return [
        'calls' => [45, 52, 38, 67, 73, 89, 94],
        'dates' => ['1.1', '2.1', '3.1', '4.1', '5.1', '6.1', '7.1'],
        'peakHours' => [12, 25, 45, 67, 43, 23, 56, 78, 34],
        'hours' => ['8:00', '9:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00']
    ];
}
?>
</body>
</html>

<div class="tab-content active" id="appearance">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Colors Section -->
        <div class="space-y-4">
            <h3 class="text-lg font-semibold text-gray-900">Barvy</h3>
            
            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <label for="primaryColor" class="text-sm font-medium text-gray-700">
                        Primární barva
                    </label>
                    <input 
                        type="color" 
                        id="primaryColor" 
                        name="primaryColor" 
                        value="#4fd1c5"
                        class="w-20 h-10"
                        onchange="updatePreview()"
                    >
                </div>

                <div class="flex items-center justify-between">
                    <label for="secondaryColor" class="text-sm font-medium text-gray-700">
                        Sekundární barva
                    </label>
                    <input 
                        type="color" 
                        id="secondaryColor" 
                        name="secondaryColor" 
                        value="#38b2ac"
                        class="w-20 h-10"
                        onchange="updatePreview()"
                    >
                </div>

                <div class="flex items-center justify-between">
                    <label for="backgroundColor" class="text-sm font-medium text-gray-700">
                        Barva pozadí
                    </label>
                    <input 
                        type="color" 
                        id="backgroundColor" 
                        name="backgroundColor" 
                        value="#ffffff"
                        class="w-20 h-10"
                        onchange="updatePreview()"
                    >
                </div>
            </div>
        </div>

        <!-- Avatar Section -->
        <div class="space-y-4">
            <h3 class="text-lg font-semibold text-gray-900">Avatar</h3>
            
            <div class="space-y-4">
                <div>
                    <label for="avatarUrl" class="block text-sm font-medium text-gray-700">
                        URL obrázku avatara
                    </label>
                    <input 
                        type="text" 
                        id="avatarUrl" 
                        name="avatarUrl" 
                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        placeholder="https://example.com/avatar.png"
                        onchange="updatePreview()"
                    >
                </div>

                <div class="flex items-center">
                    <input 
                        type="checkbox" 
                        id="showAvatar" 
                        name="showAvatar" 
                        class="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        onchange="updatePreview()"
                    >
                    <label for="showAvatar" class="ml-2 block text-sm text-gray-900">
                        Zobrazit avatar
                    </label>
                </div>
            </div>
        </div>
    </div>
</div>


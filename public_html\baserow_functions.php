<?php
// Definujeme funkci getBaserowConfig pouze jednou
// FIXED: Improved getBaserowConfig function to handle direct API tests
function getBaserowConfig() {
   global $mysqli;
   
   // Check if we have cached config to avoid repeated database queries
   static $cachedConfig = null;
   if ($cachedConfig !== null) {
       return $cachedConfig;
   }
   
   // First try to get config from session user
   if (isset($_SESSION['user_id'])) {
       $stmt = $mysqli->prepare("SELECT baserow_api_token, baserow_database_id, baserow_table_id FROM users WHERE id = ?");
       $stmt->bind_param("i", $_SESSION['user_id']);
       $stmt->execute();
       $result = $stmt->get_result();
       $config = $result->fetch_assoc();
       
       if (isset($config['baserow_api_token']) && !empty($config['baserow_api_token']) && 
           isset($config['baserow_database_id']) && !empty($config['baserow_database_id']) && 
           isset($config['baserow_table_id']) && !empty($config['baserow_table_id'])) {
           
           // Cache the config for future calls
           $cachedConfig = $config;
           
           writeErrorLog('Baserow Config Retrieved from user session', [
               'database_id' => $config['baserow_database_id'],
               'table_id' => $config['baserow_table_id'],
               'api_token_length' => strlen($config['baserow_api_token'])
           ]);
           
           return $config;
       }
   }
   
   // If session user doesn't have config, try to get from any admin user
   $stmt = $mysqli->prepare("SELECT baserow_api_token, baserow_database_id, baserow_table_id FROM users WHERE role = 'admin' AND baserow_api_token IS NOT NULL AND baserow_api_token != '' LIMIT 1");
   $stmt->execute();
   $result = $stmt->get_result();
   $config = $result->fetch_assoc();
   
   if (!isset($config['baserow_api_token']) || empty($config['baserow_api_token']) || 
       !isset($config['baserow_database_id']) || empty($config['baserow_database_id']) || 
       !isset($config['baserow_table_id']) || empty($config['baserow_table_id'])) {
       
       // As a last resort, try to get from config.php constants
       if (defined('BASEROW_API_TOKEN') && defined('BASEROW_DATABASE_ID') && defined('BASEROW_TABLE_ID')) {
           $config = [
               'baserow_api_token' => BASEROW_API_TOKEN,
               'baserow_database_id' => BASEROW_DATABASE_ID,
               'baserow_table_id' => BASEROW_TABLE_ID
           ];
           
           // Cache the config for future calls
           $cachedConfig = $config;
           
           writeErrorLog('Baserow Config Retrieved from constants', [
               'database_id' => $config['baserow_database_id'],
               'table_id' => $config['baserow_table_id'],
               'api_token_length' => strlen($config['baserow_api_token'])
           ]);
           
           return $config;
       }
       
       throw new Exception("Chybí konfigurace Baserow. Prosím, nastavte API token, Database ID a Table ID v nastavení.");
   }
   
   // Cache the config for future calls
   $cachedConfig = $config;
   
   writeErrorLog('Baserow Config Retrieved from admin user', [
       'database_id' => $config['baserow_database_id'],
       'table_id' => $config['baserow_table_id'],
       'api_token_length' => strlen($config['baserow_api_token'])
   ]);
   
   return $config;
}

require_once 'config.php';

// FIXED: Simplified baserowRequest function with direct URL construction
function baserowRequest($method, $endpoint, $data = null) {
    try {
        $config = getBaserowConfig();
        
        // Check if this is a PATCH request with a numeric ID
        if ($method === 'PATCH' && preg_match('/^(\d+)/', $endpoint, $matches)) {
            $patientId = $matches[1];
            
            // Construct URL directly
            $url = "https://api.baserow.io/api/database/rows/table/{$config['baserow_table_id']}/{$patientId}/";
            
            // Add query parameters if present
            if (strpos($endpoint, '?') !== false) {
                $url .= substr($endpoint, strpos($endpoint, '?'));
            }
        } else {
            // For other requests (GET, POST, etc.)
            $endpoint = ltrim($endpoint, '/');
            $url = "https://api.baserow.io/api/database/rows/table/{$config['baserow_table_id']}/" . $endpoint;
        }
        
        error_log("Baserow Request URL: " . $url);
        error_log("Baserow Request Method: " . $method);
        error_log("Baserow Request Data: " . ($data ? json_encode($data) : 'null'));
        
        $headers = [
            "Authorization: Token {$config['baserow_api_token']}",
            "Content-Type: application/json"
        ];

        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        
        if ($data !== null) {
            $jsonData = json_encode($data);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);
            error_log("Baserow Request JSON: " . $jsonData);
        }

        $response = curl_exec($ch);
        $statusCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        
        curl_close($ch);

        error_log("Baserow Response Status: " . $statusCode);
        error_log("Baserow Response: " . substr($response, 0, 500));

        if ($curlError) {
            error_log("Curl Error: " . $curlError);
            throw new Exception("Curl error: " . $curlError);
        }

        if ($statusCode >= 400) {
            $errorResponse = json_decode($response, true);
            $errorMessage = isset($errorResponse['error']) 
                ? $errorResponse['error'] 
                : "Neznámá chyba (Status: $statusCode)";
            
            error_log("Baserow API Error: " . $errorMessage);
            throw new Exception("Chyba API: " . $errorMessage);
        }

        $decodedResponse = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            error_log("JSON Decode Error: " . json_last_error_msg());
            throw new Exception("Chyba při dekódování JSON odpovědi: " . json_last_error_msg());
        }

        return $decodedResponse;
    } catch (Exception $e) {
        error_log("Baserow Request Error: " . $e->getMessage());
        throw $e;
    }
}

// Rest of the functions remain unchanged
/**
* Převede datum z UTC do časového pásma Europe/Prague
* a formátuje ho do českého formátu
*/
function convertAndFormatDateTime($dateTimeString) {
   if (empty($dateTimeString)) {
       return '';
   }
   
   try {
       // Vytvoříme DateTime objekt s předpokladem, že vstupní datum je v UTC
       $dateTime = new DateTime($dateTimeString, new DateTimeZone('UTC'));
       
       // Převedeme do časového pásma Europe/Prague
       $dateTime->setTimezone(new DateTimeZone('Europe/Prague'));
       
       // Formátujeme do českého formátu
       return $dateTime->format('d.m.Y H:i');
   } catch (Exception $e) {
       error_log("Error converting date: " . $e->getMessage() . ", original date: " . $dateTimeString);
       return $dateTimeString; // Vrátíme původní hodnotu v případě chyby
   }
}

/**
* Převede datum z českého formátu (d.m.Y H:i) na ISO formát pro Baserow
* Baserow očekává datum ve formátu ISO 8601 (YYYY-MM-DDTHH:MM:SSZ)
*/
function convertToBaserowDateFormat($dateTimeString) {
   if (empty($dateTimeString) || $dateTimeString === '-') {
       return null;
   }
   
   try {
       error_log("Converting date to Baserow format: " . $dateTimeString);
       
       // Nejprve zkontrolujeme, zda je již ve formátu ISO
       if (preg_match('/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}/', $dateTimeString)) {
           // Už je ve správném formátu, jen zajistíme, že končí Z pro UTC
           if (substr($dateTimeString, -1) !== 'Z') {
               return $dateTimeString . 'Z';
           }
           return $dateTimeString;
       }
       
       // Zkusíme zparsovat český formát (d.m.Y H:i)
       if (preg_match('/^(\d{1,2})\.(\d{1,2})\.(\d{4})\s+(\d{1,2}):(\d{1,2})$/', $dateTimeString, $matches)) {
           // Vytvoříme DateTime objekt s časovou zónou Europe/Prague
           $dateTime = new DateTime();
           $dateTime->setTimezone(new DateTimeZone('Europe/Prague'));
           $dateTime->setDate((int)$matches[3], (int)$matches[2], (int)$matches[1]);
           $dateTime->setTime((int)$matches[4], (int)$matches[5]);
           
           // Převedeme do UTC pro Baserow
           $dateTime->setTimezone(new DateTimeZone('UTC'));
           
           // Vrátíme v ISO formátu
           $result = $dateTime->format('Y-m-d\TH:i:s\Z');
           error_log("Converted date (d.m.Y H:i format): " . $dateTimeString . " -> " . $result);
           return $result;
       }
       
       // Pokud se nepodaří zparsovat jako d.m.Y H:i, zkusíme jen d.m.Y
       if (preg_match('/^(\d{1,2})\.(\d{1,2})\.(\d{4})$/', $dateTimeString, $matches)) {
           // Vytvoříme DateTime objekt s časovou zónou Europe/Prague
           $dateTime = new DateTime();
           $dateTime->setTimezone(new DateTimeZone('Europe/Prague'));
           $dateTime->setDate((int)$matches[3], (int)$matches[2], (int)$matches[1]);
           $dateTime->setTime(0, 0);
           
           // Převedeme do UTC pro Baserow
           $dateTime->setTimezone(new DateTimeZone('UTC'));
           
           // Vrátíme v ISO formátu
           $result = $dateTime->format('Y-m-d\TH:i:s\Z');
           error_log("Converted date (d.m.Y format): " . $dateTimeString . " -> " . $result);
           return $result;
       }
       
       // Zkusíme zparsovat formát datetime-local (YYYY-MM-DDTHH:MM)
       if (preg_match('/^(\d{4})-(\d{2})-(\d{2})T(\d{2}):(\d{2})$/', $dateTimeString, $matches)) {
           // Vytvoříme DateTime objekt s časovou zónou Europe/Prague
           $dateTime = new DateTime();
           $dateTime->setTimezone(new DateTimeZone('Europe/Prague'));
           $dateTime->setDate((int)$matches[1], (int)$matches[2], (int)$matches[3]);
           $dateTime->setTime((int)$matches[4], (int)$matches[5]);
           
           // Převedeme do UTC pro Baserow
           $dateTime->setTimezone(new DateTimeZone('UTC'));
           
           // Vrátíme v ISO formátu
           $result = $dateTime->format('Y-m-d\TH:i:s\Z');
           error_log("Converted date (YYYY-MM-DDTHH:MM format): " . $dateTimeString . " -> " . $result);
           return $result;
       }
       
       // Pokusíme se přímý převod přes strtotime
       $timestamp = strtotime($dateTimeString);
       if ($timestamp === false) {
           error_log("Error parsing date: " . $dateTimeString);
           return null;
       }
       
       // Vytvoříme DateTime s časovou zónou
       $dateTime = new DateTime();
       $dateTime->setTimestamp($timestamp);
       $dateTime->setTimezone(new DateTimeZone('UTC'));
       
       $result = $dateTime->format('Y-m-d\TH:i:s\Z');
       error_log("Converted date (generic format): " . $dateTimeString . " -> " . $result);
       return $result;
   } catch (Exception $e) {
       error_log("Error converting date for Baserow: " . $e->getMessage() . ", original date: " . $dateTimeString);
       return null;
   }
}

// FIXED: Updated updateExaminationDateInBaserow function
function updateExaminationDateInBaserow($patientId, $dateValue) {
 try {
     // Podrobné logování vstupních dat
     writeErrorLog('Update Examination Date Input', [
         'patient_id' => $patientId,
         'date_value' => $dateValue
     ]);
     
     // Převedeme datum do formátu, který Baserow očekává
     $formattedDate = convertToBaserowDateFormat($dateValue);
     
     if ($formattedDate === null) {
         writeErrorLog('Date Conversion Failed', [
             'original_date' => $dateValue
         ]);
         return false;
     }
     
     // Připravíme data pro aktualizaci
     // Podle dokumentace API je ID pole pro datum prohlídky "Datum_prohlidky"
     $updateData = [
         'Datum_prohlidky' => $formattedDate
     ];
     
     writeErrorLog('Updating Examination Date', [
         'patient_id' => $patientId,
         'original_date' => $dateValue,
         'formatted_date' => $formattedDate,
         'update_data' => $updateData
     ]);
     
     // FIXED: Use the improved baserowRequest function
     $response = baserowRequest('PATCH', $patientId . '?user_field_names=true', $updateData);
     
     if (!is_array($response)) {
         writeErrorLog('Unexpected Response', [
             'response' => $response
         ]);
         return false;
     }
     
     writeErrorLog('Successfully Updated Examination Date', [
         'patient_id' => $patientId,
         'response' => $response
     ]);
     
     return true;
 } catch (Exception $e) {
     writeErrorLog('Error Updating Examination Date', [
         'error' => $e->getMessage(),
         'trace' => $e->getTraceAsString()
     ]);
     return false;
 }
}

// FIXED: getPatients function to include row_id
function getPatients($page = 1, $perPage = 20, $search = '', $singlePatientId = null, $loadAll = false) {
   try {
       error_log("=== GET_PATIENTS START ===");
       error_log("Search term: " . $search);
       error_log("Page: " . $page);
       error_log("Per page: " . $perPage);

       $config = getBaserowConfig();
       
       $params = [
           'user_field_names' => 'true',
           'size' => $perPage,
           'page' => $page
       ];
       
       if ($loadAll) {
           $params['size'] = 1000000;
           $params['page'] = 1;
       }
       
       if ($singlePatientId) {
           $params['filter__id__equal'] = $singlePatientId;
       } elseif (!empty($search)) {
           // Vylepšené vyhledávání přes více polí
           $searchEncoded = urlencode($search);
           $params['filter_type'] = 'OR';
           
           // Vyhledávání v různých polích s různými názvy
           $searchFields = [
               'Jmeno_pacienta',
               'jmeno_pacienta',
               'jmeno',
               'Emailova_adresa',
               'emailova_adresa',
               'email',
               'Telefonni_cislo',
               'telefonni_cislo',
               'telefon'
           ];
           
           foreach ($searchFields as $field) {
               $params["filter__" . $field . "__contains"] = $searchEncoded;
           }
           
           error_log("Search params: " . json_encode($params));
       }
       
       $queryString = http_build_query($params);
       error_log("Final query string: " . $queryString);
       
       $response = baserowRequest('GET', '?' . $queryString);
       
       error_log("Raw response: " . json_encode($response));

       if (!isset($response['results'])) {
           throw new Exception("Neočekávaná struktura odpovědi z Baserow");
       }

       // Mapování pacientů s debugováním
       $patients = array_map(function($record) {
           error_log("Processing record: " . json_encode($record));
           
           // IMPORTANT: Get the row_id from the Baserow response
           $row_id = $record['id'] ?? null;
           
           // Výpis všech klíčů v záznamu pro debugování
           error_log("Record keys: " . implode(", ", array_keys($record)));
           
           // Pokus se najít správné názvy polí
           $name = $record['Jmeno_pacienta'] ?? $record['jmeno_pacienta'] ?? $record['jmeno'] ?? '';
           $email = $record['Emailova_adresa'] ?? $record['emailova_adresa'] ?? $record['email'] ?? '';
           $phone = $record['Telefonni_cislo'] ?? $record['telefonni_cislo'] ?? $record['telefon'] ?? '';
           
           // Get patient ID (might be different from row_id)
           $patient_id = $record['id'] ?? null;
           
           // Získání hodnoty pole "Čeká na přidělení termínu" - rozšířené hledání
           $waitingStatus = $record['Ceka_na_prideleni_terminu'] ?? 
                           $record['ceka_na_prideleni_terminu'] ?? 
                           $record['Čeká na přidělení termínu'] ?? 
                           $record['čeká na přidělení termínu'] ?? 
                           $record['waiting_status'] ?? null;
           
           // Podrobné logování pro debugging
           error_log("Raw waiting status: " . (is_array($waitingStatus) ? json_encode($waitingStatus) : (string)$waitingStatus));
           
           $waitingStatusText = '';
           
           // Vylepšené zpracování hodnoty single select pole
           if (is_array($waitingStatus)) {
               error_log("Waiting status is array: " . json_encode($waitingStatus));
               if (isset($waitingStatus['value'])) {
                   $waitingStatusText = $waitingStatus['value'];
               } elseif (isset($waitingStatus['name'])) {
                   $waitingStatusText = $waitingStatus['name'];
               } elseif (isset($waitingStatus['text'])) {
                   $waitingStatusText = $waitingStatus['text'];
               } else {
                   $waitingStatusText = json_encode($waitingStatus);
               }
           } elseif (is_string($waitingStatus)) {
               $waitingStatusText = $waitingStatus;
           } elseif (is_numeric($waitingStatus)) {
               // Může být ID hodnoty
               $waitingStatusText = $waitingStatus == 1 ? 'Čeká' : 'Nečeká';
           } else {
               $waitingStatusText = 'Nečeká'; // Výchozí hodnota
               error_log("Unknown waiting status type: " . gettype($waitingStatus));
               if ($waitingStatus !== null) {
                   error_log("Waiting status value: " . json_encode($waitingStatus));
               }
           }
           
           // Získání a zpracování pole "Datum prohlídky"
           $examinationDate = $record['Datum_prohlidky'] ?? 
                             $record['datum_prohlidky'] ?? 
                             $record['Datum prohlídky'] ?? 
                             $record['datum prohlídky'] ?? null;

           // Podrobné logování pro debugging
           error_log("Raw examination date: " . (is_string($examinationDate) ? $examinationDate : json_encode($examinationDate)));

           $formattedDate = '';

           // Zpracování datumu prohlídky s ohledem na časové pásmo
           if (!empty($examinationDate)) {
               if (is_string($examinationDate)) {
                   // Převedeme datum z UTC do Europe/Prague a formátujeme
                   $formattedDate = convertAndFormatDateTime($examinationDate);
                   error_log("Original date: " . $examinationDate . ", Formatted date: " . $formattedDate);
               } else {
                   $formattedDate = is_array($examinationDate) ? json_encode($examinationDate) : (string)$examinationDate;
               }
           }
           
           // Získání hodnot pro další pole
           $akutni = $record['akutní'] ?? '';
           $brouseni = $record['broušení'] ?? '';
           $endo = $record['endo'] ?? '';
           $extrakce_chirurgie = $record['extrakce, chirurgie'] ?? '';
           $postendo = $record['postendo'] ?? '';
           $predni_protetiky = $record['předání protetiky'] ?? '';
           $sanace_dite = $record['sanace - dítě'] ?? '';
           $sanace_dospely = $record['sanace - dospělý'] ?? '';
           $snimatelna_protetika = $record['snímatelná protetika - otisky'] ?? '';
           
           $patient = [
               'id' => $patient_id,
               'row_id' => $row_id, // IMPORTANT: Include the row_id
               'name' => $name,
               'email' => $email,
               'phone' => $phone,
               'waiting_status' => $waitingStatusText,
               'examination_date' => $formattedDate,
               'akutni' => $akutni,
               'brouseni' => $brouseni,
               'endo' => $endo,
               'extrakce_chirurgie' => $extrakce_chirurgie,
               'postendo' => $postendo,
               'predni_protetiky' => $predni_protetiky,
               'sanace_dite' => $sanace_dite,
               'sanace_dospely' => $sanace_dospely,
               'snimatelna_protetika' => $snimatelna_protetika
           ];
           
           error_log("Mapped patient: " . json_encode($patient));
           return $patient;
           
       }, $response['results']);
       
       $result = [
           'success' => true,
           'patients' => $patients,
           'totalRecords' => $response['count'] ?? count($patients),
           'debug' => [
               'search' => $search,
               'params' => $params,
               'recordsFound' => count($patients)
           ]
       ];
       
       error_log("Final result: " . json_encode($result));
       
       return $result;
       
   } catch (Exception $e) {
       error_log("Error in getPatients: " . $e->getMessage());
       error_log("Stack trace: " . $e->getTraceAsString());
       
       return [
           'success' => false,
           'error' => $e->getMessage(),
           'debug' => [
               'search' => $search,
               'error' => $e->getMessage(),
               'trace' => $e->getTraceAsString()
           ]
       ];
   }
}
function syncPatientsWithBaserow() {
   try {
       writeErrorLog('Sync Started', ['time' => date('Y-m-d H:i:s')]);
       $totalSynced = 0;
       $page = 1;
       $perPage = 100;

       do {
           writeErrorLog('Fetching page', ['page' => $page, 'perPage' => $perPage]);
           $response = getPatients($page, $perPage);
           
           if (!$response['success']) {
               throw new Exception($response['error']);
           }
           
           $patients = $response['patients'];

           writeErrorLog('Page fetched', [
               'page' => $page,
               'patients_count' => count($patients),
               'total_records' => $response['totalRecords'],
               'has_next_page' => $response['hasNextPage']
           ]);

           $totalSynced += count($patients);

           writeErrorLog('Sync Progress', [
               'page' => $page,
               'patients_processed' => count($patients),
               'total_synced' => $totalSynced
           ]);
           
           $page++;
       } while ($response['hasNextPage']);

       writeErrorLog('Sync Finished', [
           'total_synced' => $totalSynced,
           'time' => date('Y-m-d H:i:s')
       ]);

       return $totalSynced;
   } catch (Exception $e) {
       writeErrorLog('Sync Error', [
           'error' => $e->getMessage(),
           'trace' => $e->getTraceAsString()
       ]);
       throw new Exception("Chyba při synchronizaci s Baserow: " . $e->getMessage());
   }
}

// Funkce pro šifrování a dešifrování dat zůstávají nezměněny
function encryptData($data) {
   if (empty($data)) return '';
   $iv = openssl_random_pseudo_bytes(openssl_cipher_iv_length('aes-256-cbc'));
   $encrypted = openssl_encrypt($data, 'aes-256-cbc', ENCRYPTION_KEY, 0, $iv);
   return base64_encode($encrypted . '::' . $iv);
}

function decryptData($data) {
   if (empty($data)) return '';
   list($encrypted_data, $iv) = explode('::', base64_decode($data), 2);
   return openssl_decrypt($encrypted_data, 'aes-256-cbc', ENCRYPTION_KEY, 0, $iv);
}

// FIXED: Improved testDirectBaserowUpdate function
function testDirectBaserowUpdate($patientId, $field, $value) {
    try {
        $config = getBaserowConfig();
        
        // FIXED: Ensure patientId is properly included in the URL
        $url = "https://api.baserow.io/api/database/rows/table/{$config['baserow_table_id']}/{$patientId}/?user_field_names=true";
        
        $headers = [
            "Authorization: Token {$config['baserow_api_token']}",
            "Content-Type: application/json"
        ];
        
        $data = [
            $field => $value
        ];
        
        $jsonData = json_encode($data);
        
        writeErrorLog('Direct Baserow Update Test', [
            'url' => $url,
            'headers' => $headers,
            'data' => $data,
            'json_data' => $jsonData
        ]);
        
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PATCH');
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        
        $response = curl_exec($ch);
        $statusCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        
        curl_close($ch);
        
        writeErrorLog('Direct Baserow Update Response', [
            'status_code' => $statusCode,
            'response' => $response,
            'curl_error' => $curlError
        ]);
        
        if ($curlError) {
            return [
                'success' => false,
                'error' => "Curl error: " . $curlError
            ];
        }
        
        if ($statusCode >= 400) {
            return [
                'success' => false,
                'error' => "HTTP error: " . $statusCode,
                'response' => $response
            ];
        }
        
        $decodedResponse = json_decode($response, true);
        
        return [
            'success' => true,
            'response' => $decodedResponse
        ];
    } catch (Exception $e) {
        writeErrorLog('Direct Baserow Update Error', [
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
        
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}
?>
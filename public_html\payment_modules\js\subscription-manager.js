/**
 * Subscription Manager - JavaScript funkce pro správu předplatného
 */

// Změna plánu předplatného
function changePlan(plan, interval) {
  return fetch("payment_modules/subscription_handler.php", {
    method: "POST",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    body: new URLSearchParams({
      action: "change_plan",
      plan: plan,
      interval: interval,
    }),
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.success) {
        showNotification("success", data.message || "Plán byl úspěšně změněn")
        return true
      } else {
        showNotification("error", data.message || "Nepodařilo se změnit plán")
        return false
      }
    })
    .catch((error) => {
      console.error("Chyba při změně plánu:", error)
      showNotification("error", "<PERSON><PERSON><PERSON> k chy<PERSON> při komuni<PERSON> se serverem")
      return false
    })
}

// Zrušen<PERSON> předplatného
function cancelSubscription() {
  if (!confirm("Opravdu chcete zrušit své předplatné?")) {
    return Promise.resolve(false)
  }

  return fetch("payment_modules/subscription_handler.php", {
    method: "POST",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    body: new URLSearchParams({
      action: "cancel_subscription",
    }),
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.success) {
        showNotification("success", data.message || "Předplatné bylo úspěšně zrušeno")
        return true
      } else {
        showNotification("error", data.message || "Nepodařilo se zrušit předplatné")
        return false
      }
    })
    .catch((error) => {
      console.error("Chyba při rušení předplatného:", error)
      showNotification("error", "Došlo k chybě při komunikaci se serverem")
      return false
    })
}

// Zobrazení notifikace
function showNotification(type, message) {
  // Předpokládám, že máte funkci pro zobrazení notifikací
  // Pokud ne, můžete použít tuto jednoduchou implementaci
  const notificationContainer = document.getElementById("notification-container")
  if (!notificationContainer) return

  const notification = document.createElement("div")
  notification.className = `notification ${type}`
  notification.textContent = message

  notificationContainer.appendChild(notification)

  // Automatické odstranění notifikace po 5 sekundách
  setTimeout(() => {
    notification.classList.add("fade-out")
    setTimeout(() => {
      notification.remove()
    }, 500)
  }, 5000)
}

// Inicializace při načtení stránky
document.addEventListener("DOMContentLoaded", () => {
  // Nastavení event listenerů pro tlačítka
  setupEventListeners()
})

// Nastavení event listenerů
function setupEventListeners() {
  // Tlačítko pro zrušení předplatného
  const cancelButton = document.getElementById("cancel-subscription")
  if (cancelButton) {
    cancelButton.addEventListener("click", (e) => {
      e.preventDefault()
      cancelSubscription()
    })
  }
}


<?php
// Základní error reporting pro vývoj
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Základní konfigurace
$config = [
    'storage_file' => __DIR__ . '/voiceflow/data/settings.json',
    'storage_dir' => __DIR__ . '/voiceflow/data'
];

// Vytvoření složky pro data, pokud neexistuje
if (!file_exists($config['storage_dir'])) {
    mkdir($config['storage_dir'], 0777, true);
}

// Funkce pro validaci Voiceflow údajů
function isValidProjectId($projectId) {
    return preg_match('/^[0-9a-fA-F]{24}$/', $projectId);
}

function isValidApiKey($apiKey) {
    return preg_match('/^VF\.[0-9a-zA-Z]{64}$/', $apiKey);
}

function isValidWorkspaceId($workspaceId) {
    return preg_match('/^[0-9a-fA-F]{24}$/', $workspaceId);
}
?>
<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Voiceflow Customizer | Dentibot</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        .tab-button.active {
            border-bottom: 2px solid #3B82F6;
            color: #3B82F6;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        #widgetPreview {
            position: relative;
            width: 100%;
            height: 600px;
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
            overflow: hidden;
        }
        #widgetPreview iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        .code-block {
            background-color: #1a1a1a;
            color: #e5e7eb;
            padding: 1rem;
            border-radius: 0.5rem;
            font-family: monospace;
            position: relative;
        }
        .copy-button {
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
            padding: 0.25rem 0.5rem;
            background-color: #374151;
            color: white;
            border-radius: 0.25rem;
            font-size: 0.875rem;
            cursor: pointer;
        }
        .copy-button:hover {
            background-color: #4B5563;
        }
        .api-credentials {
            background-color: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 0.5rem;
            padding: 1.5rem;
        }
        .required-field::after {
            content: "*";
            color: #ef4444;
            margin-left: 0.25rem;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="min-h-screen">
        <main class="container mx-auto px-4 py-8">
            <div class="max-w-4xl mx-auto">
                <!-- Voiceflow Integration Section -->
                <div class="bg-white rounded-lg shadow-lg mb-8">
                    <div class="p-6">
                        <h2 class="text-xl font-bold text-gray-900 mb-4">Nastavení Voiceflow integrace</h2>
                        
                        <div class="api-credentials space-y-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- API Key -->
                                <div>
                                    <label for="apiKey" class="block text-sm font-medium text-gray-700 required-field">
                                        API Key
                                    </label>
                                    <div class="mt-1">
                                        <input
                                            type="password"
                                            name="apiKey"
                                            id="apiKey"
                                            class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                                            placeholder="VF.xxxxxxxx..."
                                            required
                                        >
                                    </div>
                                    <p class="mt-1 text-xs text-gray-500">
                                        Najdete v Voiceflow Dashboard → Settings → API Keys
                                    </p>
                                </div>

                                <!-- Project ID -->
                                <div>
                                    <label for="projectId" class="block text-sm font-medium text-gray-700 required-field">
                                        Project ID
                                    </label>
                                    <div class="mt-1">
                                        <input
                                            type="text"
                                            name="projectId"
                                            id="projectId"
                                            class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                                            placeholder="67aa8899998733b89b23a5"
                                            required
                                        >
                                    </div>
                                    <p class="mt-1 text-xs text-gray-500">
                                        ID projektu z URL vašeho Voiceflow chatbota
                                    </p>
                                </div>

                                <!-- Workspace ID -->
                                <div>
                                    <label for="workspaceId" class="block text-sm font-medium text-gray-700 required-field">
                                        Workspace ID
                                    </label>
                                    <div class="mt-1">
                                        <input
                                            type="text"
                                            name="workspaceId"
                                            id="workspaceId"
                                            class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                                            placeholder="89cc7788665544dd3322a1"
                                            required
                                        >
                                    </div>
                                    <p class="mt-1 text-xs text-gray-500">
                                        ID workspace z Voiceflow Dashboard
                                    </p>
                                </div>

                                <!-- Version -->
                                <div>
                                    <label for="versionId" class="block text-sm font-medium text-gray-700 required-field">
                                        Verze
                                    </label>
                                    <div class="mt-1">
                                        <select
                                            id="versionId"
                                            name="versionId"
                                            class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                                        >
                                            <option value="production">Production</option>
                                            <option value="development">Development</option>
                                            <option value="custom">Vlastní verze</option>
                                        </select>
                                    </div>
                                </div>

                                <!-- Custom Version ID (conditional) -->
                                <div id="customVersionContainer" class="hidden">
                                    <label for="customVersionId" class="block text-sm font-medium text-gray-700">
                                        ID vlastní verze
                                    </label>
                                    <div class="mt-1">
                                        <input
                                            type="text"
                                            name="customVersionId"
                                            id="customVersionId"
                                            class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                                            placeholder="v2.5"
                                        >
                                    </div>
                                </div>

                                <!-- API Base URL -->
                                <div>
                                    <label for="apiBaseUrl" class="block text-sm font-medium text-gray-700 required-field">
                                        API Base URL
                                    </label>
                                    <div class="mt-1">
                                        <input
                                            type="url"
                                            name="apiBaseUrl"
                                            id="apiBaseUrl"
                                            class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                                            value="https://general-runtime.voiceflow.com"
                                            required
                                        >
                                    </div>
                                </div>

                                <!-- Persistence -->
                                <div>
                                    <label for="persistence" class="block text-sm font-medium text-gray-700 required-field">
                                        Persistence chatu
                                    </label>
                                    <div class="mt-1">
                                        <select
                                            id="persistence"
                                            name="persistence"
                                            class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                                        >
                                            <option value="localStorage">localStorage (výchozí)</option>
                                            <option value="sessionStorage">sessionStorage</option>
                                            <option value="memory">memory</option>
                                        </select>
                                    </div>
                                    <p class="mt-1 text-xs text-gray-500">
                                        Určuje, jak dlouho bude konverzace uchována
                                    </p>
                                </div>
                            </div>

                            <div class="flex justify-end space-x-4">
                                <button
                                    id="saveConfigButton"
                                    type="button"
                                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                                >
                                    Uložit konfiguraci
                                </button>
                                <button
                                    type="button"
                                    onclick="loadWidgetPreview()"
                                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                                >
                                    Načíst náhled chatbota
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Widget Preview Section -->
                <div class="bg-white rounded-lg shadow-lg mb-8">
                    <div class="p-6">
                        <h2 class="text-xl font-bold text-gray-900 mb-4">Náhled chatbota</h2>
                        
                        <!-- Widget Preview -->
                        <div id="widgetPreview" class="hidden">
                            <div id="widgetContainer"></div>
                        </div>

                        <!-- Installation Code -->
                        <div class="mt-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-2">Instalační kód</h3>
                            <div class="code-block">
                                <button onclick="copyCode()" class="copy-button">
                                    Kopírovat
                                </button>
                                <pre id="installationCode" class="text-sm overflow-x-auto whitespace-pre-wrap">
// Nejdříve vyplňte údaje pro zobrazení instalačního kódu
                                </pre>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Compatibility and Testing Section -->
                <div class="bg-white rounded-lg shadow-lg mb-8">
                    <div class="p-6">
                        <h2 class="text-xl font-bold text-gray-900 mb-4">Kompatibilita a testování</h2>
                        <ul class="list-disc list-inside space-y-2">
                            <li>Otestujte všechny existující přizpůsobení a funkce v testovacím prostředí.</li>
                            <li>Ověřte chování persistence chatu podle zvolené konfigurace.</li>
                            <li>Pokud používáte vlastní CSS, otestujte styly na různých zařízeních a prohlížečích.</li>
                            <li>Zkontrolujte funkčnost všech implementovaných rozšíření s novou verzí webchatu.</li>
                        </ul>
                        <p class="mt-4">
                            V případě problémů nebo dotazů navštivte naše komunitní fórum na 
                            <a href="https://link.voiceflow.com/community" class="text-blue-600 hover:underline" target="_blank" rel="noopener noreferrer">
                                https://link.voiceflow.com/community
                            </a>.
                        </p>
                    </div>
                </div>

                <!-- Migration Warning -->
                <div class="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mt-8" role="alert">
                    <p class="font-bold">Důležité upozornění k migraci</p>
                    <p>Při přechodu na novou verzi webchatu vezměte prosím na vědomí následující:</p>
                    <ul class="list-disc list-inside mt-2">
                        <li>Většina existujících CSS stylů by měla fungovat, ale některé selektory mohou vyžadovat aktualizaci.</li>
                        <li>Pokud používáte vlastní rozšíření UI, bude nutné je aktualizovat pro novou verzi webchatu.</li>
                        <li>Proaktivní zprávy jsou dočasně nedostupné v nové verzi. Budou přidány v blízké budoucnosti.</li>
                    </ul>
                    <p class="mt-2">Doporučujeme důkladně otestovat všechny přizpůsobení před nasazením do produkce.</p>
                </div>
            </div>
        </main>
    </div>

    <script>
        let currentConfig = null;

        function loadWidgetPreview() {
            const config = {
                apiKey: document.getElementById('apiKey').value.trim(),
                projectId: document.getElementById('projectId').value.trim(),
                workspaceId: document.getElementById('workspaceId').value.trim(),
                versionId: document.getElementById('versionId').value,
                customVersionId: document.getElementById('customVersionId').value.trim(),
                apiBaseUrl: document.getElementById('apiBaseUrl').value.trim(),
                persistence: document.getElementById('persistence').value
            };

            // Validace povinných polí
            if (!config.apiKey || !config.projectId || !config.workspaceId) {
                alert('Vyplňte prosím všechna povinná pole označená hvězdičkou.');
                return;
            }

            // Validace formátu údajů
            if (!config.apiKey.match(/^VF\.[0-9a-zA-Z]{64}$/)) {
                alert('Neplatný formát API Key. Měl by začínat "VF." následovaný 64 znaky.');
                return;
            }

            if (!config.projectId.match(/^[0-9a-fA-F]{24}$/)) {
                alert('Neplatný formát Project ID.');
                return;
            }

            if (!config.workspaceId.match(/^[0-9a-fA-F]{24}$/)) {
                alert('Neplatný formát Workspace ID.');
                return;
            }

            currentConfig = config;

            // Aktualizace instalačního kódu
            const code = generateInstallationCode(config);
            document.getElementById('installationCode').textContent = code;

            // Odstranění předchozího widgetu pokud existuje
            const container = document.getElementById('widgetContainer');
            container.innerHTML = '';

            // Vytvoření a inicializace nového widgetu
            const script = document.createElement('script');
            script.type = 'text/javascript';
            script.src = 'https://cdn.voiceflow.com/widget-next/bundle.mjs';
            script.onload = () => {
                window.voiceflow?.chat?.load({
                    verify: { 
                        projectID: config.projectId,
                        versionID: config.versionId === 'custom' ? config.customVersionId : config.versionId,
                    },
                    url: config.apiBaseUrl,
                    headers: {
                        'Authorization': config.apiKey,
                        'vf-workspace-id': config.workspaceId
                    },
                    assistant: {
                        persistence: config.persistence
                    }
                });
            };

            container.appendChild(script);
            document.getElementById('widgetPreview').classList.remove('hidden');
        }

        function generateInstallationCode(config) {
            return `<script type="text/javascript">
    (function(d, t) {
        var v = d.createElement(t), s = d.getElementsByTagName(t)[0];
        v.onload = function() {
            window.voiceflow.chat.load({
                verify: { 
                    projectID: '${config.projectId}',
                    versionID: '${config.versionId === 'custom' ? config.customVersionId : config.versionId}'
                },
                url: '${config.apiBaseUrl}',
                headers: {
                    'Authorization': '${config.apiKey}',
                    'vf-workspace-id': '${config.workspaceId}'
                },
                assistant: {
                    persistence: '${config.persistence}'
                }
            });
        }
        v.src = "https://cdn.voiceflow.com/widget-next/bundle.mjs"; v.type = "text/javascript";
        s.parentNode.insertBefore(v, s);
    })(document, 'script');
</script>`;
        }

        function copyCode() {
            const code = document.getElementById('installationCode').textContent;
            navigator.clipboard.writeText(code).then(() => {
                const copyButton = document.querySelector('.copy-button');
                copyButton.textContent = 'Zkopírováno!';
                setTimeout(() => {
                    copyButton.textContent = 'Kopírovat';
                }, 2000);
            }).catch(err => {
                console.error('Chyba při kopírování:', err);
                alert('Nepodařilo se zkopírovat kód. Zkuste to prosím znovu.');
            });
        }

        // Zobrazení/skrytí pole pro vlastní verzi
        document.getElementById('versionId').addEventListener('change', function() {
            const customVersionContainer = document.getElementById('customVersionContainer');
            if (this.value === 'custom') {
                customVersionContainer.classList.remove('hidden');
            } else {
                customVersionContainer.classList.add('hidden');
            }
        });

        // Inicializace
        document.addEventListener('DOMContentLoaded', () => {
            // Načtení uložených hodnot z localStorage
            const savedConfig = localStorage.getItem('voiceflowConfig');
            if (savedConfig) {
                const config = JSON.parse(savedConfig);
                Object.keys(config).forEach(key => {
                    const element = document.getElementById(key);
                    if (element) {
                        element.value = config[key];
                    }
                });
            }
        });

        // Přidání nové funkce pro ukládání konfigurace
        function saveConfig() {
            const config = {
                apiKey: document.getElementById('apiKey').value,
                projectId: document.getElementById('projectId').value,
                workspaceId: document.getElementById('workspaceId').value,
                versionId: document.getElementById('versionId').value,
                customVersionId: document.getElementById('customVersionId').value,
                apiBaseUrl: document.getElementById('apiBaseUrl').value,
                persistence: document.getElementById('persistence').value
            };
            localStorage.setItem('voiceflowConfig', JSON.stringify(config));
            alert('Konfigurace byla úspěšně uložena!');
        }

        // Přidání event listeneru pro tlačítko uložení
        document.getElementById('saveConfigButton').addEventListener('click', saveConfig);
    </script>
</body>
</html>


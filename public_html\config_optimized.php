<?php
/**
 * Optimalizovaný config.php pro testovací upload
 * Zachovává původní funkcionalitu + přidává podporu pro automatickou kartu pacienta
 */

// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Custom error logging function
function customErrorLog($message, $errorType = 'ERROR') {
    $logMessage = date('Y-m-d H:i:s') . " [$errorType] $message\n";
    error_log($logMessage, 3, __DIR__ . '/config_error.log');
}

customErrorLog("Starting optimized config.php execution", "INFO");

// Start output buffering
ob_start();

try {
    // Session management - zachováváme původní
    if (session_status() == PHP_SESSION_NONE) {
        ini_set('session.cookie_httponly', 1);
        ini_set('session.use_only_cookies', 1);
        ini_set('session.cookie_secure', 0); // Pro localhost testování
        ini_set('session.gc_maxlifetime', 3600);
        ini_set('session.cookie_lifetime', 0);
        ini_set('session.name', 'DENTIBOTID');
        session_start();
        customErrorLog("Session started", "INFO");
    }

    // Database configuration - zachováváme původní + přidáváme nové
    define('DB_HOST', getenv('DB_HOST') ?: 'localhost');
    define('DB_USER', getenv('DB_USER') ?: 'root');
    define('DB_PASS', getenv('DB_PASS') ?: '');
    define('DB_NAME', getenv('DB_NAME') ?: 'dentibot');

    // VAPI API konfigurace - zachováváme původní
    define('VAPI_API_KEY', getenv('VAPI_API_KEY') ?: '');
    define('VAPI_BASE_URL', 'https://api.vapi.ai');
    define('DEFAULT_ASSISTANT_ID', getenv('DEFAULT_ASSISTANT_ID') ?: 'default_assistant_id');

    // Baserow konfigurace - zachováváme původní
    define('BASEROW_API_TOKEN', getenv('BASEROW_API_TOKEN') ?: '');
    define('BASEROW_DATABASE_ID', getenv('BASEROW_DATABASE_ID') ?: '');
    define('BASEROW_TABLE_ID', getenv('BASEROW_TABLE_ID') ?: '');

    // Stripe configuration - zachováváme původní
    define('STRIPE_SECRET_KEY', getenv('STRIPE_SECRET_KEY'));
    define('STRIPE_PUBLISHABLE_KEY', getenv('STRIPE_PUBLISHABLE_KEY'));
    define('STRIPE_WEBHOOK_SECRET', getenv('STRIPE_WEBHOOK_SECRET'));

    // Subscription limits - zachováváme původní
    define('SUBSCRIPTION_LIMITS', [
        'starter' => 500,
        'advanced' => 1000,
        'enterprise' => PHP_INT_MAX
    ]);

    // Encryption configuration - zachováváme původní
    define('PATIENT_ENCRYPTION_KEY', getenv('PATIENT_ENCRYPTION_KEY') ?: 'dentibot_secure_key_2025');
    define('PATIENT_ENCRYPTION_METHOD', 'AES-256-CBC');

    // Error log configuration
    define('ERROR_LOG_FILE', getenv('ERROR_LOG_FILE') ?: __DIR__ . '/app.log');

    // Database connections - zachováváme původní MySQLi + přidáváme PDO
    customErrorLog("Attempting database connection", "INFO");
    
    // MySQLi connection pro zachování kompatibility
    $mysqli = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
    if ($mysqli->connect_error) {
        throw new Exception('MySQLi Connect Error (' . $mysqli->connect_errno . ') ' . $mysqli->connect_error);
    }
    $mysqli->set_charset("utf8mb4");
    
    // PDO connection pro nové funkce
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    customErrorLog("Database connections successful", "INFO");

    // Helper functions pro databázové připojení
    function getDbConnection() {
        global $pdo;
        return $pdo;
    }

    function getMysqliConnection() {
        global $mysqli;
        if (!$mysqli || $mysqli->connect_error) {
            $mysqli = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
            $mysqli->set_charset("utf8mb4");
        }
        return $mysqli;
    }

    // Authentication functions - zachováváme původní
    function isLoggedIn() {
        if (!isset($_SESSION['user_id']) || !isset($_SESSION['last_activity'])) {
            return false;
        }
        
        // Check for session timeout (30 minutes)
        if (time() - $_SESSION['last_activity'] > 1800) {
            logoutUser();
            return false;
        }
        
        $_SESSION['last_activity'] = time();
        return true;
    }

    function isAdmin() {
        return isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin';
    }

    function requireLogin() {
        if (!isLoggedIn()) {
            $_SESSION['redirect_after_login'] = $_SERVER['REQUEST_URI'];
            header('Location: index.php');
            exit;
        }
    }

    function requireAdmin() {
        requireLogin();
        if (!isAdmin()) {
            header('Location: dashboard.php?error=unauthorized');
            exit;
        }
    }

    // API configuration function - zachováváme původní
    function getCurrentUserApiKey() {
        if (!isset($_SESSION['user_id'])) {
            return null;
        }

        $mysqli = getMysqliConnection();
        $stmt = $mysqli->prepare("SELECT vapi_api_key, vapi_api_url, assistant_id FROM users WHERE id = ?");
        $stmt->bind_param("i", $_SESSION['user_id']);
        $stmt->execute();
        $result = $stmt->get_result();
        $user = $result->fetch_assoc();

        if (!$user || empty($user['vapi_api_key'])) {
            return null;
        }

        return [
            'key' => $user['vapi_api_key'],
            'url' => $user['vapi_api_url'] ?: 'https://api.vapi.ai',
            'assistant_id' => $user['assistant_id'] ?: DEFAULT_ASSISTANT_ID
        ];
    }

    // Subscription functions - zachováváme původní
    function getUserSubscriptionPlan($user_id) {
        $mysqli = getMysqliConnection();
        $stmt = $mysqli->prepare("SELECT subscription_plan FROM users WHERE id = ?");
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $user = $result->fetch_assoc();
        $stmt->close();
        return $user['subscription_plan'] ?? 'starter';
    }

    function getUserSubscriptionLimit($user_id) {
        $plan = getUserSubscriptionPlan($user_id);
        return SUBSCRIPTION_LIMITS[$plan] ?? SUBSCRIPTION_LIMITS['starter'];
    }

    function getUserUsage($user_id) {
        $mysqli = getMysqliConnection();
        $stmt = $mysqli->prepare("
            SELECT COALESCE(CEIL(SUM(duration) / 60), 0) as total_duration 
            FROM vapi_calls 
            WHERE user_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH)
        ");
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $usage = (int)($result->fetch_assoc()['total_duration']);
        $stmt->close();
        return $usage;
    }

    // Security functions - zachováváme původní
    function generateCSRFToken() {
        if (!isset($_SESSION['csrf_token']) || !isset($_SESSION['csrf_token_time']) || 
            time() - $_SESSION['csrf_token_time'] > 3600) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
            $_SESSION['csrf_token_time'] = time();
        }
        return $_SESSION['csrf_token'];
    }

    function verifyCSRFToken($token) {
        return isset($_SESSION['csrf_token']) && 
               isset($_SESSION['csrf_token_time']) && 
               time() - $_SESSION['csrf_token_time'] <= 3600 && 
               hash_equals($_SESSION['csrf_token'], $token);
    }

    function sanitizeInput($input) {
        if (is_array($input)) {
            return array_map('sanitizeInput', $input);
        }
        return htmlspecialchars(strip_tags(trim($input)), ENT_QUOTES, 'UTF-8');
    }

    // Error logging - zachováváme původní
    function writeErrorLog($message, $context = []) {
        $timestamp = date('Y-m-d H:i:s');
        $contextStr = !empty($context) ? ' ' . json_encode($context) : '';
        $logMessage = "[$timestamp] $message$contextStr\n";

        if (!file_exists(ERROR_LOG_FILE)) {
            touch(ERROR_LOG_FILE);
            chmod(ERROR_LOG_FILE, 0666);
        }

        error_log($logMessage, 3, ERROR_LOG_FILE);
    }

    // Logout function - zachováváme původní
    function logoutUser() {
        $_SESSION = array();
        if (ini_get("session.use_cookies")) {
            $params = session_get_cookie_params();
            setcookie(session_name(), '', time() - 42000,
                $params["path"], $params["domain"],
                $params["secure"], $params["httponly"]
            );
        }
        session_destroy();
    }

    // Encryption functions - zachováváme původní
    function encryptPatientData($data) {
        if (empty($data)) {
            return ['data' => '', 'iv' => ''];
        }
        
        try {
            $iv = openssl_random_pseudo_bytes(openssl_cipher_iv_length(PATIENT_ENCRYPTION_METHOD));
            $encrypted = openssl_encrypt($data, PATIENT_ENCRYPTION_METHOD, PATIENT_ENCRYPTION_KEY, 0, $iv);
            
            if ($encrypted === false) {
                writeErrorLog("Patient data encryption failed", ['error' => openssl_error_string()]);
                return ['data' => '', 'iv' => ''];
            }
            
            return ['data' => $encrypted, 'iv' => bin2hex($iv)];
        } catch (Exception $e) {
            writeErrorLog("Exception during patient data encryption", ['error' => $e->getMessage()]);
            return ['data' => '', 'iv' => ''];
        }
    }

    function decryptPatientData($encryptedData, $iv) {
        if (empty($encryptedData) || empty($iv)) {
            return '';
        }
        
        try {
            $iv = hex2bin($iv);
            $decrypted = openssl_decrypt($encryptedData, PATIENT_ENCRYPTION_METHOD, PATIENT_ENCRYPTION_KEY, 0, $iv);
            
            if ($decrypted === false) {
                writeErrorLog("Patient data decryption failed", ['error' => openssl_error_string()]);
                return '';
            }
            
            return $decrypted;
        } catch (Exception $e) {
            writeErrorLog("Exception during patient data decryption", ['error' => $e->getMessage()]);
            return '';
        }
    }

    // Set timezone
    date_default_timezone_set('Europe/Prague');

    // Mock session pro testování - pouze pokud není nastaven
    if (!isset($_SESSION['user_id'])) {
        $_SESSION['user_id'] = 1;
        $_SESSION['username'] = 'demo_user';
        $_SESSION['user_role'] = 'admin';
        $_SESSION['last_activity'] = time();
        customErrorLog("Mock session created for testing", "INFO");
    }

    customErrorLog("Optimized config file execution completed successfully", "INFO");

} catch (Exception $e) {
    customErrorLog("Error in optimized config.php: " . $e->getMessage(), "ERROR");
    die("Došlo k chybě při inicializaci aplikace. Prosím zkuste to později.");
}

// Flush the output buffer
if (ob_get_length()) ob_end_flush();
?>

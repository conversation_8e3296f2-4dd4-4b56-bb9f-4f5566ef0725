<?php
// This is a simple debug file to help identify PHP errors
// Place this in your root directory and access it via browser

// Display all PHP errors
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>PHP Debug Information</h1>";

// PHP Version
echo "<h2>PHP Version</h2>";
echo "<p>" . phpversion() . "</p>";

// Check if required directories exist
echo "<h2>Directory Structure Check</h2>";
$directories = [
    'landing-page',
    'landing-page/config',
    'landing-page/includes',
    'landing-page/sections',
    'landing-page/css',
    'landing-page/js',
    'seo'
];

echo "<ul>";
foreach ($directories as $dir) {
    if (is_dir($dir)) {
        echo "<li>✅ Directory exists: $dir</li>";
    } else {
        echo "<li>❌ Directory missing: $dir</li>";
    }
}
echo "</ul>";

// Check if required files exist
echo "<h2>File Check</h2>";
$files = [
    'landing.php',
    'landing-page/config/config.php',
    'landing-page/includes/header.php',
    'landing-page/includes/navigation.php',
    'seo/index.php'
];

echo "<ul>";
foreach ($files as $file) {
    if (file_exists($file)) {
        echo "<li>✅ File exists: $file</li>";
    } else {
        echo "<li>❌ File missing: $file</li>";
    }
}
echo "</ul>";

// Check PHP extensions
echo "<h2>PHP Extensions</h2>";
$required_extensions = ['xml', 'mbstring', 'json'];

echo "<ul>";
foreach ($required_extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "<li>✅ Extension loaded: $ext</li>";
    } else {
        echo "<li>❌ Extension missing: $ext</li>";
    }
}
echo "</ul>";

// Check file permissions
echo "<h2>File Permissions</h2>";
$permission_files = [
    'landing.php',
    'landing-page/config/config.php'
];

echo "<ul>";
foreach ($permission_files as $file) {
    if (file_exists($file)) {
        $perms = fileperms($file);
        $perms_string = sprintf('%o', $perms);
        echo "<li>File: $file - Permissions: $perms_string</li>";
    }
}
echo "</ul>";

echo "<p>End of debug information</p>";
?>


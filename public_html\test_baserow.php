<?php
// Create a new file called direct_baserow_test.php

// Enable error reporting for debugging
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Define constants for encryption (should match your config.php)
define('ENCRYPTION_KEY', 'your-encryption-key'); // Replace with your actual encryption key

// Function to write to error log
function writeErrorLog($message, $data = []) {
    error_log($message . ': ' . json_encode($data));
}

// Function to make Baserow API request
function directBaserowRequest($method, $endpoint, $data = null) {
    try {
        // IMPORTANT: Replace these with your actual Baserow credentials
        $apiToken = 'your-baserow-api-token'; // Replace with your actual API token
        $tableId = 'your-baserow-table-id';   // Replace with your actual table ID
        
        // Make sure endpoint doesn't have double slashes
        $endpoint = ltrim($endpoint, '/');
        
        $url = "https://api.baserow.io/api/database/rows/table/{$tableId}/" . $endpoint;
        
        echo "<p>Request URL: " . $url . "</p>";
        echo "<p>Request Method: " . $method . "</p>";
        echo "<p>Request Data: " . ($data ? json_encode($data) : 'null') . "</p>";
        
        $headers = [
            "Authorization: Token {$apiToken}",
            "Content-Type: application/json"
        ];

        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
        curl_setopt($ch, CURLOPT_VERBOSE, true);

        $verbose = fopen('php://temp', 'w+');
        curl_setopt($ch, CURLOPT_STDERR, $verbose);

        if ($data !== null) {
            $jsonData = json_encode($data);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);
            echo "<p>Request JSON: " . $jsonData . "</p>";
        }

        $response = curl_exec($ch);
        $statusCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        $curlErrno = curl_errno($ch);

        rewind($verbose);
        $verboseLog = stream_get_contents($verbose);
        echo "<p>Curl Verbose Log:</p>";
        echo "<pre>" . htmlspecialchars($verboseLog) . "</pre>";

        if ($curlErrno) {
            echo "<p>Curl Error: " . $curlError . "</p>";
            throw new Exception("Curl error: " . $curlError);
        }

        curl_close($ch);

        echo "<p>Response Status: " . $statusCode . "</p>";
        echo "<p>Response:</p>";
        echo "<pre>" . htmlspecialchars(substr($response, 0, 1000)) . "</pre>";

        if ($statusCode >= 400) {
            $errorResponse = json_decode($response, true);
            $errorMessage = isset($errorResponse['error']) 
                ? $errorResponse['error'] 
                : "Neznámá chyba (Status: $statusCode)";
            
            echo "<p>API Error: " . $errorMessage . "</p>";
            throw new Exception("Chyba API: " . $errorMessage);
        }

        $decodedResponse = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            echo "<p>JSON Decode Error: " . json_last_error_msg() . "</p>";
            throw new Exception("Chyba při dekódování JSON odpovědi: " . json_last_error_msg());
        }

        return $decodedResponse;
    } catch (Exception $e) {
        echo "<p>Request Error: " . $e->getMessage() . "</p>";
        throw $e;
    }
}

// Get patient ID from URL
$patientId = isset($_GET['id']) ? intval($_GET['id']) : 0;
$field = isset($_GET['field']) ? $_GET['field'] : 'brouseni';
$value = isset($_GET['value']) ? $_GET['value'] : 'test-' . time();

echo "<h1>Direct Baserow API Test</h1>";
echo "<p>Testing update for patient ID: $patientId, field: $field, value: $value</p>";

try {
    // Field mapping
    $fieldMapping = [
        'examination_date' => 'Datum_prohlidky',
        'akutni' => 'Akutní',
        'brouseni' => 'Broušení',
        'endo' => 'Endo',
        'extrakce_chirurgie' => 'Extrakce, chirurgie',
        'postendo' => 'Postendo',
        'predni_protetiky' => 'Předání protetiky',
        'sanace_dite' => 'Sanace - dítě',
        'sanace_dospely' => 'Sanace - dospělý',
        'snimatelna_protetika' => 'Snímatelná protetika - otisky'
    ];
    
    if (!isset($fieldMapping[$field])) {
        throw new Exception("Invalid field: $field");
    }
    
    $baserowField = $fieldMapping[$field];
    echo "<p>Mapped field: $field -> $baserowField</p>";
    
    // Prepare update data
    $updateData = [];
    $updateData[$baserowField] = $value;
    
    echo "<p>Update data: " . json_encode($updateData) . "</p>";
    
    // Make direct API request
    echo "<p>Making API request...</p>";
    $response = directBaserowRequest('PATCH', $patientId . '/?user_field_names=true', $updateData);
    
    echo "<p>API request successful!</p>";
    echo "<pre>" . json_encode($response, JSON_PRETTY_PRINT) . "</pre>";
    
    echo "<p style='color:green;font-weight:bold;'>Test completed successfully!</p>";
    
} catch (Exception $e) {
    echo "<p style='color:red;font-weight:bold;'>Error: " . $e->getMessage() . "</p>";
    echo "<p>Stack trace:</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
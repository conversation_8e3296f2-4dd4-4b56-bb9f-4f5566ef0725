"use client"

import { useState } from "react"
import { CalendarHeader } from "./calendar-header"
import { CalendarGrid } from "./calendar-grid"

interface CalendarProps {
  events?: Array<{
    id: string
    title: string
    start: Date
    end: Date
    type: "kontrola" | "osetreni" | "konzultace" | "operace"
  }>
}

export function Calendar({ events = [] }: CalendarProps) {
  const [currentDate, setCurrentDate] = useState(new Date())
  const [currentView, setCurrentView] = useState<"month" | "week" | "day">("month")

  const handlePrevMonth = () => {
    setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1))
  }

  const handleNextMonth = () => {
    setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 1))
  }

  const handleToday = () => {
    setCurrentDate(new Date())
  }

  return (
    <div className="p-4">
      <CalendarHeader
        currentDate={currentDate}
        onPrevMonth={handlePrevMonth}
        onNextMonth={handleNextMonth}
        onToday={handleToday}
        onViewChange={setCurrentView}
        currentView={currentView}
      />
      <CalendarGrid currentDate={currentDate} events={events} />
    </div>
  )
}


<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo - <PERSON><PERSON><PERSON> ka<PERSON> pacienta</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
        .sidebar {
            width: 16rem;
            background: linear-gradient(135deg, #1e3a8a, #1e40af);
            color: white;
            position: fixed;
            height: 100vh;
            left: 0;
            top: 0;
            z-index: 100;
            overflow-y: auto;
        }
        .main-content {
            margin-left: 16rem;
            min-height: 100vh;
        }
        .nav-link {
            display: flex;
            align-items: center;
            padding: 0.75rem 1.5rem;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.2s;
            cursor: pointer;
        }
        .nav-link:hover, .nav-link.active {
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }
        .nav-icon {
            width: 1.25rem;
            height: 1.25rem;
            margin-right: 0.75rem;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="p-6 border-b border-blue-600">
            <div class="flex items-center">
                <div class="w-8 h-8 bg-white rounded mr-3 flex items-center justify-center">
                    <span class="text-blue-600 font-bold text-sm">D</span>
                </div>
                <span class="text-xl font-bold">Dentibot</span>
            </div>
            <div class="mt-3 text-sm text-blue-200">
                <div>MUDr. Jan Novák</div>
                <div class="text-xs">Advanced plán</div>
            </div>
        </div>
        <nav class="mt-6">
            <a href="index.html" class="nav-link">
                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <rect x="3" y="3" width="18" height="18" rx="2"/><path d="M3 9h18"/><path d="M9 21V9"/>
                </svg>
                Přehledy
            </a>
            <a href="#" class="nav-link">
                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
                </svg>
                Historie hovorů
            </a>
            <a href="#" class="nav-link">
                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
                </svg>
                Hromadné SMS
            </a>
            <a href="#" class="nav-link">
                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/><circle cx="9" cy="7" r="4"/><path d="M23 21v-2a4 4 0 0 0-3-3.87"/><path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                </svg>
                Seznam pacientů
            </a>
            <a href="#" class="nav-link">
                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/><polyline points="14,2 14,8 20,8"/><line x1="16" y1="13" x2="8" y2="13"/><line x1="16" y1="17" x2="8" y2="17"/><polyline points="10,9 9,9 8,9"/><circle cx="12" cy="15" r="2"/>
                </svg>
                Karta pacienta
                <span class="ml-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full">auto</span>
            </a>
            <a href="demo_events.html" class="nav-link active">
                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                </svg>
                Demo události
                <span class="ml-2 bg-orange-500 text-white text-xs px-2 py-1 rounded-full">test</span>
            </a>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <div class="p-8">
            <!-- Header -->
            <div class="mb-8">
                <h1 class="text-3xl font-bold text-gray-900">Demo - Automatické události</h1>
                <p class="text-gray-600 mt-2">Simulátor pro testování automatického naplňování karty pacienta</p>
            </div>

            <!-- Success Alert -->
            <div id="successAlert" class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6" style="display: none;">
                <span id="successMessage"></span>
                <a href="patient_card_demo.html" class="ml-4 underline">Zobrazit kartu pacienta</a>
            </div>

            <!-- Info Box -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
                <h2 class="text-lg font-semibold text-blue-900 mb-2">Jak to funguje</h2>
                <div class="text-blue-800 space-y-2">
                    <p>• Každá aktivita v CRM automaticky vyvolá událost</p>
                    <p>• CardEventHandler zachytí událost a přidá položku do karty pacienta</p>
                    <p>• Každá položka dostane okamžité časové razítko (TST)</p>
                    <p>• Celá karta se denně agregovaně razítkuje</p>
                    <p>• Lékař nemusí dělat nic - vše běží automaticky na pozadí</p>
                </div>
            </div>

            <!-- Patient Selection -->
            <div class="bg-white rounded-lg shadow-sm border p-6 mb-8">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">Výběr pacienta</h2>
                <div class="flex items-center gap-4">
                    <label class="text-sm font-medium text-gray-700">Pacient:</label>
                    <select id="patientSelect" class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                        <option value="1">1 - Jan Novák</option>
                        <option value="2">2 - Marie Svobodová</option>
                        <option value="3">3 - Pavel Dvořák</option>
                        <option value="4">4 - Anna Procházková</option>
                    </select>
                    <button onclick="viewPatientCard()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors">
                        Zobrazit kartu
                    </button>
                </div>
            </div>

            <!-- Event Simulators -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- Document Event -->
                <div class="bg-white rounded-lg shadow-sm border p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">📝 Uložení záznamu</h3>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Název</label>
                        <input type="text" id="docTitle" value="Preventivní prohlídka" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Obsah</label>
                        <textarea id="docContent" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">Provedena rutinní preventivní prohlídka. Stav chrupu dobrý.</textarea>
                    </div>
                    <button onclick="simulateDocument()" class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 rounded-lg transition-colors">
                        Simulovat uložení
                    </button>
                </div>

                <!-- Image Event -->
                <div class="bg-white rounded-lg shadow-sm border p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">📷 Nahrání snímku</h3>
                    <p class="text-sm text-gray-600 mb-4">Simuluje nahrání RTG snímku do karty pacienta.</p>
                    <button onclick="simulateImage()" class="w-full bg-green-600 hover:bg-green-700 text-white py-2 rounded-lg transition-colors">
                        Simulovat RTG snímek
                    </button>
                </div>

                <!-- Procedure Event -->
                <div class="bg-white rounded-lg shadow-sm border p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">🦷 Dokončený výkon</h3>
                    <p class="text-sm text-gray-600 mb-4">Simuluje dokončení preventivní prohlídky.</p>
                    <button onclick="simulateProcedure()" class="w-full bg-yellow-600 hover:bg-yellow-700 text-white py-2 rounded-lg transition-colors">
                        Simulovat výkon
                    </button>
                </div>

                <!-- Consent Event -->
                <div class="bg-white rounded-lg shadow-sm border p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">✍️ Podepsaný souhlas</h3>
                    <p class="text-sm text-gray-600 mb-4">Simuluje podepsání souhlasu s léčbou.</p>
                    <button onclick="simulateConsent()" class="w-full bg-purple-600 hover:bg-purple-700 text-white py-2 rounded-lg transition-colors">
                        Simulovat souhlas
                    </button>
                </div>

                <!-- Payment Event -->
                <div class="bg-white rounded-lg shadow-sm border p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">💳 Přijatá platba</h3>
                    <p class="text-sm text-gray-600 mb-4">Simuluje platbu kartou za výkon.</p>
                    <button onclick="simulatePayment()" class="w-full bg-red-600 hover:bg-red-700 text-white py-2 rounded-lg transition-colors">
                        Simulovat platbu
                    </button>
                </div>

                <!-- Call Event -->
                <div class="bg-white rounded-lg shadow-sm border p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">📞 Dokončený hovor</h3>
                    <p class="text-sm text-gray-600 mb-4">Simuluje hovor s pacientem přes voicebot.</p>
                    <button onclick="simulateCall()" class="w-full bg-indigo-600 hover:bg-indigo-700 text-white py-2 rounded-lg transition-colors">
                        Simulovat hovor
                    </button>
                </div>
            </div>

            <!-- Recent Events -->
            <div class="mt-12 bg-white rounded-lg shadow-sm border">
                <div class="p-6 border-b">
                    <h2 class="text-xl font-semibold text-gray-900">Nedávné události</h2>
                </div>
                <div class="p-6">
                    <div id="recentEvents" class="space-y-4">
                        <p class="text-gray-500 text-center py-8">Zatím žádné události. Zkuste některý simulátor výše.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let eventCounter = 0;
        let recentEvents = [];

        function getSelectedPatient() {
            const select = document.getElementById('patientSelect');
            return {
                id: select.value,
                name: select.options[select.selectedIndex].text.split(' - ')[1]
            };
        }

        function addEvent(type, title, description) {
            eventCounter++;
            const patient = getSelectedPatient();
            const event = {
                id: eventCounter,
                type: type,
                title: title,
                description: description,
                patient: patient,
                timestamp: new Date().toLocaleString('cs-CZ')
            };
            
            recentEvents.unshift(event);
            if (recentEvents.length > 10) {
                recentEvents.pop();
            }
            
            updateRecentEvents();
            showSuccess(`Událost "${title}" byla úspěšně zpracována pro pacienta ${patient.name}!`);
        }

        function updateRecentEvents() {
            const container = document.getElementById('recentEvents');
            if (recentEvents.length === 0) {
                container.innerHTML = '<p class="text-gray-500 text-center py-8">Zatím žádné události. Zkuste některý simulátor výše.</p>';
                return;
            }

            container.innerHTML = recentEvents.map(event => `
                <div class="flex items-start gap-4 p-4 bg-gray-50 rounded-lg">
                    <div class="w-10 h-10 rounded-full bg-${getEventColor(event.type)}-100 flex items-center justify-center">
                        <span class="text-${getEventColor(event.type)}-600 text-lg">${getEventIcon(event.type)}</span>
                    </div>
                    <div class="flex-1">
                        <div class="flex items-center justify-between">
                            <h4 class="font-medium text-gray-900">${event.title}</h4>
                            <span class="text-xs text-gray-500">${event.timestamp}</span>
                        </div>
                        <p class="text-sm text-gray-600 mt-1">${event.description}</p>
                        <p class="text-xs text-gray-500 mt-1">Pacient: ${event.patient.name}</p>
                    </div>
                </div>
            `).join('');
        }

        function getEventColor(type) {
            const colors = {
                'document': 'blue',
                'image': 'green',
                'procedure': 'yellow',
                'consent': 'purple',
                'payment': 'red',
                'call': 'indigo'
            };
            return colors[type] || 'gray';
        }

        function getEventIcon(type) {
            const icons = {
                'document': '📝',
                'image': '📷',
                'procedure': '🦷',
                'consent': '✍️',
                'payment': '💳',
                'call': '📞'
            };
            return icons[type] || '📄';
        }

        function showSuccess(message) {
            const alert = document.getElementById('successAlert');
            const messageEl = document.getElementById('successMessage');
            messageEl.textContent = message;
            alert.style.display = 'block';
            
            setTimeout(() => {
                alert.style.display = 'none';
            }, 5000);
        }

        function simulateDocument() {
            const title = document.getElementById('docTitle').value;
            const content = document.getElementById('docContent').value;
            addEvent('document', title, `Záznam: ${content.substring(0, 50)}...`);
        }

        function simulateImage() {
            addEvent('image', 'RTG snímek', 'Panoramatický RTG snímek pro diagnostiku');
        }

        function simulateProcedure() {
            addEvent('procedure', 'Preventivní prohlídka', 'Rutinní preventivní prohlídka s čištěním (30 min, 500 Kč)');
        }

        function simulateConsent() {
            addEvent('consent', 'Souhlas s léčbou', 'Podepsán souhlas s provedením preventivní prohlídky');
        }

        function simulatePayment() {
            addEvent('payment', 'Platba kartou', 'Přijata platba 500 Kč za preventivní prohlídku');
        }

        function simulateCall() {
            addEvent('call', 'Telefonní hovor', 'Hovor s pacientem ohledně změny termínu (2 min)');
        }

        function viewPatientCard() {
            const patient = getSelectedPatient();
            window.open('patient_card_demo.html?patient_id=' + patient.id, '_blank');
        }
    </script>
</body>
</html>

<?php
// Prevent any output before headers
ob_start();

ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
error_reporting(0);

header('Content-Type: application/json');

require_once __DIR__ . '/config.php';

// Check if session is not already active before starting it
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Ensure any errors are caught and returned as JSON
function handleError($errno, $errstr, $errfile, $errline) {
    echo json_encode([
        'success' => false,
        'error' => $errstr
    ]);
    exit;
}
set_error_handler('handleError');

// Catch any uncaught exceptions and return as JSON
function handleException($e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
    exit;
}
set_exception_handler('handleException');

// Clear any output buffers to prevent HTML leaking into response
while (ob_get_level()) {
    ob_end_clean();
}

if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'error' => 'Uživatel nen<PERSON>šen']);
    exit;
}

// Get the raw input and decode it
$rawInput = file_get_contents('php://input');
$input = json_decode($rawInput, true);

if (json_last_error() !== JSON_ERROR_NONE) {
    echo json_encode([
        'success' => false,
        'error' => 'Neplatná JSON data: ' . json_last_error_msg()
    ]);
    exit;
}

try {
    $mysqli = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
    if ($mysqli->connect_error) {
        throw new Exception("Připojení k databázi selhalo");
    }

    $method = $_SERVER['REQUEST_METHOD'];

    switch ($method) {
        case 'POST':
            if (!isset($input['title'], $input['start'], $input['end'], $input['className'])) {
                throw new Exception("Chybí povinná pole");
            }

            // Extract event type before using it in queries
            $event_type = str_replace('event-', '', $input['className']);

            if (isset($input['id'])) {
                // First check if the event exists and belongs to the user
                $check_stmt = $mysqli->prepare("SELECT id FROM calendar_events WHERE id = ? AND user_id = ?");
                if (!$check_stmt) {
                    throw new Exception("Chyba při přípravě kontrolního dotazu");
                }
                $check_stmt->bind_param("ii", $input['id'], $_SESSION['user_id']);
                $check_stmt->execute();
                $check_result = $check_stmt->get_result();
                
                if ($check_result->num_rows === 0) {
                    throw new Exception("Událost nebyla nalezena nebo nemáte oprávnění ji upravit");
                }
                $check_stmt->close();

                // If check passed, proceed with update
                $stmt = $mysqli->prepare("UPDATE calendar_events SET title = ?, start_time = ?, end_time = ?, event_type = ?, patient_name = ?, patient_phone = ?, notes = ? WHERE id = ? AND user_id = ?");
                if (!$stmt) {
                    throw new Exception("Chyba při přípravě SQL dotazu");
                }
                $stmt->bind_param("sssssssii", 
                    $input['title'],
                    $input['start'],
                    $input['end'],
                    $event_type,
                    $input['extendedProps']['patientName'],
                    $input['extendedProps']['patientPhone'],
                    $input['extendedProps']['notes'],
                    $input['id'],
                    $_SESSION['user_id']
                );
            } else {
                $stmt = $mysqli->prepare("INSERT INTO calendar_events (user_id, title, start_time, end_time, event_type, patient_name, patient_phone, notes) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
                if (!$stmt) {
                    throw new Exception("Chyba při přípravě SQL dotazu");
                }
                $stmt->bind_param("isssssss", 
                    $_SESSION['user_id'],
                    $input['title'],
                    $input['start'],
                    $input['end'],
                    $event_type,
                    $input['extendedProps']['patientName'],
                    $input['extendedProps']['patientPhone'],
                    $input['extendedProps']['notes']
                );
            }

            if (!$stmt->execute()) {
                throw new Exception("Chyba při ukládání události");
            }

            $event_id = isset($input['id']) ? $input['id'] : $mysqli->insert_id;
            echo json_encode(['success' => true, 'id' => $event_id]);
            break;

        case 'DELETE':
            if (!isset($input['id'])) {
                throw new Exception("Chybí ID události");
            }

            // First check if the event exists and belongs to the user
            $check_stmt = $mysqli->prepare("SELECT id FROM calendar_events WHERE id = ? AND user_id = ?");
            if (!$check_stmt) {
                throw new Exception("Chyba při přípravě kontrolního dotazu");
            }
            $check_stmt->bind_param("ii", $input['id'], $_SESSION['user_id']);
            $check_stmt->execute();
            $check_result = $check_stmt->get_result();
            
            if ($check_result->num_rows === 0) {
                throw new Exception("Událost nebyla nalezena nebo nemáte oprávnění ji smazat");
            }
            $check_stmt->close();

            // If check passed, proceed with delete
            $stmt = $mysqli->prepare("DELETE FROM calendar_events WHERE id = ? AND user_id = ?");
            if (!$stmt) {
                throw new Exception("Chyba při přípravě SQL dotazu pro mazání");
            }

            $stmt->bind_param("ii", $input['id'], $_SESSION['user_id']);

            if (!$stmt->execute()) {
                throw new Exception("Chyba při mazání události");
            }

            echo json_encode(['success' => true]);
            break;

        default:
            throw new Exception("Nepodporovaná metoda");
    }

    if (isset($stmt)) {
        $stmt->close();
    }
    $mysqli->close();

} catch (Exception $e) {
    error_log('Chyba v save_event.php: ' . $e->getMessage());
    echo json_encode(['success' => false, 'error' => $e->getMessage()]);
}
?>


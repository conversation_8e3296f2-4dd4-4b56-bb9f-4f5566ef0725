// Funkce pro přepínání tabů
function switchTab(tabId) {
  // Skryjeme všechny obsahy tabů
  document.querySelectorAll(".tab-content").forEach((content) => {
    content.classList.add("hidden")
  })

  // Deaktivujeme všechny taby
  document.querySelectorAll(".tab-button").forEach((tab) => {
    tab.classList.remove("active")
  })

  // Zobrazíme vybraný obsah a aktivujeme tab
  document.getElementById(tabId).classList.remove("hidden")
  document.querySelector(`[data-tab="${tabId}"]`).classList.add("active")
}

// Funkce pro aktualizaci náhledu
function updatePreview() {
  const preview = {
    primaryColor: document.getElementById("primaryColor").value,
    secondaryColor: document.getElementById("secondaryColor").value,
    backgroundColor: document.getElementById("backgroundColor").value,
    avatarUrl: document.getElementById("avatarUrl").value,
    showAvatar: document.getElementById("showAvatar").checked,
    welcomeTitle: document.getElementById("welcomeTitle").value,
    welcomeMessage: document.getElementById("welcomeMessage").value,
    showHeader: document.getElementById("showHeader").checked,
    showTimestamp: document.getElementById("showTimestamp").checked,
    showMessageAvatar: document.getElementById("showMessageAvatar").checked,
  }

  // Aktualizace barev
  const previewHeader = document.getElementById("previewHeader")
  previewHeader.style.backgroundColor = preview.primaryColor

  // Aktualizace avataru
  const previewAvatarImage = document.getElementById("previewAvatarImage")
  const previewAvatarPlaceholder = document.getElementById("previewAvatarPlaceholder")

  if (preview.showAvatar && preview.avatarUrl) {
    previewAvatarImage.src = preview.avatarUrl
    previewAvatarImage.classList.remove("hidden")
    previewAvatarPlaceholder.classList.add("hidden")
  } else {
    previewAvatarImage.classList.add("hidden")
    previewAvatarPlaceholder.classList.remove("hidden")
  }

  // Aktualizace textu
  document.getElementById("previewTitle").textContent = preview.welcomeTitle
  document.getElementById("previewMessage").textContent = preview.welcomeMessage

  // Aktualizace zobrazení prvků
  const timestamps = document.querySelectorAll(".text-xs.text-gray-500")
  timestamps.forEach((timestamp) => {
    timestamp.style.display = preview.showTimestamp ? "block" : "none"
  })

  // Generování CSS pro Voiceflow
  const css = generateVoiceflowCSS(preview)

  // Uložení konfigurace do localStorage pro pozdější použití
  localStorage.setItem(
    "voiceflowConfig",
    JSON.stringify({
      style: preview,
      css: css,
    }),
  )
}

// Funkce pro generování CSS pro Voiceflow
function generateVoiceflowCSS(config) {
  return `
        .vf-chat-container {
            --vf-primary-color: ${config.primaryColor};
            --vf-secondary-color: ${config.secondaryColor};
            background-color: ${config.backgroundColor};
        }
        
        .vf-message-bubble {
            background-color: var(--vf-primary-color);
            color: white;
        }
        
        .vf-user-message {
            background-color: var(--vf-secondary-color);
        }
        
        .vf-timestamp {
            display: ${config.showTimestamp ? "block" : "none"};
        }
        
        .vf-avatar {
            display: ${config.showMessageAvatar ? "block" : "none"};
        }
    `
}

// Funkce pro uložení nastavení
async function saveSettings() {
  const config = localStorage.getItem("voiceflowConfig")

  try {
    const response = await fetch("/voiceflow/api/save-settings.php", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: config,
    })

    if (response.ok) {
      alert("Nastavení bylo úspěšně uloženo!")
    } else {
      throw new Error("Chyba při ukládání nastavení")
    }
  } catch (error) {
    console.error("Chyba:", error)
    alert("Nastala chyba při ukládání nastavení. Zkuste to prosím znovu.")
  }
}

// Inicializace při načtení stránky
document.addEventListener("DOMContentLoaded", () => {
  // Načtení uložené konfigurace
  const savedConfig = localStorage.getItem("voiceflowConfig")
  if (savedConfig) {
    const config = JSON.parse(savedConfig)
    // TODO: Nastavení hodnot formuláře podle uložené konfigurace
  }

  // Přidání event listenerů pro tlačítka
  document.querySelector('button[type="submit"]').addEventListener("click", saveSettings)

  // Inicializace prvního tabu
  switchTab("appearance")
})


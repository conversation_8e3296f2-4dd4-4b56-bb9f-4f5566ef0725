// Globální ochrana proti nežá<PERSON>ucímu přesměrování
;(() => {
  const originalPushState = history.pushState
  const originalReplaceState = history.replaceState

  history.pushState = function () {
    if (window.preventDashboardRedirect) {
      console.log("Pokus o pushState byl zastaven")
      return
    }
    return originalPushState.apply(this, arguments)
  }

  history.replaceState = function () {
    if (window.preventDashboardRedirect) {
      console.log("Pokus o replaceState byl zastaven")
      return
    }
    return originalReplaceState.apply(this, arguments)
  }
})()

// Přidejte tento kód do vašeho hlavního JavaScript souboru
document.addEventListener("DOMContentLoaded", () => {
  // Kontrola meta tagu pro zabránění přesměrování
  const preventRedirectMeta = document.querySelector('meta[name="prevent-dashboard-redirect"]')
  if (preventRedirectMeta && preventRedirectMeta.getAttribute("content") === "true") {
    // Zabránění př<PERSON>měrování na dashboard
    window.preventDefaultRedirect = () => {
      console.log("Přesměrování na dashboard bylo zabráněno")
      // Zde můžete přidat další logiku, pokud je potřeba
    }
  }
})


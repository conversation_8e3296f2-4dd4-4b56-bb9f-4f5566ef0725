<?php
require_once 'config.php';
require_once 'subscription_constants.php';

session_start();
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'admin') {
    header('Location: login.php');
    exit;
}

$mysqli = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add':
                $name = $mysqli->real_escape_string($_POST['name']);
                $minutes_limit = intval($_POST['minutes_limit']);
                $is_unlimited = isset($_POST['is_unlimited']) ? 1 : 0;
                $mysqli->query("INSERT INTO subscription_types (name, minutes_limit, is_unlimited) VALUES ('$name', $minutes_limit, $is_unlimited)");
                break;
            case 'edit':
                $id = intval($_POST['id']);
                $name = $mysqli->real_escape_string($_POST['name']);
                $minutes_limit = intval($_POST['minutes_limit']);
                $is_unlimited = isset($_POST['is_unlimited']) ? 1 : 0;
                $mysqli->query("UPDATE subscription_types SET name = '$name', minutes_limit = $minutes_limit, is_unlimited = $is_unlimited WHERE id = $id");
                break;
            case 'delete':
                $id = intval($_POST['id']);
                $mysqli->query("DELETE FROM subscription_types WHERE id = $id");
                break;
        }
    }
}

$result = $mysqli->query("SELECT * FROM subscription_types");
$types = $result->fetch_all(MYSQLI_ASSOC);
?>

<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Správa typů předplatného</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
</head>
<body class="bg-gray-100">
    <div class="container mx-auto p-6">
        <h1 class="text-3xl font-bold mb-6">Správa typů předplatného</h1>
        
        <form method="POST" class="mb-8">
            <input type="hidden" name="action" value="add">
            <div class="flex items-center space-x-4">
                <input type="text" name="name" placeholder="Název" required class="px-3 py-2 border rounded">
                <input type="number" name="minutes_limit" placeholder="Limit minut" required class="px-3 py-2 border rounded">
                <label class="flex items-center">
                    <input type="checkbox" name="is_unlimited" class="mr-2">
                    Neomezený
                </label>
                <button type="submit" class="px-4 py-2 bg-blue-500 text-white rounded">Přidat</button>
            </div>
        </form>

        <table class="w-full bg-white shadow-md rounded">
            <thead>
                <tr class="bg-gray-200 text-gray-600 uppercase text-sm leading-normal">
                    <th class="py-3 px-6 text-left">Název</th>
                    <th class="py-3 px-6 text-left">Limit minut</th>
                    <th class="py-3 px-6 text-left">Neomezený</th>
                    <th class="py-3 px-6 text-left">Akce</th>
                </tr>
            </thead>
            <tbody class="text-gray-600 text-sm font-light">
                <?php foreach ($types as $type): ?>
                <tr class="border-b border-gray-200 hover:bg-gray-100">
                    <td class="py-3 px-6 text-left"><?= htmlspecialchars($type['name']) ?></td>
                    <td class="py-3 px-6 text-left"><?= $type['minutes_limit'] ?></td>
                    <td class="py-3 px-6 text-left"><?= $type['is_unlimited'] ? 'Ano' : 'Ne' ?></td>
                    <td class="py-3 px-6 text-left">
                        <form method="POST" class="inline-block mr-2">
                            <input type="hidden" name="action" value="edit">
                            <input type="hidden" name="id" value="<?= $type['id'] ?>">
                            <input type="text" name="name" value="<?= htmlspecialchars($type['name']) ?>" required class="px-2 py-1 border rounded">
                            <input type="number" name="minutes_limit" value="<?= $type['minutes_limit'] ?>" required class="px-2 py-1 border rounded">
                            <label class="inline-flex items-center">
                                <input type="checkbox" name="is_unlimited" <?= $type['is_unlimited'] ? 'checked' : '' ?> class="mr-1">
                                Neomezený
                            </label>
                            <button type="submit" class="px-2 py-1 bg-green-500 text-white rounded">Upravit</button>
                        </form>
                        <form method="POST" class="inline-block">
                            <input type="hidden" name="action" value="delete">
                            <input type="hidden" name="id" value="<?= $type['id'] ?>">
                            <button type="submit" class="px-2 py-1 bg-red-500 text-white rounded" onclick="return confirm('Opravdu chcete smazat tento typ předplatného?')">Smazat</button>
                        </form>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
</body>
</html>
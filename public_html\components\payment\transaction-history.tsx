"use client"

import { useState } from "react"
import { usePayment } from "@/components/payment/payment-provider"
import { Card } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Skeleton } from "@/components/ui/skeleton"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Download, FileText, CheckCircle, Clock, XCircle, RotateCcw } from "lucide-react"

interface TransactionHistoryProps {
  limit?: number
}

export function TransactionHistory({ limit }: TransactionHistoryProps) {
  const { transactions, isLoading } = usePayment()
  const [filter, setFilter] = useState("")
  const [statusFilter, setStatusFilter] = useState<string | null>(null)
  const [typeFilter, setTypeFilter] = useState<string | null>(null)

  if (isLoading) {
    return (
      <div className="space-y-4">
        {!limit && (
          <div className="flex flex-col sm:flex-row gap-4 justify-between mb-4">
            <Skeleton className="h-10 w-full sm:w-64" />
            <div className="flex gap-2">
              <Skeleton className="h-10 w-32" />
              <Skeleton className="h-10 w-32" />
            </div>
          </div>
        )}
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Datum</TableHead>
                <TableHead>Popis</TableHead>
                <TableHead>Stav</TableHead>
                <TableHead className="text-right">Částka</TableHead>
                <TableHead></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {Array.from({ length: limit || 5 }).map((_, i) => (
                <TableRow key={i}>
                  <TableCell>
                    <Skeleton className="h-5 w-24" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-5 w-40" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-5 w-20" />
                  </TableCell>
                  <TableCell className="text-right">
                    <Skeleton className="h-5 w-16 ml-auto" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-8 w-8 rounded-full" />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    )
  }

  const filteredTransactions = transactions
    .filter(
      (tx) =>
        (filter === "" ||
          tx.description.toLowerCase().includes(filter.toLowerCase()) ||
          tx.paymentMethod.toLowerCase().includes(filter.toLowerCase())) &&
        (statusFilter === null || tx.status === statusFilter) &&
        (typeFilter === null || tx.type === typeFilter),
    )
    .slice(0, limit || undefined)

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("cs-CZ", {
      day: "numeric",
      month: "long",
      year: "numeric",
    })
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case "pending":
        return <Clock className="h-5 w-5 text-amber-500" />
      case "failed":
        return <XCircle className="h-5 w-5 text-red-500" />
      case "refunded":
        return <RotateCcw className="h-5 w-5 text-blue-500" />
      default:
        return null
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case "completed":
        return "Dokončeno"
      case "pending":
        return "Zpracovává se"
      case "failed":
        return "Selhalo"
      case "refunded":
        return "Vráceno"
      default:
        return status
    }
  }

  return (
    <div className="space-y-4">
      {!limit && (
        <div className="flex flex-col sm:flex-row gap-4 justify-between mb-4">
          <div className="w-full sm:w-64">
            <Input placeholder="Vyhledat transakci..." value={filter} onChange={(e) => setFilter(e.target.value)} />
          </div>
          <div className="flex gap-2">
            <Select value={statusFilter || "all"} onValueChange={(value) => setStatusFilter(value || null)}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Stav" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Všechny stavy</SelectItem>
                <SelectItem value="completed">Dokončeno</SelectItem>
                <SelectItem value="pending">Zpracovává se</SelectItem>
                <SelectItem value="failed">Selhalo</SelectItem>
                <SelectItem value="refunded">Vráceno</SelectItem>
              </SelectContent>
            </Select>
            <Select value={typeFilter || "all"} onValueChange={(value) => setTypeFilter(value || null)}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Typ" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Všechny typy</SelectItem>
                <SelectItem value="subscription">Předplatné</SelectItem>
                <SelectItem value="one-time">Jednorázové</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      )}

      {filteredTransactions.length === 0 ? (
        <Card className="p-8 text-center">
          <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-muted">
            <FileText className="h-6 w-6 text-muted-foreground" />
          </div>
          <h3 className="mt-4 text-lg font-semibold">Žádné transakce</h3>
          <p className="mt-2 text-sm text-muted-foreground">
            Nebyly nalezeny žádné transakce odpovídající vašim kritériím.
          </p>
        </Card>
      ) : (
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Datum</TableHead>
                <TableHead>Popis</TableHead>
                <TableHead>Stav</TableHead>
                <TableHead className="text-right">Částka</TableHead>
                <TableHead></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredTransactions.map((transaction) => (
                <TableRow key={transaction.id}>
                  <TableCell>{formatDate(transaction.date)}</TableCell>
                  <TableCell>
                    <div>{transaction.description}</div>
                    <div className="text-xs text-muted-foreground">{transaction.paymentMethod}</div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      {getStatusIcon(transaction.status)}
                      <span className="ml-2">{getStatusText(transaction.status)}</span>
                    </div>
                  </TableCell>
                  <TableCell className="text-right font-medium">{transaction.amount.toLocaleString()} Kč</TableCell>
                  <TableCell>
                    {transaction.invoiceUrl && (
                      <Button variant="ghost" size="icon" asChild>
                        <a href={transaction.invoiceUrl} target="_blank" rel="noopener noreferrer">
                          <Download className="h-4 w-4" />
                          <span className="sr-only">Stáhnout fakturu</span>
                        </a>
                      </Button>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}
    </div>
  )
}


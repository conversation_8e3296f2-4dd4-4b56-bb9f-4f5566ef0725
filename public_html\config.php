<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Custom error logging function
function customErrorLog($message, $errorType = 'ERROR') {
    $logMessage = date('Y-m-d H:i:s') . " [$errorType] $message\n";
    error_log($logMessage, 3, __DIR__ . '/config_error.log');
}

customErrorLog("Starting config.php execution", "INFO");

// Start output buffering
ob_start();

try {
    // Check if a session is already active
    customErrorLog("Checking session status", "INFO");
    if (session_status() == PHP_SESSION_NONE) {
        customErrorLog("No active session, starting new session", "INFO");
        // Session settings before session_start
        ini_set('session.cookie_httponly', 1);
        ini_set('session.use_only_cookies', 1);
        ini_set('session.cookie_secure', 1);
        ini_set('session.gc_maxlifetime', 3600); // 1 hour
        ini_set('session.cookie_lifetime', 0); // Session cookie expires when browser closes
        ini_set('session.cookie_samesite', 'Strict');
        ini_set('session.name', 'DENTIBOTID');

        session_start();
        customErrorLog("New session started", "INFO");
    } else {
        customErrorLog("Session already active", "INFO");
    }

    // Database configuration
    customErrorLog("Setting database configuration", "INFO");
    define('DB_HOST', getenv('DB_HOST') ?: 'localhost');
    define('DB_USER', getenv('DB_USER') ?: 'default_user');
    define('DB_PASS', getenv('DB_PASS') ?: 'default_password');
    define('DB_NAME', getenv('DB_NAME') ?: 'default_database');

    // Error log configuration
    define('ERROR_LOG_FILE', getenv('ERROR_LOG_FILE') ?: '/home/<USER>/domains/dentibot.eu/public_html/app.log');

    // Stripe configuration
    define('STRIPE_SECRET_KEY', getenv('STRIPE_SECRET_KEY'));
    define('STRIPE_PUBLISHABLE_KEY', getenv('STRIPE_PUBLISHABLE_KEY'));
    define('STRIPE_WEBHOOK_SECRET', getenv('STRIPE_WEBHOOK_SECRET'));

    // Subscription limits (in minutes)
    define('SUBSCRIPTION_LIMITS', [
        'starter' => 500,
        'advanced' => 1000,
        'enterprise' => PHP_INT_MAX
    ]);

    // Default assistant ID configuration
    define('DEFAULT_ASSISTANT_ID', getenv('DEFAULT_ASSISTANT_ID') ?: 'default_assistant_id');

    // Encryption configuration for patient messages
    define('PATIENT_ENCRYPTION_KEY', getenv('PATIENT_ENCRYPTION_KEY') ?: 'dentibot_secure_key_2025'); // In production, use environment variable
    define('PATIENT_ENCRYPTION_METHOD', 'AES-256-CBC');

    // Database connection
    customErrorLog("Attempting database connection", "INFO");
    $mysqli = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);

    if ($mysqli->connect_error) {
        throw new Exception('Connect Error (' . $mysqli->connect_errno . ') ' . $mysqli->connect_error);
    }

    customErrorLog("Database connection successful", "INFO");
    $mysqli->set_charset("utf8mb4");
    customErrorLog("Database charset set to utf8mb4", "INFO");

    // Helper function for database connection
    function getDbConnection() {
        global $mysqli;
        if (!$mysqli || $mysqli->connect_error) {
            $mysqli = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
            $mysqli->set_charset("utf8mb4");
        }
        return $mysqli;
    }

    // Authentication functions with session timeout check
    function isLoggedIn() {
        if (!isset($_SESSION['user_id']) || !isset($_SESSION['last_activity'])) {
            customErrorLog("User not logged in: session variables not set", "INFO");
            return false;
        }
        
        // Check for session timeout (30 minutes)
        if (time() - $_SESSION['last_activity'] > 1800) {
            customErrorLog("Session timeout occurred", "INFO");
            logoutUser();
            return false;
        }
        
        // Update last activity time
        $_SESSION['last_activity'] = time();
        customErrorLog("User is logged in, last activity updated", "INFO");
        return true;
    }

    function isAdmin() {
        return isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin';
    }

    function requireLogin() {
        if (!isLoggedIn()) {
            customErrorLog("Login required, redirecting to index.php", "INFO");
            // Store the requested URL for redirect after login
            $_SESSION['redirect_after_login'] = $_SERVER['REQUEST_URI'];
            header('Location: index.php');
            exit;
        }
    }

    function requireAdmin() {
        requireLogin();
        if (!isAdmin()) {
            customErrorLog("Admin access required but user is not admin", "INFO");
            header('Location: dashboard.php?error=unauthorized');
            exit;
        }
    }

    // API configuration function
    function getCurrentUserApiKey() {
        if (!isset($_SESSION['user_id'])) {
            return null;
        }

        $mysqli = getDbConnection();
        $stmt = $mysqli->prepare("SELECT vapi_api_key, vapi_api_url, assistant_id FROM users WHERE id = ?");
        $stmt->bind_param("i", $_SESSION['user_id']);
        $stmt->execute();
        $result = $stmt->get_result();
        $user = $result->fetch_assoc();

        if (!$user || empty($user['vapi_api_key'])) {
            return null;
        }

        return [
            'key' => $user['vapi_api_key'],
            'url' => $user['vapi_api_url'] ?: 'https://api.vapi.ai',
            'assistant_id' => $user['assistant_id'] ?: DEFAULT_ASSISTANT_ID
        ];
    }

    // Subscription functions
    function getUserSubscriptionPlan($user_id) {
        $mysqli = getDbConnection();
        $stmt = $mysqli->prepare("SELECT subscription_plan FROM users WHERE id = ?");
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $user = $result->fetch_assoc();
        $stmt->close();
        return $user['subscription_plan'] ?? 'starter';
    }

    function getUserSubscriptionLimit($user_id) {
        $plan = getUserSubscriptionPlan($user_id);
        return SUBSCRIPTION_LIMITS[$plan] ?? SUBSCRIPTION_LIMITS['starter'];
    }

    function getUserUsage($user_id) {
        $mysqli = getDbConnection();
        $stmt = $mysqli->prepare("
            SELECT COALESCE(CEIL(SUM(duration) / 60), 0) as total_duration 
            FROM vapi_calls 
            WHERE user_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH)
        ");
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $usage = (int)($result->fetch_assoc()['total_duration']);
        $stmt->close();
        return $usage;
    }

    function updateUserSubscriptionPlan($user_id, $new_plan) {
        if (!in_array($new_plan, array_keys(SUBSCRIPTION_LIMITS))) {
            return false;
        }

        $mysqli = getDbConnection();
        $stmt = $mysqli->prepare("UPDATE users SET subscription_plan = ? WHERE id = ?");
        $stmt->bind_param("si", $new_plan, $user_id);
        $success = $stmt->execute();
        $stmt->close();
        return $success;
    }

    // Security functions with enhanced CSRF protection
    function generateCSRFToken() {
        if (!isset($_SESSION['csrf_token']) || !isset($_SESSION['csrf_token_time']) || 
            time() - $_SESSION['csrf_token_time'] > 3600) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
            $_SESSION['csrf_token_time'] = time();
        }
        return $_SESSION['csrf_token'];
    }

    function verifyCSRFToken($token) {
        return isset($_SESSION['csrf_token']) && 
               isset($_SESSION['csrf_token_time']) && 
               time() - $_SESSION['csrf_token_time'] <= 3600 && 
               hash_equals($_SESSION['csrf_token'], $token);
    }

    // Input sanitization
    function sanitizeInput($input) {
        if (is_array($input)) {
            return array_map('sanitizeInput', $input);
        }
        return htmlspecialchars(strip_tags(trim($input)), ENT_QUOTES, 'UTF-8');
    }

    // Error logging
    function writeErrorLog($message, $context = []) {
        $timestamp = date('Y-m-d H:i:s');
        $contextStr = !empty($context) ? ' ' . json_encode($context) : '';
        $logMessage = "[$timestamp] $message$contextStr\n";

        if (!file_exists(ERROR_LOG_FILE)) {
            touch(ERROR_LOG_FILE);
            chmod(ERROR_LOG_FILE, 0666);
        }

        error_log($logMessage, 3, ERROR_LOG_FILE);
    }

    // Logout function
    function logoutUser() {
        customErrorLog("Logout process started", "INFO");
        // Destroy session
        $_SESSION = array();
        if (ini_get("session.use_cookies")) {
            $params = session_get_cookie_params();
            setcookie(session_name(), '', time() - 42000,
                $params["path"], $params["domain"],
                $params["secure"], $params["httponly"]
            );
        }
        session_destroy();
        customErrorLog("Session destroyed during logout", "INFO");
    }

    // Set timezone
    date_default_timezone_set('Europe/Prague');

    // New security functions
    function set_content_security_policy() {
        $csp = "default-src 'self'; " .
               "script-src 'self' https://cdn.jsdelivr.net; " .
               "style-src 'self' https://cdn.jsdelivr.net https://fonts.googleapis.com; " .
               "font-src 'self' https://fonts.gstatic.com; " .
               "img-src 'self' data:; " .
               "connect-src 'self';";
        header("Content-Security-Policy: " . $csp);
    }

    function set_security_headers() {
        header("X-XSS-Protection: 1; mode=block");
        header("X-Frame-Options: SAMEORIGIN");
        header("X-Content-Type-Options: nosniff");
        header("Referrer-Policy: strict-origin-when-cross-origin");
        header("Permissions-Policy: geolocation=(), microphone=(), camera=()");
    }

    // Encryption functions for patient messages - renamed to avoid conflicts
    function encryptPatientData($data) {
        if (empty($data)) {
            return [
                'data' => '',
                'iv' => ''
            ];
        }
        
        try {
            $iv = openssl_random_pseudo_bytes(openssl_cipher_iv_length(PATIENT_ENCRYPTION_METHOD));
            $encrypted = openssl_encrypt($data, PATIENT_ENCRYPTION_METHOD, PATIENT_ENCRYPTION_KEY, 0, $iv);
            
            if ($encrypted === false) {
                writeErrorLog("Patient data encryption failed", ['error' => openssl_error_string()]);
                return [
                    'data' => '',
                    'iv' => ''
                ];
            }
            
            return [
                'data' => $encrypted,
                'iv' => bin2hex($iv)
            ];
        } catch (Exception $e) {
            writeErrorLog("Exception during patient data encryption", ['error' => $e->getMessage()]);
            return [
                'data' => '',
                'iv' => ''
            ];
        }
    }

    function decryptPatientData($encryptedData, $iv) {
        if (empty($encryptedData) || empty($iv)) {
            return '';
        }
        
        try {
            $iv = hex2bin($iv);
            $decrypted = openssl_decrypt($encryptedData, PATIENT_ENCRYPTION_METHOD, PATIENT_ENCRYPTION_KEY, 0, $iv);
            
            if ($decrypted === false) {
                writeErrorLog("Patient data decryption failed", ['error' => openssl_error_string()]);
                return '';
            }
            
            return $decrypted;
        } catch (Exception $e) {
            writeErrorLog("Exception during patient data decryption", ['error' => $e->getMessage()]);
            return '';
        }
    }

    // Function to create patient_messages table if it doesn't exist
    function ensurePatientMessagesTable() {
        try {
            $mysqli = getDbConnection();
            
            // Check if table exists
            $result = $mysqli->query("SHOW TABLES LIKE 'patient_messages'");
            $tableExists = $result && $result->num_rows > 0;
            
            if (!$tableExists) {
                $createTableSql = "
                    CREATE TABLE `patient_messages` (
                      `id` int(11) NOT NULL AUTO_INCREMENT,
                      `name_encrypted` text NOT NULL,
                      `phone_encrypted` text NOT NULL,
                      `message_encrypted` text NOT NULL,
                      `timestamp` timestamp NOT NULL DEFAULT current_timestamp(),
                      `read` tinyint(1) NOT NULL DEFAULT 0,
                      `encryption_iv` varchar(32) NOT NULL,
                      `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
                      PRIMARY KEY (`id`),
                      KEY `idx_timestamp` (`timestamp`),
                      KEY `idx_read` (`read`)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
                ";
                
                $createResult = $mysqli->query($createTableSql);
                
                if ($createResult) {
                    writeErrorLog("Created patient_messages table", ['status' => 'success']);
                } else {
                    writeErrorLog("Failed to create patient_messages table", ['error' => $mysqli->error]);
                }
            }
        } catch (Exception $e) {
            writeErrorLog("Exception checking/creating patient_messages table", ['error' => $e->getMessage()]);
        }
    }

    // Set security headers
    set_security_headers();

    // Set Content Security Policy
    set_content_security_policy();

    // Ensure patient_messages table exists
    ensurePatientMessagesTable();

    customErrorLog("Config file execution completed successfully", "INFO");

} catch (Exception $e) {
    customErrorLog("Error in config.php: " . $e->getMessage(), "ERROR");
    // Log the error but don't expose details to the user
    die("Došlo k chybě. Prosím zkuste to později.");
}

// Flush the output buffer
if (ob_get_length()) ob_end_flush();
?>
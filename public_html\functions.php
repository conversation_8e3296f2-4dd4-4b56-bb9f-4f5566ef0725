<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Include the config file to ensure database connection is available
require_once 'config.php';

// Function to get reservations for a specific date
function getReservations($date) {
    global $mysqli;

    try {
        error_log("getReservations called with date: " . $date);

        if (!$mysqli) {
            error_log("mysqli is not available, attempting to reconnect");
            $mysqli = getDbConnection();
        }

        if (!$mysqli) {
            throw new Exception("Failed to establish database connection");
        }

        error_log("MySQL connection status: " . ($mysqli->ping() ? "connected" : "disconnected"));

        $query = "SELECT id, patient_name, date, time, notes FROM reservations WHERE date = ? ORDER BY time ASC";
        error_log("Preparing query: " . $query);
        
        $stmt = $mysqli->prepare($query);
        if (!$stmt) {
            throw new Exception("Database prepare error: " . $mysqli->error);
        }

        error_log("Query prepared successfully");

        if (!$stmt->bind_param("s", $date)) {
            throw new Exception("Parameter binding error: " . $stmt->error);
        }

        error_log("Parameters bound successfully");

        if (!$stmt->execute()) {
            throw new Exception("Execution error: " . $stmt->error);
        }

        error_log("Query executed successfully");

        $result = $stmt->get_result();
        if (!$result) {
            throw new Exception("Result error: " . $stmt->error);
        }

        $reservations = [];
        while ($row = $result->fetch_assoc()) {
            $reservations[] = $row;
        }

        error_log("Found " . count($reservations) . " reservations");

        $stmt->close();

        return $reservations;
    } catch (Exception $e) {
        error_log("Error in getReservations(): " . $e->getMessage() . "\nTrace: " . $e->getTraceAsString());
        if ($mysqli) {
            error_log("MySQL Error: " . $mysqli->error);
        }
        throw $e;
    }
}

// Function to add a new reservation
function addReservation($patient_name, $date, $time, $notes = '') {
    global $mysqli;

    try {
        $query = "INSERT INTO reservations (patient_name, date, time, notes) VALUES (?, ?, ?, ?)";
        $stmt = $mysqli->prepare($query);
        
        if (!$stmt) {
            throw new Exception("Prepare failed: " . $mysqli->error);
        }

        $stmt->bind_param("ssss", $patient_name, $date, $time, $notes);
        
        if (!$stmt->execute()) {
            throw new Exception("Execute failed: " . $stmt->error);
        }

        $stmt->close();
        return true;
    } catch (Exception $e) {
        error_log("Error in addReservation: " . $e->getMessage());
        throw $e;
    }
}

// Function to update an existing reservation
function updateReservation($id, $patient_name, $date, $time, $notes = '') {
    global $mysqli;

    try {
        $query = "UPDATE reservations SET patient_name = ?, date = ?, time = ?, notes = ? WHERE id = ?";
        $stmt = $mysqli->prepare($query);
        
        if (!$stmt) {
            throw new Exception("Prepare failed: " . $mysqli->error);
        }

        $stmt->bind_param("ssssi", $patient_name, $date, $time, $notes, $id);
        
        if (!$stmt->execute()) {
            throw new Exception("Execute failed: " . $stmt->error);
        }

        $stmt->close();
        return true;
    } catch (Exception $e) {
        error_log("Error in updateReservation: " . $e->getMessage());
        throw $e;
    }
}

// Function to delete a reservation
function deleteReservation($id) {
    global $mysqli;

    try {
        $query = "DELETE FROM reservations WHERE id = ?";
        $stmt = $mysqli->prepare($query);
        
        if (!$stmt) {
            throw new Exception("Prepare failed: " . $mysqli->error);
        }

        $stmt->bind_param("i", $id);
        
        if (!$stmt->execute()) {
            throw new Exception("Execute failed: " . $stmt->error);
        }

        $stmt->close();
        return true;
    } catch (Exception $e) {
        error_log("Error in deleteReservation: " . $e->getMessage());
        throw $e;
    }
}

// Function to get a single reservation by ID
function getReservationById($id) {
    global $mysqli;

    try {
        $query = "SELECT id, patient_name, date, time, notes FROM reservations WHERE id = ?";
        $stmt = $mysqli->prepare($query);
        
        if (!$stmt) {
            throw new Exception("Prepare failed: " . $mysqli->error);
        }

        $stmt->bind_param("i", $id);
        
        if (!$stmt->execute()) {
            throw new Exception("Execute failed: " . $stmt->error);
        }

        $result = $stmt->get_result();
        $reservation = $result->fetch_assoc();

        $stmt->close();
        return $reservation;
    } catch (Exception $e) {
        error_log("Error in getReservationById: " . $e->getMessage());
        throw $e;
    }
}
/**
 * Get user's subscription minute limit
 * 
 * @param int $user_id User ID
 * @return int Minute limit
 */
function getUserSubscriptionLimit($user_id) {
    global $mysqli;
    
    try {
        $query = "SELECT minute_limit FROM users WHERE id = ?";
        $stmt = $mysqli->prepare($query);
        
        if (!$stmt) {
            throw new Exception("Prepare failed: " . $mysqli->error);
        }

        $stmt->bind_param("i", $user_id);
        
        if (!$stmt->execute()) {
            throw new Exception("Execute failed: " . $stmt->error);
        }

        $stmt->bind_result($limit);
        $stmt->fetch();
        $stmt->close();
        
        return $limit;
    } catch (Exception $e) {
        error_log("Error in getUserSubscriptionLimit: " . $e->getMessage());
        // Return a default value in case of error
        return 500;
    }
}
?>





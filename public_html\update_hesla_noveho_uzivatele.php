<?php
require_once 'config.php';
require_once 'error_log.php';

function updateUserPassword($username, $plainPassword) {
    try {
        $mysqli = getDbConnection();
        
        // Hash the password
        $hashedPassword = password_hash($plainPassword, PASSWORD_DEFAULT);
        
        // Update the password in the database
        $stmt = $mysqli->prepare("UPDATE users SET password = ? WHERE username = ?");
        $stmt->bind_param("ss", $hashedPassword, $username);
        
        if ($stmt->execute()) {
            echo "Password successfully updated for user: " . htmlspecialchars($username);
            writeErrorLog('password_update', "Password hash updated for user: $username");
        } else {
            echo "Error updating password: " . $mysqli->error;
            writeErrorLog('password_update_error', "Failed to update password for user: $username");
        }
        
        $stmt->close();
    } catch (Exception $e) {
        echo "Error: " . $e->getMessage();
        writeErrorLog('password_update_error', $e->getMessage());
    }
}

// Update matyastroup's password
updateUserPassword('matyastroup', 'trumpeta');


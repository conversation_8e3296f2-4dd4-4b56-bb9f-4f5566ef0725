<?php
require_once __DIR__ . '/../config.php';

function getUsedMinutes($user_id) {
    $conn = getDbConnection();
    if (!$conn) {
        error_log('Failed to establish database connection in getUsedMinutes');
        return 0;
    }
    
    $stmt = null;
    
    try {
        $stmt = $conn->prepare("SELECT used_minutes FROM users WHERE id = ?");
        if (!$stmt) {
            throw new Exception("Failed to prepare statement: " . $conn->error);
        }
        
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        
        $result = $stmt->get_result();
        if ($row = $result->fetch_assoc()) {
            return intval($row['used_minutes']);
        }
        
        return 0; // Return 0 if no data found
    } catch (Exception $e) {
        error_log('Get used minutes error: ' . json_encode([
            'message' => $e->getMessage(),
            'user_id' => $user_id
        ]));
        return 0; // Return 0 in case of error
    } finally {
        if ($stmt) {
            $stmt->close();
        }
        // We don't close the connection here anymore
    }
}

function updateUsedMinutes($user_id, $used_minutes) {
    $conn = getDbConnection();
    if (!$conn) {
        error_log('Failed to establish database connection in updateUsedMinutes');
        return false;
    }
    
    $stmt = null;
    
    try {
        $stmt = $conn->prepare("UPDATE users SET used_minutes = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
        if (!$stmt) {
            throw new Exception("Failed to prepare statement: " . $conn->error);
        }
        
        $stmt->bind_param("ii", $used_minutes, $user_id);
        $result = $stmt->execute();
        
        if ($result) {
            error_log('Used minutes updated: ' . json_encode([
                'user_id' => $user_id,
                'used_minutes' => $used_minutes,
                'timestamp' => date('Y-m-d H:i:s')
            ]));
            return true;
        }
        
        return false;
    } catch (Exception $e) {
        error_log('Update used minutes error: ' . json_encode([
            'message' => $e->getMessage(),
            'user_id' => $user_id,
            'used_minutes' => $used_minutes
        ]));
        return false;
    } finally {
        if ($stmt) {
            $stmt->close();
        }
        // We don't close the connection here anymore
    }
}


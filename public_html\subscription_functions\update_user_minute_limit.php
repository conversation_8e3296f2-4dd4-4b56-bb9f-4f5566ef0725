<?php
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../error_log.php';

function updateUserMinuteLimitFromSubscription($user_id, $subscription_limit) {
    try {
        $mysqli = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
        
        if ($mysqli->connect_error) {
            writeErrorLog("Database connection failed in updateUserMinuteLimitFromSubscription", [
                'error' => $mysqli->connect_error,
                'user_id' => $user_id
            ]);
            return false;
        }

        // Prepare the update statement
        $stmt = $mysqli->prepare("UPDATE users SET minute_limit = ? WHERE id = ?");
        if (!$stmt) {
            writeErrorLog("Failed to prepare statement in updateUserMinuteLimitFromSubscription", [
                'error' => $mysqli->error,
                'user_id' => $user_id
            ]);
            return false;
        }

        // Bind parameters and execute
        $stmt->bind_param("ii", $subscription_limit, $user_id);
        $result = $stmt->execute();

        if (!$result) {
            writeErrorLog("Failed to update user minute limit", [
                'error' => $stmt->error,
                'user_id' => $user_id,
                'subscription_limit' => $subscription_limit
            ]);
            return false;
        }

        $stmt->close();
        $mysqli->close();

        writeErrorLog("Successfully updated user minute limit", [
            'user_id' => $user_id,
            'subscription_limit' => $subscription_limit
        ]);

        return true;
    } catch (Exception $e) {
        writeErrorLog("Exception in updateUserMinuteLimitFromSubscription", [
            'error' => $e->getMessage(),
            'user_id' => $user_id,
            'subscription_limit' => $subscription_limit
        ]);
        return false;
    }
}


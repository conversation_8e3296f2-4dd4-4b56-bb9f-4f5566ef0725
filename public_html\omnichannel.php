<?php
require_once 'config.php';
require_once 'api_functions.php';
require_once 'error_log.php';

// Initialize variables
$patients = [];
$conversations = [];
$totalPatients = 0;
$error = null;
$success = null;

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Kontrola přihlášení
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

$currentPage = 'omnichannel';

try {
    // Get database connection with error logging
    $mysqli = getDbConnection();
    if (!$mysqli) {
        throw new Exception("Database connection failed");
    }

    // Debug log
    writeErrorLog('Debug: Database connection successful', [
        'user_id' => $_SESSION['user_id']
    ]);

    // Load patients first - with detailed error logging
    $patientsQuery = "
        SELECT 
            p.id,
            p.name,
            p.phone,
            p.email,
            p.whatsapp,
            p.messenger,
            GROUP_CONCAT(DISTINCT pc.channel) as channels
        FROM patients p
        LEFT JOIN patient_channels pc ON p.id = pc.patient_id
        WHERE p.deleted_at IS NULL
        GROUP BY p.id
        ORDER BY p.name ASC
    ";

    // Debug log the query
    writeErrorLog('Debug: Executing patients query', [
        'query' => $patientsQuery
    ]);

    $stmt = $mysqli->prepare($patientsQuery);
    if (!$stmt) {
        throw new Exception("Error preparing patients query: " . $mysqli->error);
    }

    if (!$stmt->execute()) {
        throw new Exception("Error executing patients query: " . $stmt->error);
    }

    $result = $stmt->get_result();
    if (!$result) {
        throw new Exception("Error getting result set: " . $mysqli->error);
    }

    // Debug log
    writeErrorLog('Debug: Query executed successfully', [
        'num_rows' => $result->num_rows
    ]);

    while ($row = $result->fetch_assoc()) {
        // Initialize channels array
        $row['channels'] = [];
        
        // Add channels based on contact info with validation
        if (!empty($row['email'])) $row['channels'][] = 'email';
        if (!empty($row['phone'])) $row['channels'][] = 'sms';
        if (!empty($row['whatsapp']) && $row['whatsapp'] == 1) $row['channels'][] = 'whatsapp';
        if (!empty($row['messenger']) && $row['messenger'] == 1) $row['channels'][] = 'messenger';
        
        // Add any additional channels from patient_channels table
        if (!empty($row['channels'])) {
            $additionalChannels = explode(',', $row['channels']);
            $row['channels'] = array_merge($row['channels'], $additionalChannels);
        }
        
        // Remove duplicates and null values
        $row['channels'] = array_unique(array_filter($row['channels']));
        
        // Debug log each patient
        writeErrorLog('Debug: Processing patient', [
            'patient_id' => $row['id'],
            'channels' => $row['channels']
        ]);

        $patients[] = $row;
    }
    $stmt->close();

    // Set total patients count
    $totalPatients = count($patients);

    // Debug log
    writeErrorLog('Debug: Patients loaded successfully', [
        'total_patients' => $totalPatients
    ]);

    // Now load conversations
    $conversationsQuery = "
        SELECT c.*, p.name as patient_name, p.phone, p.email 
        FROM conversations c 
        LEFT JOIN patients p ON c.patient_id = p.id 
        WHERE c.user_id = ?
        ORDER BY c.last_message_time DESC
    ";

    $stmt = $mysqli->prepare($conversationsQuery);
    if (!$stmt) {
        throw new Exception("Error preparing conversations query: " . $mysqli->error);
    }

    $stmt->bind_param("i", $_SESSION['user_id']);
    $stmt->execute();
    $result = $stmt->get_result();
    
    while ($row = $result->fetch_assoc()) {
        $conversations[] = $row;
    }
    $stmt->close();

} catch (Exception $e) {
    // Detailed error logging
    writeErrorLog('Error in omnichannel.php', [
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString(),
        'user_id' => $_SESSION['user_id'] ?? 'not set',
        'query' => $patientsQuery ?? 'not set'
    ]);
    
    // Set user-friendly error message
    $error = "Došlo k chybě při načítání dat. Prosím zkuste to znovu později. (Error: " . $e->getMessage() . ")";
    
    // Initialize empty arrays if they weren't set
    if (!isset($patients)) $patients = [];
    if (!isset($conversations)) $conversations = [];
    if (!isset($totalPatients)) $totalPatients = 0;
} finally {
    // Close database connection if it exists
    if (isset($mysqli)) {
        $mysqli->close();
    }
}
?>

<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Omnichannel Chat</title>
    
    <!-- Load React -->
    <script crossorigin src="https://unpkg.com/react@17/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@17/umd/react-dom.development.js"></script>
    
    <!-- Load Tailwind -->
    <script src="https://cdn.tailwindcss.com"></script>

    <style>
        :root {
            --bg-primary: #1e293b;
            --bg-secondary: #1f2937;
            --text-primary: #f8fafc;
            --text-secondary: #94a3b8;
            --accent: #0d9488;
            --border: #334155;
            --input-bg: #374151;
        }

        .conversation-list {
            background-color: var(--bg-secondary);
            border-right: 1px solid var(--border);
        }

        .chat-window {
            background-color: var(--bg-primary);
        }

        .message-input {
            background-color: var(--input-bg);
            border: 1px solid var(--border);
            color: var(--text-primary);
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            width: 100%;
        }

        .message-input::placeholder {
            color: var(--text-secondary);
        }

        .send-button {
            background-color: var(--accent);
            transition: opacity 0.2s;
        }

        .send-button:hover {
            opacity: 0.9;
        }

        .nav-tabs button {
            color: var(--text-secondary);
            font-weight: 500;
            opacity: 0.7;
            transition: opacity 0.2s;
        }

        .nav-tabs button:hover {
            opacity: 1;
        }

        .nav-tabs button.active {
            color: var(--accent);
            opacity: 1;
        }

        .search-input {
            background-color: var(--input-bg);
            border: 1px solid var(--border);
            color: var(--text-primary);
        }

        .search-input::placeholder {
            color: var(--text-secondary);
        }

        .message-bubble {
            background-color: var(--bg-secondary);
            border: 1px solid var(--border);
        }

        .message-bubble.outgoing {
            background-color: var(--accent);
            border: none;
        }

        .channel-badge {
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            font-size: 0.75rem;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            border: 1px solid var(--border);
            opacity: 0.9;
        }

        .timestamp {
            color: var(--text-primary);
            font-size: 0.75rem;
            opacity: 0.7;
        }

        .contact-details {
            background-color: var(--bg-secondary);
            border-left: 1px solid var(--border);
            color: var(--text-primary);
            padding: 1rem;
        }

        .contact-details h3 {
            color: var(--text-primary);
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .tag {
            background-color: var(--bg-primary);
            color: var(--text-secondary);
            border: 1px solid var(--border);
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
        }

        .create-opportunity-btn {
            background-color: var(--bg-primary);
            color: var(--text-secondary);
            border: 1px solid var(--border);
            padding: 0.5rem;
            border-radius: 0.5rem;
            width: 100%;
            margin-top: 1rem;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .create-opportunity-btn:hover {
            border-color: var(--accent);
            color: var(--text-primary);
        }

        select.channel-select {
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            border: 1px solid var(--border);
        }

        .message-header {
            color: #f8fafc;
            font-weight: 500;
            opacity: 1;
        }

        .chat-window .h-16 {
            background-color: #1f2937;
            border-bottom: 1px solid #334155;
        }
    </style>

    <!-- Inline all our components -->
    <script>
        // Omnichannel Context
        const OmnichannelContext = React.createContext();

        function OmnichannelProvider({ children }) {
            const [conversations, setConversations] = React.useState([
                // Sample data for testing
                {
                    id: '1',
                    patientName: 'Jan Novák',
                    lastMessage: 'Dobrý den, kdy mám přijít na kontrolu?',
                    lastMessageTime: new Date().toISOString(),
                    channel: 'email',
                    unread: 2,
                    phone: '+420 123 456 789',
                    email: '<EMAIL>',
                    tags: ['VIP', 'Kontrola'],
                    messages: [
                        {
                            id: '1',
                            text: 'Dobrý den, kdy mám přijít na kontrolu?',
                            sender: 'patient',
                            timestamp: new Date().toISOString(),
                            channel: 'email'
                        }
                    ]
                }
            ]);
            const [selectedId, setSelectedId] = React.useState(null);

            const selectedConversation = React.useMemo(() => 
                conversations.find(c => c.id === selectedId) || null,
                [conversations, selectedId]
            );

            const selectConversation = React.useCallback((id) => {
                setSelectedId(id);
            }, []);

            const sendMessage = React.useCallback(async (text) => {
                if (!selectedConversation) return;

                try {
                    const response = await fetch('/api/omnichannel/send_message.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            conversationId: selectedConversation.id,
                            text,
                            channel: selectedConversation.channel
                        }),
                    });

                    if (!response.ok) {
                        throw new Error('Failed to send message');
                    }

                    const newMessage = {
                        id: Date.now().toString(),
                        text,
                        sender: 'dentist',
                        timestamp: new Date().toISOString(),
                        channel: selectedConversation.channel
                    };

                    setConversations(prev => prev.map(conv => {
                        if (conv.id === selectedConversation.id) {
                            return {
                                ...conv,
                                lastMessage: text,
                                lastMessageTime: new Date().toISOString(),
                                messages: [...conv.messages, newMessage]
                            };
                        }
                        return conv;
                    }));
                } catch (error) {
                    console.error('Failed to send message:', error);
                }
            }, [selectedConversation]);

            const value = React.useMemo(() => ({
                conversations,
                selectedConversation,
                selectConversation,
                sendMessage
            }), [conversations, selectedConversation, selectConversation, sendMessage]);

            return React.createElement(
                OmnichannelContext.Provider,
                { value: value },
                children
            );
        }

        function useOmnichannel() {
            const context = React.useContext(OmnichannelContext);
            if (context === undefined) {
                throw new Error('useOmnichannel must be used within an OmnichannelProvider');
            }
            return context;
        }

        window.OmnichannelContext = {
            OmnichannelProvider,
            useOmnichannel
        };

        // Conversation List Component
        const ConversationListComponent = (() => {
            function ConversationList() {
                const [activeTab, setActiveTab] = React.useState('unread');
                const { conversations, selectConversation } = window.OmnichannelContext.useOmnichannel();

                const getChannelIcon = (channel) => {
                    const iconProps = {
                        className: "h-4 w-4",
                        fill: "none",
                        stroke: "currentColor",
                        viewBox: "0 0 24 24"
                    };

                    const paths = {
                        email: "M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z",
                        sms: "M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z",
                        whatsapp: "M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                    };

                    return React.createElement(
                        'svg',
                        iconProps,
                        React.createElement('path', {
                            strokeLinecap: "round",
                            strokeLinejoin: "round",
                            strokeWidth: 2,
                            d: paths[channel] || paths.sms
                        })
                    );
                };

                return React.createElement(
                    'div',
                    { className: "flex flex-col h-full w-80 bg-[var(--bg-secondary)] border-r border-[var(--border)] conversation-list" },
                    React.createElement(
                        'div',
                        { className: "p-4 border-b nav-tabs" },
                        React.createElement(
                            'div',
                            { className: "flex space-x-4 mb-4" },
                            React.createElement(
                                'button',
                                {
                                    className: `text-sm font-medium ${activeTab === 'unread' ? 'active' : ''}`,
                                    onClick: () => setActiveTab('unread')
                                },
                                "Nepřečtené"
                            ),
                            React.createElement(
                                'button',
                                {
                                    className: `text-sm font-medium ${activeTab === 'recent' ? 'active' : ''}`,
                                    onClick: () => setActiveTab('recent')
                                },
                                "Nedávné"
                            ),
                            React.createElement(
                                'button',
                                {
                                    className: `text-sm font-medium ${activeTab === 'all' ? 'active' : ''}`,
                                    onClick: () => setActiveTab('all')
                                },
                                "Vše"
                            )
                        ),
                        React.createElement(
                            'div',
                            { className: "relative" },
                            React.createElement('input', {
                                type: "text",
                                placeholder: "Hledat konverzace...",
                                className: "w-full px-4 py-2 pl-10 bg-[#374151] border border-[#334155] rounded-lg text-gray-100 placeholder-gray-400 focus:outline-none focus:border-[#0d9488] search-input"
                            }),
                            React.createElement(
                                'svg',
                                {
                                    className: "absolute left-3 top-3 h-4 w-4 text-gray-400",
                                    fill: "none",
                                    stroke: "currentColor",
                                    viewBox: "0 0 24 24"
                                },
                                React.createElement('path', {
                                    strokeLinecap: "round",
                                    strokeLinejoin: "round",
                                    strokeWidth: 2,
                                    d: "M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                                })
                            )
                        )
                    ),
                    React.createElement(
                        'div',
                        { className: "flex-1 overflow-y-auto" },
                        conversations.map((conv) =>
                            React.createElement(
                                'div',
                                {
                                    key: conv.id,
                                    className: "p-4 border-b hover:bg-gray-50 cursor-pointer",
                                    onClick: () => selectConversation(conv.id)
                                },
                                React.createElement(
                                    'div',
                                    { className: "flex items-center justify-between mb-1" },
                                    React.createElement('span', { className: "font-medium" }, conv.patientName),
                                    React.createElement(
                                        'span',
                                        { className: "text-sm text-gray-500" },
                                        new Date(conv.lastMessageTime).toLocaleTimeString()
                                    )
                                ),
                                React.createElement(
                                    'div',
                                    { className: "text-sm text-gray-600 truncate" },
                                    conv.lastMessage
                                ),
                                React.createElement(
                                    'div',
                                    { className: "flex items-center gap-2 mt-2" },
                                    getChannelIcon(conv.channel),
                                    React.createElement(
                                        'span',
                                        { className: "text-xs channel-badge" },
                                        conv.channel
                                    ),
                                    conv.unread > 0 && React.createElement(
                                        'span',
                                        { className: "ml-auto bg-red-500 text-white text-xs px-2 py-1 rounded-full" },
                                        conv.unread
                                    )
                                )
                            )
                        )
                    )
                );
            }

            return { ConversationList };
        })();

        window.ConversationListComponent = ConversationListComponent;

        // Chat Window Component
        const ChatWindowComponent = (() => {
            function ChatWindow() {
                const [message, setMessage] = React.useState('');
                const { selectedConversation, sendMessage } = window.OmnichannelContext.useOmnichannel();

                const handleSend = () => {
                    if (!message.trim()) return;
                    sendMessage(message);
                    setMessage('');
                };

                if (!selectedConversation) {
                    return React.createElement(
                        'div',
                        { className: "flex-1 flex items-center justify-center bg-gray-50" },
                        React.createElement(
                            'p',
                            { className: "text-gray-500" },
                            "Vyberte konverzaci pro zobrazení zpráv"
                        )
                    );
                }

                return React.createElement(
                    'div',
                    { className: "flex-1 flex flex-col chat-window" },
                    React.createElement(
                        'div',
                        { className: "h-16 bg-[#1f2937] border-b flex items-center justify-between px-6" },
                        React.createElement(
                            'div',
                            { className: "flex items-center gap-2" },
                            React.createElement(
                                'span',
                                { className: "font-medium text-[#f8fafc]" },
                                selectedConversation.patientName
                            ),
                            React.createElement(
                                'span',
                                { className: "text-xs bg-[#1f2937] text-[#f8fafc] px-2 py-1 rounded-full border border-[#334155]" },
                                selectedConversation.channel
                            )
                        )
                    ),
                    React.createElement(
                        'div',
                        { className: "flex-1 overflow-y-auto p-6 bg-gray-50" },
                        selectedConversation.messages.map((msg) =>
                            React.createElement(
                                'div',
                                {
                                    key: msg.id,
                                    className: `flex ${msg.sender === 'patient' ? 'justify-end' : 'justify-start'} mb-4`
                                },
                                React.createElement(
                                    'div',
                                    {
                                        className: `max-w-xl rounded-lg p-3 ${msg.sender === 'patient' ? 'bg-[#0d9488] text-white' : 'bg-[#1f2937] border border-[#334155] text-gray-100 message-bubble'}`
                                    },
                                    React.createElement(
                                        'div',
                                        { className: "text-sm" },
                                        msg.text
                                    ),
                                    React.createElement(
                                        'div',
                                        { className: "timestamp" },
                                        `${new Date(msg.timestamp).toLocaleTimeString()} · ${msg.channel}`
                                    )
                                )
                            )
                        )
                    ),
                    React.createElement(
                        'div',
                        { className: "bg-white border-t p-4" },
                        React.createElement(
                            'div',
                            { className: "flex items-center gap-4" },
                            React.createElement(
                                'select',
                                {
                                    className: "channel-select border rounded-lg px-3 py-2 focus:outline-none focus:border-indigo-500",
                                    defaultValue: selectedConversation.channel
                                },
                                React.createElement('option', { value: "email" }, "Email"),
                                React.createElement('option', { value: "sms" }, "SMS"),
                                React.createElement('option', { value: "whatsapp" }, "WhatsApp"),
                                React.createElement('option', { value: "messenger" }, "Messenger")
                            ),
                            React.createElement(
                                'div',
                                { className: "flex-1 relative" },
                                React.createElement('input', {
                                    type: "text",
                                    value: message,
                                    onChange: (e) => setMessage(e.target.value),
                                    placeholder: "Napište zprávu...",
                                    className: "w-full px-4 py-2 bg-[#374151] border border-[#334155] rounded-lg text-gray-100 placeholder-gray-400 focus:outline-none focus:border-[#0d9488] message-input",
                                    onKeyPress: (e) => e.key === 'Enter' && handleSend()
                                })
                            ),
                            React.createElement(
                                'button',
                                {
                                    onClick: handleSend,
                                    className: "px-4 py-2 bg-[#0d9488] text-white rounded-lg hover:opacity-90 transition-opacity flex items-center gap-2 send-button"
                                },
                                React.createElement(
                                    'span', null, "Odeslat"
                                )
                            )
                        )
                    )
                );
            }

            return { ChatWindow };
        })();

        window.ChatWindowComponent = ChatWindowComponent;

        // Contact Details Component
        const ContactDetailsComponent = (() => {
            function ContactDetails() {
                const { selectedConversation } = window.OmnichannelContext.useOmnichannel();

                if (!selectedConversation) {
                    return null;
                }

                return React.createElement(
                    'div',
                    { className: "w-64 contact-details" },
                    React.createElement('h3', { className: "font-medium mb-4" }, "Detaily kontaktu"),
                    React.createElement(
                        'div',
                        { className: "space-y-4" },
                        React.createElement(
                            'div',
                            null,
                            React.createElement(
                                'label',
                                { className: "text-sm text-gray-500 flex items-center gap-2" },
                                React.createElement(
                                    'svg',
                                    {
                                        className: "h-4 w-4",
                                        fill: "none",
                                        stroke: "currentColor",
                                        viewBox: "0 0 24 24"
                                    },
                                    React.createElement('path', {
                                        strokeLinecap: "round",
                                        strokeLinejoin: "round",
                                        strokeWidth: 2,
                                        d: "M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                                    })
                                ),
                                "Telefon"
                            ),
                            React.createElement('p', { className: "font-medium" }, selectedConversation.phone)
                        ),
                        React.createElement(
                            'div',
                            null,
                            React.createElement(
                                'label',
                                { className: "text-sm text-gray-500 flex items-center gap-2" },
                                React.createElement(
                                    'svg',
                                    {
                                        className: "h-4 w-4",
                                        fill: "none",
                                        stroke: "currentColor",
                                        viewBox: "0 0 24 24"
                                    },
                                    React.createElement('path', {
                                        strokeLinecap: "round",
                                        strokeLinejoin: "round",
                                        strokeWidth: 2,
                                        d: "M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                                    })
                                ),
                                "Email"
                            ),
                            React.createElement('p', { className: "font-medium" }, selectedConversation.email)
                        ),
                        React.createElement(
                            'div',
                            null,
                            React.createElement(
                                'label',
                                { className: "text-sm text-gray-500 flex items-center gap-2" },
                                React.createElement(
                                    'svg',
                                    {
                                        className: "h-4 w-4",
                                        fill: "none",
                                        stroke: "currentColor",
                                        viewBox: "0 0 24 24"
                                    },
                                    React.createElement('path', {
                                        strokeLinecap: "round",
                                        strokeLinejoin: "round",
                                        strokeWidth: 2,
                                        d: "M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"
                                    })
                                ),
                                "Štítky"
                            ),
                            React.createElement(
                                'div',
                                { className: "flex flex-wrap gap-2 mt-1" },
                                selectedConversation.tags.map((tag) =>
                                    React.createElement(
                                        'span',
                                        {
                                            key: tag,
                                            className: "bg-gray-200 text-gray-700 text-xs px-2 py-1 rounded-full tag"
                                        },
                                        tag
                                    )
                                )
                            )
                        )
                    ),
                    React.createElement(
                        'button',
                        {
                            className: "w-full mt-4 create-opportunity-btn flex items-center justify-center gap-2"
                        },
                        React.createElement(
                            'svg',
                            {
                                className: "h-4 w-4",
                                fill: "none",
                                stroke: "currentColor",
                                viewBox: "0 0 24 24"
                            },
                            React.createElement('path', {
                                strokeLinecap: "round",
                                strokeLinejoin: "round",
                                strokeWidth: 2,
                                d: "M12 6v6m0 0v6m0-6h6m-6 0H6"
                            })
                        ),
                        "Vytvořit příležitost"
                    )
                );
            }

            return { ContactDetails };
        })();

        window.ContactDetailsComponent = ContactDetailsComponent;
    </script>
</head>
<body class="bg-[#1e293b] text-gray-100">
    <div class="flex h-screen bg-[#1e293b]">
        <!-- Zde vložíme sidebar -->
        <?php include 'sidebar.php'; ?>
        
        <!-- Hlavní obsah -->
        <div id="root" class="flex-1 bg-[#1e293b]"></div>
    </div>

    <script>
        class ErrorBoundary extends React.Component {
            constructor(props) {
                super(props);
                this.state = { hasError: false };
            }

            static getDerivedStateFromError(error) {
                return { hasError: true };
            }

            componentDidCatch(error, errorInfo) {
                console.error('React Error:', error, errorInfo);
            }

            render() {
                if (this.state.hasError) {
                    return React.createElement('div', { 
                        className: 'text-red-500 p-4' 
                    }, 'Something went wrong.');
                }

                return this.props.children;
            }
        }

        function App() {
            return React.createElement(
                ErrorBoundary,
                null,
                React.createElement(
                    window.OmnichannelContext.OmnichannelProvider,
                    null,
                    React.createElement(
                        'div',
                        { className: 'flex h-full' },
                        React.createElement(window.ConversationListComponent.ConversationList),
                        React.createElement(window.ChatWindowComponent.ChatWindow),
                        React.createElement(window.ContactDetailsComponent.ContactDetails)
                    )
                )
            );
        }

        document.addEventListener('DOMContentLoaded', function() {
            const root = document.getElementById('root');
            if (root) {
                ReactDOM.render(
                    React.createElement(App),
                    root
                );
            }

            // Theme handling
            const savedTheme = localStorage.getItem('theme');
            if (savedTheme === 'light') {
                document.body.classList.add('light-theme');
            }

            window.addEventListener('themeChanged', function(e) {
                if (e.detail.isDark) {
                    document.body.classList.remove('light-theme');
                } else {
                    document.body.classList.add('light-theme');
                }
            });
        });
    </script>
</body>
</html>


<?php
// Tento soubor slouží k testování funkčnosti update_subscription.php
// Umístěte ho do stejného adresáře jako update_subscription.php

// Nastavení pro zachycení a logování všech chyb
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Načtení potřebných souborů
require_once 'config.php';
require_once 'error_log.php';

// Kontrola, zda existují potřebné soubory
echo "<h1>Test subscription update</h1>";
echo "<h2>Checking required files:</h2>";
echo "<ul>";

$requiredFiles = [
    'subscription_constants.php',
    'subscription_functions.php',
    'update_subscription.php'
];

foreach ($requiredFiles as $file) {
    if (file_exists($file)) {
        echo "<li style='color:green'>✓ $file exists</li>";
    } else {
        echo "<li style='color:red'>✗ $file does not exist</li>";
    }
}
echo "</ul>";

// Inicializace session
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Kontrola přihlášení
echo "<h2>Session status:</h2>";
echo "<ul>";
if (isset($_SESSION['user_id'])) {
    echo "<li style='color:green'>✓ User is logged in (ID: {$_SESSION['user_id']})</li>";
} else {
    echo "<li style='color:red'>✗ User is not logged in</li>";
    echo "<li>Setting temporary user_id for testing...</li>";
    $_SESSION['user_id'] = 1; // Dočasné nastavení pro testování
}
echo "</ul>";

// Generování CSRF tokenu
if (!function_exists('generateCSRFToken')) {
    echo "<p style='color:red'>generateCSRFToken function not defined. Defining a simple version for testing.</p>";
    function generateCSRFToken() {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['csrf_token'];
    }
}

$csrf_token = generateCSRFToken();
echo "<h2>CSRF Token:</h2>";
echo "<p>$csrf_token</p>";

// Kontrola funkce getSubscriptionTypes
echo "<h2>Testing getSubscriptionTypes():</h2>";
if (function_exists('getSubscriptionTypes')) {
    $types = getSubscriptionTypes();
    echo "<pre>";
    print_r($types);
    echo "</pre>";
} else {
    echo "<p style='color:red'>getSubscriptionTypes function not defined</p>";
}

// Testovací formulář
echo "<h2>Test Form:</h2>";
echo "<form id='testForm'>";
echo "<div>";
echo "<label for='subscription_type'>Subscription Type:</label>";
echo "<select id='subscription_type' name='subscription_type'>";
if (isset($types) && is_array($types)) {
    foreach (array_keys($types) as $type) {
        echo "<option value='$type'>$type</option>";
    }
} else {
    echo "<option value='Základní'>Základní</option>";
    echo "<option value='Pokročilý'>Pokročilý</option>";
    echo "<option value='Enterprise'>Enterprise</option>";
}
echo "</select>";
echo "</div>";
echo "<div style='margin-top: 10px;'>";
echo "<label for='custom_minute_limit'>Custom Minute Limit (for Enterprise):</label>";
echo "<input type='number' id='custom_minute_limit' name='custom_minute_limit' value='1000'>";
echo "</div>";
echo "<div style='margin-top: 10px;'>";
echo "<input type='hidden' name='csrf_token' value='$csrf_token'>";
echo "<button type='button' id='submitBtn'>Test Update</button>";
echo "</div>";
echo "</form>";

echo "<div id='result' style='margin-top: 20px; padding: 10px; border: 1px solid #ccc;'></div>";

// JavaScript pro testování
echo "<script>
document.getElementById('submitBtn').addEventListener('click', function() {
    const form = document.getElementById('testForm');
    const resultDiv = document.getElementById('result');
    
    resultDiv.innerHTML = 'Sending request...';
    resultDiv.style.backgroundColor = '#f0f0f0';
    
    const subscriptionType = document.getElementById('subscription_type').value;
    const customLimit = document.getElementById('custom_minute_limit').value;
    const csrfToken = document.querySelector('input[name=\"csrf_token\"]').value;
    
    const requestData = {
        subscription_type: subscriptionType,
        csrf_token: csrfToken
    };
    
    if (subscriptionType === 'Enterprise' && customLimit) {
        requestData.custom_minute_limit = parseInt(customLimit);
    }
    
    console.log('Sending data:', requestData);
    
    fetch('update_subscription.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify(requestData)
    })
    .then(response => {
        console.log('Response status:', response.status);
        return response.text();
    })
    .then(text => {
        console.log('Raw response:', text);
        let data;
        try {
            data = JSON.parse(text);
            if (data.success) {
                resultDiv.innerHTML = '<p style=\"color:green\">Success: ' + data.message + '</p>';
                resultDiv.innerHTML += '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
                resultDiv.style.backgroundColor = '#e6ffe6';
            } else {
                resultDiv.innerHTML = '<p style=\"color:red\">Error: ' + data.message + '</p>';
                resultDiv.innerHTML += '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
                resultDiv.style.backgroundColor = '#ffe6e6';
            }
        } catch (e) {
            resultDiv.innerHTML = '<p style=\"color:red\">Error parsing JSON response:</p>';
            resultDiv.innerHTML += '<p>' + e.message + '</p>';
            resultDiv.innerHTML += '<p>Raw response:</p>';
            resultDiv.innerHTML += '<pre>' + text + '</pre>';
            resultDiv.style.backgroundColor = '#ffe6e6';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        resultDiv.innerHTML = '<p style=\"color:red\">Error: ' + error.message + '</p>';
        resultDiv.style.backgroundColor = '#ffe6e6';
    });
});
</script>";
?>


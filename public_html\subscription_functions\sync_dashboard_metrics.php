<?php
require_once __DIR__ . '/../config.php';

function syncDashboardMetrics($user_id, $metrics) {
    $conn = getDbConnection();
    if (!$conn) {
        error_log('Failed to establish database connection in syncDashboardMetrics');
        return false;
    }
    
    $stmt = null;
    
    try {
        // Get the minutes used from metrics array
        $used_minutes = 0;
        foreach ($metrics as $metric) {
            if ($metric['label'] === 'Spotřeba minut') {
                $used_minutes = intval($metric['value']);
                break;
            }
        }
        
        // Update the database with the metrics
        $stmt = $conn->prepare("
            UPDATE users 
            SET used_minutes = ?,
                minutes_remaining = CASE 
                    WHEN custom_minute_limit IS NOT NULL THEN custom_minute_limit - ?
                    WHEN subscription_plan = 'Enterprise' THEN -1
                    ELSE (SELECT COALESCE(
                        CASE subscription_plan
                            WHEN 'Základní' THEN 500
                            WHEN 'Pro' THEN 2000
                            WHEN 'Business' THEN 5000
                            ELSE 500
                        END, 500) - ?
                    )
                END,
                last_minutes_update = CURRENT_TIMESTAMP
            WHERE id = ?
        ");
        
        if (!$stmt) {
            throw new Exception("Failed to prepare statement: " . $conn->error);
        }
        
        $stmt->bind_param("iiii", $used_minutes, $used_minutes, $used_minutes, $user_id);
        $result = $stmt->execute();
        
        if ($result) {
            error_log('Dashboard metrics synced: ' . json_encode([
                'user_id' => $user_id,
                'used_minutes' => $used_minutes,
                'timestamp' => date('Y-m-d H:i:s')
            ]));
            return true;
        }
        
        return false;
    } catch (Exception $e) {
        error_log('Sync dashboard metrics error: ' . json_encode([
            'message' => $e->getMessage(),
            'user_id' => $user_id,
            'metrics' => $metrics
        ]));
        return false;
    } finally {
        if ($stmt) {
            $stmt->close();
        }
        if ($conn) {
            $conn->close();
        }
    }
}


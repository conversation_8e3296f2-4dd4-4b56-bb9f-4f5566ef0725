<?php
require_once __DIR__ . '/stripe-php/init.php';
require_once 'config.php';

// Inicializace Stripe
\Stripe\Stripe::setApiKey(STRIPE_SECRET_KEY);

// Mapování Stripe produktů na naše plány
const STRIPE_PLAN_MAPPING = [
    'price_xxxxx1' => ['plan' => 'starter', 'interval' => 'monthly'],
    'price_xxxxx2' => ['plan' => 'starter', 'interval' => 'yearly'],
    'price_xxxxx3' => ['plan' => 'advanced', 'interval' => 'monthly'],
    'price_xxxxx4' => ['plan' => 'advanced', 'interval' => 'yearly']
];

// Limity minut pro jednotlivé plány
const PLAN_LIMITS = [
    'starter' => [
        'monthly' => 500,
        'yearly' => 6000
    ],
    'advanced' => [
        'monthly' => 2000,
        'yearly' => 24000
    ],
    'free' => [
        'monthly' => 100,
        'yearly' => 100
    ]
];

function createOrUpdateStripeCustomer($userId) {
    $mysqli = getDbConnection();
    $stmt = $mysqli->prepare("SELECT email, stripe_customer_id FROM users WHERE id = ?");
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();
    $user = $result->fetch_assoc();
    $stmt->close();

    try {
        if ($user['stripe_customer_id']) {
            $customer = \Stripe\Customer::retrieve($user['stripe_customer_id']);
            return $customer;
        } else {
            $customer = \Stripe\Customer::create([
                'email' => $user['email']
            ]);

            // Uložení Stripe Customer ID do databáze
            $stmt = $mysqli->prepare("UPDATE users SET stripe_customer_id = ? WHERE id = ?");
            $stmt->bind_param("si", $customer->id, $userId);
            $stmt->execute();
            $stmt->close();

            return $customer;
        }
    } catch (\Stripe\Exception\ApiErrorException $e) {
        writeErrorLog('Stripe API Error', [
            'error' => $e->getMessage(),
            'user_id' => $userId
        ]);
        return null;
    }
}

function updateUserSubscriptionDetails($userId, $stripePriceId, $stripeSubscriptionId) {
    if (!isset(STRIPE_PLAN_MAPPING[$stripePriceId])) {
        writeErrorLog('Invalid Stripe Price ID', ['price_id' => $stripePriceId]);
        return false;
    }

    $planDetails = STRIPE_PLAN_MAPPING[$stripePriceId];
    $mysqli = getDbConnection();
    
    try {
        $stmt = $mysqli->prepare("
            UPDATE users 
            SET subscription_plan = ?,
                subscription_interval = ?,
                stripe_subscription_id = ?,
                subscription_status = 'active',
                subscription_start_date = CURRENT_DATE(),
                subscription_end_date = DATE_ADD(CURRENT_DATE(), INTERVAL ? DAY)
            WHERE id = ?
        ");

        $interval = $planDetails['interval'] === 'monthly' ? 30 : 365;
        $stmt->bind_param(
            "sssii",
            $planDetails['plan'],
            $planDetails['interval'],
            $stripeSubscriptionId,
            $interval,
            $userId
        );

        $result = $stmt->execute();
        $stmt->close();

        if ($result) {
            writeErrorLog('Subscription updated successfully', [
                'user_id' => $userId,
                'plan' => $planDetails['plan'],
                'interval' => $planDetails['interval']
            ]);
            return true;
        }
    } catch (\Exception $e) {
        writeErrorLog('Database error while updating subscription', [
            'error' => $e->getMessage(),
            'user_id' => $userId
        ]);
    }

    return false;
}

function handleSubscriptionWebhook($event) {
    $subscription = $event->data->object;
    $mysqli = getDbConnection();

    // Najít uživatele podle Stripe Customer ID
    $stmt = $mysqli->prepare("SELECT id FROM users WHERE stripe_customer_id = ?");
    $stmt->bind_param("s", $subscription->customer);
    $stmt->execute();
    $result = $stmt->get_result();
    $user = $result->fetch_assoc();
    $stmt->close();

    if (!$user) {
        writeErrorLog('User not found for webhook event', [
            'stripe_customer_id' => $subscription->customer
        ]);
        return false;
    }

    $status = '';
    switch ($event->type) {
        case 'customer.subscription.created':
        case 'customer.subscription.updated':
            $status = $subscription->status === 'active' ? 'active' : 'inactive';
            break;
        case 'customer.subscription.deleted':
            $status = 'cancelled';
            break;
    }

    try {
        $stmt = $mysqli->prepare("
            UPDATE users 
            SET subscription_status = ?,
                subscription_end_date = FROM_UNIXTIME(?),
                subscription_plan = ?,
                subscription_interval = ?
            WHERE id = ?
        ");
        
        $plan = STRIPE_PLAN_MAPPING[$subscription->items->data[0]->price->id]['plan'] ?? 'free';
        $interval = $subscription->items->data[0]->price->recurring->interval;
        
        $stmt->bind_param("ssssi", $status, $subscription->current_period_end, $plan, $interval, $user['id']);
        $result = $stmt->execute();
        $stmt->close();

        writeErrorLog('Subscription webhook processed', [
            'user_id' => $user['id'],
            'status' => $status,
            'plan' => $plan,
            'interval' => $interval,
            'event_type' => $event->type
        ]);

        return $result;
    } catch (\Exception $e) {
        writeErrorLog('Error processing subscription webhook', [
            'error' => $e->getMessage(),
            'user_id' => $user['id']
        ]);
        return false;
    }
}

function getSubscriptionDetails($userId) {
    $mysqli = getDbConnection();
    
    // Check if the stripe_subscription_id column exists
    $columnExists = $mysqli->query("SHOW COLUMNS FROM users LIKE 'stripe_subscription_id'")->num_rows > 0;
    
    if ($columnExists) {
        $stmt = $mysqli->prepare("
            SELECT subscription_plan, subscription_interval, subscription_status,
                   subscription_start_date, subscription_end_date, stripe_subscription_id
            FROM users 
            WHERE id = ?
        ");
    } else {
        $stmt = $mysqli->prepare("
            SELECT subscription_plan, subscription_interval, subscription_status,
                   subscription_start_date, subscription_end_date
            FROM users 
            WHERE id = ?
        ");
    }
    
    if (!$stmt) {
        writeErrorLog('Database Error', [
            'error' => $mysqli->error,
            'user_id' => $userId
        ]);
        return null;
    }

    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();
    $details = $result->fetch_assoc();
    $stmt->close();

    if (!$details) {
        return [
            'subscription_plan' => 'free',
            'subscription_interval' => 'monthly',
            'subscription_status' => 'inactive',
            'subscription_start_date' => date('Y-m-d'),
            'subscription_end_date' => date('Y-m-d', strtotime('+30 days')),
            'stripe_subscription_id' => null
        ];
    }

    // Add stripe_subscription_id if it doesn't exist in the result
    if (!isset($details['stripe_subscription_id'])) {
        $details['stripe_subscription_id'] = null;
    }

    return $details;
}

function isSubscriptionActive($userId) {
    $mysqli = getDbConnection();
    $stmt = $mysqli->prepare("
        SELECT subscription_status, subscription_end_date 
        FROM users 
        WHERE id = ?
    ");
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();
    $subscription = $result->fetch_assoc();
    $stmt->close();

    if (!$subscription) {
        return false;
    }

    return $subscription['subscription_status'] === 'active' && 
           strtotime($subscription['subscription_end_date']) > time();
}

function createSubscription($userId, $priceId) {
    try {
        $customer = createOrUpdateStripeCustomer($userId);
        if (!$customer) {
            throw new Exception('Failed to create/retrieve customer');
        }

        $subscription = \Stripe\Subscription::create([
            'customer' => $customer->id,
            'items' => [['price' => $priceId]],
            'payment_behavior' => 'default_incomplete',
            'payment_settings' => ['save_default_payment_method' => 'on_subscription'],
            'expand' => ['latest_invoice.payment_intent'],
        ]);

        return [
            'subscriptionId' => $subscription->id,
            'clientSecret' => $subscription->latest_invoice->payment_intent->client_secret,
        ];
    } catch (\Exception $e) {
        writeErrorLog('Subscription Creation Error', [
            'error' => $e->getMessage(),
            'user_id' => $userId
        ]);
        throw $e;
    }
}

function cancelSubscription($userId) {
    $mysqli = getDbConnection();
    $stmt = $mysqli->prepare("
        SELECT stripe_subscription_id 
        FROM users 
        WHERE id = ?
    ");
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();
    $user = $result->fetch_assoc();
    $stmt->close();

    if (!$user || !$user['stripe_subscription_id']) {
        return false;
    }

    try {
        $subscription = \Stripe\Subscription::retrieve($user['stripe_subscription_id']);
        $subscription->cancel();

        $stmt = $mysqli->prepare("
            UPDATE users 
            SET subscription_status = 'cancelled',
                subscription_end_date = CURRENT_TIMESTAMP
            WHERE id = ?
        ");
        $stmt->bind_param("i", $userId);
        $result = $stmt->execute();
        $stmt->close();

        return $result;
    } catch (\Exception $e) {
        writeErrorLog('Subscription Cancellation Error', [
            'error' => $e->getMessage(),
            'user_id' => $userId
        ]);
        return false;
    }
}

function updateSubscriptionPlan($userId, $newPriceId) {
    $mysqli = getDbConnection();
    $stmt = $mysqli->prepare("
        SELECT stripe_subscription_id 
        FROM users 
        WHERE id = ?
    ");
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();
    $user = $result->fetch_assoc();
    $stmt->close();

    if (!$user || !$user['stripe_subscription_id']) {
        return false;
    }

    try {
        $subscription = \Stripe\Subscription::retrieve($user['stripe_subscription_id']);
        \Stripe\Subscription::update($user['stripe_subscription_id'], [
            'items' => [
                [
                    'id' => $subscription->items->data[0]->id,
                    'price' => $newPriceId,
                ],
            ],
        ]);

        return updateUserSubscriptionDetails($userId, $newPriceId, $user['stripe_subscription_id']);
    } catch (\Exception $e) {
        writeErrorLog('Subscription Update Error', [
            'error' => $e->getMessage(),
            'user_id' => $userId
        ]);
        return false;
    }
}

// Pomocná funkce pro získání ceny předplatného
function getSubscriptionPrice($plan, $interval) {
    $prices = [
        'starter' => [
            'monthly' => 499,
            'yearly' => 4990
        ],
        'advanced' => [
            'monthly' => 999,
            'yearly' => 9990
        ]
    ];

    return $prices[$plan][$interval] ?? 0;
}

?>













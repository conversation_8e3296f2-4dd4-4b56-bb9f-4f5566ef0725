<?php
require_once 'config.php';
require_once 'error_log.php';

function getHashedPasswordFromDatabase($username) {
    global $mysqli;
    $stmt = $mysqli->prepare("SELECT password_hash FROM users WHERE username = ?");
    $stmt->bind_param("s", $username);
    $stmt->execute();
    $result = $stmt->get_result();
    if ($user = $result->fetch_assoc()) {
        return $user['password_hash'];
    }
    return null;
}

function testPasswordVerification($username, $password) {
    $storedHash = getHashedPasswordFromDatabase($username);
    if ($storedHash === null) {
        echo "Uživatel nenalezen.\n";
        return;
    }

    echo "Uložený hash pro uživatele $username: $storedHash\n";
    echo "Délka uloženého hashe: " . strlen($storedHash) . "\n";

    $verified = password_verify($password, $storedHash);
    echo "Výsledek ověření hesla: " . ($verified ? "Úspěšné" : "Neúspěšné") . "\n";

    // Zkusíme vytvořit nový hash pomocí password_hash a porovnat
    $newHash = password_hash($password, PASSWORD_DEFAULT);
    echo "Nově vytvořený hash pomocí password_hash: $newHash\n";
    echo "Délka nově vytvořeného hashe: " . strlen($newHash) . "\n";

    $manualVerify = ($storedHash === crypt($password, $storedHash));
    echo "Ruční ověření pomocí crypt(): " . ($manualVerify ? "Úspěšné" : "Neúspěšné") . "\n";
}

// Test pro existujícího uživatele
$testUsername = 'testuser'; // Nahraďte skutečným uživatelským jménem
$testPassword = 'testpassword'; // Nahraďte skutečným heslem

echo "Testování ověření hesla pro uživatele: $testUsername\n";
testPasswordVerification($testUsername, $testPassword);
?>


<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Karta pacienta - Demo</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
        .sidebar {
            width: 16rem;
            background: linear-gradient(135deg, #1e3a8a, #1e40af);
            color: white;
            position: fixed;
            height: 100vh;
            left: 0;
            top: 0;
            z-index: 100;
            overflow-y: auto;
        }
        .main-content {
            margin-left: 16rem;
            min-height: 100vh;
        }
        .nav-link {
            display: flex;
            align-items: center;
            padding: 0.75rem 1.5rem;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.2s;
            cursor: pointer;
        }
        .nav-link:hover, .nav-link.active {
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }
        .nav-icon {
            width: 1.25rem;
            height: 1.25rem;
            margin-right: 0.75rem;
        }
        .timeline-item {
            position: relative;
            padding-left: 2rem;
            margin-bottom: 2rem;
        }
        .timeline-item::before {
            content: '';
            position: absolute;
            left: 0.5rem;
            top: 0;
            bottom: -2rem;
            width: 2px;
            background: #e5e7eb;
        }
        .timeline-item:last-child::before {
            bottom: 0;
        }
        .timeline-dot {
            position: absolute;
            left: 0;
            top: 0.5rem;
            width: 1rem;
            height: 1rem;
            border-radius: 50%;
            border: 2px solid white;
            box-shadow: 0 0 0 2px #e5e7eb;
        }
        .timeline-dot.note { background: #3b82f6; }
        .timeline-dot.image { background: #10b981; }
        .timeline-dot.procedure { background: #f59e0b; }
        .timeline-dot.consent { background: #8b5cf6; }
        .timeline-dot.payment { background: #ef4444; }
        
        .card-header {
            background: linear-gradient(135deg, #1e3a8a, #3b82f6);
            color: white;
        }
        
        .auto-badge {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
            border-radius: 9999px;
            font-weight: 500;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="p-6 border-b border-blue-600">
            <div class="flex items-center">
                <div class="w-8 h-8 bg-white rounded mr-3 flex items-center justify-center">
                    <span class="text-blue-600 font-bold text-sm">D</span>
                </div>
                <span class="text-xl font-bold">Dentibot</span>
            </div>
            <div class="mt-3 text-sm text-blue-200">
                <div>MUDr. Jan Novák</div>
                <div class="text-xs">Advanced plán</div>
            </div>
        </div>
        <nav class="mt-6">
            <a href="index.html" class="nav-link">
                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <rect x="3" y="3" width="18" height="18" rx="2"/><path d="M3 9h18"/><path d="M9 21V9"/>
                </svg>
                Přehledy
            </a>
            <a href="#" class="nav-link">
                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
                </svg>
                Historie hovorů
            </a>
            <a href="#" class="nav-link">
                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
                </svg>
                Hromadné SMS
            </a>
            <a href="#" class="nav-link">
                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/><circle cx="9" cy="7" r="4"/><path d="M23 21v-2a4 4 0 0 0-3-3.87"/><path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                </svg>
                Seznam pacientů
            </a>
            <a href="patient_card_demo.html" class="nav-link active">
                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/><polyline points="14,2 14,8 20,8"/><line x1="16" y1="13" x2="8" y2="13"/><line x1="16" y1="17" x2="8" y2="17"/><polyline points="10,9 9,9 8,9"/><circle cx="12" cy="15" r="2"/>
                </svg>
                Karta pacienta
                <span class="ml-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full">auto</span>
            </a>
            <a href="demo_events.html" class="nav-link">
                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                </svg>
                Demo události
                <span class="ml-2 bg-orange-500 text-white text-xs px-2 py-1 rounded-full">test</span>
            </a>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <div class="p-8">
            <!-- Header -->
            <div class="mb-8">
                <nav class="text-sm text-gray-600 mb-2">
                    <a href="#" class="hover:text-blue-600">Seznam pacientů</a>
                    <span class="mx-2">/</span>
                    <span>Karta pacienta</span>
                </nav>
                
                <div class="card-header rounded-lg p-6 mb-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <h1 class="text-3xl font-bold" id="patientName">Jan Novák</h1>
                            <div class="flex items-center gap-4 mt-2 text-blue-100">
                                <span>+420 123 456 789</span>
                                <span>•</span>
                                <span><EMAIL></span>
                                <span>•</span>
                                <span>Verze karty: <span id="cardVersion">1</span></span>
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="auto-badge mb-2">
                                Automaticky generováno
                            </div>
                            <div class="text-sm text-blue-200">
                                Poslední aktualizace: <span id="lastUpdate">15.01.2025 16:42</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
                <!-- Timeline -->
                <div class="lg:col-span-3">
                    <div class="bg-white rounded-lg shadow-sm border">
                        <div class="p-6 border-b">
                            <div class="flex items-center justify-between">
                                <h2 class="text-xl font-semibold text-gray-900">Timeline aktivit</h2>
                                <span class="text-sm text-gray-500" id="itemCount">
                                    3 položky
                                </span>
                            </div>
                        </div>
                        <div class="p-6">
                            <div class="timeline" id="timeline">
                                <!-- Demo data -->
                                <div class="timeline-item">
                                    <div class="timeline-dot procedure"></div>
                                    <div class="bg-gray-50 rounded-lg p-4">
                                        <div class="flex items-start justify-between mb-2">
                                            <div>
                                                <h3 class="font-semibold text-gray-900">Preventivní prohlídka</h3>
                                                <p class="text-sm text-gray-600">15.01.2025 14:30</p>
                                            </div>
                                            <div class="flex items-center gap-2">
                                                <span class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full">Výkon</span>
                                                <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full" title="Časové razítko">TST</span>
                                            </div>
                                        </div>
                                        <p class="text-gray-700 mb-3">
                                            Rutinní preventivní prohlídka s čištěním zubního kamene. Celkový stav chrupu dobrý.
                                        </p>
                                        <div class="text-xs text-gray-500">
                                            Hash: a1b2c3d4e5f6789... | TST platné do: 15.01.2035
                                        </div>
                                    </div>
                                </div>

                                <div class="timeline-item">
                                    <div class="timeline-dot image"></div>
                                    <div class="bg-gray-50 rounded-lg p-4">
                                        <div class="flex items-start justify-between mb-2">
                                            <div>
                                                <h3 class="font-semibold text-gray-900">RTG snímek</h3>
                                                <p class="text-sm text-gray-600">15.01.2025 14:15</p>
                                            </div>
                                            <div class="flex items-center gap-2">
                                                <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">Snímek</span>
                                                <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full" title="Časové razítko">TST</span>
                                            </div>
                                        </div>
                                        <p class="text-gray-700 mb-3">
                                            Panoramatický RTG snímek pro diagnostiku před preventivní prohlídkou.
                                        </p>
                                        <div class="flex items-center gap-2 text-sm text-blue-600 mb-3">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path>
                                            </svg>
                                            <a href="#" onclick="alert('Demo - RTG snímek by se otevřel v novém okně')">
                                                Zobrazit RTG snímek
                                            </a>
                                        </div>
                                        <div class="text-xs text-gray-500">
                                            Hash: b2c3d4e5f6789... | TST platné do: 15.01.2035
                                        </div>
                                    </div>
                                </div>

                                <div class="timeline-item">
                                    <div class="timeline-dot note"></div>
                                    <div class="bg-gray-50 rounded-lg p-4">
                                        <div class="flex items-start justify-between mb-2">
                                            <div>
                                                <h3 class="font-semibold text-gray-900">Telefonní hovor</h3>
                                                <p class="text-sm text-gray-600">14.01.2025 16:20</p>
                                            </div>
                                            <div class="flex items-center gap-2">
                                                <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">Záznam</span>
                                                <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full" title="Časové razítko">TST</span>
                                            </div>
                                        </div>
                                        <p class="text-gray-700 mb-3">
                                            Pacient volal ohledně objednání termínu preventivní prohlídky. Termín dohodnut na 15.1. v 14:30.
                                        </p>
                                        <div class="text-xs text-gray-500">
                                            Hash: c3d4e5f6789... | TST platné do: 14.01.2035
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="lg:col-span-1">
                    <!-- Card Info -->
                    <div class="bg-white rounded-lg shadow-sm border mb-6">
                        <div class="p-6 border-b">
                            <h3 class="text-lg font-semibold text-gray-900">Informace o kartě</h3>
                        </div>
                        <div class="p-6 space-y-4">
                            <div>
                                <p class="text-sm font-medium text-gray-700">ID pacienta</p>
                                <p class="text-sm text-gray-600" id="patientId">1</p>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-700">Verze karty</p>
                                <p class="text-sm text-gray-600" id="cardVersionSidebar">1</p>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-700">Hash karty</p>
                                <p class="text-xs text-gray-600 font-mono break-all">
                                    a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456
                                </p>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-700">Vytvořeno</p>
                                <p class="text-sm text-gray-600">14.01.2025 10:00</p>
                            </div>
                        </div>
                    </div>

                    <!-- Actions -->
                    <div class="bg-white rounded-lg shadow-sm border mb-6">
                        <div class="p-6 border-b">
                            <h3 class="text-lg font-semibold text-gray-900">Akce</h3>
                        </div>
                        <div class="p-6 space-y-3">
                            <button onclick="exportCard()" class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition-colors">
                                Export PDF/A
                            </button>
                            <button onclick="verifyCard()" class="w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg transition-colors">
                                Ověřit razítka
                            </button>
                            <button onclick="refreshCard()" class="w-full bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-lg transition-colors">
                                Obnovit kartu
                            </button>
                        </div>
                    </div>

                    <!-- Statistics -->
                    <div class="bg-white rounded-lg shadow-sm border">
                        <div class="p-6 border-b">
                            <h3 class="text-lg font-semibold text-gray-900">Statistiky</h3>
                        </div>
                        <div class="p-6 space-y-3">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Záznamy</span>
                                <span class="font-semibold">1</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Snímky</span>
                                <span class="font-semibold">1</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Výkony</span>
                                <span class="font-semibold">1</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Celkem TST</span>
                                <span class="font-semibold">3</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Get patient ID from URL
        const urlParams = new URLSearchParams(window.location.search);
        const patientId = urlParams.get('patient_id') || '1';
        
        // Patient data
        const patients = {
            '1': { name: 'Jan Novák', phone: '+420 123 456 789', email: '<EMAIL>' },
            '2': { name: 'Marie Svobodová', phone: '+420 987 654 321', email: '<EMAIL>' },
            '3': { name: 'Pavel Dvořák', phone: '+420 555 123 456', email: '<EMAIL>' },
            '4': { name: 'Anna Procházková', phone: '+420 777 888 999', email: '<EMAIL>' }
        };
        
        // Update patient info
        const patient = patients[patientId];
        if (patient) {
            document.getElementById('patientName').textContent = patient.name;
            document.getElementById('patientId').textContent = patientId;
            document.querySelector('.text-blue-100').innerHTML = `
                <span>${patient.phone}</span>
                <span>•</span>
                <span>${patient.email}</span>
                <span>•</span>
                <span>Verze karty: <span id="cardVersion">1</span></span>
            `;
        }

        function exportCard() {
            alert('Export PDF/A s embedded soubory a všemi TST - funkce bude implementována v produkční verzi.');
        }
        
        function verifyCard() {
            alert('Ověření všech časových razítek v kartě - všechna razítka jsou platná!');
        }
        
        function refreshCard() {
            window.location.reload();
        }
    </script>
</body>
</html>

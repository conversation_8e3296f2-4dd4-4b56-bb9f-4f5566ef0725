/**
 * Mock data pro lokální testování aplikace
 * Simuluje data z databáze a API
 */

// Mock uživatelská data
const mockUser = {
    id: 1,
    username: 'dr.novak',
    name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    email: '<EMAIL>',
    role: 'doctor',
    subscription_plan: 'Advanced',
    minute_limit: 1000,
    minutes_used: 347
};

// Mock data pro dashboard
const mockDashboardData = {
    metrics: [
        { label: '<PERSON><PERSON><PERSON> hovor<PERSON>', value: '1,247', change: '+12%', trend: 'up' },
        { label: 'Úspěšné hovory', value: '1,156', change: '+8%', trend: 'up' },
        { label: 'Spotřeba minut', value: '347', change: '+15%', trend: 'up' },
        { label: 'Průměrná délka', value: '2:34', change: '-3%', trend: 'down' }
    ],
    callsData: {
        labels: ['1.1', '2.1', '3.1', '4.1', '5.1', '6.1', '7.1'],
        data: [45, 52, 38, 67, 73, 89, 94]
    },
    peakHours: {
        labels: ['8:00', '9:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00'],
        data: [12, 25, 45, 67, 43, 23, 56, 78, 34]
    },
    topReasons: [
        { reason: 'Objednání termínu', count: 456, percentage: 65 },
        { reason: 'Zrušení termínu', count: 123, percentage: 18 },
        { reason: 'Dotaz na ordinační hodiny', count: 89, percentage: 13 },
        { reason: 'Jiné', count: 28, percentage: 4 }
    ]
};

// Mock data pro historii hovorů
const mockCallHistory = [
    {
        id: 1,
        phone: '+420 123 456 789',
        patient_name: 'Jan Novák',
        duration: 156,
        status: 'successful',
        type: 'appointment',
        created_at: '2025-01-15 14:30:00',
        reason: 'Objednání termínu',
        notes: 'Pacient si objednal kontrolu na 20.1. v 10:00'
    },
    {
        id: 2,
        phone: '+420 987 654 321',
        patient_name: 'Marie Svobodová',
        duration: 89,
        status: 'successful',
        type: 'cancellation',
        created_at: '2025-01-15 11:15:00',
        reason: 'Zrušení termínu',
        notes: 'Zrušení termínu z důvodu nemoci'
    },
    {
        id: 3,
        phone: '+420 555 123 456',
        patient_name: 'Pavel Dvořák',
        duration: 234,
        status: 'needs_attention',
        type: 'info',
        created_at: '2025-01-14 16:45:00',
        reason: 'Dotaz na ceny',
        notes: 'Pacient se ptal na ceny implantátů - vyžaduje zpětné volání'
    },
    {
        id: 4,
        phone: '+420 777 888 999',
        patient_name: 'Anna Procházková',
        duration: 67,
        status: 'successful',
        type: 'appointment',
        created_at: '2025-01-14 09:20:00',
        reason: 'Objednání termínu',
        notes: 'Preventivní prohlídka na 25.1. v 14:00'
    }
];

// Mock data pro pacienty
const mockPatients = [
    {
        id: 1,
        name: 'Jan Novák',
        phone: '+420 123 456 789',
        email: '<EMAIL>',
        birth_date: '1985-03-15',
        address: 'Hlavní 123, Praha 1',
        insurance: 'VZP',
        last_visit: '2025-01-10',
        next_appointment: '2025-01-20 10:00:00',
        notes: 'Pravidelný pacient, bez alergií'
    },
    {
        id: 2,
        name: 'Marie Svobodová',
        phone: '+420 987 654 321',
        email: '<EMAIL>',
        birth_date: '1978-07-22',
        address: 'Nová 456, Praha 2',
        insurance: 'ČPZP',
        last_visit: '2024-12-15',
        next_appointment: null,
        notes: 'Alergie na penicilin'
    },
    {
        id: 3,
        name: 'Pavel Dvořák',
        phone: '+420 555 123 456',
        email: '<EMAIL>',
        birth_date: '1992-11-08',
        address: 'Krátká 789, Praha 3',
        insurance: 'VoZP',
        last_visit: '2025-01-05',
        next_appointment: '2025-01-18 15:30:00',
        notes: 'Ortodontická léčba v průběhu'
    },
    {
        id: 4,
        name: 'Anna Procházková',
        phone: '+420 777 888 999',
        email: '<EMAIL>',
        birth_date: '1965-12-03',
        address: 'Dlouhá 321, Praha 4',
        insurance: 'VZP',
        last_visit: '2024-11-20',
        next_appointment: '2025-01-25 14:00:00',
        notes: 'Diabetik - pozor na hojení'
    }
];

// Mock data pro CGM dokumenty
const mockDocuments = [
    {
        id: 1,
        title: 'Preventivní prohlídka - Jan Novák',
        category_name: 'Preventivní péče',
        content: 'Rutinní preventivní prohlídka pacienta Jana Nováka. Provedeno:\n- Kontrola zubního kazu\n- Čištění zubního kamene\n- Fluoridace\n- Instruktáž ústní hygieny\n\nNález: Celkový stav chrupu dobrý, drobný kaz na zubu 16.',
        created_at: '2025-01-15 14:30:00',
        updated_at: '2025-01-15 14:30:00',
        author_name: 'MUDr. Jan Novák',
        document_hash: 'a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456',
        timestamp_count: 2,
        file_path: null,
        file_type: null,
        file_size: null
    },
    {
        id: 2,
        title: 'Lékařská zpráva - Marie Svobodová',
        category_name: 'Zprávy',
        content: 'Zpráva o provedené endodontické léčbě zubu 16 u pacientky Marie Svobodové.\n\nProvedené výkony:\n- Aplikace koferdamu\n- Preparace kavity\n- Extirpace pulpy\n- Instrumentace kořenových kanálků\n- Obturation kanálků\n- Dočasná výplň\n\nDoporučení: Kontrola za 14 dní, následně definitivní výplň.',
        created_at: '2025-01-14 10:15:00',
        updated_at: '2025-01-14 10:15:00',
        author_name: 'MUDr. Jan Novák',
        document_hash: 'b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef1234567',
        timestamp_count: 1,
        file_path: null,
        file_type: null,
        file_size: null
    },
    {
        id: 3,
        title: 'RTG snímek - Pavel Dvořák',
        category_name: 'Vyšetření',
        content: 'Panoramatický RTG snímek pro diagnostiku před ortodontickou léčbou.\n\nNález:\n- Kompletní chrup\n- Mírná malpozice řezáků\n- Doporučena ortodontická léčba\n- Žádné patologické změny',
        created_at: '2025-01-13 16:45:00',
        updated_at: '2025-01-13 16:45:00',
        author_name: 'MUDr. Jan Novák',
        document_hash: 'c3d4e5f6789012345678901234567890abcdef1234567890abcdef12345678',
        timestamp_count: 3,
        file_path: 'uploads/documents/rtg_pavel_dvorak.jpg',
        file_type: 'image/jpeg',
        file_size: 245760
    }
];

// Mock data pro časová razítka
const mockTimestamps = {
    1: [
        {
            id: 1,
            document_id: 1,
            created_at: '2025-01-15 14:30:15',
            expires_at: '2035-01-15 14:30:15',
            is_valid: true,
            tsa_url: 'http://timestamp.digicert.com',
            timestamp_hash: 'ts1a2b3c4d5e6f789012345678901234567890abcdef'
        },
        {
            id: 2,
            document_id: 1,
            created_at: '2025-01-15 15:45:22',
            expires_at: '2035-01-15 15:45:22',
            is_valid: true,
            tsa_url: 'http://timestamp.sectigo.com',
            timestamp_hash: 'ts2b3c4d5e6f789012345678901234567890abcdef1'
        }
    ],
    2: [
        {
            id: 3,
            document_id: 2,
            created_at: '2025-01-14 10:15:30',
            expires_at: '2035-01-14 10:15:30',
            is_valid: true,
            tsa_url: 'http://timestamp.digicert.com',
            timestamp_hash: 'ts3c4d5e6f789012345678901234567890abcdef12'
        }
    ],
    3: [
        {
            id: 4,
            document_id: 3,
            created_at: '2025-01-13 16:45:10',
            expires_at: '2035-01-13 16:45:10',
            is_valid: true,
            tsa_url: 'http://timestamp.digicert.com',
            timestamp_hash: 'ts4d5e6f789012345678901234567890abcdef123'
        },
        {
            id: 5,
            document_id: 3,
            created_at: '2025-01-13 17:00:25',
            expires_at: '2035-01-13 17:00:25',
            is_valid: true,
            tsa_url: 'http://timestamp.globalsign.com',
            timestamp_hash: 'ts5e6f789012345678901234567890abcdef1234'
        },
        {
            id: 6,
            document_id: 3,
            created_at: '2025-01-14 09:15:40',
            expires_at: '2035-01-14 09:15:40',
            is_valid: true,
            tsa_url: 'http://timestamp.sectigo.com',
            timestamp_hash: 'ts6f789012345678901234567890abcdef12345'
        }
    ]
};

// Mock data pro kategorie dokumentů
const mockCategories = [
    { id: 1, name: 'Zdravotnická dokumentace', description: 'Základní zdravotnická dokumentace pacienta' },
    { id: 2, name: 'Vyšetření', description: 'Výsledky vyšetření a diagnostiky' },
    { id: 3, name: 'Terapie', description: 'Terapeutické plány a postupy' },
    { id: 4, name: 'Recepty', description: 'Elektronické recepty a předpisy' },
    { id: 5, name: 'Zprávy', description: 'Lékařské zprávy a posudky' },
    { id: 6, name: 'Preventivní péče', description: 'Dokumentace preventivních prohlídek' },
    { id: 7, name: 'Administrativní', description: 'Administrativní dokumenty a formuláře' }
];

// Mock SMS kampaně
const mockSMSCampaigns = [
    {
        id: 1,
        name: 'Připomínka preventivních prohlídek',
        message: 'Dobrý den, připomínáme Vám termín preventivní prohlídky. Ordinace Dr. Nováka.',
        recipients_count: 45,
        sent_count: 43,
        status: 'completed',
        created_at: '2025-01-10 09:00:00',
        sent_at: '2025-01-10 09:15:00'
    },
    {
        id: 2,
        name: 'Novoroční přání',
        message: 'Přejeme Vám šťastný nový rok a pevné zdraví! Ordinace Dr. Nováka',
        recipients_count: 156,
        sent_count: 156,
        status: 'completed',
        created_at: '2025-01-01 10:00:00',
        sent_at: '2025-01-01 10:30:00'
    },
    {
        id: 3,
        name: 'Změna ordinačních hodin',
        message: 'Informujeme o změně ordinačních hodin od 20.1. Nové hodiny: Po-Pá 8-16h.',
        recipients_count: 89,
        sent_count: 0,
        status: 'draft',
        created_at: '2025-01-15 14:00:00',
        sent_at: null
    }
];

// Export všech mock dat
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        mockUser,
        mockDashboardData,
        mockCallHistory,
        mockPatients,
        mockDocuments,
        mockTimestamps,
        mockCategories,
        mockSMSCampaigns
    };
}

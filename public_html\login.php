<?php
require_once 'config.php';
require_once 'error_log.php';

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

writeErrorLog("Login page accessed");

// Redirect to dashboard if user is already logged in
if (isset($_SESSION['user_id'])) {
    writeErrorLog("User already logged in, redirecting to dashboard");
    header('Location: dashboard.php');
    exit;
}

$error = '';

// Generate CSRF token
$csrf_token = generateCSRFToken();

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verify CSRF token
    if (!verifyCSRFToken($_POST['csrf_token'])) {
        $error = 'Neplatný CSRF token. Akce byla zru<PERSON>.';
        writeErrorLog("Login failed: Invalid CSRF token");
    } else {
        $username = sanitizeInput($_POST['username'] ?? '');
        $password = $_POST['password'] ?? '';

        writeErrorLog("Login attempt", ['username' => $username]);

        if (empty($username) || empty($password)) {
            $error = 'Prosím vyplňte všechna pole.';
            writeErrorLog("Login failed: Empty fields");
        } else {
            try {
                $mysqli = getDbConnection();
                $stmt = $mysqli->prepare("SELECT id, password_hash, role FROM users WHERE username = ?");
                $stmt->bind_param("s", $username);
                $stmt->execute();
                $result = $stmt->get_result();

                if ($user = $result->fetch_assoc()) {
                    writeErrorLog("User found", ['user_id' => $user['id']]);

                    $passwordVerified = password_verify($password, $user['password_hash']);
                    writeErrorLog("Password verification result", ['verified' => $passwordVerified, 'user_id' => $user['id']]);

                    if ($passwordVerified) {
                        // Successful login
                        writeErrorLog("Password verified successfully", ['user_id' => $user['id'], 'username' => $username]);
                        $_SESSION['user_id'] = $user['id'];
                        $_SESSION['username'] = $username;
                        $_SESSION['user_role'] = $user['role'];
                        $_SESSION['last_activity'] = time();

                        // Regenerate session ID to prevent session fixation
                        session_regenerate_id(true);

                        // Generate new CSRF token after login
                        generateCSRFToken();

                        // Log the login action
                        $stmt = $mysqli->prepare("INSERT INTO access_logs (user_id, action, access_time, ip_address, user_agent) VALUES (?, 'login', NOW(), ?, ?)");
                        $ip_address = $_SERVER['REMOTE_ADDR'];
                        $user_agent = $_SERVER['HTTP_USER_AGENT'];
                        $stmt->bind_param("iss", $user['id'], $ip_address, $user_agent);
                        $stmt->execute();
                        $stmt->close();

                        writeErrorLog("User logged in successfully", ['user_id' => $user['id'], 'username' => $username]);

                        header('Location: dashboard.php');
                        exit;
                    } else {
                        $error = 'Nesprávné heslo.';
                        writeErrorLog("Login failed: Incorrect password", ['username' => $username]);
                    }
                } else {
                    $error = 'Uživatel nenalezen.';
                    writeErrorLog("Login failed: User not found", ['username' => $username]);
                }
                $stmt->close();
            } catch (Exception $e) {
                $error = 'Došlo k chybě při přihlašování. Zkuste to prosím později.';
                writeErrorLog("Login error", ['message' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
            }
        }
    }
}

// Check for logout message
$logoutMessage = '';
if (isset($_GET['logout']) && $_GET['logout'] == 'success') {
    $logoutMessage = 'Byli jste úspěšně odhlášeni.';
}

// Set security headers
set_security_headers();

// Set Content Security Policy
set_content_security_policy();
?>
<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Přihlášení - Dentibot</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #0A0A0A;
            color: #fff;
        }
        .login-container {
            background-color: #1E1E2E;
            border-radius: 8px;
            width: 100%;
            max-width: 400px;
            padding: 2rem;
        }
        .gradient-text {
            background: linear-gradient(90deg, #4fd1c5 0%, #38b2ac 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        .form-input {
            width: 100%;
            padding: 0.75rem;
            background-color: #2A2A3C;
            border: 1px solid #2A2A3C;
            border-radius: 6px;
            color: #fff;
            margin-top: 0.5rem;
            transition: border-color 0.2s;
        }
        .form-input:focus {
            outline: none;
            border-color: #4fd1c5;
        }
        .form-label {
            color: #94A3B8;
            font-size: 0.875rem;
            margin-bottom: 0.5rem;
            display: block;
        }
        .submit-button {
            width: 100%;
            padding: 0.75rem;
            background: linear-gradient(90deg, #4fd1c5 0%, #38b2ac 100%);
            color: white;
            border: none;
            border-radius: 6px;
            font-weight: 500;
            cursor: pointer;
            transition: opacity 0.2s;
        }
        .submit-button:hover {
            opacity: 0.9;
        }
        .register-link {
            color: #94A3B8;
            text-decoration: none;
            font-size: 0.875rem;
            transition: color 0.2s;
        }
        .register-link:hover {
            color: #4fd1c5;
        }
    </style>
</head>
<body class="min-h-screen flex items-center justify-center p-4">
    <div class="login-container">
        <h1 class="text-2xl font-bold mb-8 gradient-text text-center">Přihlášení do Dentibot</h1>
        
        <?php if ($error): ?>
            <div class="bg-red-500 bg-opacity-10 border border-red-500 text-red-500 px-4 py-2 rounded mb-4 text-sm text-center">
                <?php echo htmlspecialchars($error, ENT_QUOTES, 'UTF-8'); ?>
            </div>
        <?php endif; ?>
        
        <?php if ($logoutMessage): ?>
            <div class="bg-green-500 bg-opacity-10 border border-green-500 text-green-500 px-4 py-2 rounded mb-4 text-sm text-center">
                <?php echo htmlspecialchars($logoutMessage, ENT_QUOTES, 'UTF-8'); ?>
            </div>
        <?php endif; ?>

        <form method="POST" action="" class="space-y-4">
            <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
            <div>
                <label for="username" class="form-label">Uživatelské jméno</label>
                <input type="text" id="username" name="username" required class="form-input">
            </div>
            
            <div>
                <label for="password" class="form-label">Heslo</label>
                <input type="password" id="password" name="password" required class="form-input">
            </div>
            
            <div class="pt-2">
                <button type="submit" class="submit-button">
                    Přihlásit se
                </button>
            </div>
        </form>
        
        <div class="mt-6 text-center">
            <a href="register.php" class="register-link">
                Nemáte účet? Zaregistrujte se
            </a>
        </div>
    </div>
</body>
</html>


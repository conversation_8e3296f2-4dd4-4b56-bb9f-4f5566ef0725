<?php
session_start();
error_log("Test session page loaded. Session ID: " . session_id());
error_log("Session data: " . print_r($_SESSION, true));

echo "<h1>Session Test</h1>";
echo "<p>Session ID: " . session_id() . "</p>";
echo "<h2>Session Data:</h2>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

if (isset($_SESSION['user_id'])) {
    echo "<p>User is logged in. User ID: " . $_SESSION['user_id'] . "</p>";
} else {
    echo "<p>User is not logged in.</p>";
}

phpinfo();


<?php
require_once 'api_functions.php';
require_once 'payment_modules/payment_processor.php';

// Zpracování callbacků z platební brány
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Získání dat z požadavku
    $data = json_decode(file_get_contents('php://input'), true);
    
    if (!$data) {
        // Pokud nejsou data v JSON formátu, zkusíme je získat z $_POST
        $data = $_POST;
    }
    
    // Kontrola, zda jsou data platná
    if (!isset($data['transaction_id']) || !isset($data['status'])) {
        header('HTTP/1.1 400 Bad Request');
        echo json_encode(['success' => false, 'message' => 'Chybí povinné parametry']);
        exit;
    }
    
    // Zpracování callbacku podle typu transakce
    $transactionId = $data['transaction_id'];
    $status = $data['status'];
    
    // Určení typu transakce podle prefixu
    $transactionType = '';
    if (strpos($transactionId, 'SMS_') === 0) {
        $transactionType = 'sms_recharge';
    } elseif (strpos($transactionId, 'SUB_') === 0) {
        $transactionType = 'subscription';
    } else {
        $transactionType = 'unknown';
    }
    
    // Zpracování podle typu transakce a statusu
    switch ($transactionType) {
        case 'sms_recharge':
            processSmsRechargeCallback($transactionId, $status, $data);
            break;
            
        case 'subscription':
            processSubscriptionCallback($transactionId, $status, $data);
            break;
            
        default:
            header('HTTP/1.1 400 Bad Request');
            echo json_encode(['success' => false, 'message' => 'Neznámý typ transakce']);
            break;
    }
} else {
    // Metoda není povolena
    header('HTTP/1.1 405 Method Not Allowed');
    echo json_encode(['success' => false, 'message' => 'Metoda není povolena']);
}

/**
 * Zpracuje callback pro dobití SMS kreditu
 * @param string $transactionId ID transakce
 * @param string $status Status platby
 * @param array $data Data z callbacku
 */
function processSmsRechargeCallback($transactionId, $status, $data) {
    // Získání ID uživatele z transakce
    $userId = $data['user_id'] ?? 0;
    
    if ($userId <= 0) {
        // Pokud nemáme ID uživatele, zkusíme ho získat z API
        $transactionInfo = makeApiCall('GET', "transactions/{$transactionId}");
        $userId = $transactionInfo['user_id'] ?? 0;
        
        if ($userId <= 0) {
            header('HTTP/1.1 400 Bad Request');
            echo json_encode(['success' => false, 'message' => 'Nelze identifikovat uživatele']);
            exit;
        }
    }
    
    // Zpracování podle statusu
    switch ($status) {
        case 'completed':
            // Platba byla úspěšně dokončena
            $amount = $data['amount'] ?? 0;
            
            // Aktualizace kreditu uživatele
            $result = makeApiCall('POST', "users/{$userId}/sms-credit/confirm", [
                'transaction_id' => $transactionId,
                'amount' => $amount
            ]);
            
            if ($result['success'] ?? false) {
                echo json_encode(['success' => true, 'message' => 'Kredit byl úspěšně potvrzen']);
            } else {
                echo json_encode(['success' => false, 'message' => $result['message'] ?? 'Nepodařilo se potvrdit kredit']);
            }
            break;
            
        case 'failed':
            // Platba selhala
            $result = makeApiCall('POST', "users/{$userId}/sms-credit/cancel", [
                'transaction_id' => $transactionId
            ]);
            
            echo json_encode(['success' => true, 'message' => 'Transakce byla označena jako neúspěšná']);
            break;
            
        case 'pending':
            // Platba čeká na zpracování
            echo json_encode(['success' => true, 'message' => 'Transakce čeká na zpracování']);
            break;
            
        default:
            echo json_encode(['success' => false, 'message' => 'Neznámý status platby']);
            break;
    }
}

/**
 * Zpracuje callback pro předplatné
 * @param string $transactionId ID transakce
 * @param string $status Status platby
 * @param array $data Data z callbacku
 */
function processSubscriptionCallback($transactionId, $status, $data) {
    // Získání ID uživatele z transakce
    $userId = $data['user_id'] ?? 0;
    
    if ($userId <= 0) {
        // Pokud nemáme ID uživatele, zkusíme ho získat z API
        $transactionInfo = makeApiCall('GET', "transactions/{$transactionId}");
        $userId = $transactionInfo['user_id'] ?? 0;
        
        if ($userId <= 0) {
            header('HTTP/1.1 400 Bad Request');
            echo json_encode(['success' => false, 'message' => 'Nelze identifikovat uživatele']);
            exit;
        }
    }
    
    // Zpracování podle statusu
    switch ($status) {
        case 'completed':
            // Platba byla úspěšně dokončena
            $plan = $data['plan'] ?? '';
            $interval = $data['interval'] ?? 'monthly';
            
            // Aktivace předplatného
            $result = makeApiCall('POST', "users/{$userId}/subscription/confirm", [
                'transaction_id' => $transactionId,
                'plan' => $plan,
                'interval' => $interval
            ]);
            
            if ($result['success'] ?? false) {
                echo json_encode(['success' => true, 'message' => 'Předplatné bylo úspěšně aktivováno']);
            } else {
                echo json_encode(['success' => false, 'message' => $result['message'] ?? 'Nepodařilo se aktivovat předplatné']);
            }
            break;
            
        case 'failed':
            // Platba selhala
            $result = makeApiCall('POST', "users/{$userId}/subscription/cancel", [
                'transaction_id' => $transactionId
            ]);
            
            echo json_encode(['success' => true, 'message' => 'Transakce byla označena jako neúspěšná']);
            break;
            
        case 'pending':
            // Platba čeká na zpracování
            echo json_encode(['success' => true, 'message' => 'Transakce čeká na zpracování']);
            break;
            
        default:
            echo json_encode(['success' => false, 'message' => 'Neznámý status platby']);
            break;
    }
}
?>


<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);
define('DENTIBOT_LOADED', true);
require_once __DIR__ . '/config.php';
require_once __DIR__ . '/db_connection.php';

header('Content-Type: application/json');

// Log the start of the script execution
error_log("get_campaigns.php started execution");

try {
    if (!$conn) {
        throw new Exception("Database connection failed");
    }

    // Log successful database connection
    error_log("Database connection successful");

    $sql = "SELECT c.*, 
            COUNT(d.id) as total_messages,
            SUM(CASE WHEN d.status = 'delivered' THEN 1 ELSE 0 END) as delivered_messages,
            SUM(CASE WHEN d.status = 'failed' THEN 1 ELSE 0 END) as failed_messages
            FROM sms_campaigns c
            LEFT JOIN sms_delivery_reports d ON c.id = d.campaign_id
            GROUP BY c.id
            ORDER BY c.sent_date DESC";

    // Log the SQL query
    error_log("Executing SQL query: " . $sql);

    $result = $conn->query($sql);

    if (!$result) {
        throw new Exception("Error executing query: " . $conn->error);
    }

    // Log successful query execution
    error_log("SQL query executed successfully");

    $campaigns = [];
    while ($row = $result->fetch_assoc()) {
        $campaigns[] = $row;
    }

    // Log the number of campaigns fetched
    error_log("Fetched " . count($campaigns) . " campaigns");

    $response = json_encode([
        'success' => true,
        'campaigns' => $campaigns
    ]);

    // Log the JSON response
    error_log("JSON response: " . $response);

    echo $response;
    exit;

} catch (Exception $e) {
    error_log("Error in get_campaigns.php: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => "Chyba při načítání kampaní: " . $e->getMessage()
    ]);
}

// Log the end of the script execution
error_log("get_campaigns.php finished execution");
?>








































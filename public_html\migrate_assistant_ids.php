<?php
require_once 'config.php';
require_once 'db_functions.php';

$mysqli = connectToDatabase();

// Získejte všechny uživatele
$result = $mysqli->query("SELECT id, username FROM users WHERE assistant_id IS NULL");

while ($user = $result->fetch_assoc()) {
  // Zde byste měli implementovat logiku pro získání správného ID asistenta pro každého uživatele
  // Toto je jen příklad, ve skutečnosti byste měli mít způsob, jak určit správné ID asistenta
  $assistantId = "default_assistant_id";

  $stmt = $mysqli->prepare("UPDATE users SET assistant_id = ? WHERE id = ?");
  $stmt->bind_param("si", $assistantId, $user['id']);
  $stmt->execute();

  echo "Updated user {$user['username']} with assistant ID: {$assistantId}\n";
}

$mysqli->close();
echo "Migration completed.\n";


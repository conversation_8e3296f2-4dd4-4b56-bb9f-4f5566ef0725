<?php
require_once 'config.php';
require_once 'cgm_timestamps/DocumentManager.php';
require_once 'cgm_timestamps/TimestampManager.php';

// Kontrola přihlášení
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

$document_id = $_GET['id'] ?? 0;
if (!$document_id) {
    header('Location: cgm_documents.php');
    exit;
}

$documentManager = new DocumentManager();
$timestampManager = new TimestampManager();

$error = null;
$success = null;

// Získání dokumentu
$document_result = $documentManager->getDocument($document_id, $_SESSION['user_id']);
if (!$document_result['success']) {
    header('Location: cgm_documents.php?error=' . urlencode($document_result['error']));
    exit;
}

$document = $document_result['document'];

// Zpracování akcí
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'create_timestamp':
                $result = $timestampManager->createTimestamp($document_id, $document['document_hash']);
                if ($result['success']) {
                    $success = 'Nové časové razítko bylo úspěšně vytvořeno.';
                    // Refresh document data
                    $document_result = $documentManager->getDocument($document_id, $_SESSION['user_id']);
                    $document = $document_result['document'];
                } else {
                    $error = $result['error'];
                }
                break;
                
            case 'verify_timestamp':
                $timestamp_id = $_POST['timestamp_id'] ?? 0;
                $result = $timestampManager->verifyTimestamp($timestamp_id);
                if ($result['success']) {
                    $success = $result['is_valid'] ? 'Časové razítko je platné.' : 'Časové razítko není platné!';
                } else {
                    $error = $result['error'];
                }
                break;
        }
    }
}

$currentPage = 'cgm_documents';
?>

<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Detail dokumentu - <?php echo htmlspecialchars($document['title']); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="css/cgm_documents.css" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
        .timestamp-card {
            border-left: 4px solid #10b981;
            background: linear-gradient(135deg, #f0fdf4, #ecfdf5);
        }
        .document-content {
            background: #fafafa;
            border: 1px solid #e5e7eb;
        }
        .hash-display {
            font-family: 'Courier New', monospace;
            background: #f3f4f6;
            border: 1px solid #d1d5db;
            word-break: break-all;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="flex min-h-screen">
        <?php include 'sidebar.php'; ?>
        
        <div class="flex-1 ml-64">
            <div class="p-8">
                <!-- Header -->
                <div class="mb-8">
                    <div class="flex items-center justify-between">
                        <div>
                            <nav class="text-sm text-gray-600 mb-2">
                                <a href="cgm_documents.php" class="hover:text-blue-600">CGM Dokumenty</a>
                                <span class="mx-2">/</span>
                                <span>Detail dokumentu</span>
                            </nav>
                            <h1 class="text-3xl font-bold text-gray-900">
                                <?php echo htmlspecialchars($document['title']); ?>
                            </h1>
                            <div class="flex items-center gap-4 mt-2 text-sm text-gray-600">
                                <span>Kategorie: <?php echo htmlspecialchars($document['category_name']); ?></span>
                                <span>•</span>
                                <span>Vytvořeno: <?php echo date('d.m.Y H:i', strtotime($document['created_at'])); ?></span>
                                <span>•</span>
                                <span>Autor: <?php echo htmlspecialchars($document['author_name']); ?></span>
                            </div>
                        </div>
                        <div class="flex gap-3">
                            <form method="POST" class="inline">
                                <input type="hidden" name="action" value="create_timestamp">
                                <button type="submit" class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                                    <svg class="w-5 h-5 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                                    </svg>
                                    Nové časové razítko
                                </button>
                            </form>
                            <button onclick="exportToPDF()" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                                <svg class="w-5 h-5 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                Export PDF
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Alerts -->
                <?php if ($error): ?>
                    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                        <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>

                <?php if ($success): ?>
                    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                        <?php echo htmlspecialchars($success); ?>
                    </div>
                <?php endif; ?>

                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- Document Content -->
                    <div class="lg:col-span-2">
                        <div class="bg-white rounded-lg shadow-sm border">
                            <div class="p-6 border-b">
                                <h2 class="text-xl font-semibold text-gray-900">Obsah dokumentu</h2>
                            </div>
                            <div class="p-6">
                                <?php if ($document['file_path']): ?>
                                    <div class="mb-6">
                                        <div class="flex items-center gap-3 p-4 bg-blue-50 rounded-lg">
                                            <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                            </svg>
                                            <div>
                                                <p class="font-medium text-blue-900">Přiložený soubor</p>
                                                <p class="text-sm text-blue-700">
                                                    <?php echo htmlspecialchars(basename($document['file_path'])); ?>
                                                    (<?php echo number_format($document['file_size'] / 1024, 1); ?> KB)
                                                </p>
                                            </div>
                                            <a href="<?php echo htmlspecialchars($document['file_path']); ?>" target="_blank" 
                                               class="ml-auto bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded text-sm transition-colors">
                                                Stáhnout
                                            </a>
                                        </div>
                                    </div>
                                <?php endif; ?>

                                <?php if ($document['content']): ?>
                                    <div class="document-content p-6 rounded-lg">
                                        <pre class="whitespace-pre-wrap text-gray-800"><?php echo htmlspecialchars($document['content']); ?></pre>
                                    </div>
                                <?php endif; ?>

                                <!-- Document Hash -->
                                <div class="mt-6">
                                    <h3 class="text-sm font-medium text-gray-700 mb-2">Hash dokumentu (SHA-256)</h3>
                                    <div class="hash-display p-3 rounded text-xs">
                                        <?php echo htmlspecialchars($document['document_hash']); ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Timestamps Sidebar -->
                    <div class="lg:col-span-1">
                        <div class="bg-white rounded-lg shadow-sm border">
                            <div class="p-6 border-b">
                                <h2 class="text-xl font-semibold text-gray-900">Časová razítka</h2>
                                <p class="text-sm text-gray-600 mt-1">
                                    <?php echo count($document['timestamps']); ?> razítek
                                </p>
                            </div>
                            <div class="p-6">
                                <?php if (empty($document['timestamps'])): ?>
                                    <div class="text-center py-8">
                                        <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                                        </svg>
                                        <p class="text-gray-600 text-sm">Žádná časová razítka</p>
                                    </div>
                                <?php else: ?>
                                    <div class="space-y-4">
                                        <?php foreach ($document['timestamps'] as $timestamp): ?>
                                            <div class="timestamp-card p-4 rounded-lg">
                                                <div class="flex items-start justify-between mb-2">
                                                    <div>
                                                        <p class="font-medium text-green-900 text-sm">
                                                            Razítko #<?php echo $timestamp['id']; ?>
                                                        </p>
                                                        <p class="text-xs text-green-700">
                                                            <?php echo date('d.m.Y H:i:s', strtotime($timestamp['created_at'])); ?>
                                                        </p>
                                                    </div>
                                                    <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                                                        <?php echo $timestamp['is_valid'] ? 'Platné' : 'Neplatné'; ?>
                                                    </span>
                                                </div>
                                                
                                                <div class="text-xs text-green-700 mb-3">
                                                    <p>TSA: <?php echo htmlspecialchars(parse_url($timestamp['tsa_url'], PHP_URL_HOST)); ?></p>
                                                    <p>Platnost do: <?php echo date('d.m.Y', strtotime($timestamp['expires_at'])); ?></p>
                                                </div>
                                                
                                                <div class="flex gap-2">
                                                    <form method="POST" class="flex-1">
                                                        <input type="hidden" name="action" value="verify_timestamp">
                                                        <input type="hidden" name="timestamp_id" value="<?php echo $timestamp['id']; ?>">
                                                        <button type="submit" class="w-full bg-green-50 hover:bg-green-100 text-green-700 px-3 py-1 rounded text-xs font-medium transition-colors">
                                                            Ověřit
                                                        </button>
                                                    </form>
                                                    <button onclick="showTimestampDetail(<?php echo $timestamp['id']; ?>)" 
                                                            class="bg-gray-50 hover:bg-gray-100 text-gray-700 px-3 py-1 rounded text-xs font-medium transition-colors">
                                                        Detail
                                                    </button>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Document Info -->
                        <div class="bg-white rounded-lg shadow-sm border mt-6">
                            <div class="p-6 border-b">
                                <h3 class="text-lg font-semibold text-gray-900">Informace o dokumentu</h3>
                            </div>
                            <div class="p-6 space-y-4">
                                <div>
                                    <p class="text-sm font-medium text-gray-700">ID dokumentu</p>
                                    <p class="text-sm text-gray-600"><?php echo $document['id']; ?></p>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-700">Typ souboru</p>
                                    <p class="text-sm text-gray-600"><?php echo $document['file_type'] ?: 'Textový dokument'; ?></p>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-700">Velikost</p>
                                    <p class="text-sm text-gray-600">
                                        <?php echo $document['file_size'] ? number_format($document['file_size'] / 1024, 1) . ' KB' : 'N/A'; ?>
                                    </p>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-700">Poslední úprava</p>
                                    <p class="text-sm text-gray-600"><?php echo date('d.m.Y H:i', strtotime($document['updated_at'])); ?></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function exportToPDF() {
            alert('Export do PDF bude implementován v další verzi.');
        }
        
        function showTimestampDetail(timestampId) {
            alert('Detail časového razítka #' + timestampId + ' bude implementován v další verzi.');
        }
    </script>
</body>
</html>

<?php
/**
 * CGM Dokumenty - přesměrování na nový systém automatických karet
 */

require_once 'config_optimized.php';

// Kontrola přihlášení
requireLogin();

$currentPage = 'cgm_documents';
?>

<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CGM Dokumenty - Dentibot</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
        .sidebar {
            width: 16rem;
            background: linear-gradient(135deg, #1e3a8a, #1e40af);
            color: white;
            position: fixed;
            height: 100vh;
            left: 0;
            top: 0;
            z-index: 100;
            overflow-y: auto;
        }
        .main-content {
            margin-left: 16rem;
            min-height: 100vh;
        }
        .nav-link {
            display: flex;
            align-items: center;
            padding: 0.75rem 1.5rem;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.2s;
            cursor: pointer;
        }
        .nav-link:hover, .nav-link.active {
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }
        .nav-icon {
            width: 1.25rem;
            height: 1.25rem;
            margin-right: 0.75rem;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="p-6 border-b border-blue-600">
            <div class="flex items-center">
                <div class="w-8 h-8 bg-white rounded mr-3 flex items-center justify-center">
                    <span class="text-blue-600 font-bold text-sm">D</span>
                </div>
                <span class="text-xl font-bold">Dentibot</span>
            </div>
            <div class="mt-3 text-sm text-blue-200">
                <div><?php echo htmlspecialchars($_SESSION['username'] ?? 'Demo User'); ?></div>
                <div class="text-xs"><?php echo ucfirst(getUserSubscriptionPlan($_SESSION['user_id'])); ?> plán</div>
            </div>
        </div>
        <nav class="mt-6">
            <a href="main_dashboard.php" class="nav-link">
                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <rect x="3" y="3" width="18" height="18" rx="2"/><path d="M3 9h18"/><path d="M9 21V9"/>
                </svg>
                Dashboard
            </a>
            <a href="call_history.php" class="nav-link">
                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
                </svg>
                Historie hovorů
            </a>
            <a href="sms_campaigns.php" class="nav-link">
                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
                </svg>
                Hromadné SMS
            </a>
            <a href="patients_list.php" class="nav-link">
                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/><circle cx="9" cy="7" r="4"/><path d="M23 21v-2a4 4 0 0 0-3-3.87"/><path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                </svg>
                Seznam pacientů
            </a>
            <a href="cgm_redirect.php" class="nav-link active">
                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/><polyline points="14,2 14,8 20,8"/><line x1="16" y1="13" x2="8" y2="13"/><line x1="16" y1="17" x2="8" y2="17"/><polyline points="10,9 9,9 8,9"/><circle cx="12" cy="15" r="2"/>
                </svg>
                CGM Razítka
                <span class="ml-2 bg-red-500 text-white text-xs px-2 py-1 rounded-full">legacy</span>
            </a>
            <a href="patient_cards.php" class="nav-link">
                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/><polyline points="14,2 14,8 20,8"/><line x1="16" y1="13" x2="8" y2="13"/><line x1="16" y1="17" x2="8" y2="17"/><polyline points="10,9 9,9 8,9"/><circle cx="12" cy="15" r="2"/>
                </svg>
                Karty pacientů
                <span class="ml-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full">auto</span>
            </a>
            <a href="demo_events.html" class="nav-link">
                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                </svg>
                Demo události
                <span class="ml-2 bg-orange-500 text-white text-xs px-2 py-1 rounded-full">test</span>
            </a>
            <a href="settings.php" class="nav-link">
                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <circle cx="12" cy="12" r="3"/><path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
                </svg>
                Nastavení
            </a>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <div class="p-8">
            <!-- Header -->
            <div class="mb-8">
                <h1 class="text-3xl font-bold text-gray-900">CGM Časová razítka</h1>
                <p class="text-gray-600 mt-2">Starý systém manuálního razítkování dokumentů</p>
            </div>

            <!-- Migration Notice -->
            <div class="bg-gradient-to-r from-blue-50 to-green-50 rounded-lg p-8 mb-8">
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <svg class="w-12 h-12 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                        </svg>
                    </div>
                    <div class="ml-6">
                        <h2 class="text-2xl font-bold text-gray-900 mb-4">🎉 Přechod na automatickou kartu pacienta!</h2>
                        <p class="text-gray-700 mb-6">
                            Starý systém manuálního razítkování dokumentů byl nahrazen <strong>automatickou kartou pacienta</strong> 
                            s event-driven architekturou. Už žádné "Uložit + Razítko" - vše běží automaticky!
                        </p>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                            <div>
                                <h3 class="font-semibold text-gray-900 mb-2">❌ Starý způsob:</h3>
                                <ul class="text-sm text-gray-600 space-y-1">
                                    <li>• Manuální "Uložit + Razítko"</li>
                                    <li>• Jednotlivé dokumenty</li>
                                    <li>• Riziko zapomenutí razítka</li>
                                    <li>• Složité vyhledávání</li>
                                </ul>
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-900 mb-2">✅ Nový způsob:</h3>
                                <ul class="text-sm text-gray-600 space-y-1">
                                    <li>• Zero-click administrace</li>
                                    <li>• Jeden zdroj pravdy</li>
                                    <li>• Automatické razítkování</li>
                                    <li>• JSON struktura pro rychlé dotazy</li>
                                </ul>
                            </div>
                        </div>

                        <div class="flex flex-wrap gap-4">
                            <a href="patient_cards.php" class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                                🚀 Přejít na automatické karty
                            </a>
                            <a href="demo_events.html" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                                🧪 Vyzkoušet demo události
                            </a>
                            <a href="migrate_patient_cards.php" class="bg-orange-600 hover:bg-orange-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                                ⚙️ Spustit migraci
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Legacy System Info -->
            <div class="bg-white rounded-lg shadow-sm border p-6 mb-8">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">Informace o starém systému</h2>
                <div class="prose text-gray-600">
                    <p>
                        Starý CGM systém vyžadoval manuální kroky pro každý dokument:
                    </p>
                    <ol class="list-decimal list-inside space-y-2 mt-4">
                        <li>Vytvoření dokumentu</li>
                        <li>Uložení do systému</li>
                        <li>Manuální kliknutí na "Přidat časové razítko"</li>
                        <li>Čekání na zpracování TST</li>
                        <li>Export jednotlivých PDF</li>
                    </ol>
                    
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mt-6">
                        <h3 class="font-semibold text-yellow-800 mb-2">⚠️ Problémy starého systému:</h3>
                        <ul class="text-yellow-700 space-y-1">
                            <li>• Riziko zapomenutí razítka</li>
                            <li>• Časově náročná administrace</li>
                            <li>• Fragmentované dokumenty</li>
                            <li>• Složité vyhledávání a reporty</li>
                            <li>• Manuální chyby</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- New System Benefits -->
            <div class="bg-white rounded-lg shadow-sm border p-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">Výhody nového systému</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                            </svg>
                        </div>
                        <h3 class="font-semibold text-gray-900 mb-2">Zero-click administrace</h3>
                        <p class="text-sm text-gray-600">Lékař nedělá "Uložit + Razítko", vše běží automaticky po událostech</p>
                    </div>
                    
                    <div class="text-center">
                        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                        <h3 class="font-semibold text-gray-900 mb-2">Jeden zdroj pravdy</h3>
                        <p class="text-sm text-gray-600">Kontrolor vidí jediný soubor "Karta Jana Nováka" - snadné audity</p>
                    </div>
                    
                    <div class="text-center">
                        <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                            </svg>
                        </div>
                        <h3 class="font-semibold text-gray-900 mb-2">Vyhledávání & reporty</h3>
                        <p class="text-sm text-gray-600">JSON struktura umožní elastické full-text i BI bez parsování PDF</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Auto redirect after 10 seconds
        setTimeout(function() {
            if (confirm('Chcete být automaticky přesměrováni na nové automatické karty pacientů?')) {
                window.location.href = 'patient_cards.php';
            }
        }, 10000);
    </script>
</body>
</html>

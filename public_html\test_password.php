<?php
require_once 'config.php';
require_once 'error_log.php';

function hashPassword($password) {
    $options = [
        'cost' => 10,
    ];
    $hash = password_hash($password, PASSWORD_BCRYPT, $options);
    if ($hash === false) {
        throw new Exception("Failed to generate password hash");
    }
    return $hash;
}

function createTestUser($username, $email, $password) {
    global $mysqli;
    
    $hashedPassword = hashPassword($password);
    
    $query = "INSERT INTO users (username, email, password_hash) VALUES (?, ?, ?)";
    $stmt = $mysqli->prepare($query);
    
    if (!$stmt) {
        throw new Exception("Error preparing query: " . $mysqli->error);
    }
    
    $stmt->bind_param("sss", $username, $email, $hashedPassword);
    
    if (!$stmt->execute()) {
        throw new Exception("Error creating user: " . $stmt->error);
    }
    
    $userId = $mysqli->insert_id;
    $stmt->close();
    
    return [$userId, $hashedPassword];
}

function verifyPassword($username, $password) {
    global $mysqli;
    
    $stmt = $mysqli->prepare("SELECT id, password_hash FROM users WHERE username = ?");
    $stmt->bind_param("s", $username);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($user = $result->fetch_assoc()) {
        $verified = password_verify($password, $user['password_hash']);
        $stmt->close();
        return [$verified, $user['password_hash']];
    }

    $stmt->close();
    return [false, null];
}

// Test the password hashing and verification
$testUsername = 'VojtaDorny';
$testEmail = $testUsername . '@example.com';
$testPassword = 'Dorny29';

try {
    // Create a new test user
    list($userId, $originalHash) = createTestUser($testUsername, $testEmail, $testPassword);
    echo "Test user created with ID: $userId\n";
    echo "Original hash: $originalHash\n";

    // Verify the password for the new user
    list($verified, $storedHash) = verifyPassword($testUsername, $testPassword);
    echo "Password verification " . ($verified ? "successful" : "failed") . "\n";
    echo "Stored hash: $storedHash\n";

    // Compare original and stored hashes
    echo "Hashes match: " . ($originalHash === $storedHash ? "Yes" : "No") . "\n";

    // Try an incorrect password
    list($incorrectVerified, $incorrectStoredHash) = verifyPassword($testUsername, 'WrongPassword');
    echo "Incorrect password verification " . ($incorrectVerified ? "successful (this is bad)" : "failed (this is good)") . "\n";

    // Display hash info
    $hashInfo = password_get_info($storedHash);
    echo "Hash info: " . print_r($hashInfo, true) . "\n";

} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}

$mysqli->close();
?>


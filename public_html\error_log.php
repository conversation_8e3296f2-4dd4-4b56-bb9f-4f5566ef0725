<?php
if (!function_exists('writeErrorLog')) {
    function writeErrorLog($message, $context = []) {
        $timestamp = date('Y-m-d H:i:s');
        $contextStr = !empty($context) ? ' Context: ' . json_encode($context) : '';
        $logMessage = "[{$timestamp}] {$message}{$contextStr}\n";
        
        // Log to PHP error log
        error_log($logMessage);
        
        // You might also want to log to a custom file
        $logFile = __DIR__ . '/logs/app.log';
        
        // Ensure logs directory exists
        if (!is_dir(__DIR__ . '/logs')) {
            mkdir(__DIR__ . '/logs', 0755, true);
        }
        
        // Append to log file
        file_put_contents($logFile, $logMessage, FILE_APPEND);
    }
}
?>
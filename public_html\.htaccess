# Nastavení vý<PERSON><PERSON><PERSON> stránky
DirectoryIndex landing.php index.php

# Povolení přepisování URL
RewriteEngine On

# Pokud soubor nebo adresář existuje, použij ho přímo
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d

# Explicitní povolení přístupu k PHP souborům
RewriteRule ^([^/]+)\.php$ - [L]

# Přesměrování z root URL na landing.php
RewriteRule ^$ landing.php [L]

# Zabezpečení - zakázání přístupu k souborům .htaccess a .htpasswd
<FilesMatch "^\.ht">
    Require all denied
</FilesMatch>

# Povolení přístupu ke všem PHP souborům
<FilesMatch "\.php$">
    Require all granted
</FilesMatch>

# Nastavení proměnných prostředí
<IfModule mod_env.c>
    SetEnv DB_HOST localhost
    SetEnv DB_USER u345712091_dentibot
    SetEnv DB_PASS G0highlevel223##
    SetEnv DB_NAME u345712091_dentibot
    SetEnv ERROR_LOG_FILE /home/<USER>/domains/dentibot.eu/public_html/app.log
    SetEnv STRIPE_RESTRICTED_KEY ***********************************************************************************************************
    SetEnv STRIPE_PUBLISHABLE_KEY pk_live_51P3KeDFAUrabx3XWMdVelxoP7fwT9Z4cwxswNnET8aEm93KVFezCI7Pzg5QBV8d7GUeWZ5sTIoDHAL9Bpwzr9rOp00pv7iV5Mj
    SetEnv STRIPE_WEBHOOK_SECRET whsec_your_webhook_secret_here
    SetEnv SMSVIO_SERVER https://gate.smsvio.cz
    SetEnv SMSVIO_API_KEY 5c6cca3a1f5029d017e32bde4a63839f57bd074b
    SetEnv DEFAULT_ASSISTANT_ID your_default_assistant_id_here
</IfModule>

# Define the password as an environment variable
SetEnv SETTINGS_PASSWORD "G0highlevel"

# Protect the settings.php file
<Files "settings.php">
    # Additional security headers
    Header set X-Content-Type-Options "nosniff"
    Header set X-Frame-Options "DENY"
    Header set X-XSS-Protection "1; mode=block"
</Files>

# Disable directory browsing
Options -Indexes

# Protect .htaccess file
<Files .htaccess>
    Order allow,deny
    Deny from all
</Files>

# Protect sensitive files
<FilesMatch "(^#.*#|\.(bak|config|dist|fla|inc|ini|log|psd|sh|sql|sw[op])|~)$">
    Order allow,deny
    Deny from all
    Satisfy All
</FilesMatch>

# Force HTTPS
RewriteEngine On
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Set default character set
AddDefaultCharset UTF-8

# Enable PHP error logging
php_flag log_errors on
php_value error_log ${ERROR_LOG_FILE}

# Allow POST requests to webhook_handler.php
<Files "webhook_handler.php">
    Order Allow,Deny
    Allow from all
</Files>

# Set correct content type for JSON responses
AddType application/json .json

# Enable CORS for webhook endpoint
<IfModule mod_headers.c>
    Header set Access-Control-Allow-Origin "*"
    Header set Access-Control-Allow-Methods "POST"
    Header set Access-Control-Allow-Headers "Content-Type"
</IfModule>

# Protect credentials.json file
<Files "credentials.json">
    Order allow,deny
    Deny from all
</Files>

# Protect config.php file
<Files "config.php">
    Order Allow,Deny
    Deny from all
</Files>

# Additional security headers
<IfModule mod_headers.c>
    Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains"
    Header always set X-Content-Type-Options "nosniff"
    Header always set X-Frame-Options "SAMEORIGIN"
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://js.stripe.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net; img-src 'self' data: https:; font-src 'self' data: https:; connect-src 'self' https://api.stripe.com;"
</IfModule>

# Prevent access to .git directory
RedirectMatch 404 /\.git

# Limit file upload size
LimitRequestBody 10485760

Header set Content-Security-Policy "frame-src 'self' https://baserow.io https://*.baserow.io https://cal.com https://*.cal.com https://app.cal.com;"
Header set X-Frame-Options "ALLOW-FROM https://baserow.io"

# Enable the rewrite engine
RewriteEngine On

# Ensure the request is not for an existing directory or file
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME} !-f

# Remove .php extension for URLs without it
RewriteRule ^([^\.]+)$ $1.php [NC,L]

# Redirect URLs with .php to non-.php versions
RewriteCond %{THE_REQUEST} \s/+(.+?)\.php[\s?] [NC]
RewriteRule ^ /%1 [R=301,NE,L]

# Redirect /index to /
RewriteRule ^index$ / [R=301,L]

# Redirect /index.php to /
RewriteRule ^index\.php$ / [R=301,L]

# Prevent access to .htaccess file
<Files .htaccess>
    Order allow,deny
    Deny from all
</Files>

# Prevent directory listing
Options -Indexes

# Set default character set
AddDefaultCharset UTF-8

# Disable server signature
ServerSignature Off

php_value session.gc_maxlifetime 86400
php_value session.cookie_lifetime 86400


# TADY ZAČÍNA SEKCE SEO: 

# Zapnutí mod_rewrite
RewriteEngine On

# Přesměrování na HTTPS
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Přesměrování www na non-www
RewriteCond %{HTTP_HOST} ^www\.(.*)$ [NC]
RewriteRule ^(.*)$ https://%1/$1 [R=301,L]

# Odstranění koncového lomítka
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)/$ /$1 [L,R=301]

# Komprese obsahu
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/html text/plain text/xml text/css text/javascript application/javascript application/x-javascript application/xml
</IfModule>

# Nastavení cache
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/webp "access plus 1 year"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    ExpiresByType text/html "access plus 1 day"
</IfModule>

# Ochrana proti hotlinkingu
RewriteCond %{HTTP_REFERER} !^$
RewriteCond %{HTTP_REFERER} !^https?://(www\.)?dentibot\.eu [NC]
RewriteRule \.(jpg|jpeg|png|gif|webp)$ - [NC,F,L]

RewriteEngine On
RewriteBase /

# Pokud soubor nebo adresář neexistuje, přesměruj na index.php
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [L,QSA]

# Speciální pravidlo pro /platby
RewriteRule ^platby/?$ platby.php [L,QSA]

# Povolení přístupu ke všem PHP souborům
<FilesMatch "\.php$">
    Order Allow,Deny
    Allow from all
</FilesMatch>

# Povolení přístupu k error.log pro autorizované uživatele
<Files "error.log">
    Order Deny,Allow
    Deny from all
    # Povolení přístupu pouze z lokální sítě nebo pro přihlášené uživatele
    Require valid-user
    AuthType Basic
    AuthName "Restricted Access"
    AuthUserFile /path/to/.htpasswd
    Satisfy Any
</Files>

# Povolení CORS pro update_subscription.php
<Files "update_subscription.php">
    Header set Access-Control-Allow-Origin "*"
    Header set Access-Control-Allow-Methods "POST, OPTIONS"
    Header set Access-Control-Allow-Headers "Content-Type"
    Header set Access-Control-Max-Age "3600"
    
    # Zpracování OPTIONS požadavků pro preflight
    RewriteEngine On
    RewriteCond %{REQUEST_METHOD} OPTIONS
    RewriteRule ^(.*)$ $1 [R=200,L]
</Files>

# Povolení přístupu k update_subscription.php
<Files "update_subscription.php">
    Order Allow,Deny
    Allow from all
</Files>

# Nastavení pro zpracování JSON požadavků
<IfModule mod_mime.c>
    AddType application/json .json
</IfModule>

# Zvýšení limitu pro POST požadavky (pokud je to potřeba)
<IfModule mod_php.c>
    php_value post_max_size 8M
    php_value upload_max_filesize 8M
</IfModule>

php_flag display_startup_errors off
php_flag display_errors off
php_flag html_errors off
php_value docref_root 0
php_value docref_ext 0

<IfModule mod_headers.c>
    SetEnvIf Origin "^http(s)?://(.+\.)?(dentibot\.eu)$" ORIGIN_DOMAIN=$0
    Header set Access-Control-Allow-Origin "%{ORIGIN_DOMAIN}e" env=ORIGIN_DOMAIN
    Header set Access-Control-Allow-Credentials "true"
    Header set Access-Control-Allow-Methods "POST, OPTIONS"
    Header set Access-Control-Allow-Headers "Content-Type, X-Requested-With"
</IfModule>




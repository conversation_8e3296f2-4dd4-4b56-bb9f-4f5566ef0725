<?php
// Vytvořte soubor fix_patient_update.php s tímto obsahem

require_once 'config.php';
require_once 'error_log.php';

// Enable error reporting for debugging
ini_set('display_errors', 1);
error_reporting(E_ALL);

echo "<h1>Oprava aktualizace pacientů</h1>";

if (file_exists('patients.php')) {
    // Create a backup
    copy('patients.php', 'patients.php.bak.' . time());
    
    // Read the file
    $content = file_get_contents('patients.php');
    
    // Find the update_patient_field case
    if (preg_match('/case\s+\'update_patient_field\':(.*?)break;/s', $content, $matches)) {
        echo "<p>Nalezen case 'update_patient_field' v souboru patients.php</p>";
        
        // Create a new file with the fixed code
        $fixedCode = '<?php
// Přidejte tento kód do souboru patients.php, nahraďte existující case "update_patient_field"

case \'update_patient_field\':
    // Enhanced logging for debugging
    error_log("Update patient field request method: " . $_SERVER[\'REQUEST_METHOD\']);
    error_log("Content-Type: " . ($_SERVER[\'CONTENT_TYPE\'] ?? \'Not set\'));
    
    // Log all possible sources of data
    $rawPostData = file_get_contents(\'php://input\');
    error_log("Raw POST data: " . $rawPostData);
    error_log("POST variables: " . json_encode($_POST));
    error_log("GET variables: " . json_encode($_GET));
    
    // Initialize variables
    $patientId = 0;
    $field = \'\';
    $value = \'\';
    
    // Method 1: Get from GET parameters (most reliable for this case)
    if (isset($_GET[\'patient_id\']) && !empty($_GET[\'patient_id\'])) {
        $patientId = intval($_GET[\'patient_id\']);
        error_log("Patient ID from GET[\'patient_id\']: " . $patientId);
        $field = $_GET[\'field\'] ?? \'\';
        $value = $_GET[\'value\'] ?? \'\';
    }
    else if (isset($_GET[\'id\']) && !empty($_GET[\'id\'])) {
        $patientId = intval($_GET[\'id\']);
        error_log("Patient ID from GET[\'id\']: " . $patientId);
        $field = $_GET[\'field\'] ?? \'\';
        $value = $_GET[\'value\'] ?? \'\';
    }
    // Method 2: Get from POST data
    else if (isset($_POST[\'id\']) && !empty($_POST[\'id\'])) {
        $patientId = intval($_POST[\'id\']);
        error_log("Patient ID from POST[\'id\']: " . $patientId);
        $field = $_POST[\'field\'] ?? \'\';
        $value = $_POST[\'value\'] ?? \'\';
    }
    // Method 3: Try to parse JSON data
    else {
        $jsonData = json_decode($rawPostData, true);
        if (isset($jsonData[\'id\']) && !empty($jsonData[\'id\'])) {
            $patientId = intval($jsonData[\'id\']);
            error_log("Patient ID from JSON data: " . $patientId);
            $field = $jsonData[\'field\'] ?? \'\';
            $value = $jsonData[\'value\'] ?? \'\';
        }
    }
    
    // Log the final extracted values
    error_log("Final extracted values: patientId={$patientId}, field={$field}, value={$value}");
    
    // Field mapping
    $fieldMapping = [
        \'examination_date\' => \'Datum_prohlidky\',
        \'akutni\' => \'Akutní\',
        \'brouseni\' => \'Broušení\',
        \'endo\' => \'Endo\',
        \'extrakce_chirurgie\' => \'Extrakce, chirurgie\',
        \'postendo\' => \'Postendo\',
        \'predni_protetiky\' => \'Předání protetiky\',
        \'sanace_dite\' => \'Sanace - dítě\',
        \'sanace_dospely\' => \'Sanace - dospělý\',
        \'snimatelna_protetika\' => \'Snímatelná protetika - otisky\'
    ];
    
    try {
        // Validate patient ID - must be greater than 0
        if ($patientId <= 0) {
            throw new Exception("Neplatné ID pacienta: " . $patientId);
        }
        
        if (!isset($fieldMapping[$field])) {
            throw new Exception("Neplatné pole: " . $field);
        }
        
        $baserowField = $fieldMapping[$field];
        
        // Special handling for examination date
        if ($field === \'examination_date\') {
            $success = updateExaminationDateInBaserow($patientId, $value);
            
            if (!$success) {
                throw new Exception("Nepodařilo se aktualizovat datum prohlídky v Baserow");
            }
            
            echo json_encode([\'success\' => true, \'message\' => "Datum prohlídky bylo úspěšně aktualizováno"]);
        } else {
            // For other fields, use standard update
            $updateData = [];
            $updateData[$baserowField] = $value;
            
            error_log("Updating patient field: " . json_encode([
                \'patient_id\' => $patientId,
                \'field\' => $field,
                \'baserow_field\' => $baserowField,
                \'value\' => $value,
                \'update_data\' => $updateData
            ]));
            
            // Make the Baserow API request
            $response = baserowRequest(\'PATCH\', $patientId . \'/?user_field_names=true\', $updateData);
            
            if (!is_array($response)) {
                throw new Exception("Neočekávaná odpověď při aktualizaci pacienta v Baserow");
            }
            
            echo json_encode([\'success\' => true, \'message\' => "Pole bylo úspěšně aktualizováno"]);
        }
    } catch (Exception $e) {
        error_log("Error updating patient field: " . $e->getMessage());
        echo json_encode([\'success\' => false, \'message\' => $e->getMessage()]);
    }
    break;
?>';
        
        file_put_contents('fixed_update_patient_field.php', $fixedCode);
        
        echo "<p style='color:green;'>Vytvořen soubor fixed_update_patient_field.php s opraveným kódem.</p>";
        echo "<p>Zkopírujte obsah tohoto souboru a nahraďte existující case 'update_patient_field' v souboru patients.php.</p>";
    } else {
        echo "<p style='color:red;'>Case 'update_patient_field' nebyl nalezen v souboru patients.php!</p>";
    }
} else {
    echo "<p style='color:red;'>Soubor patients.php nebyl nalezen!</p>";
}

// Now let's also fix the JavaScript function that sends the data
echo "<h2>Oprava JavaScript funkce pro odesílání dat</h2>";

// Create a JavaScript fix file
$jsFixCode = '// Nahraďte funkci saveCellValue v JavaScript kódu touto verzí:

saveCellValue(event) {
    const input = event.target;

    // Check if element is still in DOM
    if (!input || !input.parentNode) {
        console.log("Element already removed, skipping save");
        return;
    }
    
    // Get data attributes
    const patientId = input.dataset.patientId;
    const field = input.dataset.field;
    const value = input.value.trim() || \'-\';
    
    // Validate patient ID
    if (!patientId || isNaN(parseInt(patientId)) || parseInt(patientId) <= 0) {
        console.error(`Invalid patient ID: ${patientId}`);
        alert(`Nelze uložit: Neplatné ID pacienta (${patientId})`);
        
        // Remove the element
        if (input.parentNode) {
            input.parentNode.removeChild(input);
        }
        document.removeEventListener(\'click\', this.handleDocumentClick);
        return;
    }
    
    // Add visual saving indicator
    const cell = document.querySelector(`td[data-patient-id="${patientId}"][data-field="${field}"]`);
    if (cell) {
        cell.innerHTML = \'<span class="saving-indicator">Ukládání...</span>\';
    }
    
    // Update value in UI
    const patientIndex = this.patients.findIndex(p => p.id == patientId);
    if (patientIndex !== -1) {
        this.patients[patientIndex][field] = value;
    }
    
    // Remove element from DOM
    if (input.parentNode) {
        input.parentNode.removeChild(input);
    }
    document.removeEventListener(\'click\', this.handleDocumentClick);
    
    console.log(`Saving cell value: Patient ID: ${patientId}, Field: ${field}, Value: ${value}`);
    
    // Use GET parameters for more reliable data transmission
    const url = `patients.php?action=update_patient_field&patient_id=${encodeURIComponent(patientId)}&field=${encodeURIComponent(field)}&value=${encodeURIComponent(value)}`;
    
    console.log("Sending request to URL:", url);
    
    // Send request to server using GET with URL parameters
    fetch(url, {
        method: \'GET\',
        headers: {
            \'Cache-Control\': \'no-cache, no-store\'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`Server responded with status: ${response.status}`);
        }
        return response.text();
    })
    .then(responseText => {
        console.log(\'Raw server response:\', responseText);
        
        // Try to parse as JSON
        let result;
        try {
            result = JSON.parse(responseText);
            console.log(\'Parsed server response:\', result);
        } catch (parseError) {
            console.error(\'Failed to parse server response as JSON:\', parseError);
            console.error(\'Raw response was:\', responseText);
            throw new Error(`Invalid JSON response: ${parseError.message}`);
        }
        
        if (!result.success) {
            console.error(\'Chyba při ukládání hodnoty:\', result.message);
            throw new Error(result.message || \'Unknown error\');
        }
        
        // Show success indicator
        if (cell) {
            cell.innerHTML = `<span class="save-success">${value}</span>`;
            setTimeout(() => {
                if (cell) {
                    // Return to original formatting
                    const className = this.getCellClassName(field, value);
                    cell.innerHTML = `<span class="${className}">${value}</span>`;
                }
            }, 1500);
        }
    })
    .catch(error => {
        console.error(\'Error saving value:\', error);
        
        // Show error indicator
        if (cell) {
            cell.innerHTML = `<span class="save-error">Chyba: ${error.message}</span>`;
            setTimeout(() => {
                this.fetchPatients(); // Refresh data after 3 seconds
            }, 3000);
        }
    });
}';

file_put_contents('fixed_saveCellValue.js', $jsFixCode);
echo "<p style='color:green;'>Vytvořen soubor fixed_saveCellValue.js s opravenou JavaScript funkcí.</p>";
echo "<p>Zkopírujte obsah tohoto souboru a nahraďte existující funkci saveCellValue ve vašem JavaScript kódu.</p>";

// Create a test script
$testScript = '<?php
require_once \'config.php\';
require_once \'baserow_functions.php\';
require_once \'error_log.php\';

// Enable error reporting for debugging
ini_set(\'display_errors\', 1);
error_reporting(E_ALL);

// Initialize session
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Get patient ID from URL
$patientId = isset($_GET[\'id\']) ? intval($_GET[\'id\']) : 0;
$field = isset($_GET[\'field\']) ? $_GET[\'field\'] : \'brouseni\';
$value = isset($_GET[\'value\']) ? $_GET[\'value\'] : \'test-\' . time();

echo "<h1>Test aktualizace pacienta</h1>";
echo "<p>Testování aktualizace pro pacienta ID: $patientId, pole: $field, hodnota: $value</p>";

if ($patientId <= 0) {
    echo "<p style=\'color:red;\'>Chyba: Neplatné ID pacienta. Zadejte platné ID v URL parametru \'id\'.</p>";
    exit;
}

try {
    // Get Baserow config
    $config = getBaserowConfig();
    echo "<p>Baserow konfigurace načtena úspěšně</p>";
    echo "<ul>";
    echo "<li>Database ID: " . $config[\'baserow_database_id\'] . "</li>";
    echo "<li>Table ID: " . $config[\'baserow_table_id\'] . "</li>";
    echo "<li>API Token délka: " . strlen($config[\'baserow_api_token\']) . "</li>";
    echo "</ul>";
    
    // Field mapping
    $fieldMapping = [
        \'examination_date\' => \'Datum_prohlidky\',
        \'akutni\' => \'Akutní\',
        \'brouseni\' => \'Broušení\',
        \'endo\' => \'Endo\',
        \'extrakce_chirurgie\' => \'Extrakce, chirurgie\',
        \'postendo\' => \'Postendo\',
        \'predni_protetiky\' => \'Předání protetiky\',
        \'sanace_dite\' => \'Sanace - dítě\',
        \'sanace_dospely\' => \'Sanace - dospělý\',
        \'snimatelna_protetika\' => \'Snímatelná protetika - otisky\'
    ];
    
    if (!isset($fieldMapping[$field])) {
        echo "<p style=\'color:red;\'>Chyba: Neplatné pole: $field</p>";
        exit;
    }
    
    $baserowField = $fieldMapping[$field];
    echo "<p>Mapované pole: $field -> $baserowField</p>";
    
    // Prepare update data
    $updateData = [];
    $updateData[$baserowField] = $value;
    
    echo "<p>Data pro aktualizaci: " . json_encode($updateData) . "</p>";
    
    // Make direct API request
    echo "<p>Odesílání API požadavku...</p>";
    $response = baserowRequest(\'PATCH\', $patientId . \'/?user_field_names=true\', $updateData);
    
    echo "<p style=\'color:green;\'>API požadavek úspěšný!</p>";
    echo "<pre>" . json_encode($response, JSON_PRETTY_PRINT) . "</pre>";
    
    echo "<p style=\'color:green;font-weight:bold;\'>Test dokončen úspěšně!</p>";
    
} catch (Exception $e) {
    echo "<p style=\'color:red;font-weight:bold;\'>Chyba: " . $e->getMessage() . "</p>";
    echo "<p>Stack trace:</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>';

file_put_contents('test_patient_update.php', $testScript);
echo "<p style='color:green;'>Testovací skript test_patient_update.php byl vytvořen.</p>";
echo "<p>Můžete ho použít pro testování aktualizace pacienta: <a href='test_patient_update.php?id=3798&field=brouseni&value=test-" . time() . "'>Test aktualizace pacienta</a></p>";

echo "<h2>Shrnutí oprav:</h2>";
echo "<ol>";
echo "<li>Vytvořili jsme soubor fixed_update_patient_field.php s opraveným kódem pro case 'update_patient_field'.</li>";
echo "<li>Vytvořili jsme soubor fixed_saveCellValue.js s opravenou JavaScript funkcí.</li>";
echo "<li>Vytvořili jsme testovací skript test_patient_update.php pro ověření, že aktualizace pacienta funguje správně.</li>";
echo "</ol>";

echo "<p>Po implementaci těchto oprav by měla aktualizace pacientů fungovat správně.</p>";
?>
<?php
// <PERSON><PERSON>žte jako diagnose.php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Zkontrolujte oprávnění k zápisu
$directories = [
    'landing-page/css',
    'landing-page/js',
    './'  // kořenový adresář
];

echo "<h1>Diagnostika oprávnění</h1>";
foreach ($directories as $dir) {
    if (file_exists($dir)) {
        echo "$dir existuje. ";
        if (is_writable($dir)) {
            echo "<span style='color:green'>Zapisovatelný ✓</span><br>";
        } else {
            echo "<span style='color:red'>Není zapisovatelný ✗</span><br>";
        }
    } else {
        echo "$dir <span style='color:red'>neexistuje ✗</span><br>";
    }
}

// Zkontrolujte limity PHP
echo "<h1>PHP Limity</h1>";
echo "Memory limit: " . ini_get('memory_limit') . "<br>";
echo "Max execution time: " . ini_get('max_execution_time') . " sekund<br>";
echo "Post max size: " . ini_get('post_max_size') . "<br>";
echo "Upload max filesize: " . ini_get('upload_max_filesize') . "<br>";

// Zkuste vytvořit testovací soubor
echo "<h1>Test zápisu souboru</h1>";
$test_file = 'test_write.txt';
$result = @file_put_contents($test_file, 'Test zápisu');
if ($result !== false) {
    echo "<span style='color:green'>Úspěšně vytvořen testovací soubor ✓</span><br>";
    unlink($test_file); // Smažte testovací soubor
} else {
    echo "<span style='color:red'>Nelze vytvořit testovací soubor ✗</span><br>";
}
?>
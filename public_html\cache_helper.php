<?php
class CacheHelper {
    private $cacheDir;
    private $defaultTTL = 300; // 5 minut v sekundách

    public function __construct() {
        $this->cacheDir = __DIR__ . '/cache';
        if (!is_dir($this->cacheDir)) {
            mkdir($this->cacheDir, 0755, true);
        }
    }

    private function getCacheKey($key, $params = []) {
        return md5($key . serialize($params));
    }

    private function getCacheFile($key) {
        return $this->cacheDir . '/' . $key . '.cache';
    }

    public function get($key, $params = []) {
        $cacheKey = $this->getCacheKey($key, $params);
        $cacheFile = $this->getCacheFile($cacheKey);

        if (!file_exists($cacheFile)) {
            return null;
        }

        $content = file_get_contents($cacheFile);
        $data = json_decode($content, true);

        if (!$data || !isset($data['expires']) || !isset($data['data'])) {
            return null;
        }

        if (time() > $data['expires']) {
            unlink($cacheFile);
            return null;
        }

        return $data['data'];
    }

    // Opravené pořadí parametrů - $data je nyní před volitelným $ttl
    public function set($key, $data, $params = [], $ttl = null) {
        $cacheKey = $this->getCacheKey($key, $params);
        $cacheFile = $this->getCacheFile($cacheKey);

        $ttl = $ttl ?? $this->defaultTTL;
        
        $content = json_encode([
            'expires' => time() + $ttl,
            'data' => $data
        ]);

        file_put_contents($cacheFile, $content);
    }

    public function clear($key = null, $params = []) {
        if ($key === null) {
            // Clear all cache
            array_map('unlink', glob($this->cacheDir . '/*.cache'));
            return;
        }

        $cacheKey = $this->getCacheKey($key, $params);
        $cacheFile = $this->getCacheFile($cacheKey);
        
        if (file_exists($cacheFile)) {
            unlink($cacheFile);
        }
    }
}
?>
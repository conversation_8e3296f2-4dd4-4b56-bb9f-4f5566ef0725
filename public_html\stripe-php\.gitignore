# Ignore build files
build/*

# Mac OS X dumps these all over the place.
.DS_Store

# Ignore the SimpleTest library if it is installed to /test/.
/test/simpletest/

# Ignore the /vendor/ directory for people using composer
/vendor/

# If the vendor directory isn't being commited the composer.lock file should also be ignored
composer.lock

# Ignore IDE's configuration files
.idea

# Ignore PHP CS Fixer local config and cache
.php_cs
.php_cs.cache
.php-cs-fixer.cache

# Ignore PHPStan local config
.phpstan.neon

# Ignore phpDocumentor's local config and artifacts
.phpdoc/*
phpdoc.xml

# Ignore cached PHPUnit results.
.phpunit.result.cache

<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dentibot - Demo CGM <PERSON> raz<PERSON></title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="css/cgm_documents.css" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
        .sidebar {
            width: 16rem;
            background: linear-gradient(135deg, #1e3a8a, #1e40af);
            color: white;
            position: fixed;
            height: 100vh;
            left: 0;
            top: 0;
            z-index: 100;
        }
        .main-content {
            margin-left: 16rem;
            min-height: 100vh;
        }
        .nav-link {
            display: flex;
            align-items: center;
            padding: 0.75rem 1.5rem;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.2s;
        }
        .nav-link:hover, .nav-link.active {
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }
        .nav-icon {
            width: 1.25rem;
            height: 1.25rem;
            margin-right: 0.75rem;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="p-6 border-b border-blue-600">
            <div class="flex items-center">
                <img src="images/logo.png" alt="Dentibot" class="w-8 h-8 mr-3">
                <span class="text-xl font-bold">Dentibot</span>
            </div>
        </div>
        <nav class="mt-6">
            <a href="#" class="nav-link">
                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <rect x="3" y="3" width="18" height="18" rx="2"/><path d="M3 9h18"/><path d="M9 21V9"/>
                </svg>
                Přehledy
            </a>
            <a href="#" class="nav-link">
                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
                </svg>
                Historie hovorů
            </a>
            <a href="#" class="nav-link">
                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
                </svg>
                Hromadné SMS
            </a>
            <a href="#" class="nav-link">
                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/><circle cx="9" cy="7" r="4"/><path d="M23 21v-2a4 4 0 0 0-3-3.87"/><path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                </svg>
                Seznam pacientů
            </a>
            <a href="#" class="nav-link active">
                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/><polyline points="14,2 14,8 20,8"/><line x1="16" y1="13" x2="8" y2="13"/><line x1="16" y1="17" x2="8" y2="17"/><polyline points="10,9 9,9 8,9"/><circle cx="12" cy="15" r="2"/>
                </svg>
                CGM Časová razítka
                <span class="ml-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full">nové</span>
            </a>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <div class="p-8">
            <!-- Header -->
            <div class="mb-8">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">CGM Časová razítka</h1>
                        <p class="text-gray-600 mt-2">Elektronická dokumentace bez kompromisů</p>
                    </div>
                    <button onclick="openCreateModal()" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                        <svg class="w-5 h-5 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                        </svg>
                        Nový dokument
                    </button>
                </div>
            </div>

            <!-- Success Alert -->
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                ✅ CGM modul byl úspěšně implementován! Toto je demo verze designu.
            </div>

            <!-- Filters -->
            <div class="bg-white rounded-lg shadow-sm border p-6 mb-6">
                <div class="flex flex-wrap gap-4">
                    <div class="flex-1 min-w-64">
                        <input type="text" placeholder="Hledat v dokumentech..." 
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    <div>
                        <select class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                            <option value="">Všechny kategorie</option>
                            <option value="1">Zdravotnická dokumentace</option>
                            <option value="2">Vyšetření</option>
                            <option value="3">Terapie</option>
                            <option value="4">Recepty</option>
                        </select>
                    </div>
                    <button class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg transition-colors">
                        Filtrovat
                    </button>
                </div>
            </div>

            <!-- Documents Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- Document Card 1 -->
                <div class="document-card bg-white rounded-lg p-6">
                    <div class="flex items-start justify-between mb-4">
                        <div class="flex-1">
                            <h3 class="font-semibold text-gray-900 mb-2">Preventivní prohlídka - Jan Novák</h3>
                            <p class="text-sm text-gray-600 mb-2">Preventivní péče</p>
                            <p class="text-xs text-gray-500">15.01.2025 14:30</p>
                        </div>
                        <span class="timestamp-badge text-white text-xs px-2 py-1 rounded-full">2 razítka</span>
                    </div>
                    <p class="text-sm text-gray-700 mb-4 line-clamp-3">
                        Rutinní preventivní prohlídka, kontrola zubního kazu, čištění zubního kamene...
                    </p>
                    <div class="flex gap-2">
                        <button class="flex-1 bg-blue-50 hover:bg-blue-100 text-blue-700 px-4 py-2 rounded text-sm font-medium transition-colors">
                            Zobrazit
                        </button>
                        <button class="bg-green-50 hover:bg-green-100 text-green-700 px-4 py-2 rounded text-sm font-medium transition-colors">
                            + Razítko
                        </button>
                    </div>
                </div>

                <!-- Document Card 2 -->
                <div class="document-card bg-white rounded-lg p-6">
                    <div class="flex items-start justify-between mb-4">
                        <div class="flex-1">
                            <h3 class="font-semibold text-gray-900 mb-2">Lékařská zpráva - Marie Svobodová</h3>
                            <p class="text-sm text-gray-600 mb-2">Zprávy</p>
                            <p class="text-xs text-gray-500">14.01.2025 10:15</p>
                        </div>
                        <span class="timestamp-badge text-white text-xs px-2 py-1 rounded-full">1 razítko</span>
                    </div>
                    <p class="text-sm text-gray-700 mb-4 line-clamp-3">
                        Zpráva o provedené endodontické léčbě zubu 16, aplikace koferdamu...
                    </p>
                    <div class="flex gap-2">
                        <button class="flex-1 bg-blue-50 hover:bg-blue-100 text-blue-700 px-4 py-2 rounded text-sm font-medium transition-colors">
                            Zobrazit
                        </button>
                        <button class="bg-green-50 hover:bg-green-100 text-green-700 px-4 py-2 rounded text-sm font-medium transition-colors">
                            + Razítko
                        </button>
                    </div>
                </div>

                <!-- Document Card 3 -->
                <div class="document-card bg-white rounded-lg p-6">
                    <div class="flex items-start justify-between mb-4">
                        <div class="flex-1">
                            <h3 class="font-semibold text-gray-900 mb-2">RTG snímek - Pavel Dvořák</h3>
                            <p class="text-sm text-gray-600 mb-2">Vyšetření</p>
                            <p class="text-xs text-gray-500">13.01.2025 16:45</p>
                        </div>
                        <span class="timestamp-badge text-white text-xs px-2 py-1 rounded-full">3 razítka</span>
                    </div>
                    <p class="text-sm text-gray-700 mb-4 line-clamp-3">
                        Panoramatický RTG snímek pro diagnostiku před ortodontickou léčbou...
                    </p>
                    <div class="flex gap-2">
                        <button class="flex-1 bg-blue-50 hover:bg-blue-100 text-blue-700 px-4 py-2 rounded text-sm font-medium transition-colors">
                            Zobrazit
                        </button>
                        <button class="bg-green-50 hover:bg-green-100 text-green-700 px-4 py-2 rounded text-sm font-medium transition-colors">
                            + Razítko
                        </button>
                    </div>
                </div>
            </div>

            <!-- Features Info -->
            <div class="mt-12 bg-gradient-to-r from-blue-50 to-green-50 rounded-lg p-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">Klíčové funkce CGM Časová razítka</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="text-center">
                        <div class="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
                            </svg>
                        </div>
                        <h3 class="font-semibold text-gray-900 mb-2">Vysoká bezpečnost</h3>
                        <p class="text-sm text-gray-600">SHA-256 hash dokumentů a časová razítka podle RFC 3161</p>
                    </div>
                    <div class="text-center">
                        <div class="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                        <h3 class="font-semibold text-gray-900 mb-2">Legislativní compliance</h3>
                        <p class="text-sm text-gray-600">Splnění všech požadavků ČR na vedení zdravotnické dokumentace</p>
                    </div>
                    <div class="text-center">
                        <div class="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                            </svg>
                        </div>
                        <h3 class="font-semibold text-gray-900 mb-2">Převoditelnost</h3>
                        <p class="text-sm text-gray-600">Export do PDF i po ztrátě certifikátu</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Create Document Modal -->
    <div id="createModal" class="modal">
        <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-screen overflow-y-auto">
            <div class="p-6 border-b">
                <h2 class="text-xl font-semibold text-gray-900">Nový dokument</h2>
            </div>
            <div class="p-6">
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Název dokumentu *</label>
                    <input type="text" required class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Kategorie</label>
                    <select class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                        <option>Zdravotnická dokumentace</option>
                        <option>Vyšetření</option>
                        <option>Terapie</option>
                        <option>Recepty</option>
                    </select>
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Obsah dokumentu</label>
                    <textarea rows="6" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"></textarea>
                </div>
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Soubor (volitelné)</label>
                    <input type="file" accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.gif,.txt" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    <p class="text-xs text-gray-500 mt-1">Podporované formáty: PDF, DOC, DOCX, JPG, PNG, GIF, TXT (max 10MB)</p>
                </div>
                <div class="flex gap-4">
                    <button class="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-lg font-medium transition-colors">
                        Vytvořit dokument
                    </button>
                    <button onclick="closeCreateModal()" class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                        Zrušit
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function openCreateModal() {
            document.getElementById('createModal').classList.add('active');
        }
        
        function closeCreateModal() {
            document.getElementById('createModal').classList.remove('active');
        }
        
        // Close modal when clicking outside
        document.getElementById('createModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeCreateModal();
            }
        });
    </script>
</body>
</html>

<?php
// Strukturovaná data pro Google

// Funkce pro generování strukturovaných dat pro SoftwareApplication
function generate_software_application_schema() {
    return [
        "@context" => "https://schema.org",
        "@type" => "SoftwareApplication",
        "name" => "Dentibot",
        "applicationCategory" => "HealthcareSoftware",
        "operatingSystem" => "Web-based",
        "offers" => [
            "@type" => "Offer",
            "price" => "2500",
            "priceCurrency" => "CZK",
            "frequency" => "monthly"
        ],
        "description" => "Inteligentní voicebot systém pro automatizaci komunikace v zubních ordinacích",
        "aggregateRating" => [
            "@type" => "AggregateRating",
            "ratingValue" => "4.8",
            "ratingCount" => "150"
        ],
        "featureList" => [
            "Automatické objednávání pacientů",
            "SMS notifikace",
            "24/7 dostupnost",
            "Integrace s kalendářem"
        ]
    ];
}

// Funkce pro generování strukturovaných dat pro FAQ
function generate_faq_schema() {
    return [
        "@context" => "https://schema.org",
        "@type" => "FAQPage",
        "mainEntity" => [
            [
                "@type" => "Question",
                "name" => "Co se stane, když vyčerpáme počet minut?",
                "acceptedAnswer" => [
                    "@type" => "Answer",
                    "text" => "Připočítáme další minuty podle vašeho balíčku."
                ]
            ],
            [
                "@type" => "Question",
                "name" => "Jaké jsou technické požadavky?",
                "acceptedAnswer" => [
                    "@type" => "Answer",
                    "text" => "Stačí internetové připojení a základní zařízení."
                ]
            ]
        ]
    ];
}

// Funkce pro generování strukturovaných dat pro LocalBusiness
function generate_local_business_schema() {
    return [
        "@context" => "https://schema.org",
        "@type" => "LocalBusiness",
        "name" => "Dentibot",
        "description" => "Inteligentní voicebot systém pro automatizaci komunikace v zubních ordinacích",
        "url" => "https://www.dentibot.eu",
        "logo" => "https://www.dentibot.eu/images/DENTIBOT LOGO.png",
        "telephone" => "+420123456789",
        "email" => "<EMAIL>",
        "priceRange" => "$$"
    ];
}

// Funkce pro získání strukturovaných dat podle typu
function get_structured_data($type = 'software') {
    switch ($type) {
        case 'software':
            return generate_software_application_schema();
        case 'faq':
            return generate_faq_schema();
        case 'business':
            return generate_local_business_schema();
        default:
            return generate_software_application_schema();
    }
}
?>


<?php
require_once 'config.php';
require_once 'api_functions.php';

if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

if (!isset($_SESSION['user_id'])) {
    die("Už<PERSON>l nen<PERSON>.");
}

$userId = $_SESSION['user_id'];
$apiConfig = getCurrentUserApiKey($userId);

if (!$apiConfig) {
    echo "Chyba: Nelze načíst konfiguraci API pro uživatele.";
} else {
    echo "API URL: " . htmlspecialchars($apiConfig['url']) . "<br>";
    echo "API Key (prvních 5 znaků): " . htmlspecialchars(substr($apiConfig['key'], 0, 5)) . "...<br>";
    
    // Test connection to API
    $testUrl = rtrim($apiConfig['url'], '/') . '/api/v1/calls';
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $testUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HEADER, true);
    curl_setopt($ch, CURLOPT_NOBODY, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        "Authorization: Bearer " . $apiConfig['key'],
        "Content-Type: application/json",
        "Accept: application/json"
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "Test připojení k API: ";
    if ($httpCode == 200) {
        echo "Úspěšné (HTTP 200)";
    } else {
        echo "Neúspěšné (HTTP " . $httpCode . ")";
    }
}
?>
<?php
require_once 'config.php';
require_once 'error_log.php';

function checkUsers() {
    $mysqli = getDbConnection();
    $query = "SELECT id, username, password FROM users";
    $result = $mysqli->query($query);

    if ($result) {
        while ($row = $result->fetch_assoc()) {
            echo "User ID: " . $row['id'] . "<br>";
            echo "Username: " . $row['username'] . "<br>";
            echo "Password Hash: " . $row['password'] . "<br>";
            echo "Password Hash Info: " . password_get_info($row['password'])['algoName'] . "<br>";
            echo "<hr>";
        }
    } else {
        echo "Chyba při načítání uživatelů: " . $mysqli->error;
    }
}

// Spustíme kontrolu
checkUsers();


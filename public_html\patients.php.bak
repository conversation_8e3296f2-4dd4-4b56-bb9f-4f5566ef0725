<?php
require_once 'config.php';
require_once 'baserow_functions.php';
require_once 'error_log.php';

// Podrobné logování HTTP požadavků
$requestMethod = $_SERVER['REQUEST_METHOD'] ?? 'UNKNOWN';
$requestUri = $_SERVER['REQUEST_URI'] ?? 'UNKNOWN';
$contentType = $_SERVER['CONTENT_TYPE'] ?? 'UNKNOWN';
$requestBody = file_get_contents('php://input');

error_log("=== HTTP REQUEST DEBUG ===");
error_log("Method: " . $requestMethod);
error_log("URI: " . $requestUri);
error_log("Content-Type: " . $contentType);
error_log("Body length: " . strlen($requestBody));
error_log("POST data: " . json_encode($_POST));

// Enable error reporting for debugging
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Initialize session and check for errors
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Kontrola přihlášení
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

$error = null;
$success = null;

// Set the current page for the sidebar
$currentPage = 'patients';

// Generate CSRF token
$csrfToken = generateCSRFToken();

// Logo path
$logo_path = "/images/logo.png";

// Zpracování AJAX požadavků
if (isset($_GET['action'])) {
    // Always set the content type to JSON for AJAX responses
    header('Content-Type: application/json');
    
    try {
        switch ($_GET['action']) {
            case 'get_patients':
                $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
                $perPage = isset($_GET['perPage']) ? intval($_GET['perPage']) : 20;
                $searchTerm = isset($_GET['search']) ? trim($_GET['search']) : '';
                
                try {
                    // Přidáme detailní logování
                    error_log("=== GET_PATIENTS REQUEST ===");
                    error_log("Page: " . $page);
                    error_log("PerPage: " . $perPage);
                    error_log("Search Term: " . $searchTerm);
                    
                    // Validace vstupních parametrů
                    if ($page < 1) $page = 1;
                    if ($perPage < 1) $perPage = 20;
                    
                    $result = getPatients($page, $perPage, $searchTerm);
                    
                    // Logování výsledku
                    error_log("Result success: " . ($result['success'] ? 'true' : 'false'));
                    error_log("Found patients: " . (isset($result['patients']) ? count($result['patients']) : 0));
                    error_log("Total records: " . ($result['totalRecords'] ?? 0));
                    
                    if (!$result['success']) {
                        throw new Exception($result['error'] ?? 'Unknown error');
                    }
                    
                    echo json_encode([
                        'success' => true,
                        'patients' => $result['patients'],
                        'totalRecords' => $result['totalRecords'],
                        'debug' => [
                            'searchTerm' => $searchTerm,
                            'page' => $page,
                            'perPage' => $perPage
                        ]
                    ]);
                    
                    error_log("Response sent successfully");
                    
                } catch (Exception $e) {
                    error_log("Error in get_patients: " . $e->getMessage());
                    error_log("Stack trace: " . $e->getTraceAsString());
                    
                    echo json_encode([
                        'success' => false,
                        'error' => 'Chyba při načítání pacientů: ' . $e->getMessage(),
                        'debug' => [
                            'searchTerm' => $searchTerm,
                            'page' => $page,
                            'perPage' => $perPage
                        ]
                    ]);
                }
                break;
                
            case 'get_all_patients':
                try {
                    $searchTerm = isset($_GET['search']) ? $_GET['search'] : '';
                    $batchSize = 100; // Menší velikost stránky, kterou Baserow zvládne
                    $allPatients = [];
                    $page = 1;
                    
                    // Nejprve získáme celkový počet pacientů
                    $countResult = getPatients(1, 1, $searchTerm);
                    $totalRecords = $countResult['totalRecords'];
                    
                    // Postupně načítáme pacienty po dávkách
                    while (count($allPatients) < $totalRecords) {
                        $result = getPatients($page, $batchSize, $searchTerm);
                        
                        if (!isset($result['patients']) || !is_array($result['patients'])) {
                            throw new Exception('Invalid data structure returned from getPatients');
                        }
                        
                        // Přidáme pacienty do celkového pole
                        foreach ($result['patients'] as $patient) {
                            $allPatients[] = [
                                'id' => isset($patient['id']) ? (int)$patient['id'] : null,
                                'name' => isset($patient['name']) ? htmlspecialchars($patient['name']) : '',
                                'email' => isset($patient['email']) ? htmlspecialchars($patient['email']) : '',
                                'phone' => isset($patient['phone']) ? htmlspecialchars($patient['phone']) : '',
                                'waiting_status' => isset($patient['waiting_status']) ? htmlspecialchars($patient['waiting_status']) : 'Nečeká',
                                'examination_date' => isset($patient['examination_date']) ? htmlspecialchars($patient['examination_date']) : '-',
                                'akutni' => isset($patient['akutni']) ? htmlspecialchars($patient['akutni']) : '-',
                                'brouseni' => isset($patient['brouseni']) ? htmlspecialchars($patient['brouseni']) : '-',
                                'endo' => isset($patient['endo']) ? htmlspecialchars($patient['endo']) : '-',
                                'extrakce_chirurgie' => isset($patient['extrakce_chirurgie']) ? htmlspecialchars($patient['extrakce_chirurgie']) : '-',
                                'postendo' => isset($patient['postendo']) ? htmlspecialchars($patient['postendo']) : '-',
                                'predni_protetiky' => isset($patient['predni_protetiky']) ? htmlspecialchars($patient['predni_protetiky']) : '-',
                                'sanace_dite' => isset($patient['sanace_dite']) ? htmlspecialchars($patient['sanace_dite']) : '-',
                                'sanace_dospely' => isset($patient['sanace_dospely']) ? htmlspecialchars($patient['sanace_dospely']) : '-',
                                'snimatelna_protetika' => isset($patient['snimatelna_protetika']) ? htmlspecialchars($patient['snimatelna_protetika']) : '-'
                            ];
                        }
                        
                        $page++;
                        
                        // Kontrola pro případ, že by se něco pokazilo
                        if ($page > ceil($totalRecords / $batchSize) + 1) {
                            break;
                        }
                    }
                    
                    // Odstranění pacientů s chybějícím ID
                    $allPatients = array_filter($allPatients, function($patient) {
                        return $patient['id'] !== null;
                    });
                    
                    error_log("Total patients loaded: " . count($allPatients));
                    
                    echo json_encode([
                        'success' => true,
                        'patients' => array_values($allPatients)
                    ]);
                    
                } catch (Exception $e) {
                    error_log("Error in get_all_patients: " . $e->getMessage());
                    echo json_encode([
                        'success' => false,
                        'error' => 'Chyba při načítání všech pacientů: ' . $e->getMessage()
                    ]);
                }
                break;
                
            case 'add_patient':
                if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                    $patientData = [
                        'Jmeno_pacienta' => $_POST['name'] ?? '',
                        'Emailova_adresa' => $_POST['email'] ?? '',
                        'Telefonni_cislo' => $_POST['phone'] ?? '',
                        'Rodne_cislo' => $_POST['birth_number'] ?? '',
                        'Ceka_na_prideleni_terminu' => $_POST['waiting_status'] ?? 'Nečeká',
                        'Datum_prohlidky' => convertToBaserowDateFormat($_POST['examination_date'] ?? date('Y-m-d')),
                        'Stav preventivních prohlídek' => 2324745,
                        'Komunikační preference' => 2324747,
                        'Akutní' => $_POST['akutni'] ?? '-',
                        'Broušení' => $_POST['brouseni'] ?? '-',
                        'Endo' => $_POST['endo'] ?? '-',
                        'Extrakce, chirurgie' => $_POST['extrakce_chirurgie'] ?? '-',
                        'Postendo' => $_POST['postendo'] ?? '-',
                        'Předání protetiky' => $_POST['predni_protetiky'] ?? '-',
                        'Sanace - dítě' => $_POST['sanace_dite'] ?? '-',
                        'Sanace - dospělý' => $_POST['sanace_dospely'] ?? '-',
                        'Snímatelná protetika - otisky' => $_POST['snimatelna_protetika'] ?? '-'
                    ];

                    try {
                        if (empty($patientData['Jmeno_pacienta']) || empty($patientData['Emailova_adresa']) || empty($patientData['Telefonni_cislo'])) {
                            throw new Exception("Všechna pole jsou povinná");
                        }

                        $response = baserowRequest('POST', '?user_field_names=true', $patientData);

                        if (!is_array($response) || !isset($response['id'])) {
                            throw new Exception("Neočekávaná odpověď při přidávání pacienta do Baserow");
                        }

                        echo json_encode(['success' => true, 'message' => "Pacient byl úspěšně přidán do Baserow", 'id' => $response['id']]);
                    } catch (Exception $e) {
                        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
                    }
                } else {
                    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
                }
                break;
                
            // UPDATED: Improved update_patient_field case
           case 'update_patient_field':
    // Enhanced logging for debugging
    error_log("Update patient field request method: " . $_SERVER['REQUEST_METHOD']);
    error_log("Content-Type: " . ($_SERVER['CONTENT_TYPE'] ?? 'Not set'));
    
    // Get patient ID from multiple possible sources
    $patientId = 0;
    
    // Try GET parameters first (most reliable)
    if (isset($_GET['patient_id']) && !empty($_GET['patient_id'])) {
        $patientId = intval($_GET['patient_id']);
        error_log("Patient ID from GET['patient_id']: " . $patientId);
    }
    else if (isset($_GET['id']) && !empty($_GET['id'])) {
        $patientId = intval($_GET['id']);
        error_log("Patient ID from GET['id']: " . $patientId);
    }
    // Then try POST data
    else if (isset($_POST['id']) && !empty($_POST['id'])) {
        $patientId = intval($_POST['id']);
        error_log("Patient ID from POST['id']: " . $patientId);
    }
    // Finally try JSON data
    else {
        $rawPostData = file_get_contents('php://input');
        $jsonData = json_decode($rawPostData, true);
        if (isset($jsonData['id']) && !empty($jsonData['id'])) {
            $patientId = intval($jsonData['id']);
            error_log("Patient ID from JSON data: " . $patientId);
        }
    }
    
    // Get field and value
    $field = $_GET['field'] ?? $_POST['field'] ?? $jsonData['field'] ?? '';
    $value = $_GET['value'] ?? $_POST['value'] ?? $jsonData['value'] ?? '';
    
    // Log the final extracted values
    error_log("Final extracted values: patientId={$patientId}, field={$field}, value={$value}");
    
    // Field mapping
    $fieldMapping = [
        'examination_date' => 'Datum_prohlidky',
        'akutni' => 'Akutní',
        'brouseni' => 'Broušení',
        'endo' => 'Endo',
        'extrakce_chirurgie' => 'Extrakce, chirurgie',
        'postendo' => 'Postendo',
        'predni_protetiky' => 'Předání protetiky',
        'sanace_dite' => 'Sanace - dítě',
        'sanace_dospely' => 'Sanace - dospělý',
        'snimatelna_protetika' => 'Snímatelná protetika - otisky'
    ];
    
    try {
        // Validate patient ID - must be greater than 0
        if ($patientId <= 0) {
            throw new Exception("Neplatné ID pacienta: " . $patientId);
        }
        
        if (!isset($fieldMapping[$field])) {
            throw new Exception("Neplatné pole: " . $field);
        }
        
        $baserowField = $fieldMapping[$field];
        
        // Special handling for examination date
        if ($field === 'examination_date') {
            $success = updateExaminationDateInBaserow($patientId, $value);
            
            if (!$success) {
                throw new Exception("Nepodařilo se aktualizovat datum prohlídky v Baserow");
            }
            
            echo json_encode(['success' => true, 'message' => "Datum prohlídky bylo úspěšně aktualizováno"]);
        } else {
            // For other fields, use standard update
            $updateData = [];
            $updateData[$baserowField] = $value;
            
            error_log("Updating patient field: " . json_encode([
                'patient_id' => $patientId,
                'field' => $field,
                'baserow_field' => $baserowField,
                'value' => $value,
                'update_data' => $updateData
            ]));
            
            // Make the Baserow API request
            $response = baserowRequest('PATCH', $patientId . '/?user_field_names=true', $updateData);
            
            if (!is_array($response)) {
                throw new Exception("Neočekávaná odpověď při aktualizaci pacienta v Baserow");
            }
            
            echo json_encode(['success' => true, 'message' => "Pole bylo úspěšně aktualizováno"]);
        }
    } catch (Exception $e) {
        error_log("Error updating patient field: " . $e->getMessage());
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
    break;
                
            // Přidáme testovací endpoint pro přímé testování Baserow API
            case 'test_baserow_update':
                if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                    try {
                        $patientId = isset($_POST['id']) ? intval($_POST['id']) : 0;
                        $field = isset($_POST['field']) ? $_POST['field'] : '';
                        $value = isset($_POST['value']) ? $_POST['value'] : '';
                        
                        if (!$patientId) {
                            throw new Exception("Neplatné ID pacienta");
                        }
                        
                        error_log("Test Baserow Update: " . json_encode([
                            'patient_id' => $patientId,
                            'field' => $field,
                            'value' => $value
                        ]));
                        
                        // Vytvoříme jednoduchý testovací požadavek
                        $updateData = [
                            $field => $value
                        ];
                        
                        // OPRAVENO: Přímé volání Baserow API s podrobným logováním
                        $response = baserowRequest('PATCH', $patientId . '/?user_field_names=true', $updateData);
                        
                        echo json_encode([
                            'success' => true,
                            'message' => 'Test byl úspěšný',
                            'response' => $response
                        ]);
                    } catch (Exception $e) {
                        error_log("Test Baserow Update Error: " . $e->getMessage());
                        echo json_encode([
                            'success' => false,
                            'message' => 'Test selhal: ' . $e->getMessage()
                        ]);
                    }
                } else {
                    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
                }
                break;
                
            case 'test_baserow_api':
                // Tento endpoint je určen pro přímé testování Baserow API
                try {
                    // Získáme konfiguraci Baserow
                    $config = getBaserowConfig();
                    
                    // Vypíšeme informace o konfiguraci
                    echo json_encode([
                        'success' => true,
                        'message' => 'Baserow konfigurace je platná',
                        'config' => [
                            'database_id' => $config['baserow_database_id'],
                            'table_id' => $config['baserow_table_id'],
                            'api_token_length' => strlen($config['baserow_api_token'])
                        ]
                    ]);
                } catch (Exception $e) {
                    echo json_encode([
                        'success' => false,
                        'message' => 'Chyba při získávání konfigurace Baserow: ' . $e->getMessage()
                    ]);
                }
                break;
                
            // Přidejte nový endpoint pro přímé testování aktualizace Baserow
            case 'direct_test_update':
                try {
                    $patientId = isset($_GET['patient_id']) ? intval($_GET['patient_id']) : 0;
                    $field = isset($_GET['field']) ? $_GET['field'] : '';
                    $value = isset($_GET['value']) ? $_GET['value'] : '';
                    
                    if ($patientId <= 0) {
                        throw new Exception("Neplatné ID pacienta: " . $patientId);
                    }
                    
                    if (empty($field)) {
                        throw new Exception("Pole nesmí být prázdné");
                    }
                    
                    // Mapování polí
                    $fieldMapping = [
                        'examination_date' => 'Datum_prohlidky',
                        'akutni' => 'Akutní',
                        'brouseni' => 'Broušení',
                        'endo' => 'Endo',
                        'extrakce_chirurgie' => 'Extrakce, chirurgie',
                        'postendo' => 'Postendo',
                        'predni_protetiky' => 'Předání protetiky',
                        'sanace_dite' => 'Sanace - dítě',
                        'sanace_dospely' => 'Sanace - dospělý',
                        'snimatelna_protetika' => 'Snímatelná protetika - otisky'
                    ];
                    
                    if (!isset($fieldMapping[$field])) {
                        throw new Exception("Neplatné pole: " . $field);
                    }
                    
                    $baserowField = $fieldMapping[$field];
                    
                    // Přímé volání Baserow API
                    $result = testDirectBaserowUpdate($patientId, $baserowField, $value);
                    
                    echo json_encode([
                        'success' => $result['success'],
                        'message' => $result['success'] ? 'Pole bylo úspěšně aktualizováno' : $result['error'],
                        'details' => $result
                    ]);
                } catch (Exception $e) {
                    echo json_encode([
                        'success' => false,
                        'message' => $e->getMessage()
                    ]);
                }
                break;
                
            default:
                echo json_encode(['success' => false, 'error' => 'Invalid action']);
        }
    } catch (Exception $e) {
        // Catch-all for any unexpected errors
        error_log("Unexpected error in AJAX handler: " . $e->getMessage());
        error_log("Stack trace: " . $e->getTraceAsString());
        echo json_encode(['success' => false, 'message' => 'Server error: ' . $e->getMessage()]);
    }
    exit;
}

?>
<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Seznam pacientů - Dentibot</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <style>
        body {
            background-color: #1A202C;
            color: white;
            font-size: 13px;
        }
        
        .custom-table {
            background-color: #1E2A3B;
        }
        
        .custom-table th {
            color: #94A3B8;
            border-bottom: 1px solid #2D3748;
            padding: 6px 10px;
            font-size: 11px;
            white-space: nowrap;
        }
        
        .custom-table td {
            color: #E2E8F0;
            border-bottom: 1px solid #2D3748;
            padding: 5px 10px;
            font-size: 12px;
        }
        
        .custom-table tr:hover {
            background-color: #243147;
        }
        
        .btn-primary {
            background-color: #00B8A9;
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            display: inline-flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
        }
        
        .btn-primary:hover {
            background-color: #009B8E;
        }
        
        .btn-outline {
            background-color: transparent;
            border: 1px solid #00B8A9;
            color: #00B8A9;
            padding: 5px 10px;
            border-radius: 4px;
            display: inline-flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
        }
        
        .btn-outline:hover {
            background-color: #00B8A9;
            color: white;
        }

        .pagination-btn {
            background-color: transparent;
            border: 1px solid #2D3748;
            color: #94A3B8;
            padding: 4px 10px;
            border-radius: 4px;
            font-size: 12px;
        }

        .pagination-btn:hover:not(:disabled) {
            background-color: #243147;
        }

        .pagination-btn.active {
            background-color: #243147;
            border-color: #00B8A9;
            color: white;
        }

        .pagination-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        /* Modal styling */
        .modal-content {
            background-color: #1E2A3B;
            color: white;
        }

        .modal-input {
            background-color: #243147;
            border: 1px solid #2D3748;
            color: white;
            padding: 6px 10px;
            border-radius: 4px;
            width: 100%;
            font-size: 13px;
        }

        .modal-input:focus {
            border-color: #00B8A9;
            outline: none;
        }

        .modal-label {
            color: #94A3B8;
            font-size: 12px;
        }

        /* Sidebar styling */
        .sidebar {
            background-color: #1E2A3B;
            width: 180px;
            height: 100vh;
            position: fixed;
            top: 0;
            left: 0;
            overflow-y: auto;
            transition: transform 0.3s ease-in-out;
            font-size: 12px;
        }

        .sidebar-hidden {
            transform: translateX(-200px);
        }

        .sidebar-toggle {
            position: fixed;
            top: 1rem;
            left: 1rem;
            z-index: 1000;
        }

        .sidebar a {
            color: #E2E8F0;
            text-decoration: none;
            padding: 8px 12px;
            display: block;
        }

        .sidebar a:hover {
            background-color: #243147;
        }

        .content {
            margin-left: 180px;
            transition: margin-left 0.3s ease-in-out;
        }

        .content-full {
            margin-left: 0;
        }
        .light-theme {
            background-color: white;
            color: black;
        }

        .light-theme .custom-table {
            background-color: white;
        }

        .light-theme .custom-table th {
            color: black;
            border-bottom: 1px solid #ccc;
        }

        .light-theme .custom-table td {
            color: black;
            border-bottom: 1px solid #ccc;
        }

        .light-theme .custom-table tr:hover {
            background-color: #f0f0f0;
        }

        .light-theme .btn-primary {
            background-color: #00B8A9;
            color: white;
        }

        .light-theme .btn-primary:hover {
            background-color: #009B8E;
        }

        .light-theme .btn-outline {
            border: 1px solid #00B8A9;
            color: #00B8A9;
        }

        .light-theme .btn-outline:hover {
            background-color: #00B8A9;
            color: white;
        }

        .light-theme .pagination-btn {
            border: 1px solid #ccc;
            color: black;
        }

        .light-theme .pagination-btn:hover:not(:disabled) {
            background-color: #f0f0f0;
        }

        .light-theme .pagination-btn.active {
            background-color: #f0f0f0;
            border-color: #00B8A9;
            color: white;
        }

        .light-theme .modal-content {
            background-color: white;
            color: black;
        }

        .light-theme .modal-input {
            background-color: white;
            border: 1px solid #ccc;
            color: black;
        }

        .light-theme .modal-label {
            color: black;
        }

        .light-theme .sidebar {
            background-color: white;
        }

        .light-theme .sidebar a {
            color: black;
        }

        .light-theme .sidebar a:hover {
            background-color: #f0f0f0;
        }
        
        /* Styling for waiting status */
        .status-waiting {
            color: #F59E0B; /* Yellow for waiting */
            font-weight: 500;
        }
        
        .status-not-waiting {
            color: #10B981; /* Green for not waiting */
            font-weight: 500;
        }
        
        /* Styling for examination date */
        .date-upcoming {
            color: #3B82F6; /* Blue for upcoming dates */
            font-weight: 500;
        }
        
        .date-past {
            color: #6B7280; /* Gray for past dates */
            font-weight: 500;
        }
        
        /* Custom scrollbar for horizontal scrolling */
        .table-container {
            position: relative;
            max-height: 78vh;
            overflow-y: auto;
            font-size: 12px;
        }
        
        /* Column width control */
        .col-fixed {
            min-width: 110px;
        }
        
        .col-actions {
            min-width: 90px;
        }
        
        .col-small {
            min-width: 70px;
            max-width: 90px;
        }
        
        /* Data status styling */
        .data-empty {
            color: #6B7280;
            font-style: italic;
        }
        
        .data-filled {
            color: #10B981;
        }

        /* Sticky header */
        .table-container {
            position: relative;
            max-height: 78vh;
            overflow-y: auto;
            font-size: 12px;
        }
        
        .table-container table {
            position: relative;
            border-collapse: collapse;
        }
        
        .table-container thead {
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        .table-container th {
            background-color: #1E2A3B;
        }
        
        .light-theme .table-container th {
            background-color: white;
        }
        
        /* Search bar styling */
        .search-container {
            margin-bottom: 0.75rem;
        }
        
        .search-input {
            background-color: #243147;
            border: 1px solid #2D3748;
            color: white;
            padding: 6px 10px 6px 28px;
            border-radius: 4px;
            width: 100%;
            max-width: 250px;
            font-size: 13px;
            transition: padding 0.2s;
        }
        
        .search-input:focus {
            padding-left: 10px;
            border-color: #00B8A9;
            outline: none;
        }
        
        .light-theme .search-input {
            background-color: white;
            border: 1px solid #ccc;
            color: black;
        }
        
        /* Column visibility toggle */
        .column-toggle {
            position: relative;
            display: inline-block;
        }
        
        .column-toggle-btn {
            background-color: transparent;
            border: 1px solid #2D3748;
            color: #94A3B8;
            padding: 5px 8px;
            border-radius: 4px;
            font-size: 11px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 3px;
        }
        
        .column-toggle-dropdown {
            position: absolute;
            right: 0;
            top: 100%;
            background-color: #1E2A3B;
            border: 1px solid #2D3748;
            border-radius: 4px;
            padding: 8px;
            z-index: 20;
            min-width: 200px;
            margin-top: 4px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .light-theme .column-toggle-dropdown {
            background-color: white;
            border: 1px solid #ccc;
        }
        
        .column-toggle-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 4px 0;
        }
        
        .column-toggle-checkbox {
            width: 16px;
            height: 16px;
        }
        
        /* Compact table */
        .compact-table th, .compact-table td {
            padding: 4px 8px;
            font-size: 12px;
        }
        
        /* Toolbar */
        .toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.75rem;
            flex-wrap: wrap;
            gap: 8px;
        }
        
        .toolbar-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        /* Responsive adjustments */
        @media (max-width: 768px) {
            .toolbar {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .toolbar-group {
                width: 100%;
                justify-content: space-between;
            }
            
            .search-input {
                max-width: 100%;
            }
        }
        /* Přidejte nový styl pro editovatelné buňky */
        .editable-cell {
            cursor: pointer;
            position: relative;
        }

        .editable-cell:hover::after {
            content: "✏️";
            position: absolute;
            right: 4px;
            font-size: 10px;
            opacity: 0.5;
        }

        .cell-editor {
            position: absolute;
            z-index: 100;
            background-color: #243147;
            border: 1px solid #00B8A9;
            padding: 4px 8px;
            border-radius: 4px;
            width: 100%;
            min-width: 200px;
            font-size: 12px;
            color: white;
        }

        .light-theme .cell-editor {
            background-color: white;
            color: black;
            border: 1px solid #00B8A9;
        }

        /* Přidejte nové styly pro zpětnou vazbu při ukládání */
        @keyframes saving-pulse {
            0% { opacity: 0.6; }
            50% { opacity: 1; }
            100% { opacity: 0.6; }
        }

        .saving-indicator {
            animation: saving-pulse 1.5s infinite;
            color: #3B82F6; /* Blue */
        }

        .save-success {
            color: #10B981; /* Green */
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .save-error {
            color: #EF4444; /* Red */
            font-weight: 500;
        }

        /* Vylepšení stylů pro editovatelné buňky */
        .editable-cell {
            cursor: pointer;
            position: relative;
            transition: background-color 0.2s;
        }

        .editable-cell:hover {
            background-color: rgba(0, 184, 169, 0.1);
        }

        .editable-cell:hover::after {
            content: "✏️";
            position: absolute;
            right: 4px;
            font-size: 10px;
            opacity: 0.5;
        }

        .cell-editor {
            position: absolute;
            z-index: 100;
            background-color: #243147;
            border: 1px solid #00B8A9;
            padding: 4px 8px;
            border-radius: 4px;
            width: 100%;
            min-width: 200px;
            font-size: 12px;
            color: white;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .light-theme .cell-editor {
            background-color: white;
            color: black;
            border: 1px solid #00B8A9;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <div x-data="patientsApp()" x-init="init()" class="flex min-h-screen">
        <!-- Sidebar -->
        <?php include 'sidebar.php'; ?>

        <!-- Main Content -->
        <div class="flex-1 p-4">
            <div class="max-w-[1400px] mx-auto">
                <!-- Header -->
                <div class="flex justify-between items-center mb-3">
                    <h1 class="text-lg font-semibold">Seznam pacientů</h1>
                    <div class="flex gap-1">
                        <button @click="darkMode = !darkMode; updateTheme()" class="btn-outline py-1 px-2 text-xs">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"></path>
                            </svg>
                            <span class="text-xs">Motiv</span>
                        </button>
                        <button @click="synchronizeWithBaserow()" class="btn-outline py-1 px-2 text-xs">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M21.5 2v6h-6M21.34 15.57a10 10 0 1 1-.57-8.38"/>
                            </svg>
                            <span class="text-xs">Sync</span>
                        </button>
                        <button @click="showAddPatientModal = true" class="btn-primary py-1 px-2 text-xs">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <line x1="12" y1="5" x2="12" y2="19"></line>
                                <line x1="5" y1="12" x2="19" y2="12"></line>
                            </svg>
                            <span class="text-xs">Přidat</span>
                        </button>
                        <!-- Přidejte toto tlačítko do sekce s tlačítky v hlavičce -->
                        <button @click="testBaserowApi()" class="btn-outline py-1 px-2 text-xs">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M22 12h-4l-3 9L9 3l-3 9H2"></path>
                            </svg>
                            <span class="text-xs">Test API</span>
                        </button>
                    </div>
                </div>
                
                <!-- Toolbar -->
                <div class="toolbar">
                    <div class="toolbar-group">
                        <!-- Search Bar -->
                        <div class="relative">
                            <input 
                                type="text" 
                                placeholder="Vyhledat pacienta..." 
                                class="search-input"
                                x-model="searchTerm"
                                @input="handleSearch"
                                @focus="searchFocused = true"
                                @blur="searchFocused = false"
                            >
                            <svg 
                                x-show="!searchFocused" 
                                xmlns="http://www.w3.org/2000/svg" 
                                class="h-4 w-4 absolute left-2 top-2 text-gray-400" 
                                viewBox="0 0 24 24" 
                                fill="none" 
                                stroke="currentColor" 
                                stroke-width="2"
                            >
                                <circle cx="11" cy="11" r="8"></circle>
                                <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                            </svg>
                        </div>
                        
                        <!-- Per Page Selector -->
                        <div class="flex items-center gap-1">
                            <label class="text-xs text-gray-400">Počet:</label>
                            <select x-model="perPage" @change="changePerPage" class="modal-input py-0.5 px-1 w-12 text-xs">
                                <option value="10">10</option>
                                <option value="20">20</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="toolbar-group">
                        <!-- Compact Mode Toggle -->
                        <button @click="compactMode = !compactMode; localStorage.setItem('compactMode', compactMode)" class="btn-outline py-1 px-2 text-xs">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                                <line x1="3" y1="9" x2="21" y2="9"></line>
                                <line x1="3" y1="15" x2="21" y2="15"></line>
                            </svg>
                            <span x-text="compactMode ? 'Normální' : 'Kompaktní'" class="text-xs"></span>
                        </button>
                        
                        <!-- Column Visibility Toggle -->
                        <div class="column-toggle">
                            <button @click="toggleColumnDropdown" class="column-toggle-btn">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M12 3v18M3 12h18"></path>
                                </svg>
                                <span class="text-xs">Sloupce</span>
                            </button>
                            <div x-show="showColumnDropdown" @click.away="showColumnDropdown = false" class="column-toggle-dropdown">
                                <template x-for="(column, index) in columns" :key="index">
                                    <div class="column-toggle-item">
                                        <input 
                                            type="checkbox" 
                                            :id="'column-' + index" 
                                            class="column-toggle-checkbox" 
                                            :checked="column.visible" 
                                            @change="toggleColumn(index)"
                                        >
                                        <label :for="'column-' + index" x-text="column.title" class="text-xs cursor-pointer"></label>
                                    </div>
                                </template>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Table -->
                <div class="table-container custom-table rounded-lg overflow-hidden mb-4">
                    <table class="min-w-full" :class="{'compact-table': compactMode}">
                        <thead>
                            <tr>
                                <template x-for="(column, index) in columns" :key="index">
                                    <th x-show="column.visible" :class="column.class" class="text-left">
                                        <span x-text="column.title"></span>
                                    </th>
                                </template>
                            </tr>
                        </thead>
                        <tbody>
                            <template x-for="patient in patients" :key="patient.id">
                                <tr>
                                    <td x-show="columns[0].visible" x-text="patient.name"></td>
                                    <td x-show="columns[1].visible" x-text="patient.email"></td>
                                    <td x-show="columns[2].visible" x-text="patient.phone"></td>
                                    <td x-show="columns[3].visible">
                                        <span x-text="patient.waiting_status || 'Nečeká'" 
                                              :class="patient.waiting_status === 'Čeká' ? 'status-waiting' : 'status-not-waiting'"></span>
                                    </td>
                                    <td x-show="columns[4].visible" @dblclick="editCell($event, patient, 'examination_date')" class="editable-cell" :data-patient-id="patient.id" data-field="examination_date">
                                        <span x-text="patient.examination_date || '-'" 
                                              :class="isUpcomingDate(patient.examination_date) ? 'date-upcoming' : 'date-past'"></span>
                                    </td>
                                    <td x-show="columns[5].visible" @dblclick="editCell($event, patient, 'akutni')" class="editable-cell" :data-patient-id="patient.id" data-field="akutni">
                                        <span x-text="patient.akutni || '-'" 
                                              :class="patient.akutni && patient.akutni !== '-' ? 'data-filled' : 'data-empty'"></span>
                                    </td>
                                    <td x-show="columns[6].visible" @dblclick="editCell($event, patient, 'brouseni')" class="editable-cell" :data-patient-id="patient.id" data-field="brouseni">
                                        <span x-text="patient.brouseni || '-'" 
                                              :class="patient.brouseni && patient.brouseni !== '-' ? 'data-filled' : 'data-empty'"></span>
                                    </td>
                                    <td x-show="columns[7].visible" @dblclick="editCell($event, patient, 'endo')" class="editable-cell" :data-patient-id="patient.id" data-field="endo">
                                        <span x-text="patient.endo || '-'" 
                                              :class="patient.endo && patient.endo !== '-' ? 'data-filled' : 'data-empty'"></span>
                                    </td>
                                    <td x-show="columns[8].visible" @dblclick="editCell($event, patient, 'extrakce_chirurgie')" class="editable-cell" :data-patient-id="patient.id" data-field="extrakce_chirurgie">
                                        <span x-text="patient.extrakce_chirurgie || '-'" 
                                              :class="patient.extrakce_chirurgie && patient.extrakce_chirurgie !== '-' ? 'data-filled' : 'data-empty'"></span>
                                    </td>
                                    <td x-show="columns[9].visible" @dblclick="editCell($event, patient, 'postendo')" class="editable-cell" :data-patient-id="patient.id" data-field="postendo">
                                        <span x-text="patient.postendo || '-'" 
                                              :class="patient.postendo && patient.postendo !== '-' ? 'data-filled' : 'data-empty'"></span>
                                    </td>
                                    <td x-show="columns[10].visible" @dblclick="editCell($event, patient, 'predni_protetiky')" class="editable-cell" :data-patient-id="patient.id" data-field="predni_protetiky">
                                        <span x-text="patient.predni_protetiky || '-'" 
                                              :class="patient.predni_protetiky && patient.predni_protetiky !== '-' ? 'data-filled' : 'data-empty'"></span>
                                    </td>
                                    <td x-show="columns[11].visible" @dblclick="editCell($event, patient, 'sanace_dite')" class="editable-cell" :data-patient-id="patient.id" data-field="sanace_dite">
                                        <span x-text="patient.sanace_dite || '-'" 
                                              :class="patient.sanace_dite && patient.sanace_dite !== '-' ? 'data-filled' : 'data-empty'"></span>
                                    </td>
                                    <td x-show="columns[12].visible" @dblclick="editCell($event, patient, 'sanace_dospely')" class="editable-cell" :data-patient-id="patient.id" data-field="sanace_dospely">
                                        <span x-text="patient.sanace_dospely || '-'" 
                                              :class="patient.sanace_dospely && patient.sanace_dospely !== '-' ? 'data-filled' : 'data-empty'"></span>
                                    </td>
                                    <td x-show="columns[13].visible" @dblclick="editCell($event, patient, 'snimatelna_protetika')" class="editable-cell" :data-patient-id="patient.id" data-field="snimatelna_protetika">
                                        <span x-text="patient.snimatelna_protetika || '-'" 
                                              :class="patient.snimatelna_protetika && patient.snimatelna_protetika !== '-' ? 'data-filled' : 'data-empty'"></span>
                                    </td>
                                    <td x-show="columns[14].visible">
                                        <div class="flex gap-2">
                                            <a href="#" class="text-[#00B8A9] hover:text-[#009B8E] text-xs">Upravit</a>
                                            <a href="#" @click.prevent="deletePatient(patient.id)" class="text-red-500 hover:text-red-400 text-xs">Smazat</a>
                                        </div>
                                    </td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="flex justify-between items-center text-gray-400 text-xs">
                    <p>
                        Zobrazeno <span x-text="(currentPage - 1) * perPage + 1"></span> až 
                        <span x-text="Math.min(currentPage * perPage, totalPatients)"></span> z 
                        <span x-text="totalPatients"></span> pacientů
                    </p>
                    <div class="flex gap-1">
                        <button @click="prevPage()" :disabled="currentPage === 1" 
                                class="pagination-btn">
                            Předchozí
                        </button>
                        <template x-for="page in pageNumbers" :key="page">
                            <button @click="goToPage(page)" 
                                    :class="{'active': page === currentPage}"
                                    class="pagination-btn">
                                <span x-text="page"></span>
                            </button>
                        </template>
                        <button @click="nextPage()" :disabled="currentPage === totalPages" 
                                class="pagination-btn">
                            Další
                        </button>
                    </div>
                </div>

                <!-- Add Patient Modal -->
                <div x-show="showAddPatientModal" x-cloak class="fixed z-10 inset-0 overflow-y-auto">
                    <div class="flex items-center justify-center min-h-screen p-4">
                        <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity"></div>
                        
                        <div x-show="showAddPatientModal" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100" x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100" x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" class="modal-content relative rounded-lg p-4 max-w-lg w-full overflow-y-auto max-h-[90vh]">
                            <h3 class="text-base font-medium mb-3">Přidat nového pacienta</h3>
                            
                            <form id="addPatientForm" @submit.prevent="addPatient">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                                    <div>
                                        <label for="name" class="modal-label block mb-1">Jméno pacienta</label>
                                        <input type="text" id="name" x-model="newPatient.name" required
                                               class="modal-input">
                                    </div>
                                    
                                    <div>
                                        <label for="email" class="modal-label block mb-1">Emailová adresa</label>
                                        <input type="email" id="email" x-model="newPatient.email" required
                                               class="modal-input">
                                    </div>
                                    
                                    <div>
                                        <label for="phone" class="modal-label block mb-1">Telefonní číslo</label>
                                        <input type="tel" id="phone" x-model="newPatient.phone" required
                                               class="modal-input">
                                    </div>
                                    
                                    <div>
                                        <label for="birth_number" class="modal-label block mb-1">Rodné číslo</label>
                                        <input type="text" id="birth_number" x-model="newPatient.birth_number"
                                               class="modal-input">
                                    </div>
                                    
                                    <div>
                                        <label for="waiting_status" class="modal-label block mb-1">Čeká na přidělení termínu</label>
                                        <select id="waiting_status" x-model="newPatient.waiting_status" class="modal-input">
                                            <option value="Čeká">Čeká</option>
                                            <option value="Nečeká">Nečeká</option>
                                        </select>
                                    </div>
                                    
                                    <div>
                                        <label for="examination_date" class="modal-label block mb-1">Datum prohlídky</label>
                                        <input type="datetime-local" id="examination_date" 
                                               x-model="newPatient.examination_date"
                                               class="modal-input">
                                    </div>
                                    
                                    <!-- Nové sloupce -->
                                    <div>
                                        <label for="akutni" class="modal-label block mb-1">Akutní</label>
                                        <input type="text" id="akutni" x-model="newPatient.akutni"
                                               class="modal-input">
                                    </div>
                                    
                                    <div>
                                        <label for="brouseni" class="modal-label block mb-1">Broušení</label>
                                        <input type="text" id="brouseni" x-model="newPatient.brouseni"
                                               class="modal-input">
                                    </div>
                                    
                                    <div>
                                        <label for="endo" class="modal-label block mb-1">Endo</label>
                                        <input type="text" id="endo" x-model="newPatient.endo"
                                               class="modal-input">
                                    </div>
                                    
                                    <div>
                                        <label for="extrakce_chirurgie" class="modal-label block mb-1">Extrakce, chirurgie</label>
                                        <input type="text" id="extrakce_chirurgie" x-model="newPatient.extrakce_chirurgie"
                                               class="modal-input">
                                    </div>
                                    
                                    <div>
                                        <label for="postendo" class="modal-label block mb-1">Postendo</label>
                                        <input type="text" id="postendo" x-model="newPatient.postendo"
                                               class="modal-input">
                                    </div>
                                    
                                    <div>
                                        <label for="predni_protetiky" class="modal-label block mb-1">Přední protetiky</label>
                                        <input type="text" id="predni_protetiky" x-model="newPatient.predni_protetiky"
                                               class="modal-input">
                                    </div>
                                    
                                    <div>
                                        <label for="sanace_dite" class="modal-label block mb-1">Sanace - dítě</label>
                                        <input type="text" id="sanace_dite" x-model="newPatient.sanace_dite"
                                               class="modal-input">
                                    </div>
                                    
                                    <div>
                                        <label for="sanace_dospely" class="modal-label block mb-1">Sanace - dospělý</label>
                                        <input type="text" id="sanace_dospely" x-model="newPatient.sanace_dospely"
                                               class="modal-input">
                                    </div>
                                    
                                    <div>
                                        <label for="snimatelna_protetika" class="modal-label block mb-1">Snímatelná protetika</label>
                                        <input type="text" id="snimatelna_protetika" x-model="newPatient.snimatelna_protetika"
                                               class="modal-input">
                                    </div>
                                </div>
                                
                                <div class="flex gap-3 mt-4">
                                    <button type="submit" class="btn-primary flex-1">
                                        Přidat pacienta
                                    </button>
                                    <button type="button" @click="showAddPatientModal = false" 
                                            class="btn-outline flex-1">
                                        Zrušit
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function patientsApp() {
            return {
                patients: [],
                totalPatients: 0,
                currentPage: 1,
                perPage: 20,
                totalPages: 1,
                showAddPatientModal: false,
                darkMode: false,
                sidebarOpen: true,
                searchTerm: '',
                searchTimeout: null,
                compactMode: false,
                showColumnDropdown: false,
                searchFocused: false,
                columns: [
                    { title: 'JMÉNO', visible: true, class: 'col-fixed' },
                    { title: 'EMAIL', visible: true, class: 'col-fixed' },
                    { title: 'TELEFON', visible: true, class: 'col-fixed' },
                    { title: 'ČEKÁ NA TERMÍN', visible: true, class: 'col-small' },
                    { title: 'DATUM PROHLÍDKY', visible: true, class: '' },
                    { title: 'AKUTNÍ', visible: true, class: 'col-small' },
                    { title: 'BROUŠENÍ', visible: true, class: 'col-small' },
                    { title: 'ENDO', visible: true, class: 'col-small' },
                    { title: 'EXTRAKCE, CHIRURGIE', visible: true, class: '' },
                    { title: 'POSTENDO', visible: true, class: 'col-small' },
                    { title: 'PŘEDNÍ PROTETIKY', visible: true, class: '' },
                    { title: 'SANACE - DÍTĚ', visible: true, class: '' },
                    { title: 'SANACE - DOSPĚLÝ', visible: true, class: '' },
                    { title: 'SNÍMATELNÁ PROTETIKA', visible: true, class: '' },
                    { title: 'AKCE', visible: true, class: 'col-actions' }
                ],
                newPatient: {
                    name: '',
                    email: '',
                    phone: '',
                    birth_number: '',
                    waiting_status: 'Nečeká',
                    examination_date: '',
                    akutni: '-',
                    brouseni: '-',
                    endo: '-',
                    extrakce_chirurgie: '-',
                    postendo: '-',
                    predni_protetiky: '-',
                    sanace_dite: '-',
                    sanace_dospely: '-',
                    snimatelna_protetika: '-'
                },
                editingCell: null,
                editedValue: '',

                init() {
                    this.fetchPatients();
                    this.darkMode = localStorage.getItem('theme') === 'dark';
                    this.updateTheme();
                    
                    // Načtení uložených nastavení sloupců
                    const savedColumns = localStorage.getItem('patientTableColumns');
                    if (savedColumns) {
                        try {
                            const parsedColumns = JSON.parse(savedColumns);
                            this.columns.forEach((col, index) => {
                                if (index < parsedColumns.length) {
                                    col.visible = parsedColumns[index].visible;
                                }
                            });
                        } catch (e) {
                            console.error('Chyba při načítání nastavení sloupců:', e);
                        }
                    }
                    
                    // Načtení kompaktního režimu
                    this.compactMode = localStorage.getItem('compactMode') === 'true';
                },

                async fetchPatients() {
                    try {
                        const response = await fetch(`patients.php?action=get_patients&page=${this.currentPage}&perPage=${this.perPage}&search=${encodeURIComponent(this.searchTerm)}`);
                        if (!response.ok) throw new Error('Network response was not ok');
                        const data = await response.json();
                        
                        this.patients = data.patients;
                        this.totalPatients = data.totalRecords;
                        this.totalPages = Math.ceil(this.totalPatients / this.perPage);
                    } catch (error) {
                        console.error('Error fetching patients:', error);
                    }
                },

                async addPatient() {
                    try {
                        console.log('Odesílání dat pacienta:', this.newPatient);
                        const response = await fetch('patients.php?action=add_patient', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/x-www-form-urlencoded',
                            },
                            body: new URLSearchParams(this.newPatient)
                        });

                        const result = await response.json();
                        console.log('Odpověď serveru:', result);

                        if (result.success) {
                            alert(`Pacient byl úspěšně přidán do Baserow s ID: ${result.id}`);
                            this.showAddPatientModal = false;
                            this.newPatient = { 
                                name: '', 
                                email: '', 
                                phone: '', 
                                birth_number: '', 
                                waiting_status: 'Nečeká',
                                examination_date: '',
                                akutni: '-',
                                brouseni: '-',
                                endo: '-',
                                extrakce_chirurgie: '-',
                                postendo: '-',
                                predni_protetiky: '-',
                                sanace_dite: '-',
                                sanace_dospely: '-',
                                snimatelna_protetika: '-'
                            };
                            await this.fetchPatients();
                        } else {
                            alert('Chyba při přidávání pacienta: ' + result.message);
                        }
                    } catch (error) {
                        console.error('Chyba při přidávání pacienta:', error);
                        alert('Chyba při přidávání pacienta: ' + error.message);
                    }
                },

                async deletePatient(id) {
                    if (!confirm('Opravdu chcete smazat tohoto pacienta?')) return;
                    
                    try {
                        const response = await fetch(`delete_patient.php?id=${id}`, { method: 'POST' });
                        if (!response.ok) throw new Error('Network response was not ok');
                        await this.fetchPatients();
                    } catch (error) {
                        console.error('Error deleting patient:', error);
                        alert('Chyba při mazání pacienta');
                    }
                },
                
                // Funkce pro určení, zda je datum prohlídky v budoucnosti
                isUpcomingDate(dateString) {
                    if (!dateString || dateString === '-') return false;
                    
                    try {
                        // Převod českého formátu data na objekt Date
                        const parts = dateString.split(' ')[0].split('.');
                        if (parts.length !== 3) return false;
                        
                        const day = parseInt(parts[0], 10);
                        const month = parseInt(parts[1], 10) - 1; // Měsíce jsou indexovány od 0
                        const year = parseInt(parts[2], 10);
                        
                        // Pokud je v řetězci i čas, zpracujeme ho
                        let hours = 0, minutes = 0;
                        if (dateString.includes(' ') && dateString.split(' ')[1].includes(':')) {
                            const timeParts = dateString.split(' ')[1].split(':');
                            hours = parseInt(timeParts[0], 10);
                            minutes = parseInt(timeParts[1], 10);
                        }
                        
                        const examDate = new Date(year, month, day, hours, minutes);
                        const today = new Date();
                        
                        return examDate >= today;
                    } catch (e) {
                        console.error('Error parsing date:', e);
                        return false;
                    }
                },

                prevPage() {
                    if (this.currentPage > 1) {
                        this.currentPage--;
                        this.fetchPatients();
                    }
                },

                nextPage() {
                    if (this.currentPage < this.totalPages) {
                        this.currentPage++;
                        this.fetchPatients();
                    }
                },

                goToPage(page) {
                    if (page >= 1 && page <= this.totalPages) {
                        this.currentPage = page;
                        this.fetchPatients();
                    }
                },

                get pageNumbers() {
                    const totalPageButtons = 5;
                    const pageNumbers = [];
                    let startPage = Math.max(1, this.currentPage - Math.floor(totalPageButtons / 2));
                    let endPage = Math.min(this.totalPages, startPage + totalPageButtons - 1);

                    if (endPage - startPage + 1 < totalPageButtons) {
                        startPage = Math.max(1, endPage - totalPageButtons + 1);
                    }

                    for (let i = startPage; i <= endPage; i++) {
                        pageNumbers.push(i);
                    }

                    return pageNumbers;
                },

                async synchronizeWithBaserow() {
                    try {
                        const response = await fetch('sync_patients.php');
                        if (!response.ok) throw new Error('Network response was not ok');
                        await this.fetchPatients();
                        alert('Synchronizace s Baserow byla úspěšná');
                    } catch (error) {
                        console.error('Error syncing with Baserow:', error);
                        alert('Chyba při synchronizaci s Baserow');
                    }
                },

                updateTheme() {
                    if (this.darkMode) {
                        document.body.classList.remove('light-theme');
                        localStorage.setItem('theme', 'dark');
                    } else {
                        document.body.classList.add('light-theme');
                        localStorage.setItem('theme', 'light');
                    }
                    // Dispatch event for sidebar theme change
                    window.dispatchEvent(new CustomEvent('themeChanged', { detail: { isDark: this.darkMode } }));
                },

                toggleSidebar() {
                    this.sidebarOpen = !this.sidebarOpen;
                    localStorage.setItem('sidebarOpen', this.sidebarOpen);
                },

                handleSearch() {
                    // Debounce vyhledávání pro lepší výkon
                    clearTimeout(this.searchTimeout);
                    this.searchTimeout = setTimeout(() => {
                        this.currentPage = 1; // Resetovat na první stránku při vyhledávání
                        this.fetchPatients();
                    }, 300);
                },
                
                toggleColumnDropdown() {
                    this.showColumnDropdown = !this.showColumnDropdown;
                },
                
                toggleColumn(index) {
                    this.columns[index].visible = !this.columns[index].visible;
                    // Uložení nastavení sloupců
                    localStorage.setItem('patientTableColumns', JSON.stringify(this.columns));
                },
                
                changePerPage() {
                    this.currentPage = 1;
                    this.fetchPatients();
                },

                // UPDATED: Improved editCell function
                editCell(event, patient, column) {
                    // Stop event propagation to prevent closing the editor when clicking
                    event.stopPropagation();
                    
                    // Create editor
                    const cell = event.currentTarget;
                    const rect = cell.getBoundingClientRect();
                    
                    // Remove existing editors
                    document.querySelectorAll('.cell-editor').forEach(el => {
                        if (el.parentNode) {
                            el.parentNode.removeChild(el);
                        }
                    });
                    
                    // Create input for editing
                    const input = document.createElement('input');
                    input.type = 'text';
                    input.className = 'cell-editor';
                    input.value = patient[column] || '';
                    
                    // IMPORTANT: Ensure patient ID is valid
                    if (!patient.id || patient.id === 0) {
                        console.error("Invalid patient ID:", patient.id);
                        alert("Nelze upravit: Neplatné ID pacienta");
                        return;
                    }
                    
                    // Set data attributes
                    input.dataset.patientId = String(patient.id);
                    input.dataset.field = column;
                    
                    // Set position
                    input.style.top = `${rect.top}px`;
                    input.style.left = `${rect.left}px`;
                    input.style.width = `${Math.max(rect.width, 200)}px`;
                    
                    // Add to DOM
                    document.body.appendChild(input);
                    
                    // Focus input
                    input.focus();
                    
                    // Add event listeners with proper binding
                    const boundSaveCellValue = this.saveCellValue.bind(this);
                    const boundHandleCellEditorKeydown = this.handleCellEditorKeydown.bind(this);
                    const boundProcessGoogleCalendarUrl = this.processGoogleCalendarUrl.bind(this);
                    
                    input.addEventListener('keydown', boundHandleCellEditorKeydown);
                    input.addEventListener('blur', boundSaveCellValue);
                    input.addEventListener('input', boundProcessGoogleCalendarUrl);
                    
                    // Save references for later removal
                    input.dataset.boundSaveCellValue = boundSaveCellValue;
                    input.dataset.boundHandleCellEditorKeydown = boundHandleCellEditorKeydown;
                    input.dataset.boundProcessGoogleCalendarUrl = boundProcessGoogleCalendarUrl;
                    
                    // Close editor when clicking outside
                    document.addEventListener('click', this.handleDocumentClick.bind(this));
                },

                // UPDATED: Improved saveCellValue function with enhanced debugging
                saveCellValue(event) {
                    const input = event.target;

                    // Check if element is still in DOM
                    if (!input || !input.parentNode) {
                        console.log("Element already removed, skipping save");
                        return;
                    }
                    
                    // Get data attributes
                    const patientId = input.dataset.patientId;
                    const field = input.dataset.field;
                    const value = input.value.trim() || '-';
                    
                    // Validate patient ID
                    if (!patientId || isNaN(parseInt(patientId)) || parseInt(patientId) <= 0) {
                        console.error(`Invalid patient ID: ${patientId}`);
                        alert(`Nelze uložit: Neplatné ID pacienta (${patientId})`);
                        
                        // Remove the element
                        if (input.parentNode) {
                            input.parentNode.removeChild(input);
                        }
                        document.removeEventListener('click', this.handleDocumentClick);
                        return;
                    }
                    
                    // Add visual saving indicator
                    const cell = document.querySelector(`td[data-patient-id="${patientId}"][data-field="${field}"]`);
                    if (cell) {
                        cell.innerHTML = '<span class="saving-indicator">Ukládání...</span>';
                    }
                    
                    // Update value in UI
                    const patientIndex = this.patients.findIndex(p => p.id == patientId);
                    if (patientIndex !== -1) {
                        this.patients[patientIndex][field] = value;
                    }
                    
                    // Remove element from DOM
                    if (input.parentNode) {
                        input.parentNode.removeChild(input);
                    }
                    document.removeEventListener('click', this.handleDocumentClick);
                    
                    console.log(`Saving cell value: Patient ID: ${patientId}, Field: ${field}, Value: ${value}`);
                    
                    // Create FormData for the request
                    const formData = new FormData();
                    formData.append('id', patientId);
                    formData.append('field', field);
                    formData.append('value', value);
                    
                    // Debug the FormData content
                    console.log("FormData entries:");
                    for (let pair of formData.entries()) {
                        console.log(pair[0] + ': ' + pair[1]);
                    }
                    
                    console.log("Sending update request with:", {
                        ID: patientId,
                        Field: field,
                        Value: value
                    });
                    
                    // Send request to server
                    fetch('patients.php?action=update_patient_field', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`Server responded with status: ${response.status}`);
                        }
                        return response.text();
                    })
                    .then(responseText => {
                        console.log('Raw server response:', responseText);
                        
                        // Try to parse as JSON
                        let result;
                        try {
                            result = JSON.parse(responseText);
                            console.log('Parsed server response:', result);
                        } catch (parseError) {
                            console.error('Failed to parse server response as JSON:', parseError);
                            console.error('Raw response was:', responseText);
                            throw new Error(`Invalid JSON response: ${parseError.message}`);
                        }
                        
                        if (!result.success) {
                            console.error('Chyba při ukládání hodnoty:', result.message);
                            throw new Error(result.message || 'Unknown error');
                        }
                        
                        // Show success indicator
                        if (cell) {
                            cell.innerHTML = `<span class="save-success">${value}</span>`;
                            setTimeout(() => {
                                if (cell) {
                                    // Return to original formatting
                                    const className = this.getCellClassName(field, value);
                                    cell.innerHTML = `<span class="${className}">${value}</span>`;
                                }
                            }, 1500);
                        }
                    })
                    .catch(error => {
                        console.error('Error saving value:', error);
                        
                        // Show error indicator
                        if (cell) {
                            cell.innerHTML = `<span class="save-error">Chyba: ${error.message}</span>`;
                            setTimeout(() => {
                                this.fetchPatients(); // Refresh data after 3 seconds
                            }, 3000);
                        }
                    });
                },

                // Přidejte pomocnou funkci pro určení třídy buňky
                getCellClassName(field, value) {
                    if (field === 'examination_date') {
                        return this.isUpcomingDate(value) ? 'date-upcoming' : 'date-past';
                    } else if (value && value !== '-') {
                        return 'data-filled';
                    } else {
                        return 'data-empty';
                    }
                },

                handleCellEditorKeydown(event) {
                    if (event.key === 'Enter') {
                        event.preventDefault();
                        // Správně odstraníme event listener pomocí uložené reference
                        const boundSaveCellValue = event.target.dataset.boundSaveCellValue;
                        if (boundSaveCellValue) {
                            event.target.removeEventListener('blur', window[boundSaveCellValue]);
                        }
                        this.saveCellValue(event);
                    } else if (event.key === 'Escape') {
                        event.preventDefault();
                        // Remove the element without saving
                        if (event.target.parentNode) {
                            event.target.parentNode.removeChild(event.target);
                        }
                        document.removeEventListener('click', this.handleDocumentClick);
                    }
                },

                handleDocumentClick(event) {
                    if (!event.target.classList.contains('cell-editor')) {
                        const editors = document.querySelectorAll('.cell-editor');
                        if (editors.length > 0) {
                            editors.forEach(el => {
                                // Check if the element is still in the DOM
                                if (el && el.parentNode) {
                                    // Remove blur event to prevent double saving
                                    el.removeEventListener('blur', this.saveCellValue);
                                    this.saveCellValue({ target: el });
                                }
                            });
                        }
                        document.removeEventListener('click', this.handleDocumentClick);
                    }
                },

                processGoogleCalendarUrl(event) {
                    const input = event.target;
                    const value = input.value;
                    
                    // Kontrola, zda se jedná o Google Calendar URL
                    if (value.includes('calendar.google.com') && value.includes('eventedit')) {
                        // Extrahujeme Event ID
                        const eventId = this.extractEventId(value);
                        if (eventId) {
                            input.value = eventId;
                        }
                    }
                },

                extractEventId(url) {
                    const match = url.match(/eventedit\/([^?]+)/);
                    return match ? match[1] : "";
                },
                
                // Přidejte tuto funkci do objektu vráceného z patientsApp()
                async testBaserowApi() {
                    try {
                        const response = await fetch('patients.php?action=test_baserow_api');
                        if (!response.ok) throw new Error('Network response was not ok');
                        const data = await response.json();
                        
                        if (data.success) {
                            alert(`Baserow API test úspěšný!\n\nDatabáze ID: ${data.config.database_id}\nTabulka ID: ${data.config.table_id}\nDélka API tokenu: ${data.config.api_token_length}`);
                        } else {
                            alert('Baserow API test selhal: ' + data.message);
                        }
                    } catch (error) {
                        console.error('Error testing Baserow API:', error);
                        alert('Chyba při testování Baserow API: ' + error.message);
                    }
                },
            }
        }
    </script>
</body>
</html>
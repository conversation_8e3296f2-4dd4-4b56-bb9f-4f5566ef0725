<!-- Features Section -->
<section class="py-24 relative">
    <div class="container mx-auto px-6">
        <h2 class="text-4xl md:text-5xl font-bold gradient-text text-center mb-16"><PERSON><PERSON><PERSON><PERSON>ce</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-12">
            <!-- Feature Card 1 -->
            <div class="card p-8 rounded-2xl glow">
                <div class="bg-teal-400 bg-opacity-20 rounded-full w-16 h-16 flex items-center justify-center mb-6">
                    <svg class="w-8 h-8 text-teal-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                </div>
                <h3 class="text-2xl font-bold mb-4 gradient-text">Voicebot Systém</h3>
                <p class="text-gray-400 mb-6 leading-relaxed">
                    Inteligentní hlasový asistent, který se postará o rutinní komunikaci s vašimi pacienty. Automatizace, která šetří váš čas.
                </p>
                <button class="text-teal-400 hover:text-teal-300 flex items-center group">
                    Přečíst si více 
                    <svg class="w-5 h-5 ml-2 transform group-hover:translate-x-2 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                    </svg>
                </button>
            </div>

            <!-- Feature Card 2 -->
            <div class="card p-8 rounded-2xl glow">
                <div class="bg-teal-400 bg-opacity-20 rounded-full w-16 h-16 flex items-center justify-center mb-6">
                    <svg class="w-8 h-8 text-teal-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z"></path>
                    </svg>
                </div>
                <h3 class="text-2xl font-bold mb-4 gradient-text">SMS Notifikace</h3>
                <p class="text-gray-400 mb-6 leading-relaxed">
                    Automatické připomínky a potvrzení termínů přes SMS pro minimalizaci zmeškaných návštěv. Efektivní komunikace s pacienty.
                </p>
                <button class="text-teal-400 hover:text-teal-300 flex items-center group">
                    Přečíst si více 
                    <svg class="w-5 h-5 ml-2 transform group-hover:translate-x-2 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                    </svg>
                </button>
            </div>

            <!-- Feature Card 3 -->
            <div class="card p-8 rounded-2xl glow">
                <div class="bg-teal-400 bg-opacity-20 rounded-full w-16 h-16 flex items-center justify-center mb-6">
                    <svg class="w-8 h-8 text-teal-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                </div>
                <h3 class="text-2xl font-bold mb-4 gradient-text">Analytika</h3>
                <p class="text-gray-400 mb-6 leading-relaxed">
                    Detailní přehledy a statistiky pro lepší pochopení potřeb vašich pacientů. Data, která vám pomohou růst.
                </p>
                <button class="text-teal-400 hover:text-teal-300 flex items-center group">
                    Přečíst si více 
                    <svg class="w-5 h-5 ml-2 transform group-hover:translate-x-2 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div>
</section>


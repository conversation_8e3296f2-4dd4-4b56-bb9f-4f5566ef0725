@layer components {
  .calendar-container {
    @apply bg-white dark:bg-gray-800 rounded-lg shadow-lg p-4;
  }

  .calendar-header {
    @apply flex items-center justify-between mb-4;
  }

  .calendar-navigation {
    @apply flex items-center space-x-2;
  }

  .calendar-nav-button {
    @apply h-8 w-8 flex items-center justify-center rounded-lg border border-teal-500/20 
           text-teal-500 hover:bg-teal-500/10 transition-colors;
  }

  .calendar-today-button {
    @apply px-3 h-8 flex items-center justify-center rounded-lg border border-teal-500/20 
           text-teal-500 hover:bg-teal-500/10 transition-colors text-sm font-medium;
  }

  .calendar-view-button {
    @apply px-3 h-8 flex items-center justify-center rounded-lg border transition-colors text-sm font-medium;
  }

  .calendar-view-button-active {
    @apply bg-teal-600 text-white border-transparent hover:bg-teal-700;
  }

  .calendar-view-button-inactive {
    @apply border-teal-500/20 text-teal-500 hover:bg-teal-500/10;
  }

  .calendar-grid {
    @apply rounded-lg border border-teal-500/20 bg-white dark:bg-gray-800;
  }

  .calendar-weekday {
    @apply py-2 text-center text-sm font-medium text-gray-500 dark:text-gray-400;
  }

  .calendar-day {
    @apply min-h-[100px] p-2 text-sm relative hover:bg-teal-50 dark:hover:bg-teal-900/10 transition-colors;
  }

  .calendar-day-number {
    @apply absolute top-2 right-2;
  }

  .calendar-event {
    @apply mt-6 truncate rounded px-2 py-1 text-xs text-white;
  }

  .calendar-event-kontrola {
    @apply bg-teal-500;
  }

  .calendar-event-osetreni {
    @apply bg-blue-500;
  }

  .calendar-event-konzultace {
    @apply bg-indigo-500;
  }

  .calendar-event-operace {
    @apply bg-pink-500;
  }
}


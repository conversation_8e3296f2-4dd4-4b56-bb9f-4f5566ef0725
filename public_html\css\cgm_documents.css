/**
 * <PERSON><PERSON> razí<PERSON>ka - Styly
 * CSS styly pro modul elektronické dokumentace
 */

/* Document Cards */
.document-card {
    transition: all 0.2s ease;
    border: 1px solid #e5e7eb;
    background: #ffffff;
}

.document-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: #3b82f6;
    transform: translateY(-2px);
}

/* Timestamp Badge */
.timestamp-badge {
    background: linear-gradient(135deg, #10b981, #059669);
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Timestamp Cards */
.timestamp-card {
    border-left: 4px solid #10b981;
    background: linear-gradient(135deg, #f0fdf4, #ecfdf5);
    transition: all 0.2s ease;
}

.timestamp-card:hover {
    background: linear-gradient(135deg, #ecfdf5, #d1fae5);
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.2);
}

/* Document Content */
.document-content {
    background: #fafafa;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    font-family: 'Georgia', serif;
    line-height: 1.6;
}

/* Hash Display */
.hash-display {
    font-family: 'Courier New', monospace;
    background: #f3f4f6;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    word-break: break-all;
    font-size: 11px;
    color: #374151;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
}

.modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.2s ease;
}

.modal-content {
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    max-width: 90vw;
    max-height: 90vh;
    overflow-y: auto;
    animation: slideIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { 
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to { 
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Form Styles */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.5rem;
}

.form-input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.form-input:focus {
    outline: none;
    ring: 2px;
    ring-color: #3b82f6;
    border-color: transparent;
}

.form-textarea {
    resize: vertical;
    min-height: 120px;
}

.form-file {
    padding: 0.5rem;
}

/* Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: 8px;
    transition: all 0.2s ease;
    cursor: pointer;
    border: none;
    text-decoration: none;
}

.btn-primary {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.btn-success {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.btn-success:hover {
    background: linear-gradient(135deg, #059669, #047857);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
}

.btn-secondary {
    background: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
}

.btn-secondary:hover {
    background: #e5e7eb;
    border-color: #9ca3af;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
}

/* Alert Styles */
.alert {
    padding: 1rem 1.5rem;
    border-radius: 8px;
    margin-bottom: 1.5rem;
    border: 1px solid;
    position: relative;
}

.alert-success {
    background-color: #f0fdf4;
    border-color: #bbf7d0;
    color: #166534;
}

.alert-error {
    background-color: #fef2f2;
    border-color: #fecaca;
    color: #dc2626;
}

.alert-warning {
    background-color: #fffbeb;
    border-color: #fed7aa;
    color: #d97706;
}

.alert-close {
    position: absolute;
    top: 0.5rem;
    right: 1rem;
    background: none;
    border: none;
    font-size: 1.25rem;
    cursor: pointer;
    color: inherit;
    opacity: 0.7;
}

.alert-close:hover {
    opacity: 1;
}

/* Loading States */
.loading {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.loading-spinner {
    width: 1rem;
    height: 1rem;
    border: 2px solid #e5e7eb;
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Status Indicators */
.status-valid {
    color: #059669;
    background: #ecfdf5;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-invalid {
    color: #dc2626;
    background: #fef2f2;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-expired {
    color: #d97706;
    background: #fffbeb;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
}

/* File Upload Area */
.file-upload-area {
    border: 2px dashed #d1d5db;
    border-radius: 8px;
    padding: 2rem;
    text-align: center;
    transition: all 0.2s ease;
    cursor: pointer;
}

.file-upload-area:hover {
    border-color: #3b82f6;
    background: #f8fafc;
}

.file-upload-area.dragover {
    border-color: #3b82f6;
    background: #eff6ff;
}

/* Responsive Design */
@media (max-width: 768px) {
    .document-card {
        margin-bottom: 1rem;
    }
    
    .modal-content {
        margin: 1rem;
        max-width: calc(100vw - 2rem);
    }
    
    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.75rem;
    }
}

/* Print Styles */
@media print {
    .modal,
    .btn,
    .alert {
        display: none !important;
    }
    
    .document-content {
        background: white !important;
        border: none !important;
        box-shadow: none !important;
    }
    
    .timestamp-card {
        background: white !important;
        border: 1px solid #000 !important;
        page-break-inside: avoid;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .document-card {
        background: #1f2937;
        border-color: #374151;
        color: #f9fafb;
    }
    
    .document-content {
        background: #111827;
        border-color: #374151;
        color: #f9fafb;
    }
    
    .hash-display {
        background: #374151;
        border-color: #4b5563;
        color: #d1d5db;
    }
}

/* Styly pro spr<PERSON>vu SMS na bázi pay as you go */

.sms-management-section {
  margin: 20px 0;
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.sms-management-section h2 {
  margin-top: 0;
  color: #333;
  border-bottom: 1px solid #ddd;
  padding-bottom: 10px;
  margin-bottom: 20px;
}

.sms-info-panel {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 30px;
}

.sms-credit-box,
.sms-recharge-box {
  flex: 1;
  min-width: 250px;
  padding: 15px;
  background-color: #fff;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.credit-amount {
  font-size: 24px;
  font-weight: bold;
  color: #2c7be5;
  margin: 10px 0;
}

.sms-price {
  font-weight: bold;
  color: #333;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.btn-primary {
  background-color: #2c7be5;
  color: white;
}

.btn-primary:hover {
  background-color: #1a68d1;
}

.period-selector {
  margin-bottom: 15px;
  display: flex;
  gap: 10px;
}

.period-button {
  padding: 6px 12px;
  background-color: #f0f0f0;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.period-button:hover {
  background-color: #e0e0e0;
}

.period-button.active {
  background-color: #2c7be5;
  color: white;
  border-color: #2c7be5;
}

.sms-history-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;
}

.sms-history-table th,
.sms-history-table td {
  padding: 10px;
  text-align: left;
  border-bottom: 1px solid #ddd;
}

.sms-history-table th {
  background-color: #f5f5f5;
  font-weight: 600;
}

.sms-history-table tr:hover {
  background-color: #f9f9f9;
}

/* Notifikace */
#notification-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
}

.notification {
  padding: 12px 20px;
  margin-bottom: 10px;
  border-radius: 4px;
  color: white;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  animation: slide-in 0.3s ease-out;
}

.notification.success {
  background-color: #28a745;
}

.notification.error {
  background-color: #dc3545;
}

.notification.fade-out {
  animation: fade-out 0.5s ease-out forwards;
}

@keyframes slide-in {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fade-out {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

/* Responzivní design */
@media (max-width: 768px) {
  .sms-info-panel {
    flex-direction: column;
  }

  .period-selector {
    flex-wrap: wrap;
  }
}


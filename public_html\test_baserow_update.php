<?php
// <PERSON><PERSON>ž<PERSON> jako test_patient_update.php

require_once 'config.php';
require_once 'baserow_functions.php';
require_once 'error_log.php';

// Enable error reporting for debugging
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Initialize session
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Get patient ID from URL
$patientId = isset($_GET['id']) ? intval($_GET['id']) : 0;
$field = isset($_GET['field']) ? $_GET['field'] : 'brouseni';
$value = isset($_GET['value']) ? $_GET['value'] : 'test-' . time();

echo "<h1>Test aktualizace pacienta</h1>";
echo "<p>Testování aktualizace pro pacienta ID: $patientId, pole: $field, hodnota: $value</p>";

if ($patientId <= 0) {
    echo "<p style='color:red;'>Chyba: Neplatné ID pacienta. Zadejte platné ID v URL parametru 'id'.</p>";
    exit;
}

try {
    // Get Baserow config
    $config = getBaserowConfig();
    echo "<p>Baserow konfigurace načtena úspěšně</p>";
    echo "<ul>";
    echo "<li>Database ID: " . $config['baserow_database_id'] . "</li>";
    echo "<li>Table ID: " . $config['baserow_table_id'] . "</li>";
    echo "<li>API Token délka: " . strlen($config['baserow_api_token']) . "</li>";
    echo "</ul>";
    
    // Field mapping
    $fieldMapping = [
        'examination_date' => 'Datum_prohlidky',
        'akutni' => 'Akutní',
        'brouseni' => 'Broušení',
        'endo' => 'Endo',
        'extrakce_chirurgie' => 'Extrakce, chirurgie',
        'postendo' => 'Postendo',
        'predni_protetiky' => 'Předání protetiky',
        'sanace_dite' => 'Sanace - dítě',
        'sanace_dospely' => 'Sanace - dospělý',
        'snimatelna_protetika' => 'Snímatelná protetika - otisky'
    ];
    
    if (!isset($fieldMapping[$field])) {
        echo "<p style='color:red;'>Chyba: Neplatné pole: $field</p>";
        exit;
    }
    
    $baserowField = $fieldMapping[$field];
    echo "<p>Mapované pole: $field -> $baserowField</p>";
    
    // Prepare update data
    $updateData = [];
    $updateData[$baserowField] = $value;
    
    echo "<p>Data pro aktualizaci: " . json_encode($updateData) . "</p>";
    
    // Make direct API request
    echo "<p>Odesílání API požadavku...</p>";
    $response = baserowRequest('PATCH', $patientId . '/?user_field_names=true', $updateData);
    
    echo "<p style='color:green;'>API požadavek úspěšný!</p>";
    echo "<pre>" . json_encode($response, JSON_PRETTY_PRINT) . "</pre>";
    
    echo "<p style='color:green;font-weight:bold;'>Test dokončen úspěšně!</p>";
    
} catch (Exception $e) {
    echo "<p style='color:red;font-weight:bold;'>Chyba: " . $e->getMessage() . "</p>";
    echo "<p>Stack trace:</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
<?php
require_once 'config.php';
require_once 'error_log.php';
require_once 'includes/credit_system.php';

// Ensure session is started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Uživatel nen<PERSON>']);
    exit;
}

$user_id = $_SESSION['user_id'];

// Handle different actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = isset($_POST['action']) ? $_POST['action'] : '';
    
    switch ($action) {
        case 'get_credit':
            // Get user's credit balance
            try {
                $balance = getUserCreditBalance($user_id);
                echo json_encode([
                    'success' => true,
                    'credit' => $balance
                ]);
            } catch (Exception $e) {
                writeErrorLog('Error getting credit balance', [
                    'user_id' => $user_id,
                    'error' => $e->getMessage()
                ]);
                echo json_encode([
                    'success' => false,
                    'message' => 'Nepodařilo se načíst zůstatek kreditu: ' . $e->getMessage()
                ]);
            }
            break;
            
        case 'recharge_credit':
            // Recharge user's credit
            try {
                $package_id = isset($_POST['package_id']) ? intval($_POST['package_id']) : 0;
                $credits = isset($_POST['credits']) ? floatval($_POST['credits']) : 0;
                $price = isset($_POST['price']) ? floatval($_POST['price']) : 0;
                $payment_method = isset($_POST['payment_method']) ? $_POST['payment_method'] : 'card';
                
                if ($package_id <= 0 || $credits <= 0 || $price <= 0) {
                    throw new Exception('Neplatné parametry pro dobití kreditu');
                }
                
                // In a real implementation, you would process the payment here
                // For now, we'll simulate a successful payment
                
                // Add credits to user's account
                $description = "Nákup balíčku kreditů: $credits kreditů";
                $reference_id = 'PAYMENT_' . time();
                
                addUserCredits($user_id, $credits, 'purchase', $description, $reference_id);
                
                // Get updated balance
                $new_balance = getUserCreditBalance($user_id);
                
                echo json_encode([
                    'success' => true,
                    'message' => "Úspěšně jste dobili $credits kreditů",
                    'new_balance' => $new_balance
                ]);
            } catch (Exception $e) {
                writeErrorLog('Error recharging credit', [
                    'user_id' => $user_id,
                    'package_id' => $_POST['package_id'] ?? 0,
                    'credits' => $_POST['credits'] ?? 0,
                    'error' => $e->getMessage()
                ]);
                echo json_encode([
                    'success' => false,
                    'message' => 'Nepodařilo se dobít kredit: ' . $e->getMessage()
                ]);
            }
            break;
            
        case 'get_transactions':
            // Get user's transaction history
            try {
                $limit = isset($_POST['limit']) ? intval($_POST['limit']) : 10;
                $offset = isset($_POST['offset']) ? intval($_POST['offset']) : 0;
                
                $transactions = getUserCreditHistory($user_id, $limit, $offset);
                
                echo json_encode([
                    'success' => true,
                    'transactions' => $transactions
                ]);
            } catch (Exception $e) {
                writeErrorLog('Error getting transaction history', [
                    'user_id' => $user_id,
                    'error' => $e->getMessage()
                ]);
                echo json_encode([
                    'success' => false,
                    'message' => 'Nepodařilo se načíst historii transakcí: ' . $e->getMessage()
                ]);
            }
            break;
            
        case 'get_sms_price':
            // Get SMS price for user's plan
            try {
                $pricing = getSmsPrice($user_id);
                
                echo json_encode([
                    'success' => true,
                    'price_per_sms' => $pricing['price_per_sms'],
                    'free_sms_limit' => $pricing['free_sms_limit']
                ]);
            } catch (Exception $e) {
                writeErrorLog('Error getting SMS price', [
                    'user_id' => $user_id,
                    'error' => $e->getMessage()
                ]);
                echo json_encode([
                    'success' => false,
                    'message' => 'Nepodařilo se načíst cenu SMS: ' . $e->getMessage()
                ]);
            }
            break;
            
        case 'calculate_sms_cost':
            // Calculate cost for sending SMS
            try {
                $sms_count = isset($_POST['sms_count']) ? intval($_POST['sms_count']) : 0;
                
                if ($sms_count <= 0) {
                    throw new Exception('Neplatný počet SMS');
                }
                
                $cost_info = calculateSmsCost($user_id, $sms_count);
                
                echo json_encode([
                    'success' => true,
                    'cost_info' => $cost_info
                ]);
            } catch (Exception $e) {
                writeErrorLog('Error calculating SMS cost', [
                    'user_id' => $user_id,
                    'sms_count' => $_POST['sms_count'] ?? 0,
                    'error' => $e->getMessage()
                ]);
                echo json_encode([
                    'success' => false,
                    'message' => 'Nepodařilo se vypočítat cenu SMS: ' . $e->getMessage()
                ]);
            }
            break;
            
        default:
            echo json_encode([
                'success' => false,
                'message' => 'Neznámá akce'
            ]);
            break;
    }
} else {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Neplatný požadavek']);
}
?>


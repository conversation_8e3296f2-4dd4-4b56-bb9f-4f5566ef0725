<?php
require_once 'config.php';
require_once 'api_functions.php';
require_once 'error_log.php';
require_once __DIR__ . '/payment_modules/includes/credit_system.php'; // Add our new credit system

// Ensure we have a database connection function
if (!function_exists('getDatabaseConnection') && function_exists('getDbConnection')) {
    function getDatabaseConnection() {
        return getDbConnection();
    }
}

// Ensure session is started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Kontrola přihlášení - odkomentujte v produkci
// if (!isset($_SESSION['user_id'])) {
//     header('Location: login.php');
//     exit;
// }

// Nastavení aktuální stránky pro sidebar
$currentPage = "platby";

// Get user's current subscription plan - použijeme statickou hodnotu, pokud funkce není dostupná
try {
    $user_id = $_SESSION['user_id'] ?? 1;
    $current_plan = getUserSubscriptionPlan($user_id) ?? 'Pokročilý';
} catch (Exception $e) {
    // Pokud funkce selže, použijeme výchozí hodnotu
    $current_plan = 'Pokročilý';
    writeErrorLog("Error getting subscription plan: " . $e->getMessage());
}

// Get usage and limit values
try {
    $user_id = $_SESSION['user_id'] ?? 1;
    $limit = getUserSubscriptionLimit($user_id);
    
    // Get metrics to extract minutes used, just like in dashboard.php
    $apiConfig = getCurrentUserApiKey();
    if ($apiConfig) {
        $dateRange = '30 DAY'; // Default range
        $analytics = getAnalytics($dateRange, $apiConfig['assistant_id']);
        $metrics = getMetrics($analytics);
        
        // Extract minutes used from metrics
        $usage = 0;
        foreach ($metrics as $metric) {
            if ($metric['label'] === 'Spotřeba minut') {
                $usage = intval($metric['value']);
                break;
            }
        }
    } else {
        $usage = 0; // Default if API config not available
    }
} catch (Exception $e) {
    // If function fails, use default values
    $usage = 0;
    $limit = 1000;
    writeErrorLog("Error getting subscription data: " . $e->getMessage());
}

// Get SMS pricing for user's plan
try {
    $pricing = getSmsPrice($user_id);
    $pricePerSms = $pricing['price_per_sms'];
    $freeLimit = $pricing['free_sms_limit'];
} catch (Exception $e) {
    $pricePerSms = 0.75; // Default price
    $freeLimit = 0; // Default free limit
    writeErrorLog("Error getting SMS pricing: " . $e->getMessage());
}

// Get user's credit balance
try {
    $creditBalance = getUserCreditBalance($user_id);
} catch (Exception $e) {
    $creditBalance = 0;
    writeErrorLog("Error getting credit balance: " . $e->getMessage());
}

// Get credit packages
try {
    $creditPackages = getCreditPackages();
} catch (Exception $e) {
    $creditPackages = [];
    writeErrorLog("Error getting credit packages: " . $e->getMessage());
}

// Get user's transaction history
try {
    $transactions = getUserCreditHistory($user_id, 5);
} catch (Exception $e) {
    $transactions = [];
    writeErrorLog("Error getting transaction history: " . $e->getMessage());
}

// Get SMS usage statistics
$smsUsage = [];
$totalSmsSent = 0;
try {
    // Create a database connection that we'll reuse throughout the script
    if (!isset($db) || !$db->ping()) {
        if (function_exists('getDbConnection')) {
            $db = getDbConnection();
        } else if (function_exists('getDatabaseConnection')) {
            $db = getDatabaseConnection();
        } else {
            // Fallback to direct connection if needed
            $db = new mysqli(DB_HOST, DB_USER, DB_PASSWORD, DB_NAME);
            if ($db->connect_error) {
                throw new Exception("Database connection failed: " . $db->connect_error);
            }
        }
    }
    
    // Get total SMS sent from sms_campaigns table
    $stmt = $db->prepare("
        SELECT SUM(total_messages) as total_sent 
        FROM sms_campaigns 
        WHERE user_id = ?
    ");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();
    $totalSmsSent = $row['total_sent'] ?: 0;
    $stmt->close();
    
    // Get recent SMS campaigns for display in the dashboard
    $stmt = $db->prepare("
        SELECT campaign_name, sent_date, total_messages 
        FROM sms_campaigns 
        WHERE user_id = ? 
        ORDER BY sent_date DESC 
        LIMIT 3
    ");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    while ($row = $result->fetch_assoc()) {
        $smsUsage[] = [
            'campaign_name' => $row['campaign_name'],
            'date' => $row['sent_date'],
            'total_sent' => $row['total_messages']
        ];
    }
    $stmt->close();
    // DO NOT close the connection here
} catch (Exception $e) {
    writeErrorLog("Error getting SMS usage: " . $e->getMessage());
    // If there's an error, we'll use empty data
    $totalSmsSent = 0;
    $smsUsage = [];
}

// Define SMS limit based on the subscription plan
$smsLimit = $freeLimit; // Use the limit from the pricing table
if ($smsLimit === 0) {
    // If no limit is defined, use default values
    if ($current_plan === 'Základní') {
        $smsLimit = 1000;
    } else if ($current_plan === 'Pokročilý') {
        $smsLimit = 2000;
    } else if ($current_plan === 'Prémiový' || $current_plan === 'Enterprise') {
        $smsLimit = 5000;
    } else {
        $smsLimit = 2000; // Default value
    }
}

// Calculate SMS usage percentage
$smsPercentage = $smsLimit > 0 ? min(100, ($totalSmsSent / $smsLimit) * 100) : 0;

// Calculate remaining free SMS
$remainingFreeSms = max(0, $smsLimit - $totalSmsSent);

// Get monthly SMS usage
$firstDayOfMonth = date('Y-m-01 00:00:00');
try {
    // Reuse the existing database connection
    if (!isset($db) || !$db->ping()) {
        if (function_exists('getDbConnection')) {
            $db = getDbConnection();
        } else if (function_exists('getDatabaseConnection')) {
            $db = getDatabaseConnection();
        } else {
            // Fallback to direct connection if needed
            $db = new mysqli(DB_HOST, DB_USER, DB_PASSWORD, DB_NAME);
            if ($db->connect_error) {
                throw new Exception("Database connection failed: " . $db->connect_error);
            }
        }
    }
    
    $stmt = $db->prepare("
        SELECT SUM(total_messages) as monthly_sent 
        FROM sms_campaigns 
        WHERE user_id = ? AND sent_date >= ?
    ");
    $stmt->bind_param("is", $user_id, $firstDayOfMonth);
    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();
    $monthlySmsSent = $row['monthly_sent'] ?: 0;
    $stmt->close();
    // DO NOT close the connection here
} catch (Exception $e) {
    $monthlySmsSent = 0;
    writeErrorLog("Error getting monthly SMS usage: " . $e->getMessage());
}

$planUrls = [
    'Základní' => [
        'monthly' => 'https://buy.stripe.com/9AQ3eYg8Q4MrgCI3ce',
        'yearly' => 'https://buy.stripe.com/4gw5n67Ck6UzaekeUX'
    ],
    'Pokročilý' => [
        'monthly' => 'https://buy.stripe.com/fZe3eY3m45Qv2LS8wA',
        'yearly' => 'https://buy.stripe.com/aEUbLu0a45Qv8c85km'
    ],
    'Enterprise' => [
        'contact' => 'https://tknurture.com/inteligentni-voicebot-pro-zubare-dentibot'
    ]
];

$plans = [
    'Základní' => [
        'price' => ['monthly' => '3000 Kč', 'yearly' => '30000 Kč'],
        'features' => ['Až 500 minut hovorů měsíčně', 'E-mailová podpora', 'Základní funkce Dentibota']
    ],
    'Pokročilý' => [
        'price' => ['monthly' => '5000 Kč', 'yearly' => '54000 Kč'],
        'features' => ['Až 1000 minut hovorů měsíčně', 'Podpora v rámci Discord skupiny', 'Pokročilé funkce Dentibota', 'Prioritní zpracování']
    ],
    'Enterprise' => [
        'price' => ['contact' => 'Individuální cena'],
        'features' => ['Neomezené minuty hovorů', 'Dedikovaná podpora', 'Vlastní integrace', 'SLA']
    ]
];

// Default to monthly
$interval = isset($_GET['interval']) && $_GET['interval'] === 'yearly' ? 'yearly' : 'monthly';
?>

<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Platby a předplatné - Dentibot</title>
    
    <!-- Základní styly -->
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #121826;
            color: #ffffff;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .card {
            background-color: #1e2533;
            border: 1px solid #2d3748;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
        }
        
        .tabs {
            display: flex;
            border-bottom: 1px solid #2d3748;
            margin-bottom: 20px;
            overflow-x: auto;
        }
        
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            white-space: nowrap;
        }
        
        .tab.active {
            border-bottom-color: #4fd1c5;
            color: #4fd1c5;
        }
        
        .button {
            background-color: #4fd1c5;
            color: #121826;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
        }
        
        .button-outline {
            background-color: transparent;
            color: #4fd1c5;
            border: 1px solid #4fd1c5;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background-color: #2d3748;
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-value {
            height: 100%;
            background-color: #4fd1c5;
            transition: width 0.3s ease;
        }
        
        /* Styly pro správu SMS na bázi pay as you go */
        .sms-management-section {
            margin: 20px 0;
            padding: 20px;
            background-color: #1e2533;
            border: 1px solid #2d3748;
            border-radius: 8px;
        }

        .sms-management-section h2 {
            margin-top: 0;
            color: #ffffff;
            border-bottom: 1px solid #2d3748;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }

        .sms-info-panel {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 30px;
        }

        .sms-credit-box, .sms-recharge-box {
            flex: 1;
            min-width: 250px;
            padding: 15px;
            background-color: #1a1f2e;
            border-radius: 6px;
            border: 1px solid #2d3748;
        }

        .sms-credit-box h3, .sms-recharge-box h3 {
            color: #ffffff;
            margin-top: 0;
            margin-bottom: 10px;
        }

        .credit-amount {
            font-size: 24px;
            font-weight: bold;
            color: #4fd1c5;
            margin: 10px 0;
        }

        .sms-price {
            font-weight: bold;
            color: #ffffff;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #ffffff;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #2d3748;
            border-radius: 4px;
            font-size: 14px;
            background-color: #1a1f2e;
            color: #ffffff;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
            transition: background-color 0.2s;
        }

        .btn-primary {
            background-color: #4fd1c5;
            color: #121826;
        }

        .btn-primary:hover {
            background-color: #38b2ac;
        }

        .period-selector {
            margin-bottom: 15px;
            display: flex;
            gap: 10px;
        }

        .period-button {
            padding: 6px 12px;
            background-color: #1a1f2e;
            border: 1px solid #2d3748;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s;
            color: #ffffff;
        }

        .period-button:hover {
            background-color: #2d3748;
        }

        .period-button.active {
            background-color: #4fd1c5;
            color: #121826;
            border-color: #4fd1c5;
        }

        .sms-history-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
            color: #ffffff;
        }

        .sms-history-table th, .sms-history-table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #2d3748;
        }

        .sms-history-table th {
            background-color: #1a1f2e;
            font-weight: 600;
        }

        .sms-history-table tr:hover {
            background-color: #1a1f2e;
        }

        .sms-usage-section h3 {
            color: #ffffff;
            margin-top: 20px;
            margin-bottom: 15px;
        }

        /* Notifikace */
        #notification-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }

        .notification {
            padding: 12px 20px;
            margin-bottom: 10px;
            border-radius: 4px;
            color: white;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            animation: slide-in 0.3s ease-out;
        }

        .notification.success {
            background-color: #10b981;
        }

        .notification.error {
            background-color: #ef4444;
        }

        .notification.info {
            background-color: #3b82f6;
        }

        .notification.fade-out {
            animation: fade-out 0.5s ease-out forwards;
        }

        @keyframes slide-in {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        @keyframes fade-out {
            from { opacity: 1; }
            to { opacity: 0; }
        }

        /* Layout pro obsah */
        .main-container {
            display: flex;
            min-height: 100vh;
        }
        
        .content-wrapper {
            flex: 1;
        }
        
        /* Responzivní design */
        @media (max-width: 768px) {
            .main-container {
                flex-direction: column;
            }
            
            .grid {
                grid-template-columns: 1fr;
            }
            
            .sms-info-panel {
                flex-direction: column;
            }
            
            .period-selector {
                flex-wrap: wrap;
            }
        }
        
        /* Credit packages styling */
        .credit-packages {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .credit-package {
            background-color: #1a1f2e;
            border: 1px solid #2d3748;
            border-radius: 6px;
            padding: 15px;
            text-align: center;
            transition: transform 0.2s, border-color 0.2s;
        }
        
        .credit-package:hover {
            transform: translateY(-5px);
            border-color: #4fd1c5;
        }
        
        .credit-package h3 {
            margin-top: 0;
            color: #ffffff;
        }
        
        .credit-amount {
            font-size: 24px;
            font-weight: bold;
            color: #4fd1c5;
            margin: 10px 0;
        }
        
        .credit-price {
            font-size: 18px;
            color: #ffffff;
            margin-bottom: 15px;
        }
        
        .transaction-history {
            margin-top: 20px;
        }
        
        .transaction-type {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .transaction-type.purchase {
            background-color: rgba(79, 209, 197, 0.2);
            color: #4fd1c5;
        }
        
        .transaction-type.usage {
            background-color: rgba(239, 68, 68, 0.2);
            color: #ef4444;
        }
        
        .transaction-type.bonus {
            background-color: rgba(59, 130, 246, 0.2);
            color: #3b82f6;
        }
        
        .transaction-type.refund {
            background-color: rgba(245, 158, 11, 0.2);
            color: #f59e0b;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Sidebar - vložen bez úprav -->
        <?php include 'sidebar.php'; ?>
        
        <!-- Hlavní obsah -->
        <div class="content-wrapper">
            <div class="container">
                <div class="header">
                    <h1>Platby a předplatné</h1>
                    <button class="button-outline" onclick="showHistoryTab()">Historie plateb</button>
                </div>
                
                <div class="tabs" id="payment-tabs">
                    <div class="tab active" data-tab="overview">Přehled</div>
                    <div class="tab" data-tab="subscription">Předplatné</div>
                    <div class="tab" data-tab="sms-credits">SMS Kredity</div>
                    <div class="tab" data-tab="payment-methods">Platební metody</div>
                    <div class="tab" data-tab="history">Historie</div>
                </div>
                
                <!-- Přehled - výchozí zobrazená záložka -->
                <div id="tab-overview" class="tab-content">
                    <div class="grid">
                        <div class="card">
                            <h3>Aktuální tarif</h3>
                            <p class="plan-price"><?php echo htmlspecialchars(ucfirst($current_plan)); ?></p>
                            <p style="color: #4fd1c5;">5000 Kč/měsíc</p>
                        </div>
                        
                        <div class="card">
                            <h3>Minuty hovorů</h3>
                            <div class="progress-bar">
                                <div class="progress-value" style="width: <?php echo min(100, ($usage / $limit) * 100); ?>%;"></div>
                            </div>
                            <p><?php echo $usage; ?> z <?php echo $limit; ?> minut</p>
                        </div>
                        
                        <div class="card">
                            <h3>SMS kredity</h3>
                            <p class="plan-price"><?php echo number_format($creditBalance, 2); ?> Kč</p>
                            <p style="color: #4fd1c5;">Aktuální zůstatek</p>
                            <button class="button-outline" style="margin-top: 10px;" onclick="showTab('sms-credits')">Dobít kredity</button>
                        </div>
                        
                        <div class="card">
                            <h3>Měsíční SMS zdarma</h3>
                            <div class="progress-bar">
                                <div class="progress-value" style="width: <?php echo $smsPercentage; ?>%;"></div>
                            </div>
                            <p><?php echo $monthlySmsSent; ?> z <?php echo $smsLimit; ?> SMS</p>
                            <p style="color: #4fd1c5;">Zbývá: <?php echo $remainingFreeSms; ?> SMS zdarma</p>
                        </div>
                    </div>
                    
                    <div class="card">
                        <h2>Historie využití SMS</h2>
                        <table class="sms-history-table">
                            <thead>
                                <tr>
                                    <th>Datum</th>
                                    <th>Kampaň</th>
                                    <th>Počet SMS</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($smsUsage)): ?>
                                    <tr>
                                        <td colspan="3" style="text-align: center; padding: 20px;">Žádná historie využití SMS</td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($smsUsage as $usage): ?>
                                        <tr>
                                            <td><?php echo date('d.m.Y H:i', strtotime($usage['date'])); ?></td>
                                            <td><?php echo htmlspecialchars($usage['campaign_name']); ?></td>
                                            <td><?php echo $usage['total_sent']; ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <h2>Změna tarifu</h2>
                    <div class="grid">
                        <div class="plan-card card">
                            <h3>Základní tarif</h3>
                            <div class="plan-price">3000 Kč <span style="font-size: 14px; font-weight: normal;">/měsíc</span></div>
                            <ul class="plan-features">
                                <li>500 minut hovorů</li>
                                <li>1000 SMS zpráv zdarma/měsíc</li>
                                <li>Základní statistiky</li>
                                <li>E-mailová podpora</li>
                            </ul>
                            <button class="button-outline" onclick="selectPlan('basic')">Vybrat tarif</button>
                        </div>
                        
                        <div class="plan-card card popular" style="border-color: #4fd1c5;">
                            <h3>Pokročilý tarif</h3>
                            <div class="plan-price">5000 Kč <span style="font-size: 14px; font-weight: normal;">/měsíc</span></div>
                            <ul class="plan-features">
                                <li>1000 minut hovorů</li>
                                <li>2000 SMS zpráv zdarma/měsíc</li>
                                <li>Pokročilé statistiky</li>
                                <li>Prioritní podpora</li>
                                <li>Vlastní nastavení</li>
                            </ul>
                            <button class="button-outline" disabled>Aktuální tarif</button>
                        </div>
                        
                        <div class="plan-card card">
                            <h3>Prémiový tarif</h3>
                            <div class="plan-price">10000 Kč <span style="font-size: 14px; font-weight: normal;">/měsíc</span></div>
                            <ul class="plan-features">
                                <li>Neomezené minuty</li>
                                <li>5000 SMS zpráv zdarma/měsíc</li>
                                <li>Kompletní analytika</li>
                                <li>24/7 podpora</li>
                                <li>Vlastní integrace</li>
                            </ul>
                            <button class="button-outline" onclick="selectPlan('premium')">Vybrat tarif</button>
                        </div>
                    </div>
                </div>
                
                <!-- Předplatné -->
                <div id="tab-subscription" class="tab-content" style="display: none;">
                    <div class="card">
                        <h2>Správa předplatného</h2>
                        <p>Zde můžete spravovat své předplatné, změnit tarif nebo zrušit předplatné.</p>
                        
                        <div class="card" style="margin-top: 20px;">
                            <h3>Aktuální předplatné</h3>
                            <p><strong>Tarif:</strong> <?php echo htmlspecialchars(ucfirst($current_plan)); ?></p>
                            <p><strong>Cena:</strong> 5000 Kč/měsíc</p>
                            <p><strong>Další platba:</strong> 15.03.2024</p>
                            <p><strong>Stav:</strong> <span style="color: #4fd1c5;">Aktivní</span></p>
                            
                            <div style="margin-top: 20px;">
                                <button class="button-outline" onclick="showTab('overview')">Změnit tarif</button>
                                <button class="button-outline" style="margin-left: 10px; border-color: #e53e3e; color: #e53e3e;" onclick="cancelSubscription()">Zrušit předplatné</button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- SMS Kredity -->
                <div id="tab-sms-credits" class="tab-content" style="display: none;">
                    <div class="card">
                        <h2>Správa SMS kreditů</h2>
                        <p>Zde můžete spravovat své SMS kredity, dobíjet je a sledovat jejich využití.</p>
                        
                        <div class="grid" style="margin-top: 20px;">
                            <div class="card">
                                <h3>Aktuální zůstatek</h3>
                                <div class="credit-amount"><?php echo number_format($creditBalance, 2); ?> Kč</div>
                                <p>Cena za 1 SMS: <span class="sms-price"><?php echo number_format($pricePerSms, 2); ?> Kč</span></p>
                                <p>Počet SMS, které můžete odeslat: <strong><?php echo floor($creditBalance / $pricePerSms); ?></strong></p>
                            </div>
                            
                            <div class="card">
                                <h3>Měsíční SMS zdarma</h3>
                                <div class="progress-bar">
                                    <div class="progress-value" style="width: <?php echo $smsPercentage; ?>%;"></div>
                                </div>
                                <p><?php echo $monthlySmsSent; ?> z <?php echo $smsLimit; ?> SMS</p>
                                <p>Zbývá: <strong><?php echo $remainingFreeSms; ?></strong> SMS zdarma v tomto měsíci</p>
                            </div>
                        </div>
                        
                        <div class="card" style="margin-top: 20px;">
                            <h3>Dobít kredity</h3>
                            <form id="recharge-form" onsubmit="return rechargeCredit()">
                                <div class="form-group">
                                    <label for="recharge-amount">Vyberte balíček:</label>
                                    <div class="credit-packages">
                                        <?php foreach ($creditPackages as $package): ?>
                                        <div class="credit-package" onclick="selectPackage(<?php echo $package['id']; ?>, <?php echo $package['credits']; ?>, <?php echo $package['price']; ?>)" data-id="<?php echo $package['id']; ?>">
                                            <h3><?php echo htmlspecialchars($package['name']); ?></h3>
                                            <div class="credit-amount"><?php echo number_format($package['credits']); ?> kreditů</div>
                                            <div class="credit-price"><?php echo number_format($package['price'], 2); ?> Kč</div>
                                            <button type="button" class="button-outline">Vybrat</button>
                                        </div>
                                        <?php endforeach; ?>
                                        
                                        <?php if (empty($creditPackages)): ?>
                                        <div class="credit-package">
                                            <h3>Malý balíček</h3>
                                            <div class="credit-amount">100 kreditů</div>
                                            <div class="credit-price">75 Kč</div>
                                            <button type="button" class="button-outline" onclick="selectPackage(1, 100, 75)">Vybrat</button>
                                        </div>
                                        
                                        <div class="credit-package">
                                            <h3>Střední balíček</h3>
                                            <div class="credit-amount">500 kreditů</div>
                                            <div class="credit-price">350 Kč</div>
                                            <button type="button" class="button-outline" onclick="selectPackage(2, 500, 350)">Vybrat</button>
                                        </div>
                                        
                                        <div class="credit-package">
                                            <h3>Velký balíček</h3>
                                            <div class="credit-amount">1000 kreditů</div>
                                            <div class="credit-price">650 Kč</div>
                                            <button type="button" class="button-outline" onclick="selectPackage(3, 1000, 650)">Vybrat</button>
                                        </div>
                                        
                                        <div class="credit-package">
                                            <h3>Mega balíček</h3>
                                            <div class="credit-amount">5000 kreditů</div>
                                            <div class="credit-price">3000 Kč</div>
                                            <button type="button" class="button-outline" onclick="selectPackage(4, 5000, 3000)">Vybrat</button>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                
                                <div class="form-group" style="margin-top: 20px;">
                                    <label for="selected-package">Vybraný balíček:</label>
                                    <input type="text" id="selected-package" readonly value="Žádný balíček není vybrán" class="form-input">
                                    <input type="hidden" id="package-id" name="package_id" value="">
                                    <input type="hidden" id="package-credits" name="credits" value="">
                                    <input type="hidden" id="package-price" name="price" value="">
                                </div>
                                
                                <div class="form-group">
                                    <label for="payment-method">Způsob platby:</label>
                                    <select id="payment-method" name="payment_method" class="form-input" required>
                                        <option value="card">Platební karta</option>
                                        <option value="bank_transfer">Bankovní převod</option>
                                        <option value="paypal">PayPal</option>
                                    </select>
                                </div>
                                
                                <div style="margin-top: 20px;">
                                    <button type="submit" class="button" id="recharge-button" disabled>Dobít kredity</button>
                                </div>
                            </form>
                        </div>
                        
                        <div class="card transaction-history" style="margin-top: 20px;">
                            <h3>Historie transakcí</h3>
                            <table class="sms-history-table">
                                <thead>
                                    <tr>
                                        <th>Datum</th>
                                        <th>Typ</th>
                                        <th>Popis</th>
                                        <th>Kredity</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (empty($transactions)): ?>
                                    <tr>
                                        <td colspan="4" style="text-align: center; padding: 20px;">Žádné transakce</td>
                                    </tr>
                                    <?php else: ?>
                                    <?php foreach ($transactions as $transaction): ?>
                                    <tr>
                                        <td><?php echo date('d.m.Y H:i', strtotime($transaction['created_at'])); ?></td>
                                        <td>
                                            <?php 
                                            $typeClass = '';
                                            $typeText = '';
                                            switch ($transaction['type']) {
                                                case 'purchase':
                                                    $typeClass = 'purchase';
                                                    $typeText = 'Nákup';
                                                    break;
                                                case 'usage':
                                                    $typeClass = 'usage';
                                                    $typeText = 'Použití';
                                                    break;
                                                case 'bonus':
                                                    $typeClass = 'bonus';
                                                    $typeText = 'Bonus';
                                                    break;
                                                case 'refund':
                                                    $typeClass = 'refund';
                                                    $typeText = 'Vrácení';
                                                    break;
                                                default:
                                                    $typeClass = '';
                                                    $typeText = $transaction['type'];
                                            }
                                            ?>
                                            <span class="transaction-type <?php echo $typeClass; ?>"><?php echo $typeText; ?></span>
                                        </td>
                                        <td><?php echo htmlspecialchars($transaction['description']); ?></td>
                                        <td><?php echo ($transaction['amount'] > 0 ? '+' : '') . number_format($transaction['amount'], 0); ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                            
                            <div style="margin-top: 20px; text-align: right;">
                                <button class="button-outline" onclick="showTab('history')">Zobrazit všechny transakce</button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Platební metody -->
                <div id="tab-payment-methods" class="tab-content" style="display: none;">
                    <div class="card">
                        <h2>Platební metody</h2>
                        <p>Zde můžete spravovat své platební metody.</p>
                        
                        <div class="card" style="margin-top: 20px;">
                            <h3>Uložené platební metody</h3>
                            
                            <div style="display: flex; align-items: center; padding: 15px; border-bottom: 1px solid #2d3748;">
                                <div style="margin-right: 15px;">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="color: #4fd1c5;">
                                        <rect x="1" y="4" width="22" height="16" rx="2" ry="2"></rect>
                                        <line x1="1" y1="10" x2="23" y2="10"></line>
                                    </svg>
                                </div>
                                <div style="flex: 1;">
                                    <p style="margin: 0;"><strong>Kreditní karta</strong></p>
                                    <p style="margin: 0; color: #a0aec0;">**** **** **** 1234</p>
                                    <p style="margin: 0; color: #a0aec0;">Platnost: 12/25</p>
                                </div>
                                <div>
                                    <button class="button-outline" style="padding: 5px 10px; margin-right: 5px;" onclick="editPaymentMethod(1)">Upravit</button>
                                    <button class="button-outline" style="padding: 5px 10px; border-color: #e53e3e; color: #e53e3e;" onclick="deletePaymentMethod(1)">Smazat</button>
                                </div>
                            </div>
                            
                            <div style="margin-top: 20px;">
                                <button class="button" onclick="addPaymentMethod()">Přidat platební metodu</button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Historie -->
                <div id="tab-history" class="tab-content" style="display: none;">
                    <div class="card">
                        <h2>Historie transakcí</h2>
                        
                        <div class="tabs" style="margin-top: 20px; border-bottom-color: #2d3748;">
                            <div class="tab active" data-tab="all-transactions">Všechny transakce</div>
                            <div class="tab" data-tab="subscription-transactions">Předplatné</div>
                            <div class="tab" data-tab="sms-transactions">SMS kredity</div>
                        </div>
                        
                        <div id="tab-all-transactions" class="transaction-tab-content">
                            <table class="sms-history-table" style="margin-top: 20px;">
                                <thead>
                                    <tr>
                                        <th>Datum</th>
                                        <th>Typ</th>
                                        <th>Popis</th>
                                        <th>Částka</th>
                                        <th>Stav</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>15.02.2024</td>
                                        <td><span class="transaction-type purchase">Předplatné</span></td>
                                        <td>Předplatné - Pokročilý tarif</td>
                                        <td>5000 Kč</td>
                                        <td><span style="color: #4fd1c5;">Zaplaceno</span></td>
                                    </tr>
                                    <tr>
                                        <td>10.02.2024</td>
                                        <td><span class="transaction-type purchase">Nákup kreditů</span></td>
                                        <td>Nákup balíčku: Střední balíček</td>
                                        <td>350 Kč</td>
                                        <td><span style="color: #4fd1c5;">Zaplaceno</span></td>
                                    </tr>
                                    <tr>
                                        <td>05.02.2024</td>
                                        <td><span class="transaction-type usage">Použití kreditů</span></td>
                                        <td>Odeslání SMS kampaně: Připomenutí termínu</td>
                                        <td>-75 kreditů</td>
                                        <td><span style="color: #4fd1c5;">Dokončeno</span></td>
                                    </tr>
                                    <tr>
                                        <td>15.01.2024</td>
                                        <td><span class="transaction-type purchase">Předplatné</span></td>
                                        <td>Předplatné - Pokročilý tarif</td>
                                        <td>5000 Kč</td>
                                        <td><span style="color: #4fd1c5;">Zaplaceno</span></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <div id="tab-subscription-transactions" class="transaction-tab-content" style="display: none;">
                            <table class="sms-history-table" style="margin-top: 20px;">
                                <thead>
                                    <tr>
                                        <th>Datum</th>
                                        <th>Popis</th>
                                        <th>Částka</th>
                                        <th>Stav</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>15.02.2024</td>
                                        <td>Předplatné - Pokročilý tarif</td>
                                        <td>5000 Kč</td>
                                        <td><span style="color: #4fd1c5;">Zaplaceno</span></td>
                                    </tr>
                                    <tr>
                                        <td>15.01.2024</td>
                                        <td>Předplatné - Pokročilý tarif</td>
                                        <td>5000 Kč</td>
                                        <td><span style="color: #4fd1c5;">Zaplaceno</span></td>
                                    </tr>
                                    <tr>
                                        <td>15.12.2023</td>
                                        <td>Předplatné - Pokročilý tarif</td>
                                        <td>5000 Kč</td>
                                        <td><span style="color: #4fd1c5;">Zaplaceno</span></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <div id="tab-sms-transactions" class="transaction-tab-content" style="display: none;">
                            <table class="sms-history-table" style="margin-top: 20px;">
                                <thead>
                                    <tr>
                                        <th>Datum</th>
                                        <th>Typ</th>
                                        <th>Popis</th>
                                        <th>Kredity</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (empty($transactions)): ?>
                                    <tr>
                                        <td colspan="4" style="text-align: center; padding: 20px;">Žádné transakce s SMS kredity</td>
                                    </tr>
                                    <?php else: ?>
                                    <?php foreach ($transactions as $transaction): ?>
                                    <tr>
                                        <td><?php echo date('d.m.Y H:i', strtotime($transaction['created_at'])); ?></td>
                                        <td>
                                            <?php 
                                            $typeClass = '';
                                            $typeText = '';
                                            switch ($transaction['type']) {
                                                case 'purchase':
                                                    $typeClass = 'purchase';
                                                    $typeText = 'Nákup';
                                                    break;
                                                case 'usage':
                                                    $typeClass = 'usage';
                                                    $typeText = 'Použití';
                                                    break;
                                                case 'bonus':
                                                    $typeClass = 'bonus';
                                                    $typeText = 'Bonus';
                                                    break;
                                                case 'refund':
                                                    $typeClass = 'refund';
                                                    $typeText = 'Vrácení';
                                                    break;
                                                default:
                                                    $typeClass = '';
                                                    $typeText = $transaction['type'];
                                            }
                                            ?>
                                            <span class="transaction-type <?php echo $typeClass; ?>"><?php echo $typeText; ?></span>
                                        </td>
                                        <td><?php echo htmlspecialchars($transaction['description']); ?></td>
                                        <td><?php echo ($transaction['amount'] > 0 ? '+' : '') . number_format($transaction['amount'], 0); ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <div style="margin-top: 20px; text-align: center;">
                            <button class="button-outline" onclick="exportTransactionHistory()">Exportovat historii</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Kontejner pro notifikace -->
    <div id="notification-container"></div>

    <script>
    // Základní JavaScript pro interaktivitu
    document.addEventListener('DOMContentLoaded', function() {
        // Přepínání záložek
        const tabs = document.querySelectorAll('.tab');
        tabs.forEach(tab => {
            tab.addEventListener('click', function() {
                // Najdeme nejbližšího rodiče s třídou 'tabs'
                const tabsContainer = this.closest('.tabs');
                // Najdeme všechny záložky v tomto kontejneru
                const siblingTabs = tabsContainer.querySelectorAll('.tab');
                
                // Odstraníme aktivní třídu ze všech záložek v tomto kontejneru
                siblingTabs.forEach(t => {
                    t.classList.remove('active');
                });
                
                // Přidáme aktivní třídu této záložce
                this.classList.add('active');
                
                // Pokud je to hlavní navigace, přepneme obsah záložek
                if (tabsContainer.id === 'payment-tabs') {
                    const tabId = this.getAttribute('data-tab');
                    showTab(tabId);
                } else {
                    // Jinak přepneme obsah podzáložek
                    const tabId = this.getAttribute('data-tab');
                    showTransactionTab(tabId);
                }
            });
        });
        
        // Inicializace - zobrazení první záložky
        showTab('overview');
        
        // Nastavení event listenerů pro SMS sekci
        setupSmsEventListeners();
        
        // Simulace načtení SMS kreditu
        updateCreditDisplay();

        // Set up a timer to update SMS usage data every 5 minutes
        updateSmsUsageData(); // Initial update
        setInterval(updateSmsUsageData, 5 * 60 * 1000); // Update every 5 minutes
    });
    
    // Funkce pro zobrazení záložky
    function showTab(tabId) {
        // Skrytí všech záložek
        const tabContents = document.querySelectorAll('.tab-content');
        tabContents.forEach(content => {
            content.style.display = 'none';
        });
        
        // Odstranění aktivní třídy ze všech záložek v hlavní navigaci
        const tabs = document.querySelectorAll('#payment-tabs .tab');
        tabs.forEach(tab => {
            tab.classList.remove('active');
        });
        
        // Zobrazení vybrané záložky
        document.getElementById('tab-' + tabId).style.display = 'block';
        
        // Přidání aktivní třídy vybrané záložce
        document.querySelector(`#payment-tabs .tab[data-tab="${tabId}"]`).classList.add('active');
    }
    
    // Funkce pro zobrazení podzáložky transakcí
    function showTransactionTab(tabId) {
        // Skrytí všech podzáložek
        const tabContents = document.querySelectorAll('.transaction-tab-content');
        tabContents.forEach(content => {
            content.style.display = 'none';
        });
        
        // Zobrazení vybrané podzáložky
        document.getElementById('tab-' + tabId).style.display = 'block';
    }
    
    // Funkce pro zobrazení záložky historie
    function showHistoryTab() {
        showTab('history');
    }
    
    // Funkce pro výběr tarifu - napojení na API
    function selectPlan(planId) {
        // Zobrazení notifikace o zpracování
        showNotification('info', 'Zpracovávám změnu tarifu...');
        
        // Simulace API volání
        setTimeout(() => {
            showNotification('success', 'Tarif byl úspěšně změněn');
            // Přesměrování na platební bránu nebo aktualizace stránky
            setTimeout(() => {
                window.location.reload();
            }, 2000);
        }, 1000);
    }
    
    // Funkce pro zrušení předplatného
    function cancelSubscription() {
        if (confirm('Opravdu chcete zrušit své předplatné?')) {
            // Zobrazení notifikace o zpracování
            showNotification('info', 'Zpracovávám zrušení předplatného...');
            
            // Simulace API volání
            setTimeout(() => {
                showNotification('success', 'Předplatné bylo úspěšně zrušeno');
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            }, 1000);
        }
    }
    
    // Funkce pro správu platebních metod
    function addPaymentMethod() {
        // Zde by bylo přesměrování na platební bránu nebo zobrazení formuláře
        showNotification('info', 'Přesměrovávám na platební bránu...');
        
        // Simulace přesměrování
        setTimeout(() => {
            window.location.href = 'payment_gateway.php?action=add';
        }, 1000);
    }
    
    function editPaymentMethod(id) {
        showNotification('info', 'Přesměrovávám na úpravu platební metody...');
        
        // Simulace přesměrování
        setTimeout(() => {
            window.location.href = `payment_gateway.php?action=edit&id=${id}`;
        }, 1000);
    }
    
    function deletePaymentMethod(id) {
        if (confirm('Opravdu chcete smazat tuto platební metodu?')) {
            showNotification('info', 'Odstraňuji platební metodu...');
            
            // Simulace API volání
            setTimeout(() => {
                showNotification('success', 'Platební metoda byla úspěšně smazána');
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            }, 1000);
        }
    }
    
    // Funkce pro export historie transakcí
    function exportTransactionHistory() {
        showNotification('info', 'Připravuji export historie transakcí...');
        
        // Simulace exportu
        setTimeout(() => {
            showNotification('success', 'Historie transakcí byla úspěšně exportována');
        }, 1000);
    }
    
    // Funkce pro zobrazení notifikací
    function showNotification(type, message) {
        const container = document.getElementById('notification-container');
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        
        // Ikona podle typu notifikace
        let icon = '';
        if (type === 'success') {
            icon = '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20 6 9 17 4 12"></polyline></svg>';
        } else if (type === 'error') {
            icon = '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="15" y1="9" x2="9" y2="15"></line><line x1="9" y1="9" x2="15" y2="15"></line></svg>';
        } else {
            icon = '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="8" x2="12" y2="12"></line><line x1="12" y1="16" x2="12.01" y2="16"></line></svg>';
        }
        
        notification.innerHTML = `
            <div class="notification-content">
                ${icon} <span style="margin-left: 10px;">${message}</span>
            </div>
            <button class="notification-close" onclick="this.parentElement.classList.add('hide'); setTimeout(() => this.parentElement.remove(), 300);">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <line x1="18" y1="6" x2="6" y2="18"></line>
                    <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
            </button>
        `;
        
        container.appendChild(notification);
        
        // Automatické skrytí notifikace po 5 sekundách
        setTimeout(() => {
            notification.classList.add('hide');
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, 5000);
    }
    
    // Nastavení event listenerů pro SMS sekci
    function setupSmsEventListeners() {
        // Formulář pro dobití kreditu
        const rechargeForm = document.getElementById('recharge-form');
        if (rechargeForm) {
            rechargeForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const packageId = document.getElementById('package-id').value;
                const credits = document.getElementById('package-credits').value;
                const price = document.getElementById('package-price').value;
                const paymentMethod = document.getElementById('payment-method').value;
                
                if (!packageId || !credits || !price) {
                    showNotification('error', 'Vyberte prosím balíček kreditů');
                    return false;
                }
                
                // Simulace dobití kreditu
                showNotification('info', 'Zpracovávám dobití kreditu...');
                
                // Zde by bylo volání API pro dobití kreditu
                // Simulujeme úspěšné dobití
                setTimeout(() => {
                    // Volání funkce pro dobití kreditu
                    fetch('credit_system_handler.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: new URLSearchParams({
                            action: 'recharge_credit',
                            package_id: packageId,
                            credits: credits,
                            price: price,
                            payment_method: paymentMethod
                        }),
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            showNotification('success', data.message || `Kredit byl úspěšně dobit o ${credits} kreditů`);
                            // Aktualizace zobrazeného kreditu
                            updateCreditDisplay(data.new_balance);
                            // Reset formuláře
                            resetPackageSelection();
                        } else {
                            showNotification('error', data.message || 'Nepodařilo se dobít kredit');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        showNotification('error', 'Došlo k chybě při komunikaci se serverem');
                    });
                }, 1000);
                
                return false;
            });
        }
    }
    
    // Funkce pro výběr balíčku kreditů
    function selectPackage(id, credits, price) {
        // Nastavení hodnot do skrytých polí
        document.getElementById('package-id').value = id;
        document.getElementById('package-credits').value = credits;
        document.getElementById('package-price').value = price;
        
        // Aktualizace zobrazení vybraného balíčku
        document.getElementById('selected-package').value = `${credits} kreditů za ${price} Kč`;
        
        // Aktivace tlačítka pro dobití
        document.getElementById('recharge-button').disabled = false;
        
        // Zvýraznění vybraného balíčku
        const packages = document.querySelectorAll('.credit-package');
        packages.forEach(pkg => {
            pkg.style.borderColor = '#2d3748';
        });
        
        const selectedPackage = document.querySelector(`.credit-package[data-id="${id}"]`);
        if (selectedPackage) {
            selectedPackage.style.borderColor = '#4fd1c5';
        }
    }
    
    // Funkce pro reset výběru balíčku
    function resetPackageSelection() {
        document.getElementById('package-id').value = '';
        document.getElementById('package-credits').value = '';
        document.getElementById('package-price').value = '';
        document.getElementById('selected-package').value = 'Žádný balíček není vybrán';
        document.getElementById('recharge-button').disabled = true;
        
        // Reset zvýraznění balíčků
        const packages = document.querySelectorAll('.credit-package');
        packages.forEach(pkg => {
            pkg.style.borderColor = '#2d3748';
        });
    }
    
    // Funkce pro aktualizaci zobrazení kreditu
    function updateCreditDisplay(newBalance) {
        // Pokud není zadán nový zůstatek, načteme ho z API
        if (newBalance === undefined) {
            fetch('credit_system_handler.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    action: 'get_credit'
                }),
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Aktualizace všech zobrazení kreditu na stránce
                    const creditElements = document.querySelectorAll('.credit-amount');
                    creditElements.forEach(element => {
                        element.textContent = `${data.credit.toFixed(2)} Kč`;
                    });
                    
                    // Aktualizace počtu SMS, které lze odeslat
                    const smsPrice = parseFloat(document.querySelector('.sms-price').textContent);
                    if (smsPrice > 0) {
                        const smsCount = Math.floor(data.credit / smsPrice);
                        const smsCountElement = document.querySelector('.card p strong');
                        if (smsCountElement) {
                            smsCountElement.textContent = smsCount;
                        }
                    }
                }
            })
            .catch(error => {
                console.error('Error fetching credit balance:', error);
            });
        } else {
            // Aktualizace všech zobrazení kreditu na stránce s novým zůstatkem
            const creditElements = document.querySelectorAll('.credit-amount');
            creditElements.forEach(element => {
                element.textContent = `${newBalance.toFixed(2)} Kč`;
            });
            
            // Aktualizace počtu SMS, které lze odeslat
            const smsPrice = parseFloat(document.querySelector('.sms-price').textContent);
            if (smsPrice > 0) {
                const smsCount = Math.floor(newBalance / smsPrice);
                const smsCountElement = document.querySelector('.card p strong');
                if (smsCountElement) {
                    smsCountElement.textContent = smsCount;
                }
            }
        }
    }

    // Funkce pro aktualizaci dat o využití SMS
    function updateSmsUsageData() {
        // Fetch updated SMS usage data via AJAX
        fetch('get_sms_usage.php')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update SMS usage display
                    const totalSentElement = document.querySelector('.card:nth-child(4) p:first-of-type');
                    if (totalSentElement) {
                        totalSentElement.textContent = `${data.totalSent} z ${data.limit} SMS`;
                    }
                    
                    // Update progress bar
                    const progressBar = document.querySelector('.card:nth-child(4) .progress-value');
                    if (progressBar) {
                        const percentage = Math.min(100, (data.totalSent / data.limit) * 100);
                        progressBar.style.width = `${percentage}%`;
                    }
                    
                    // Update remaining free SMS
                    const remainingElement = document.querySelector('.card:nth-child(4) p:nth-of-type(2)');
                    if (remainingElement) {
                        const remaining = Math.max(0, data.limit - data.totalSent);
                        remainingElement.textContent = `Zbývá: ${remaining} SMS zdarma`;
                    }
                    
                    // Update SMS history table if needed
                    if (data.recentUsage && data.recentUsage.length > 0) {
                        const tableBody = document.querySelector('.card:nth-child(5) table tbody');
                        if (tableBody) {
                            tableBody.innerHTML = '';
                            data.recentUsage.forEach(usage => {
                                const row = document.createElement('tr');
                                const date = new Date(usage.date);
                                row.innerHTML = `
                                    <td>${date.toLocaleDateString('cs-CZ')} ${date.toLocaleTimeString('cs-CZ', {hour: '2-digit', minute:'2-digit'})}</td>
                                    <td>${usage.campaign_name}</td>
                                    <td>${usage.total_sent}</td>
                                `;
                                tableBody.appendChild(row);
                            });
                        }
                    }
                }
            })
            .catch(error => {
                console.error('Error fetching SMS usage data:', error);
            });
    }
    
    // Funkce pro dobití kreditu
    function rechargeCredit() {
        const packageId = document.getElementById('package-id').value;
        const credits = document.getElementById('package-credits').value;
        const price = document.getElementById('package-price').value;
        const paymentMethod = document.getElementById('payment-method').value;
        
        if (!packageId || !credits || !price) {
            showNotification('error', 'Vyberte prosím balíček kreditů');
            return false;
        }
        
        // Simulace dobití kreditu
        showNotification('info', 'Přesměrovávám na platební bránu...');
        
        // Zde by bylo přesměrování na platební bránu
        setTimeout(() => {
            window.location.href = `payment_gateway.php?action=buy_credits&package_id=${packageId}&credits=${credits}&price=${price}&payment_method=${paymentMethod}`;
        }, 1500);
        
        return false;
    }
    </script>
</body>
</html>

<?php
// Close the database connection at the end of the script
if (isset($db) && $db->ping()) {
    $db->close();
}
?>


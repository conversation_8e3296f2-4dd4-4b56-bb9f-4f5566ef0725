<?php
require_once 'config.php';
require_once 'airtable_functions.php';
require_once 'error_log.php';

// Kontrola přihlášení
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

try {
    $startTime = microtime(true);
    
    writeErrorLog('Starting Sync', [
        'user_id' => $_SESSION['user_id'],
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    
    $syncedCount = syncPatientsWithAirtable();
    
    $endTime = microtime(true);
    $executionTime = round($endTime - $startTime, 2);
    
    writeErrorLog('Sync Complete', [
        'synced_count' => $syncedCount,
        'execution_time' => $executionTime,
        'user_id' => $_SESSION['user_id']
    ]);
    
    $success = "Úspěšně synchronizováno $syncedCount pacientů. Čas: {$executionTime}s";
    header('Location: patients.php?success=' . urlencode($success));
} catch (Exception $e) {
    writeErrorLog('Sync Error', [
        'error' => $e->getMessage(),
        'user_id' => $_SESSION['user_id']
    ]);
    
    header('Location: patients.php?error=' . urlencode($e->getMessage()));
}
exit;
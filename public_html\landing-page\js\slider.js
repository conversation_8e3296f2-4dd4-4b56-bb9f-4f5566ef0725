document.addEventListener("DOMContentLoaded", () => {
  const slider = document.getElementById("testimonialSlider")
  const sliderContent = slider.querySelector(".flex")
  const prevButton = document.getElementById("prevButton")
  const nextButton = document.getElementById("nextButton")
  const dots = document.querySelectorAll(".flex.justify-center button")
  let currentIndex = 0

  function updateSlider() {
    sliderContent.style.transform = `translateX(-${currentIndex * 100}%)`
    dots.forEach((dot, index) => {
      dot.classList.toggle("bg-teal-400", index === currentIndex)
      dot.classList.toggle("bg-gray-400", index !== currentIndex)
    })
  }

  prevButton.addEventListener("click", () => {
    currentIndex = (currentIndex - 1 + dots.length) % dots.length
    updateSlider()
  })

  nextButton.addEventListener("click", () => {
    currentIndex = (currentIndex + 1) % dots.length
    updateSlider()
  })

  dots.forEach((dot, index) => {
    dot.addEventListener("click", () => {
      currentIndex = index
      updateSlider()
    })
  })
})


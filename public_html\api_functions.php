<?php
require_once 'config.php';
require_once 'error_log.php';

// Konstanty pro konfiguraci úspěšnosti hovorů
define('MIN_SUCCESSFUL_CALL_DURATION', 10); // Minimální délka úspěšného hovoru v sekundách
define('MIN_TRANSCRIPT_LENGTH', 20); // Minimální délka transkriptu ve znacích

$callEndReasonTranslations = [
    'customer-ended-call' => 'Pacient ukončil hovor',
    'customer-busy' => 'Pacient byl nedostupný',
    'assistant-ended-call' => 'Dentibot ukončil hovor',
    'customer-did-not-give-microphone-permission' => 'Pacient nepovolil mikrofon',
    'twilio-failed-to-connect-call' => 'Chyba připojení hovoru',
    'silence-timed-out' => 'Pacient moc dlouho mlčel',
    'transferred' => 'Úsp<PERSON>šně přepojeno',
    'transferred-call-not-answered' => 'Přepojen<PERSON> nebylo přija<PERSON>',
    'assistant-forwarded-call' => 'Dentibot přepojil hovor',
    'transfer-completed' => 'Přepojení dokončeno',
    'transfer-accepted' => 'Přepojení přijato',
    'call-completed' => 'Hovor úspěšně dokončen',
    'system-error' => 'Systémová chyba',
    'network-error' => 'Síťová chyba',
    'unknown' => 'Neznámý důvod',
    'customer-did-not-answer' => 'Pacient nezvedl'
];

function getDefaultMetrics() {
    return [
        [
            'label' => 'Spotřeba minut',
            'value' => '35',
            'subValue' => 'Celkem: 35 minut',
            'trend' => '+0%',
            'highlight' => false
        ],
        [
            'label' => 'Průměrná délka hovoru',
            'value' => '01:08',
            'subValue' => 'minut',
            'trend' => '+0%',
            'highlight' => false
        ],
        [
            'label' => 'Úspěšnost hovorů',
            'value' => '100%',
            'subValue' => 'Z celkových 31 hovorů',
            'trend' => '+0%',
            'highlight' => false
        ]
    ];
}

function getCallHistoryFromAPI($apiKey, $apiUrl, $startDate, $endDate, $assistantId) {
    writeErrorLog("Starting API call", [
        'url' => $apiUrl,
        'startDate' => $startDate,
        'endDate' => $endDate,
        'assistantId' => $assistantId
    ]);
    
    if (empty($apiKey)) {
        writeErrorLog("API call failed - missing API key");
        throw new Exception("API klíč není nastaven");
    }

    $url = rtrim($apiUrl, '/') . '/call';
    $headers = [
        'Authorization: Bearer ' . $apiKey,
        'Content-Type: application/json',
        'Accept: application/json'
    ];

    // Ensure dates are in ISO 8601 format
    $startDateTime = new DateTime($startDate);
    $endDateTime = new DateTime($endDate);
    $startDateTime->setTime(0, 0, 0);
    $endDateTime->setTime(23, 59, 59);

    $queryParams = http_build_query([
        'createdAtGe' => $startDateTime->format('c'),
        'createdAtLe' => $endDateTime->format('c'),
        'limit' => 1000,
        'assistantId' => $assistantId
    ]);
    
    $fullUrl = $url . '?' . $queryParams;
    writeErrorLog("Making API request", ['full_url' => $fullUrl]);

    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $fullUrl,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_HTTPHEADER => $headers,
        CURLOPT_SSL_VERIFYPEER => true,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_VERBOSE => true,
        CURLOPT_HEADER => true
    ]);

    $verbose = fopen('php://temp', 'w+');
    curl_setopt($ch, CURLOPT_STDERR, $verbose);

    $response = curl_exec($ch);
    $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
    $responseHeaders = substr($response, 0, $headerSize);
    $responseBody = substr($response, $headerSize);
    
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);

    rewind($verbose);
    $verboseLog = stream_get_contents($verbose);

    curl_close($ch);

    writeErrorLog("API Response Info", [
        'http_code' => $httpCode,
        'headers' => $responseHeaders,
        'error' => $error,
        'verbose_log' => $verboseLog
    ]);

    writeErrorLog("API Response", [
        'http_code' => $httpCode,
        'response_body' => $responseBody
    ]);

    if ($error) {
        writeErrorLog("CURL Error", ['error' => $error]);
        throw new Exception("Chyba komunikace s API: " . $error);
    }

    if ($httpCode === 400) {
        writeErrorLog("API Bad Request", [
            'response' => $responseBody,
            'request_url' => $fullUrl,
            'assistant_id' => $assistantId
        ]);
        throw new Exception("Neplatný požadavek na API. Zkontrolujte formát dat a platnost assistant_id.");
    }

    if ($httpCode === 401) {
        writeErrorLog("API Authentication Failed", ['response' => $responseBody]);
        throw new Exception("Neplatný API klíč nebo nedostatečná oprávnění.");
    }

    if ($httpCode !== 200) {
        writeErrorLog("API Error", [
            'http_code' => $httpCode,
            'response' => $responseBody
        ]);
        throw new Exception("API vrátila chybový kód: " . $httpCode);
    }

    $data = json_decode($responseBody, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        writeErrorLog("JSON Parse Error", [
            'error' => json_last_error_msg(),
            'response' => $responseBody
        ]);
        throw new Exception("Chyba při zpracování odpovědi z API");
    }

    if (!is_array($data)) {
        writeErrorLog("Invalid API Response", [
            'response_type' => gettype($data),
            'response' => $responseBody
        ]);
        throw new Exception("Neplatný formát odpovědi z API");
    }

    writeErrorLog("API call successful", [
        'records_count' => count($data),
        'first_record' => !empty($data) ? json_encode($data[0]) : 'no records'
    ]);
    
    if (empty($data)) {
        writeErrorLog("API returned empty data", [
            'http_code' => $httpCode,
            'response_body' => $responseBody
        ]);
    }

    return $data;
}

function getDefaultGraphData() {
    return [
        'calls' => [],
        'dates' => [],
        'peakHours' => [],
        'hours' => []
    ];
}

function getAnalytics($dateRange = '30 DAY', $assistantId = null) {
    if (!isset($_SESSION['vapi_calls_data']) || empty($_SESSION['vapi_calls_data'])) {
        return ['data' => []];
    }
    
    $calls = $_SESSION['vapi_calls_data'];
    $data = [];
    $dateGroups = [];
    
    // Calculate date range
    $endDate = new DateTime();
    $startDate = clone $endDate;
    
    switch ($dateRange) {
        case '1 DAY':
            $startDate->modify('-1 day');
            break;
        case '6 MONTH':
            $startDate->modify('-6 months');
            break;
        case '30 DAY':
        default:
            $startDate->modify('-30 days');
            break;
    }
    
    // Group calls by date
    foreach ($calls as $call) {
        if (empty($call['created_at'])) continue;
        
        $callDate = substr($call['created_at'], 0, 10); // Get YYYY-MM-DD part
        $callDateTime = new DateTime($callDate);
        
        // Skip if outside date range
        if ($callDateTime < $startDate || $callDateTime > $endDate) continue;
        
        if (!isset($dateGroups[$callDate])) {
            $dateGroups[$callDate] = [
                'date' => $callDate,
                'total_calls' => 0,
                'total_duration' => 0,
                'total_cost' => 0,
                'successful_calls' => 0
            ];
        }
        
        $dateGroups[$callDate]['total_calls']++;
        $dateGroups[$callDate]['total_duration'] += (int)$call['duration'];
        $dateGroups[$callDate]['total_cost'] += (float)$call['cost'];
        
        if (isCallSuccessful($call)) {
            $dateGroups[$callDate]['successful_calls']++;
        }
    }
    
    // Convert to array and sort by date
    foreach ($dateGroups as $date => $group) {
        $data[] = $group;
    }
    
    // Sort by date in descending order
    usort($data, function($a, $b) {
        return strcmp($b['date'], $a['date']);
    });
    
    return ['data' => $data];
}

function getPeakHours($dateRange = '30 DAY', $assistantId = null) {
    if (!isset($_SESSION['vapi_calls_data']) || empty($_SESSION['vapi_calls_data'])) {
        return ['data' => []];
    }
    
    $calls = $_SESSION['vapi_calls_data'];
    $hourGroups = [];
    
    // Calculate date range
    $endDate = new DateTime();
    $startDate = clone $endDate;
    
    switch ($dateRange) {
        case '1 DAY':
            $startDate->modify('-1 day');
            break;
        case '6 MONTH':
            $startDate->modify('-6 months');
            break;
        case '30 DAY':
        default:
            $startDate->modify('-30 days');
            break;
    }
    
    // Group calls by hour and date
    foreach ($calls as $call) {
        if (empty($call['created_at'])) continue;
        
        $callDateTime = new DateTime($call['created_at']);
        
        // Skip if outside date range
        if ($callDateTime < $startDate || $callDateTime > $endDate) continue;
        
        $hour = $callDateTime->format('H');
        $date = $callDateTime->format('Y-m-d');
        $key = $hour . '_' . $date;
        
        if (!isset($hourGroups[$key])) {
            $hourGroups[$key] = [
                'hour' => $hour,
                'date' => $date,
                'total_calls' => 0
            ];
        }
        
        $hourGroups[$key]['total_calls']++;
    }
    
    // Convert to array and sort by total_calls
    $data = array_values($hourGroups);
    
    // Sort by total_calls in descending order
    usort($data, function($a, $b) {
        return $b['total_calls'] - $a['total_calls'];
    });
    
    return ['data' => $data];
}

function getTopReasons($assistantId = null) {
    if (!isset($_SESSION['vapi_calls_data']) || empty($_SESSION['vapi_calls_data'])) {
        return [];
    }
    
    $calls = $_SESSION['vapi_calls_data'];
    $reasonCounts = [];
    
    // Count occurrences of each reason
    foreach ($calls as $call) {
        $reason = $call['ended_reason'] ?? 'Neznámý důvod';
        
        if (!isset($reasonCounts[$reason])) {
            $reasonCounts[$reason] = 0;
        }
        
        $reasonCounts[$reason]++;
    }
    
    // Sort by count in descending order
    arsort($reasonCounts);
    
    // Translate reasons
    $translatedReasons = [];
    global $callEndReasonTranslations;
    
    foreach ($reasonCounts as $reason => $count) {
        $translatedReason = $callEndReasonTranslations[$reason] ?? $reason;
        $translatedReasons[$translatedReason] = $count;
    }
    
    // Limit to top 5
    return array_slice($translatedReasons, 0, 5, true);
}

// Upravená funkce pro určení úspěšnosti hovoru
function isCallSuccessful($call) {
    $status = $call['status'] ?? '';
    $endedReason = $call['ended_reason'] ?? '';
    $duration = (int)($call['duration'] ?? 0);
    $transcript = trim($call['transcript'] ?? '');
    $direction = $call['direction'] ?? '';
    
    // Základní kontroly
    if ($status !== 'ended' || $duration < MIN_SUCCESSFUL_CALL_DURATION) {
        return false;
    }

    // Kontrola transkriptu pro příchozí hovory
    if ($direction === 'inbound' && strlen($transcript) < MIN_TRANSCRIPT_LENGTH) {
        return false;
    }

    // Úspěšné důvody ukončení
    $successfulEndReasons = [
        'transferred', // Úspěšně přepojeno na ordinaci
        'assistant-forwarded-call', // Bot přepojil hovor
        'customer-ended-call', // Zákazník ukončil po získání informací
        'transfer-completed', // Přepojení bylo dokončeno
        'assistant-ended-call', // Asistent ukončil po vyřešení požadavku
        'call-completed', // Hovor byl úspěšně dokončen
        'transfer-accepted' // Přepojení bylo přijato
    ];

    // Neúspěšné důvody ukončení
    $unsuccessfulEndReasons = [
        'transferred-call-not-answered', // Přepojení nebylo přijato
        'customer-busy', // Zákazník byl nedostupný
        'customer-did-not-give-microphone-permission', // Problém s mikrofonem
        'twilio-failed-to-connect-call', // Technický problém
        'silence-timed-out', // Timeout kvůli tichu
        'system-error', // Systémová chyba
        'network-error' // Síťová chyba
    ];

    // Pokud je důvod ukončení v seznamu úspěšných, je hovor úspěšný
    if (in_array($endedReason, $successfulEndReasons)) {
        return true;
    }

    // Pokud je důvod ukončení v seznamu neúspěšných, není hovor úspěšný
    if (in_array($endedReason, $unsuccessfulEndReasons)) {
        return false;
    }

    // Pro hovory bez konkrétního důvodu ukončení kontrolujeme další faktory
    if (empty($endedReason)) {
        // Pro příchozí hovory kontrolujeme délku a transkript
        if ($direction === 'inbound') {
            return $duration >= MIN_SUCCESSFUL_CALL_DURATION && strlen($transcript) >= MIN_TRANSCRIPT_LENGTH;
        }
        // Pro odchozí hovory stačí minimální délka
        return $duration >= MIN_SUCCESSFUL_CALL_DURATION;
    }

    return false;
}

// Nová funkce pro získání detailních statistik úspěšnosti
function getDetailedCallStats($analytics) {
    $stats = [
        'total_calls' => 0,
        'successful_calls' => [
            'total' => 0,
            'transferred' => 0, // Úspěšně přepojené hovory
            'info_provided' => 0, // Hovory kde byly poskytnuty informace
            'callback_scheduled' => 0 // Hovory kde byl dohodnut zpětný hovor
        ],
        'unsuccessful_calls' => [
            'total' => 0,
            'too_short' => 0, // Příliš krátké hovory
            'no_transcript' => 0, // Hovory bez transkriptu
            'transfer_failed' => 0, // Neúspěšné přepojení
            'technical_issues' => 0 // Technické problémy
        ],
        'average_duration' => 0,
        'success_rate' => 0,
        'by_hour' => array_fill(0, 24, 0), // Statistiky po hodinách
        'peak_hour' => null,
        'peak_hour_calls' => 0,
        'intent_stats' => [], // Statistiky záměrů
        'sentiment_stats' => [ // Statistiky sentimentu
            'positive' => 0,
            'neutral' => 0,
            'negative' => 0
        ]
    ];
    
    if (!empty($analytics['data'])) {
        foreach ($analytics['data'] as $day) {
            // Získat hovory pro tento den
            $daysCalls = array_filter($_SESSION['vapi_calls_data'], function($call) use ($day) {
                return substr($call['created_at'], 0, 10) === $day['date'];
            });
            
            foreach ($daysCalls as $call) {
                $stats['total_calls']++;
                $duration = (int)($call['duration'] ?? 0);
                $stats['average_duration'] += $duration;
                
                // Sledování vytížení podle hodin
                $hour = (int)(new DateTime($call['created_at']))->format('G');
                $stats['by_hour'][$hour]++;
                
                // Aktualizace špičky
                if ($stats['by_hour'][$hour] > $stats['peak_hour_calls']) {
                    $stats['peak_hour'] = $hour;
                    $stats['peak_hour_calls'] = $stats['by_hour'][$hour];
                }
                
                // Analýza transkriptu pro sentiment a záměry
                $transcript = strtolower($call['transcript'] ?? '');
                
                // Detekce sentimentu z transkriptu
                if (strpos($transcript, 'děkuji') !== false || 
                    strpos($transcript, 'výborně') !== false ||
                    strpos($transcript, 'super') !== false) {
                    $stats['sentiment_stats']['positive']++;
                } elseif (strpos($transcript, 'nespokojený') !== false || 
                         strpos($transcript, 'problém') !== false ||
                         strpos($transcript, 'špatně') !== false) {
                    $stats['sentiment_stats']['negative']++;
                } else {
                    $stats['sentiment_stats']['neutral']++;
                }
                
                // Detekce záměrů
                $intent = detectIntent($transcript);
                if (!isset($stats['intent_stats'][$intent])) {
                    $stats['intent_stats'][$intent] = 0;
                }
                $stats['intent_stats'][$intent]++;
                
                if (isCallSuccessful($call)) {
                    $stats['successful_calls']['total']++;
                    
                    // Analyzovat typ úspěchu
                    $endedReason = $call['ended_reason'] ?? '';
                    if (in_array($endedReason, ['transferred', 'transfer-completed', 'transfer-accepted'])) {
                        $stats['successful_calls']['transferred']++;
                    } elseif ($endedReason === 'customer-ended-call' && $duration >= MIN_SUCCESSFUL_CALL_DURATION) {
                        $stats['successful_calls']['info_provided']++;
                    }
                    
                    // Detekce dohodnutého zpětného hovoru z transkriptu
                    if (strpos($transcript, 'zavoláme zpět') !== false || 
                        strpos($transcript, 'zpětný hovor') !== false ||
                        strpos($transcript, 'zavolám později') !== false ||
                        strpos($transcript, 'zavolám vám') !== false) {
                        $stats['successful_calls']['callback_scheduled']++;
                    }
                } else {
                    $stats['unsuccessful_calls']['total']++;
                    
                    // Analyzovat důvod neúspěchu
                    if ($duration < MIN_SUCCESSFUL_CALL_DURATION) {
                        $stats['unsuccessful_calls']['too_short']++;
                    }
                    if (strlen(trim($call['transcript'] ?? '')) < MIN_TRANSCRIPT_LENGTH) {
                        $stats['unsuccessful_calls']['no_transcript']++;
                    }
                    if ($call['ended_reason'] === 'transferred-call-not-answered') {
                        $stats['unsuccessful_calls']['transfer_failed']++;
                    }
                    if (in_array($call['ended_reason'], ['twilio-failed-to-connect-call', 'system-error', 'network-error'])) {
                        $stats['unsuccessful_calls']['technical_issues']++;
                    }
                }
            }
        }
        
        // Vypočítat průměry a procenta
        if ($stats['total_calls'] > 0) {
            $stats['average_duration'] = $stats['average_duration'] / $stats['total_calls'];
            $stats['success_rate'] = ($stats['successful_calls']['total'] / $stats['total_calls']) * 100;
        }
        
        // Seřadit záměry podle četnosti
        arsort($stats['intent_stats']);
    }
    
    return $stats;
}

// Pomocná funkce pro detekci záměru z transkriptu
function detectIntent($transcript) {
    $intents = [
        'objednání' => ['objednat', 'termín', 'návštěva'],
        'informace' => ['otevírací doba', 'kde', 'jak', 'kolik stojí'],
        'urgentní' => ['bolest', 'akutní', 'urgentní'],
        'změna' => ['změnit', 'přesunout', 'zrušit'],
        'recept' => ['recept', 'lék', 'předepsat'],
        'ostatní' => []
    ];
    
    foreach ($intents as $intent => $keywords) {
        foreach ($keywords as $keyword) {
            if (strpos($transcript, $keyword) !== false) {
                return $intent;
            }
        }
    }
    
    return 'ostatní';
}

function getMetrics($analytics) {
    $stats = getDetailedCallStats($analytics);
    
    // Formátování detailního popisu úspěšnosti
    $successDetails = sprintf(
        'Přepojeno: %d, Info: %d, Zpětný hovor: %d',
        $stats['successful_calls']['transferred'],
        $stats['successful_calls']['info_provided'],
        $stats['successful_calls']['callback_scheduled']
    );
    
    // Formátování detailního popisu neúspěchů
    $failureDetails = sprintf(
        'Krátké: %d, Bez transkriptu: %d, Technické: %d',
        $stats['unsuccessful_calls']['too_short'],
        $stats['unsuccessful_calls']['no_transcript'],
        $stats['unsuccessful_calls']['technical_issues']
    );
    
    // Formátování špičky
    $peakHourInfo = $stats['peak_hour'] !== null ? 
        sprintf(' (Špička: %02d:00-%02d:00, %d hovorů)', 
            $stats['peak_hour'], 
            ($stats['peak_hour'] + 1) % 24,
            $stats['peak_hour_calls']
        ) : '';
    
    // Formátování sentimentu
    $sentimentInfo = sprintf(
        'Sentiment: 😊 %d, 😐 %d, ☹️ %d',
        $stats['sentiment_stats']['positive'],
        $stats['sentiment_stats']['neutral'],
        $stats['sentiment_stats']['negative']
    );
    
    // Získání nejčastějšího záměru
    $topIntent = key($stats['intent_stats']);
    $topIntentCount = reset($stats['intent_stats']);
    
    return [
        [
            'label' => 'Spotřeba minut',
            'value' => (string)ceil($stats['average_duration'] * $stats['total_calls'] / 60),
            'subValue' => 'Celkem: ' . ceil($stats['average_duration'] * $stats['total_calls'] / 60) . ' minut' . $peakHourInfo,
            'trend' => '+0%',
            'highlight' => false
        ],
        [
            'label' => 'Průměrná délka hovoru',
            'value' => gmdate("i:s", (int)$stats['average_duration']),
            'subValue' => sprintf('%s | %s', $successDetails, $sentimentInfo),
            'trend' => '+0%',
            'highlight' => false
        ],
        [
            'label' => 'Úspěšnost hovorů',
            'value' => round($stats['success_rate']) . '%',
            'subValue' => sprintf(
                'Celkem: %d (%d úsp., %d neúsp.) | Nejčastější záměr: %s (%d)',
                $stats['total_calls'],
                $stats['successful_calls']['total'],
                $stats['unsuccessful_calls']['total'],
                $topIntent,
                $topIntentCount
            ),
            'trend' => '+0%',
            'highlight' => $stats['success_rate'] < 70 // Zvýraznit pokud je úspěšnost pod 70%
        ]
    ];
}

function makeApiCall($endpoint, $data) {
    $user_id = $_SESSION['user_id'];
    $usage = (int)getUserUsage($user_id);
    $limit = getUserSubscriptionLimit($user_id);
    
    if ($usage >= $limit) {
        throw new Exception("Dosáhli jste limitu vašeho předplatného. Pros��m, upgradujte váš plán pro další použití.");
    }
    
    $apiConfig = getCurrentUserApiKey();
    if (!$apiConfig) {
        throw new Exception("API klíč není nastaven");
    }

    $apiKey = $apiConfig['key'];
    $apiUrl = $apiConfig['url'];

    $url = rtrim($apiUrl, '/') . '/' . $endpoint;
    $headers = [
        'Authorization: Bearer ' . $apiKey,
        'Content-Type: application/json',
        'Accept: application/json'
    ];

    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_HTTPHEADER => $headers,
        CURLOPT_SSL_VERIFYPEER => true,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_VERBOSE => true,
        CURLOPT_HEADER => true,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => json_encode($data)
    ]);

    $verbose = fopen('php://temp', 'w+');
    curl_setopt($ch, CURLOPT_STDERR, $verbose);

    $response = curl_exec($ch);
    $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
    $responseHeaders = substr($response, 0, $headerSize);
    $responseBody = substr($response, $headerSize);

    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);

    rewind($verbose);
    $verboseLog = stream_get_contents($verbose);

    curl_close($ch);

    writeErrorLog("API Response Info", [
        'http_code' => $httpCode,
        'headers' => $responseHeaders,
        'error' => $error,
        'verbose_log' => $verboseLog
    ]);

    if ($error) {
        writeErrorLog("CURL Error", ['error' => $error]);
        throw new Exception("Chyba komunikace s API: " . $error);
    }

    if ($httpCode !== 200 && $httpCode !== 201) {
        writeErrorLog("API Error", [
            'http_code' => $httpCode,
            'response' => $responseBody
        ]);
        throw new Exception("API vrátila chybový kód: " . $httpCode);
    }

    $data = json_decode($responseBody, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        writeErrorLog("JSON Parse Error", [
            'error' => json_last_error_msg(),
            'response' => $responseBody
        ]);
        throw new Exception("Chyba při zpracování odpovědi z API");
    }

    return $data;
}
?>


<?php
require_once __DIR__ . '/../config.php';

function sendSMS($username, $message) {
    // Make.com webhook URL - this should be stored in your environment variables
    $webhook_url = getenv('MAKE_WEBHOOK_URL');
    
    if (!$webhook_url) {
        error_log('Make.com webhook URL not configured');
        return false;
    }
    
    // Prepare the data for the webhook
    $data = array(
        'username' => $username, // Changed from email to username
        'message' => $message,
        'timestamp' => date('Y-m-d H:i:s'),
        'source' => 'DentiBot Minute Limit Alert'
    );
    
    // Initialize cURL
    $ch = curl_init($webhook_url);
    
    // Set cURL options
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        'Content-Type: application/json',
        'Accept: application/json'
    ));
    
    try {
        // Execute the request
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        
        // Check for errors
        if ($response === false) {
            throw new Exception('Curl error: ' . curl_error($ch));
        }
        
        // Log the response
        error_log('SMS Webhook Response: ' . json_encode([
            'username' => $username,
            'http_code' => $http_code,
            'response' => $response
        ]));
        
        // Consider any 2xx status code as success
        return ($http_code >= 200 && $http_code < 300);
        
    } catch (Exception $e) {
        error_log('SMS Webhook Error: ' . $e->getMessage());
        return false;
        
    } finally {
        // Always close the cURL session
        curl_close($ch);
    }
}


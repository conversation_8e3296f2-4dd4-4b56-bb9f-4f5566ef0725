<?php
// Testovací skript pro ověř<PERSON>í funkčnosti šifrování a dešifrování

// Zapnutí zobrazování chyb pro debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Načtení konfiguračního souboru
require_once '../config.php';

// Testovací data
$testData = [
    'name' => '<PERSON>ák',
    'phone' => '+420 123 456 789',
    'message' => 'Do<PERSON>r<PERSON> den, potřebuji objednat na kontrolu.'
];

// Šifrování dat
$encryptedName = encryptPatientData($testData['name']);
$encryptedPhone = encryptPatientData($testData['phone']);
$encryptedMessage = encryptPatientData($testData['message']);

// Dešifrování dat
$decryptedName = decryptPatientData($encryptedName['data'], $encryptedName['iv']);
$decryptedPhone = decryptPatientData($encryptedPhone['data'], $encryptedPhone['iv']);
$decryptedMessage = decryptPatientData($encryptedMessage['data'], $encryptedMessage['iv']);

// Výpis výsledků
echo "<h1>Test šifrování a dešifrování</h1>";

echo "<h2>Původní data:</h2>";
echo "<pre>";
print_r($testData);
echo "</pre>";

echo "<h2>Šifrovaná data:</h2>";
echo "<pre>";
print_r([
    'name' => $encryptedName,
    'phone' => $encryptedPhone,
    'message' => $encryptedMessage
]);
echo "</pre>";

echo "<h2>Dešifrovaná data:</h2>";
echo "<pre>";
print_r([
    'name' => $decryptedName,
    'phone' => $decryptedPhone,
    'message' => $decryptedMessage
]);
echo "</pre>";

// Test, zda dešifrovaná data odpovídají původním
$success = 
    $decryptedName === $testData['name'] && 
    $decryptedPhone === $testData['phone'] && 
    $decryptedMessage === $testData['message'];

echo "<h2>Výsledek testu:</h2>";
echo $success ? 
    "<p style='color: green; font-weight: bold;'>Test úspěšný - data byla správně zašifrována a dešifrována</p>" : 
    "<p style='color: red; font-weight: bold;'>Test selhal - data nebyla správně zašifrována a dešifrována</p>";

// Test připojení k databázi
echo "<h2>Test připojení k databázi:</h2>";
$mysqli = getDbConnection();
if ($mysqli) {
    echo "<p style='color: green; font-weight: bold;'>Připojení k databázi úspěšné</p>";
    
    // Test existence tabulky
    $tableExists = $mysqli->query("SHOW TABLES LIKE 'patient_messages'")->num_rows > 0;
    
    if ($tableExists) {
        echo "<p style='color: green; font-weight: bold;'>Tabulka patient_messages existuje</p>";
        
        // Kontrola struktury tabulky
        $result = $mysqli->query("SHOW COLUMNS FROM patient_messages LIKE 'name_iv'");
        $hasNewStructure = $result && $result->num_rows > 0;
        
        if (!$hasNewStructure) {
            echo "<p style='color: orange; font-weight: bold;'>Tabulka používá starou strukturu. Aktualizuji...</p>";
            
            try {
                // Pokus o aktualizaci struktury tabulky
                $alterTableSql = "
                    ALTER TABLE `patient_messages` 
                    ADD COLUMN `name_iv` varchar(32) NOT NULL AFTER `name_encrypted`,
                    ADD COLUMN `phone_iv` varchar(32) NOT NULL AFTER `phone_encrypted`,
                    ADD COLUMN `message_iv` varchar(32) NOT NULL AFTER `message_encrypted`,
                    DROP COLUMN `encryption_iv`
                ";
                
                $alterResult = $mysqli->query($alterTableSql);
                
                if ($alterResult) {
                    echo "<p style='color: green; font-weight: bold;'>Struktura tabulky byla úspěšně aktualizována</p>";
                    $hasNewStructure = true;
                } else {
                    echo "<p style='color: red; font-weight: bold;'>Nepodařilo se aktualizovat strukturu tabulky: " . $mysqli->error . "</p>";
                }
            } catch (Exception $e) {
                echo "<p style='color: red; font-weight: bold;'>Chyba při aktualizaci struktury tabulky: " . $e->getMessage() . "</p>";
            }
        } else {
            echo "<p style='color: green; font-weight: bold;'>Tabulka používá novou strukturu s individuálními IV</p>";
        }
        
        // Test vložení a načtení dat
        try {
            // Začátek transakce pro možnost rollbacku
            $mysqli->begin_transaction();
            
            // Vložení testovacích dat
            if ($hasNewStructure) {
                $stmt = $mysqli->prepare("
                    INSERT INTO patient_messages 
                    (name_encrypted, name_iv, phone_encrypted, phone_iv, message_encrypted, message_iv) 
                    VALUES (?, ?, ?, ?, ?, ?)
                ");
                
                $stmt->bind_param("ssssss", 
                    $encryptedName['data'],
                    $encryptedName['iv'],
                    $encryptedPhone['data'],
                    $encryptedPhone['iv'],
                    $encryptedMessage['data'],
                    $encryptedMessage['iv']
                );
            } else {
                $stmt = $mysqli->prepare("
                    INSERT INTO patient_messages 
                    (name_encrypted, phone_encrypted, message_encrypted, encryption_iv) 
                    VALUES (?, ?, ?, ?)
                ");
                
                $stmt->bind_param("ssss", 
                    $encryptedName['data'],
                    $encryptedPhone['data'],
                    $encryptedMessage['data'],
                    $encryptedName['iv']
                );
            }
            
            $stmt->execute();
            $id = $mysqli->insert_id;
            $stmt->close();
            
            echo "<p style='color: green; font-weight: bold;'>Testovací zpráva byla úspěšně vložena (ID: $id)</p>";
            
            // Načtení vložených dat
            $stmt = $mysqli->prepare("SELECT * FROM patient_messages WHERE id = ?");
            $stmt->bind_param("i", $id);
            $stmt->execute();
            $result = $stmt->get_result();
            $row = $result->fetch_assoc();
            $stmt->close();
            
            if ($row) {
                if ($hasNewStructure) {
                    $retrievedName = decryptPatientData($row['name_encrypted'], $row['name_iv']);
                    $retrievedPhone = decryptPatientData($row['phone_encrypted'], $row['phone_iv']);
                    $retrievedMessage = decryptPatientData($row['message_encrypted'], $row['message_iv']);
                } else {
                    $retrievedName = decryptPatientData($row['name_encrypted'], $row['encryption_iv']);
                    $retrievedPhone = decryptPatientData($row['phone_encrypted'], $row['encryption_iv']);
                    $retrievedMessage = decryptPatientData($row['message_encrypted'], $row['encryption_iv']);
                }
                
                echo "<h3>Načtená data z databáze:</h3>";
                echo "<pre>";
                print_r([
                    'id' => $row['id'],
                    'name' => $retrievedName,
                    'phone' => $retrievedPhone,
                    'message' => $retrievedMessage,
                    'timestamp' => $row['timestamp'],
                    'read' => (bool)$row['read']
                ]);
                echo "</pre>";
                
                $retrieveSuccess = 
                    $retrievedName === $testData['name'] && 
                    $retrievedPhone === $testData['phone'] && 
                    $retrievedMessage === $testData['message'];
                
                echo $retrieveSuccess ? 
                    "<p style='color: green; font-weight: bold;'>Test úspěšný - data byla správně uložena a načtena z databáze</p>" : 
                    "<p style='color: red; font-weight: bold;'>Test selhal - data nebyla správně uložena nebo načtena z databáze</p>";
            } else {
                echo "<p style='color: red; font-weight: bold;'>Nepodařilo se načíst vloženou zprávu</p>";
            }
            
            // Rollback transakce, aby testovací data nezůstala v databázi
            $mysqli->rollback();
            echo "<p><em>Poznámka: Testovací data byla odstraněna z databáze (rollback transakce).</em></p>";
            
        } catch (Exception $e) {
            // Rollback v případě chyby
            if (isset($mysqli) && $mysqli->connect_errno === 0) {
                $mysqli->rollback();
            }
            echo "<p style='color: red; font-weight: bold;'>Chyba při testování databáze: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p style='color: orange; font-weight: bold;'>Tabulka patient_messages neexistuje</p>";
        
        // Pokus o vytvoření tabulky
        echo "<h3>Pokus o vytvoření tabulky:</h3>";
        
        if (function_exists('ensurePatientMessagesTable')) {
            try {
                ensurePatientMessagesTable();
                
                // Ověření, zda byla tabulka vytvořena
                $result = $mysqli->query("SHOW TABLES LIKE 'patient_messages'");
                $tableCreated = $result && $result->num_rows > 0;
                
                if ($tableCreated) {
                    echo "<p style='color: green; font-weight: bold;'>Tabulka patient_messages byla úspěšně vytvořena</p>";
                } else {
                    echo "<p style='color: red; font-weight: bold;'>Nepodařilo se vytvořit tabulku patient_messages</p>";
                }
            } catch (Exception $e) {
                echo "<p style='color: red; font-weight: bold;'>Chyba při vytváření tabulky: " . $e->getMessage() . "</p>";
            }
        } else {
            echo "<p style='color: red; font-weight: bold;'>Funkce ensurePatientMessagesTable není definována v konfiguračním souboru</p>";
        }
    }
} else {
    echo "<p style='color: red; font-weight: bold;'>Připojení k databázi selhalo</p>";
}
?>
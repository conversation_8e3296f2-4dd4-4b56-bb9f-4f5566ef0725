<!-- Calendar Section -->
<section class="calendar-section py-24 relative bg-black">
    <div class="container mx-auto px-6">
        <div class="text-center mb-16">
            <h2 class="text-4xl md:text-5xl font-bold gradient-text mb-4">Rezervujte si schůzku online</h2>
            <p class="text-gray-400 max-w-2xl mx-auto">
                <PERSON><PERSON><PERSON><PERSON> si termín, který vám nejvíce vyhovuje. Systém je dostupný 24/7 a automaticky potvrdí vaši rezervaci.
            </p>
        </div>

        <div class="calendar-wrapper">
            <div id="my-cal-inline" class="calendar-container"></div>
            <script type="text/javascript">
                (function (C, A, L) { let p = function (a, ar) { a.q.push(ar); }; let d = C.document; C.Cal = C.Cal || function () { let cal = C.Cal; let ar = arguments; if (!cal.loaded) { cal.ns = {}; cal.q = cal.q || []; d.head.appendChild(d.createElement("script")).src = A; cal.loaded = true; } if (ar[0] === L) { const api = function () { p(api, arguments); }; const namespace = ar[1]; api.q = api.q || []; if(typeof namespace === "string"){cal.ns[namespace] = cal.ns[namespace] || api;p(cal.ns[namespace], ar);p(cal, ["initNamespace", namespace]);} else p(cal, ar); return;} p(cal, ar); }; })(window, "https://app.cal.com/embed/embed.js", "init");
                Cal("init", "hovor-s-tknurture", {origin:"https://cal.com"});

                Cal.ns["hovor-s-tknurture"]("inline", {
                    elementOrSelector:"#my-cal-inline",
                    config: {"layout":"month_view"},
                    calLink: "tknurture/hovor-s-tknurture",
                });

                Cal.ns["hovor-s-tknurture"]("ui", {
                    "cssVarsPerTheme":{
                        "light":{"cal-brand":"#000000"},
                        "dark":{"cal-brand":"#1e95b1"}
                    },
                    "hideEventTypeDetails":false,
                    "layout":"month_view"
                });
            </script>
        </div>
    </div>
</section>


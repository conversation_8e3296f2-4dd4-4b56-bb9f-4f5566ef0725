<?php
session_start();
require_once 'config.php';
require_once 'airtable_functions.php';

header('Content-Type: application/json');

try {
    if (!isset($_SESSION['user_id'])) {
        throw new Exception("Uživatel není <PERSON>");
    }

    $settings = getAirtableSettings($_SESSION['user_id']);
    
    // Test the connection
    $testData = fetchDataFromAirtable(
        $settings['airtable_api_key'],
        $settings['airtable_base_id'],
        $settings['airtable_table_id']
    );
    
    echo json_encode([
        'success' => true,
        'message' => 'Připojení k Airtable je funkční',
        'data' => [
            'base_id' => $settings['airtable_base_id'],
            'table_id' => $settings['airtable_table_id']
        ]
    ]);
    
} catch (Exception $e) {
    error_log("Airtable verification error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>


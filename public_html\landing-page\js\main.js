document.addEventListener("DOMContentLoaded", () => {
  // Mouse glow effect
  const mouseGlow = document.querySelector(".mouse-glow")
  let mouseX = 0,
    mouseY = 0
  let currentX = 0,
    currentY = 0

  document.addEventListener("mousemove", (e) => {
    mouseX = e.clientX
    mouseY = e.clientY
  })

  function animate() {
    const dx = mouseX - currentX
    const dy = mouseY - currentY

    currentX += dx * 0.1
    currentY += dy * 0.1

    mouseGlow.style.background = `radial-gradient(600px at ${currentX}px ${currentY}px, rgba(29, 78, 216, 0.15), transparent 80%)`

    requestAnimationFrame(animate)
  }

  animate()
})


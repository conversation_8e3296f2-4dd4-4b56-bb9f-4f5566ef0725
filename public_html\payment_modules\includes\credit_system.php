<?php
require_once __DIR__ . '/../../db_connection.php';
require_once __DIR__ . '/../../error_log.php';

function getUserCreditBalance($user_id) {
   try {
       // Check if the function exists and use the appropriate one
       if (function_exists('getDatabaseConnection')) {
           $db = getDatabaseConnection();
       } else if (function_exists('getDbConnection')) {
           $db = getDbConnection();
       } else {
           // Fallback to direct connection if needed
           $db = new mysqli(DB_HOST, DB_USER, DB_PASSWORD, DB_NAME);
           if ($db->connect_error) {
               throw new Exception("Database connection failed: " . $db->connect_error);
           }
       }
       
       // Check if user has a credit record
       $stmt = $db->prepare("SELECT balance FROM user_credits WHERE user_id = ?");
       $stmt->bind_param("i", $user_id);
       $stmt->execute();
       $result = $stmt->get_result();
       
       if ($result->num_rows > 0) {
           $row = $result->fetch_assoc();
           return floatval($row['balance']);
       } else {
           // Create initial credit record with 0 balance
           $stmt = $db->prepare("INSERT INTO user_credits (user_id, balance) VALUES (?, 0)");
           $stmt->bind_param("i", $user_id);
           $stmt->execute();
           return 0.0;
       }
   } catch (Exception $e) {
       writeErrorLog('Error getting user credit balance', [
           'user_id' => $user_id,
           'error' => $e->getMessage()
       ]);
       return 0.0; // Return 0 instead of throwing to prevent page errors
   }
}

function addUserCredits($user_id, $amount, $description = null) {
    try {
        // Check if the function exists and use the appropriate one
        if (function_exists('getDatabaseConnection')) {
            $db = getDatabaseConnection();
        } else if (function_exists('getDbConnection')) {
            $db = getDbConnection();
        } else {
            // Fallback to direct connection if needed
            $db = new mysqli(DB_HOST, DB_USER, DB_PASSWORD, DB_NAME);
            if ($db->connect_error) {
                throw new Exception("Database connection failed: " . $db->connect_error);
            }
        }

        $db->begin_transaction();

        // Update user's credit balance
        $stmt = $db->prepare("UPDATE user_credits SET balance = balance + ? WHERE user_id = ?");
        $stmt->bind_param("di", $amount, $user_id);
        $stmt->execute();

        // If no rows were affected, it means the user doesn't have a credit record yet
        if ($stmt->affected_rows == 0) {
            $stmt = $db->prepare("INSERT INTO user_credits (user_id, balance) VALUES (?, ?)");
            $stmt->bind_param("id", $user_id, $amount);
            $stmt->execute();
        }

        // Record the transaction in the credit history
        $stmt = $db->prepare("INSERT INTO credit_history (user_id, amount, description, transaction_type) VALUES (?, ?, ?, 'credit')");
        $stmt->bind_param("ids", $user_id, $amount, $description);
        $stmt->execute();

        $db->commit();
        return true;
    } catch (Exception $e) {
        if ($db) {
            $db->rollback();
        }
        writeErrorLog('Error adding user credits', [
            'user_id' => $user_id,
            'amount' => $amount,
            'description' => $description,
            'error' => $e->getMessage()
        ]);
        return false;
    }
}

function deductUserCredits($user_id, $amount, $description = null) {
    try {
        // Check if the function exists and use the appropriate one
        if (function_exists('getDatabaseConnection')) {
            $db = getDatabaseConnection();
        } else if (function_exists('getDbConnection')) {
            $db = getDbConnection();
        } else {
            // Fallback to direct connection if needed
            $db = new mysqli(DB_HOST, DB_USER, DB_PASSWORD, DB_NAME);
            if ($db->connect_error) {
                throw new Exception("Database connection failed: " . $db->connect_error);
            }
        }

        $db->begin_transaction();

        // Update user's credit balance
        $stmt = $db->prepare("UPDATE user_credits SET balance = balance - ? WHERE user_id = ? AND balance >= ?");
        $stmt->bind_param("dii", $amount, $user_id, $amount);
        $stmt->execute();

        // Check if the deduction was successful
        if ($stmt->affected_rows == 0) {
            $db->rollback();
            return false; // Insufficient balance
        }

        // Record the transaction in the credit history
        $stmt = $db->prepare("INSERT INTO credit_history (user_id, amount, description, transaction_type) VALUES (?, ?, ?, 'debit')");
        $stmt->bind_param("ids", $user_id, -$amount, $description); // Note the negative amount for debit
        $stmt->execute();

        $db->commit();
        return true;
    } catch (Exception $e) {
        if ($db) {
            $db->rollback();
        }
        writeErrorLog('Error deducting user credits', [
            'user_id' => $user_id,
            'amount' => $amount,
            'description' => $description,
            'error' => $e->getMessage()
        ]);
        return false;
    }
}

function getCreditPackages() {
    try {
        // Check if the function exists and use the appropriate one
        if (function_exists('getDatabaseConnection')) {
            $db = getDatabaseConnection();
        } else if (function_exists('getDbConnection')) {
            $db = getDbConnection();
        } else {
            // Fallback to direct connection if needed
            $db = new mysqli(DB_HOST, DB_USER, DB_PASSWORD, DB_NAME);
            if ($db->connect_error) {
                throw new Exception("Database connection failed: " . $db->connect_error);
            }
        }

        $stmt = $db->prepare("SELECT id, name, credits, price FROM credit_packages");
        $stmt->execute();
        $result = $stmt->get_result();

        $packages = [];
        while ($row = $result->fetch_assoc()) {
            $packages[] = $row;
        }

        return $packages;
    } catch (Exception $e) {
        writeErrorLog('Error getting credit packages', [
            'error' => $e->getMessage()
        ]);
        return [];
    }
}

function getUserCreditHistory($user_id, $limit = 10) {
    try {
        // Check if the function exists and use the appropriate one
        if (function_exists('getDatabaseConnection')) {
            $db = getDatabaseConnection();
        } else if (function_exists('getDbConnection')) {
            $db = getDbConnection();
        } else {
            // Fallback to direct connection if needed
            $db = new mysqli(DB_HOST, DB_USER, DB_PASSWORD, DB_NAME);
            if ($db->connect_error) {
                throw new Exception("Database connection failed: " . $db->connect_error);
            }
        }

        $stmt = $db->prepare("SELECT amount, description, transaction_type, created_at FROM credit_history WHERE user_id = ? ORDER BY created_at DESC LIMIT ?");
        $stmt->bind_param("ii", $user_id, $limit);
        $stmt->execute();
        $result = $stmt->get_result();

        $history = [];
        while ($row = $result->fetch_assoc()) {
            $history[] = $row;
        }

        return $history;
    } catch (Exception $e) {
        writeErrorLog('Error getting user credit history', [
            'user_id' => $user_id,
            'limit' => $limit,
            'error' => $e->getMessage()
        ]);
        return [];
    }
}

function getSmsPrice($user_id) {
   try {
       // Check if the function exists and use the appropriate one
       if (function_exists('getDatabaseConnection')) {
           $db = getDatabaseConnection();
       } else if (function_exists('getDbConnection')) {
           $db = getDbConnection();
       } else {
           // Fallback to direct connection if needed
           $db = new mysqli(DB_HOST, DB_USER, DB_PASSWORD, DB_NAME);
           if ($db->connect_error) {
               throw new Exception("Database connection failed: " . $db->connect_error);
           }
       }
       
       // Get user's subscription plan
       $stmt = $db->prepare("SELECT subscription_plan FROM users WHERE id = ?");
       $stmt->bind_param("i", $user_id);
       $stmt->execute();
       $result = $stmt->get_result();
       $userData = $result->fetch_assoc();
       
       if (!$userData) {
           return [
               'price_per_sms' => 0.75,
               'free_sms_limit' => 0
           ];
       }
       
       $plan = $userData['subscription_plan'];
       
       // Get SMS pricing for the plan
       $stmt = $db->prepare("
           SELECT price_per_sms, free_sms_limit 
           FROM sms_pricing 
           WHERE subscription_plan = ?
       ");
       $stmt->bind_param("s", $plan);
       $stmt->execute();
       $result = $stmt->get_result();
       
       if ($result->num_rows > 0) {
           return $result->fetch_assoc();
       } else {
           // Default pricing if plan not found
           return [
               'price_per_sms' => 0.75,
               'free_sms_limit' => 0
           ];
       }
   } catch (Exception $e) {
       writeErrorLog('Error getting SMS price', [
           'user_id' => $user_id,
           'error' => $e->getMessage()
       ]);
       return [
           'price_per_sms' => 0.75,
           'free_sms_limit' => 0
       ];
   }
}

function calculateSmsCost($user_id, $sms_count) {
    try {
        // Check if the function exists and use the appropriate one
        if (function_exists('getDatabaseConnection')) {
            $db = getDatabaseConnection();
        } else if (function_exists('getDbConnection')) {
            $db = getDbConnection();
        } else {
            // Fallback to direct connection if needed
            $db = new mysqli(DB_HOST, DB_USER, DB_PASSWORD, DB_NAME);
            if ($db->connect_error) {
                throw new Exception("Database connection failed: " . $db->connect_error);
            }
        }

        $smsPricing = getSmsPrice($user_id);
        $pricePerSms = $smsPricing['price_per_sms'];
        $freeSmsLimit = $smsPricing['free_sms_limit'];

        if ($sms_count <= $freeSmsLimit) {
            return 0.0; // All SMS are free
        } else {
            $chargeableSms = $sms_count - $freeSmsLimit;
            return $chargeableSms * $pricePerSms;
        }
    } catch (Exception $e) {
        writeErrorLog('Error calculating SMS cost', [
            'user_id' => $user_id,
            'sms_count' => $sms_count,
            'error' => $e->getMessage()
        ]);
        return -1; // Indicate an error
    }
}
?>


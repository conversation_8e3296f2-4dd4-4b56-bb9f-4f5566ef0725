<?php
// Hlavní soubor pro SEO - importuje všechny potřebné soubory a poskytuje hlavní funkce

// Načtení všech SEO souborů
require_once 'seo/config.php';
require_once 'seo/schema.php';
require_once 'seo/headers.php';
require_once 'seo/sitemap.php';
require_once 'seo/analytics.php';
require_once 'seo/meta-tags.php';

// Funkce pro inicializaci SEO
function init_seo() {
    // Nastavení HTTP hlaviček
    set_all_headers();
    
    // Generování sitemap.xml
    generate_sitemap();
}

// Funkce pro získání SEO hlavičky pro konkrétní stránku
function get_seo_head($page_name = 'home') {
    global $seo_config;
    
    // Získání meta dat pro stránku
    $page_meta = get_page_meta($page_name);
    
    // Vytvoření meta tagů
    $meta = $seo_config['meta'];
    $meta['title'] = $page_meta['title'];
    $meta['description'] = $page_meta['description'];
    
    // Vytvoření kanonické URL
    $path = ($page_name === 'home') ? '' : $page_name;
    $meta['canonical'] = 'https://www.dentibot.eu/' . $path;
    
    // Generování všech meta tagů
    $output = generate_all_meta_tags($meta, $seo_config['og']);
    
    // Přidání analytických nástrojů
    $output .= get_all_analytics();
    
    return $output;
}

// Funkce pro získání strukturovaných dat pro stránku
function get_seo_structured_data($type = 'software') {
    $schema = get_structured_data($type);
    
    return '<script type="application/ld+json">' . PHP_EOL . 
           json_encode($schema, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . 
           PHP_EOL . '</script>';
}

// Inicializace SEO při načtení souboru
init_seo();
?>


document.addEventListener('DOMContentLoaded', function() {
    // Mobile menu toggle
    const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
    const mobileNavContainer = document.querySelector('.mobile-nav-container');
    const navMenu = document.querySelector('.nav-menu');
    
    if (mobileMenuToggle && mobileNavContainer) {
        mobileMenuToggle.addEventListener('click', function() {
            this.classList.toggle('active');
            mobileNavContainer.classList.toggle('active');
            document.body.classList.toggle('menu-open');
        });
        
        // Close menu when clicking on links
        const navLinks = navMenu.querySelectorAll('a');
        navLinks.forEach(link => {
            link.addEventListener('click', function() {
                mobileMenuToggle.classList.remove('active');
                mobileNavContainer.classList.remove('active');
                document.body.classList.remove('menu-open');
            });
        });
    }
    
    // Handle touch events for the mouse glow effect
    const mouseGlow = document.querySelector('.mouse-glow');
    if (mouseGlow) {
        // Disable mouse glow on mobile
        mouseGlow.style.display = 'none';
    }
    
    // Optimize scroll performance on mobile
    let ticking = false;
    window.addEventListener('scroll', function() {
        if (!ticking) {
            window.requestAnimationFrame(function() {
                // Handle scroll-based animations here
                ticking = false;
            });
            ticking = true;
        }
    });
});
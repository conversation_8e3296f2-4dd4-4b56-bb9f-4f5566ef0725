<?php
require_once 'config.php';
require_once 'api_functions.php';
require_once 'error_log.php';

function syncVapiData() {
    try {
        writeErrorLog('Starting sync process');
        
        // Get current API configuration first
        $apiConfig = getCurrentUserApiKey();
        if (!$apiConfig || empty($apiConfig['key']) || empty($apiConfig['url'])) {
            writeErrorLog('No API key or URL found', ['config' => $apiConfig]);
            throw new Exception("API klíč nebo URL není nastaveno. Prosím nastavte je v sekci Nastavení.");
        }

        // Add check for assistant_id
        if (empty($apiConfig['assistant_id'])) {
            writeErrorLog('Missing assistant_id', ['api_config' => $apiConfig]);
            throw new Exception("ID asistenta není nastaveno. Prosím nastavte jej v sekci Nastavení.");
        }

        writeErrorLog('API Config loaded', ['url' => $apiConfig['url']]);

        // Get data from API
        $endDate = date('Y-m-d');
        $startDate = date('Y-m-d', strtotime('-30 days'));
        
        writeErrorLog('Fetching data from API', [
            'startDate' => $startDate,
            'endDate' => $endDate,
            'api_url' => $apiConfig['url']
        ]);

        $calls = getCallHistoryFromAPI($apiConfig['key'], $apiConfig['url'], $startDate, $endDate, $apiConfig['assistant_id']);

        // Add logging after API call
        writeErrorLog('API Response', [
            'calls_count' => count($calls),
            'start_date' => $startDate,
            'end_date' => $endDate,
            'assistant_id' => $apiConfig['assistant_id']
        ]);

        if (empty($calls)) {
            writeErrorLog('No data returned from API', [
                'api_config' => $apiConfig,
                'start_date' => $startDate,
                'end_date' => $endDate
            ]);
            return "Žádná data nejsou k dispozici pro zadané období. Zkontrolujte prosím nastavení API a rozsah dat.";
        }

        writeErrorLog('Retrieved calls from API', ['count' => count($calls)]);

        // Process the calls data for direct use
        $processedCalls = processCallsData($calls);
        
        // Store the processed data in session for immediate use
        $_SESSION['vapi_calls_data'] = $processedCalls;
        $_SESSION['last_sync'] = time();
        $_SESSION['data_synced'] = true;
        
        writeErrorLog('Sync completed successfully', [
            'records_processed' => count($processedCalls),
            'last_sync_time' => date('Y-m-d H:i:s')
        ]);
        
        return true;

    } catch (Exception $e) {
        writeErrorLog('Sync error', [
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
        return "Synchronizace selhala: " . $e->getMessage();
    }
}

// Process the raw calls data into a format ready for dashboard use
function processCallsData($calls) {
    $processedCalls = [];
    
    foreach ($calls as $call) {
        // Calculate duration
        $duration = 0;
        if (!empty($call['startedAt']) && !empty($call['endedAt'])) {
            $startTime = new DateTime($call['startedAt']);
            $endTime = new DateTime($call['endedAt']);
            $duration = $endTime->getTimestamp() - $startTime->getTimestamp();
        }

        // Format dates
        $createdAt = !empty($call['createdAt']) ? date('Y-m-d H:i:s', strtotime($call['createdAt'])) : null;
        $startedAt = !empty($call['startedAt']) ? date('Y-m-d H:i:s', strtotime($call['startedAt'])) : null;
        $endedAt = !empty($call['endedAt']) ? date('Y-m-d H:i:s', strtotime($call['endedAt'])) : null;
        
        // Create a processed call record
        $processedCall = [
            'call_id' => $call['id'],
            'status' => $call['status'] ?? 'unknown',
            'direction' => $call['type'] ?? 'unknown',
            'from_number' => $call['customer']['number'] ?? null,
            'to_number' => $call['phoneNumber']['twilioPhoneNumber'] ?? null,
            'created_at' => $createdAt,
            'started_at' => $startedAt,
            'ended_at' => $endedAt,
            'duration' => $duration,
            'cost' => $call['cost'] ?? 0,
            'currency' => 'USD',
            'ended_reason' => $call['endedReason'] ?? null,
            'transcript' => $call['transcript'] ?? ''
        ];
        
        $processedCalls[] = $processedCall;
    }
    
    return $processedCalls;
}

// If script is run directly
if (basename(__FILE__) == basename($_SERVER["SCRIPT_FILENAME"])) {
    try {
        $result = syncVapiData();
        if ($result === true) {
            echo "Synchronizace byla úspěšně dokončena.";
        } else {
            echo "Synchronizace selhala: " . $result;
        }
    } catch (Exception $e) {
        echo "Chyba: " . $e->getMessage();
    }
}


<?php
if (!defined('DENTIBOT_LOADED')) {
    define('DENTIBOT_LOADED', true);
}

// Include configuration file
require_once __DIR__ . '/config.php';

// Initialize connection variable
$conn = null;

// Database connection with error handling
try {
    mysqli_report(MYSQLI_REPORT_ERROR | MYSQLI_REPORT_STRICT); // Enable error reporting
    
    $conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
    
    // Check connection
    if ($conn->connect_error) {
        error_log("Connection failed: " . $conn->connect_error);
        throw new Exception("Database connection failed");
    }
    
    // Set charset to utf8mb4
    if (!$conn->set_charset("utf8mb4")) {
        error_log("Error loading character set utf8mb4: " . $conn->error);
        throw new Exception("Failed to set character set");
    }
    
} catch (Exception $e) {
    error_log("Database connection error: " . $e->getMessage());
    $conn = null;
}

// Function to execute a query with error handling
function executeQuery($sql, $params = []) {
    global $conn;
    try {
        if ($conn === null) {
            throw new Exception("Database connection is not available");
        }
        
        $stmt = $conn->prepare($sql);
        
        if ($stmt === false) {
            throw new Exception("Failed to prepare statement: " . $conn->error);
        }
        
        if (!empty($params)) {
            $types = str_repeat('s', count($params));
            $stmt->bind_param($types, ...$params);
        }
        
        if (!$stmt->execute()) {
            throw new Exception("Failed to execute query: " . $stmt->error);
        }
        
        return $stmt;
    } catch (Exception $e) {
        error_log("Query execution error: " . $e->getMessage());
        throw $e;
    }
}

// Function to fetch all results from a query
function fetchAll($sql, $params = []) {
    try {
        $stmt = executeQuery($sql, $params);
        $result = $stmt->get_result();
        $data = $result->fetch_all(MYSQLI_ASSOC);
        $stmt->close();
        return $data;
    } catch (Exception $e) {
        error_log("FetchAll error: " . $e->getMessage());
        return [];
    }
}

// Function to fetch a single row from a query
function fetchOne($sql, $params = []) {
    try {
        $stmt = executeQuery($sql, $params);
        $result = $stmt->get_result();
        $data = $result->fetch_assoc();
        $stmt->close();
        return $data;
    } catch (Exception $e) {
        error_log("FetchOne error: " . $e->getMessage());
        return null;
    }
}

// Function to insert data and return the last inserted ID
function insert($sql, $params = []) {
    global $conn;
    try {
        $stmt = executeQuery($sql, $params);
        $id = $conn->insert_id;
        $stmt->close();
        return $id;
    } catch (Exception $e) {
        error_log("Insert error: " . $e->getMessage());
        throw $e;
    }
}

// Function to update or delete data and return the number of affected rows
function update($sql, $params = []) {
    global $conn;
    try {
        $stmt = executeQuery($sql, $params);
        $affectedRows = $stmt->affected_rows;
        $stmt->close();
        return $affectedRows;
    } catch (Exception $e) {
        error_log("Update error: " . $e->getMessage());
        throw $e;
    }
}
?>














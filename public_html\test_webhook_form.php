<?php
require_once __DIR__ . '/../config.php';

// Zpracování formuláře
$result = null;
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    require_once __DIR__ . '/test_webhook.php';
    $result = testWebhook($_POST['username'] ?? null, $_POST['message'] ?? null);
}
?>
<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Webhook</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
</head>
<body class="bg-gray-100 min-h-screen p-8">
    <div class="max-w-2xl mx-auto">
        <div class="bg-white rounded-lg shadow-md p-6">
            <h1 class="text-2xl font-bold mb-6">Test Webhook Notifikací</h1>
            
            <form method="POST" class="space-y-4">
                <div>
                    <label for="username" class="block text-sm font-medium text-gray-700">Uživatelské jméno</label>
                    <input type="text" 
                           id="username" 
                           name="username" 
                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                           placeholder="Zadejte uživatelské jméno">
                </div>
                
                <div>
                    <label for="message" class="block text-sm font-medium text-gray-700">Testovací zpráva</label>
                    <textarea id="message" 
                              name="message" 
                              rows="3" 
                              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                              placeholder="Zadejte testovací zprávu"></textarea>
                </div>
                
                <button type="submit" 
                        class="inline-flex justify-center rounded-md border border-transparent bg-indigo-600 py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2">
                    Odeslat test
                </button>
            </form>

            <?php if ($result): ?>
            <div class="mt-6">
                <h2 class="text-lg font-semibold mb-2">Výsledek testu:</h2>
                <div class="bg-gray-50 rounded-md p-4">
                    <pre class="text-sm overflow-auto"><?php echo json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE); ?></pre>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>


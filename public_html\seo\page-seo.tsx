import { getPageKeywords } from "@/seo/keywords"
import SEOHead from "@/seo/head"
import StructuredData from "@/seo/structured-data"

interface PageSEOProps {
  page: string
  title?: string
  description?: string
  ogImage?: string
  structuredDataType?: "business" | "faq"
  structuredData?: any
}

export default function PageSEO({
  page,
  title,
  description,
  ogImage,
  structuredDataType = "business",
  structuredData,
}: PageSEOProps) {
  const keywords = getPageKeywords(page).join(", ")

  const defaultTitles: Record<string, string> = {
    home: "DentiBot | Moderní řešení pro vaši zubní ordinaci",
    sluzby: "Služby | DentiBot - Automatizace zubní ordinace",
    "o-nas": "O nás | DentiBot - Tým za vaším úspěchem",
    kontakt: "Kontakt | DentiBot - Jsme tu pro vás",
    blog: "Blog | DentiBot - Novinky ze světa stomatologie",
    cenik: "Ceník | DentiBot - Transparentní ceny našich služeb",
    reference: "Reference | DentiBot - Spokojení zákazníci",
    faq: "Časté dotazy | DentiBot - Odpovědi na vaše otázky",
  }

  const defaultDescriptions: Record<string, string> = {
    home: "Automatizujte komunikaci s pacienty a zvyšte efektivitu vaší ordinace pomocí našeho inteligentního voicebot systému.",
    sluzby:
      "Objevte kompletní nabídku služeb DentiBot pro automatizaci vaší zubní ordinace - od voicebotu po rezervační systém.",
    "o-nas":
      "Poznejte tým DentiBot - specialisty na automatizaci a digitalizaci zubních ordinací s mnohaletými zkušenostmi.",
    kontakt: "Kontaktujte nás pro více informací o DentiBot. Jsme připraveni odpovědět na všechny vaše dotazy.",
    blog: "Přečtěte si nejnovější články o trendech v digitalizaci zubních ordinací a tipy pro efektivnější komunikaci s pacienty.",
    cenik: "Prohlédněte si transparentní ceník služeb DentiBot. Nabízíme řešení pro ordinace všech velikostí.",
    reference:
      "Přečtěte si, co o DentiBot říkají naši spokojení zákazníci. Stovky zubních ordinací již využívají naše služby.",
    faq: "Najděte odpovědi na nejčastější otázky o DentiBot a našich službách pro automatizaci zubních ordinací.",
  }

  const pageTitle = title || defaultTitles[page] || defaultTitles["home"]
  const pageDescription = description || defaultDescriptions[page] || defaultDescriptions["home"]

  return (
    <>
      <SEOHead title={pageTitle} description={pageDescription} keywords={keywords} ogImage={ogImage} />
      <StructuredData type={structuredDataType} data={structuredData} />
    </>
  )
}


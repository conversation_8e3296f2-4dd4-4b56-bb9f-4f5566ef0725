<?php
require_once 'config.php';
require_once 'error_log.php';

$callEndReasonTranslations = [
    'customer-ended-call' => 'Zákazník ukončil hovor',
    'customer-busy' => 'Pacient byl nedostupný',
    'assistant-ended-call' => 'Pacient ukončil hovor',
    'customer-did-not-give-microphone-permission' => 'Pacient nepovolil mikrofon',
    'twilio-failed-to-connect-call' => 'Chyba připojení hovoru',
    'silence-timed-out' => 'Pacient moc dlouho mlčel',
    'unknown' => 'Neznámý důvod'
];

/**
 * Získá data hovorů přímo z Vapi API
 */
function getCallDataFromVapi($dateRange = '30 DAY') {
    try {
        // Získání API konfigurace
        $apiConfig = getCurrentUserApiKey();
        if (!$apiConfig || empty($apiConfig['key']) || empty($apiConfig['assistant_id'])) {
            writeErrorLog('No API key or assistant_id found', ['config' => $apiConfig]);
            throw new Exception("API klíč nebo ID asistenta není nastaveno. Prosím nastavte je v sekci Nastavení.");
        }

        // Nastavení časového rozsahu
        $endDate = date('Y-m-d');
        $startDate = date('Y-m-d', strtotime('-' . str_replace(' DAY', '', $dateRange) . ' days'));
        
        // Získání dat z API
        $calls = getCallHistoryFromAPI($apiConfig['key'], $apiConfig['url'], $startDate, $endDate, $apiConfig['assistant_id']);
        
        if (empty($calls)) {
            writeErrorLog('No data returned from API', [
                'api_config' => $apiConfig,
                'start_date' => $startDate,
                'end_date' => $endDate
            ]);
            return [];
        }
        
        writeErrorLog('Retrieved calls from API', ['count' => count($calls)]);
        return $calls;
        
    } catch (Exception $e) {
        writeErrorLog('Error fetching data from Vapi', [
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
        throw $e;
    }
}

/**
 * Získá analytická data přímo z Vapi API
 */
function getDirectAnalytics($dateRange = '30 DAY') {
    try {
        $calls = getCallDataFromVapi($dateRange);
        
        if (empty($calls)) {
            return ['data' => []];
        }
        
        // Zpracování dat podle data
        $analyticsData = [];
        foreach ($calls as $call) {
            if (empty($call['createdAt'])) {
                continue;
            }
            
            $date = date('Y-m-d', strtotime($call['createdAt']));
            
            if (!isset($analyticsData[$date])) {
                $analyticsData[$date] = [
                    'date' => $date,
                    'total_calls' => 0,
                    'total_duration' => 0,
                    'total_cost' => 0,
                    'completed_calls' => 0,
                    'successful_calls' => 0
                ];
            }
            
            // Výpočet trvání hovoru
            $duration = 0;
            if (!empty($call['startedAt']) && !empty($call['endedAt'])) {
                $startTime = new DateTime($call['startedAt']);
                $endTime = new DateTime($call['endedAt']);
                $duration = $endTime->getTimestamp() - $startTime->getTimestamp();
            }
            
            $analyticsData[$date]['total_calls']++;
            $analyticsData[$date]['total_duration'] += $duration;
            $analyticsData[$date]['total_cost'] += isset($call['cost']) ? (float)$call['cost'] : 0;
            
            // Kontrola dokončených hovorů
            if (isset($call['status']) && $call['status'] === 'ended') {
                $analyticsData[$date]['completed_calls']++;
            }
            
            // Kontrola úspěšnosti hovoru podle successEvaluation
            if (isset($call['analysis']) && isset($call['analysis']['successEvaluation'])) {
                $successEval = $call['analysis']['successEvaluation'];
                if ($successEval === true || $successEval === 'true' || $successEval === '1' || $successEval === 1) {
                    $analyticsData[$date]['successful_calls']++;
                }
            }
        }
        
        // Seřazení dat podle data sestupně
        $result = array_values($analyticsData);
        usort($result, function($a, $b) {
            return strtotime($b['date']) - strtotime($a['date']);
        });
        
        return ['data' => $result];
        
    } catch (Exception $e) {
        writeErrorLog('Error in getDirectAnalytics', [
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
        return ['data' => [], 'error' => $e->getMessage()];
    }
}

/**
 * Získá špičkové hodiny přímo z Vapi API
 */
function getDirectPeakHours($dateRange = '30 DAY') {
    try {
        $calls = getCallDataFromVapi($dateRange);
        
        if (empty($calls)) {
            return ['data' => []];
        }
        
        // Zpracování dat podle hodiny
        $hourData = [];
        foreach ($calls as $call) {
            if (empty($call['createdAt'])) {
                continue;
            }
            
            $date = date('Y-m-d', strtotime($call['createdAt']));
            $hour = date('G', strtotime($call['createdAt'])); // 0-23 formát
            
            $key = $hour . '_' . $date;
            if (!isset($hourData[$key])) {
                $hourData[$key] = [
                    'hour' => (int)$hour,
                    'date' => $date,
                    'total_calls' => 0
                ];
            }
            
            $hourData[$key]['total_calls']++;
        }
        
        // Seřazení dat podle počtu hovorů sestupně
        $result = array_values($hourData);
        usort($result, function($a, $b) {
            return $b['total_calls'] - $a['total_calls'];
        });
        
        return ['data' => $result];
        
    } catch (Exception $e) {
        writeErrorLog('Error in getDirectPeakHours', [
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
        return ['data' => [], 'error' => $e->getMessage()];
    }
}

/**
 * Získá nejčastější důvody ukončení hovorů přímo z Vapi API
 */
function getDirectTopReasons($dateRange = '30 DAY') {
    try {
        $calls = getCallDataFromVapi($dateRange);
        
        if (empty($calls)) {
            return [];
        }
        
        // Zpracování důvodů ukončení
        $reasons = [];
        foreach ($calls as $call) {
            $reason = isset($call['endedReason']) ? $call['endedReason'] : 'unknown';
            
            if (!isset($reasons[$reason])) {
                $reasons[$reason] = 0;
            }
            
            $reasons[$reason]++;
        }
        
        // Seřazení důvodů podle četnosti
        arsort($reasons);
        
        // Omezení na 5 nejčastějších důvodů
        $reasons = array_slice($reasons, 0, 5, true);
        
        // Překlad důvodů
        $translatedReasons = [];
        global $callEndReasonTranslations;
        
        foreach ($reasons as $reason => $count) {
            $translatedReason = isset($callEndReasonTranslations[$reason]) ? 
                $callEndReasonTranslations[$reason] : $reason;
            $translatedReasons[$translatedReason] = $count;
        }
        
        return $translatedReasons;
        
    } catch (Exception $e) {
        writeErrorLog('Error in getDirectTopReasons', [
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
        return [];
    }
}

/**
 * Vypočítá metriky přímo z dat Vapi API
 */
function getDirectMetrics($dateRange = '30 DAY') {
    try {
        $analytics = getDirectAnalytics($dateRange);
        
        $totalCalls = 0;
        $totalDuration = 0;
        $totalCost = 0;
        $completedCalls = 0;
        $successfulCalls = 0;
        
        if (!empty($analytics['data'])) {
            foreach ($analytics['data'] as $day) {
                $totalCalls += (int)$day['total_calls'];
                $totalDuration += (int)$day['total_duration'];
                $totalCost += (float)$day['total_cost'];
                $completedCalls += (int)$day['completed_calls'];
                $successfulCalls += (int)$day['successful_calls'];
            }
        }
        
        // Výpočet průměrů
        $avgDuration = $totalCalls > 0 ? (int)($totalDuration / $totalCalls) : 0;
        
        // Výpočet úspěšnosti na základě successEvaluation
        $successRate = $totalCalls > 0 ? (int)(($successfulCalls / $totalCalls) * 100) : 0;
        
        return [
            [
                'label' => 'Spotřeba minut',
                'value' => (string)ceil($totalDuration / 60),
                'subValue' => 'Celkem: ' . ceil($totalDuration / 60) . ' minut',
                'trend' => '+0%',
                'highlight' => false
            ],
            [
                'label' => 'Průměrná délka hovoru',
                'value' => gmdate("i:s", $avgDuration),
                'subValue' => 'minut',
                'trend' => '+0%',
                'highlight' => false
            ],
            [
                'label' => 'Úspěšnost hovorů',
                'value' => $successRate . '%',
                'subValue' => 'Z celkových ' . $totalCalls . ' hovorů',
                'trend' => '+0%',
                'highlight' => false
            ]
        ];
        
    } catch (Exception $e) {
        writeErrorLog('Error in getDirectMetrics', [
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
        
        // Vrátit výchozí metriky v případě chyby
        return getDefaultMetrics();
    }
}

/**
 * Získá data pro graf přímo z Vapi API
 */
function getDirectGraphData($dateRange = '30 DAY') {
    try {
        $analytics = getDirectAnalytics($dateRange);
        
        if (empty($analytics['data'])) {
            return getDefaultGraphData();
        }
        
        $calls = [];
        $dates = [];
        
        // Seřazení dat podle data vzestupně pro graf
        $sortedData = $analytics['data'];
        usort($sortedData, function($a, $b) {
            return strtotime($a['date']) - strtotime($b['date']);
        });
        
        foreach ($sortedData as $day) {
            $calls[] = (int)$day['total_calls'];
            $dates[] = date('d.m.', strtotime($day['date']));
        }
        
        // Získání dat pro špičkové hodiny
        $peakHoursData = getDirectPeakHours($dateRange);
        $peakHours = [];
        $hours = [];
        
        if (!empty($peakHoursData['data'])) {
            // Omezení na top 10 hodin
            $topHours = array_slice($peakHoursData['data'], 0, 10);
            
            foreach ($topHours as $hourData) {
                $peakHours[] = (int)$hourData['total_calls'];
                $hours[] = $hourData['hour'] . ':00';
            }
        }
        
        return [
            'calls' => $calls,
            'dates' => $dates,
            'peakHours' => $peakHours,
            'hours' => $hours
        ];
        
    } catch (Exception $e) {
        writeErrorLog('Error in getDirectGraphData', [
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
        
        return getDefaultGraphData();
    }
}

/**
 * Původní funkce pro získání dat z API - zachována pro kompatibilitu
 */
function getCallHistoryFromAPI($apiKey, $apiUrl, $startDate, $endDate, $assistantId) {
    writeErrorLog("Starting API call", [
        'url' => $apiUrl,
        'startDate' => $startDate,
        'endDate' => $endDate,
        'assistantId' => $assistantId
    ]);
    
    if (empty($apiKey)) {
        writeErrorLog("API call failed - missing API key");
        throw new Exception("API klíč není nastaven");
    }

    $url = rtrim($apiUrl, '/') . '/call';
    $headers = [
        'Authorization: Bearer ' . $apiKey,
        'Content-Type: application/json',
        'Accept: application/json'
    ];

    // Ensure dates are in ISO 8601 format
    $startDateTime = new DateTime($startDate);
    $endDateTime = new DateTime($endDate);
    $startDateTime->setTime(0, 0, 0);
    $endDateTime->setTime(23, 59, 59);

    $queryParams = http_build_query([
        'createdAtGe' => $startDateTime->format('c'),
        'createdAtLe' => $endDateTime->format('c'),
        'limit' => 1000,
        'assistantId' => $assistantId
    ]);
    
    $fullUrl = $url . '?' . $queryParams;
    writeErrorLog("Making API request", ['full_url' => $fullUrl]);

    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $fullUrl,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_HTTPHEADER => $headers,
        CURLOPT_SSL_VERIFYPEER => true,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_VERBOSE => true,
        CURLOPT_HEADER => true
    ]);

    $verbose = fopen('php://temp', 'w+');
    curl_setopt($ch, CURLOPT_STDERR, $verbose);

    $response = curl_exec($ch);
    $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
    $responseHeaders = substr($response, 0, $headerSize);
    $responseBody = substr($response, $headerSize);
    
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);

    rewind($verbose);
    $verboseLog = stream_get_contents($verbose);

    curl_close($ch);

    writeErrorLog("API Response Info", [
        'http_code' => $httpCode,
        'headers' => $responseHeaders,
        'error' => $error,
        'verbose_log' => $verboseLog
    ]);

    if ($error) {
        writeErrorLog("CURL Error", ['error' => $error]);
        throw new Exception("Chyba komunikace s API: " . $error);
    }

    if ($httpCode === 400) {
        writeErrorLog("API Bad Request", [
            'response' => $responseBody,
            'request_url' => $fullUrl,
            'assistant_id' => $assistantId
        ]);
        throw new Exception("Neplatný požadavek na API. Zkontrolujte formát dat a platnost assistant_id.");
    }

    if ($httpCode === 401) {
        writeErrorLog("API Authentication Failed", ['response' => $responseBody]);
        throw new Exception("Neplatný API klíč nebo nedostatečná oprávnění.");
    }

    if ($httpCode !== 200) {
        writeErrorLog("API Error", [
            'http_code' => $httpCode,
            'response' => $responseBody
        ]);
        throw new Exception("API vrátila chybový kód: " . $httpCode);
    }

    $data = json_decode($responseBody, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        writeErrorLog("JSON Parse Error", [
            'error' => json_last_error_msg(),
            'response' => $responseBody
        ]);
        throw new Exception("Chyba při zpracování odpovědi z API");
    }

    if (!is_array($data)) {
        writeErrorLog("Invalid API Response", [
            'response_type' => gettype($data),
            'response' => $responseBody
        ]);
        throw new Exception("Neplatný formát odpovědi z API");
    }

    writeErrorLog("API call successful", [
        'records_count' => count($data),
        'first_record' => !empty($data) ? json_encode($data[0]) : 'no records'
    ]);
    
    if (empty($data)) {
        writeErrorLog("API returned empty data", [
            'http_code' => $httpCode,
            'response_body' => $responseBody
        ]);
    }

    return $data;
}

// Zachování původních funkcí pro kompatibilitu
function getDefaultMetrics() {
    return [
        [
            'label' => 'Spotřeba minut',
            'value' => '35',
            'subValue' => 'Celkem: 35 minut',
            'trend' => '+0%',
            'highlight' => false
        ],
        [
            'label' => 'Průměrná délka hovoru',
            'value' => '01:08',
            'subValue' => 'minut',
            'trend' => '+0%',
            'highlight' => false
        ],
        [
            'label' => 'Úspěšnost hovorů',
            'value' => '100%',
            'subValue' => 'Z celkových 31 hovorů',
            'trend' => '+0%',
            'highlight' => false
        ]
    ];
}

function getDefaultGraphData() {
    return [
        'calls' => [],
        'dates' => [],
        'peakHours' => [],
        'hours' => []
    ];
}
?>


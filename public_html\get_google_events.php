<?php
require_once 'config.php';
require_once 'GoogleCalendarSync.php';

if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

$start = $_GET['start'] ?? null;
$end = $_GET['end'] ?? null;

if (!$start || !$end) {
    http_response_code(400);
    echo json_encode(['error' => 'Missing start or end date']);
    exit;
}

try {
    $googleCalendarSync = new GoogleCalendarSync();
    $events = $googleCalendarSync->getGoogleEvents($_SESSION['user_id'], $start, $end);
    echo json_encode($events);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}


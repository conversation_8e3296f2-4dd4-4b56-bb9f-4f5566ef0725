import { Chart } from "@/components/ui/chart"
document.addEventListener("DOMContentLoaded", () => {
  const isLightTheme = document.body.classList.contains("light-theme")

  // Common chart options
  const commonOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: { display: false },
    },
    scales: {
      y: {
        beginAtZero: true,
        grid: {
          color: isLightTheme ? "rgba(226, 232, 240, 0.5)" : "rgba(51, 65, 85, 0.5)",
        },
        ticks: {
          color: isLightTheme ? "#475569" : "#cbd5e1",
          stepSize: 5,
          maxTicksLimit: 5,
        },
      },
      x: {
        grid: {
          display: false,
        },
        ticks: {
          color: isLightTheme ? "#475569" : "#cbd5e1",
        },
      },
    },
  }

  // Date formatting helper
  const formatDate = (dateStr) => {
    try {
      const date = new Date(dateStr)
      return new Intl.DateTimeFormat("cs-CZ", {
        day: "2-digit",
        month: "2-digit",
      }).format(date)
    } catch (e) {
      console.error("Error formatting date:", e)
      return ""
    }
  }

  // Initialize calls chart
  const dates = JSON.parse(document.getElementById("chartData").dataset.dates || "[]")
  const calls = JSON.parse(document.getElementById("chartData").dataset.calls || "[]")

  let callsChart // Declare callsChart
  if (dates && dates.length > 0) {
    callsChart = new Chart(document.getElementById("callsChart"), {
      type: "line",
      data: {
        labels: dates.map(formatDate),
        datasets: [
          {
            label: "Počet hovorů",
            data: calls,
            borderColor: "#0d9488",
            backgroundColor: "rgba(13, 148, 136, 0.1)",
            tension: 0.1,
            fill: true,
          },
        ],
      },
      options: {
        ...commonOptions,
        elements: {
          point: { radius: 0 },
          line: { borderWidth: 2 },
        },
        interaction: {
          intersect: false,
          mode: "index",
        },
        plugins: {
          tooltip: {
            enabled: true,
            callbacks: {
              label: (context) => `Počet hovorů: ${context.parsed.y}`,
            },
          },
        },
      },
    })
  }

  // Initialize peak hours chart
  const hours = JSON.parse(document.getElementById("peakData").dataset.hours || "[]")
  const peakDates = JSON.parse(document.getElementById("peakData").dataset.dates || "[]")
  const peakCounts = JSON.parse(document.getElementById("peakData").dataset.counts || "[]")

  let peakHoursChart // Declare peakHoursChart
  if (hours && hours.length > 0) {
    peakHoursChart = new Chart(document.getElementById("peakHoursChart"), {
      type: "bar",
      data: {
        labels: hours.map((hour, index) => {
          const date = peakDates[index]
          let formattedDate = ""
          try {
            if (date) {
              const dateObj = new Date(date.replace(" ", "T"))
              formattedDate = new Intl.DateTimeFormat("cs-CZ", {
                day: "2-digit",
                month: "2-digit",
              }).format(dateObj)
            }
          } catch (e) {
            console.error("Error formatting peak hour date:", e)
            formattedDate = ""
          }
          return `${String(hour).padStart(2, "0")}:00${formattedDate ? ` (${formattedDate})` : ""}`
        }),
        datasets: [
          {
            label: "Počet hovorů",
            data: peakCounts,
            backgroundColor: "rgba(13, 148, 136, 0.5)",
            borderColor: "#0d9488",
            borderWidth: 1,
          },
        ],
      },
      options: {
        ...commonOptions,
        scales: {
          x: {
            ticks: {
              maxRotation: 45,
              minRotation: 45,
            },
          },
          y: commonOptions.scales.y,
        },
        plugins: {
          tooltip: {
            callbacks: {
              label: (context) => `Počet hovorů: ${context.parsed.y}`,
            },
          },
        },
      },
    })
  }

  // Export charts for theme updates
  window.dashboardCharts = {
    calls: callsChart,
    peakHours: peakHoursChart,
  }
})


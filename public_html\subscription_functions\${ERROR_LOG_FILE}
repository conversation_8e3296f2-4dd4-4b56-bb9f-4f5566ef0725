[15-Feb-2025 00:54:12 Europe/Prague] PHP Fatal error:  Uncaught Error: Undefined constant "MAKE_WEBHOOK_URL" in /home/<USER>/domains/dentibot.eu/public_html/subscription_functions/test_webhook_form.php:6
Stack trace:
#0 /home/<USER>/domains/dentibot.eu/public_html/subscription_functions/test_webhook_form.php(85): testWebhook()
#1 {main}
  thrown in /home/<USER>/domains/dentibot.eu/public_html/subscription_functions/test_webhook_form.php on line 6
[15-Feb-2025 00:55:18 Europe/Prague] Webhook Test: {"username":"test_user","http_code":200,"response":"Accepted","timestamp":"2025-02-15 00:55:18"}
[15-Feb-2025 01:09:38 Europe/Prague] TEST: Failed to send low minutes notification: {"user_id":4,"username":"matyastroup"}
[15-Feb-2025 01:09:38 Europe/Prague] TEST: Failed to send low minutes notification: {"user_id":38,"username":"<PERSON><PERSON>j<PERSON>Dornak"}
[15-Feb-2025 01:09:38 Europe/Prague] TEST: Failed to send low minutes notification: {"user_id":40,"username":"MartanRak"}
[15-Feb-2025 01:09:57 Europe/Prague] TEST: Failed to send low minutes notification: {"user_id":4,"username":"matyastroup"}
[15-Feb-2025 01:09:57 Europe/Prague] TEST: Failed to send low minutes notification: {"user_id":38,"username":"VojtaDornak"}
[15-Feb-2025 01:09:57 Europe/Prague] TEST: Failed to send low minutes notification: {"user_id":40,"username":"MartanRak"}
[15-Feb-2025 01:09:59 Europe/Prague] TEST: Failed to send low minutes notification: {"user_id":4,"username":"matyastroup"}
[15-Feb-2025 01:09:59 Europe/Prague] TEST: Failed to send low minutes notification: {"user_id":38,"username":"VojtaDornak"}
[15-Feb-2025 01:09:59 Europe/Prague] TEST: Failed to send low minutes notification: {"user_id":40,"username":"MartanRak"}

/* Main styles */
body {
  background-color: #0a0a0a;
  color: #fff;
  font-family: "Inter", sans-serif;
}

.gradient-text {
  background: linear-gradient(90deg, #4fd1c5 0%, #38b2ac 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.card {
  background: rgba(17, 17, 17, 0.7);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-5px);
  border-color: rgba(79, 209, 197, 0.5);
}

.glow {
  box-shadow: 0 0 30px rgba(79, 209, 197, 0.1);
}

.glow:hover {
  box-shadow: 0 0 40px rgba(79, 209, 197, 0.2);
}

.nav-link {
  position: relative;
  padding-bottom: 2px;
}

.nav-link::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background: #4fd1c5;
  transition: width 0.3s ease;
}

.nav-link:hover::after {
  width: 100%;
}

.button-primary {
  background: linear-gradient(90deg, #4fd1c5 0%, #38b2ac 100%);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.button-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(79, 209, 197, 0.4);
}

.stats-container {
  background: rgba(17, 17, 17, 0.5);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 2rem;
}

.background-glow {
  position: absolute;
  width: 500px;
  height: 500px;
  background: radial-gradient(circle, rgba(79, 209, 197, 0.1) 0%, transparent 70%);
  pointer-events: none;
  z-index: -1;
}
/* Mobile Navigation Styles */
@media (max-width: 768px) {
    .nav-wrapper {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px 0;
    }
    
    .mobile-menu-toggle {
        display: block;
        background: none;
        border: none;
        width: 30px;
        height: 25px;
        position: relative;
        cursor: pointer;
        z-index: 100;
    }
    
    .mobile-menu-toggle span {
        display: block;
        width: 100%;
        height: 3px;
        background-color: #0cf4ff;
        margin: 5px 0;
        transition: all 0.3s ease;
    }
    
    .mobile-menu-toggle.active span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }
    
    .mobile-menu-toggle.active span:nth-child(2) {
        opacity: 0;
    }
    
    .mobile-menu-toggle.active span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -7px);
    }
    
    .nav-menu {
        position: fixed;
        top: 0;
        right: -100%;
        width: 80%;
        height: 100vh;
        background-color: rgba(0, 0, 0, 0.95);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        transition: right 0.3s ease;
        z-index: 99;
    }
    
    .nav-menu.active {
        right: 0;
    }
    
    .nav-menu li {
        margin: 15px 0;
    }
    
    .nav-menu a {
        font-size: 18px;
        color: white;
    }
    
    .cta-button {
        margin-top: 20px;
    }
    
    body.menu-open {
        overflow: hidden;
    }
}

/* Mouse glow effect */
.mouse-glow {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 9999;
}

/* Section-specific styles */
.demo-form-section {
  background-color: #0a0a0a;
  color: #fff;
  padding: 4rem 0;
  position: relative;
  overflow: hidden;
}

.background-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.2;
  pointer-events: none;
}

.pattern {
  position: absolute;
  inset: 0;
  background: radial-gradient(ellipse at 50% 50%, #4fd1c5 0%, transparent 70%);
  opacity: 0.3;
  transform: rotate(45deg);
}

.demo-form-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.demo-form-content {
  max-width: 1000px;
  margin: 0 auto;
}

.demo-form-grid {
  display: grid;
  gap: 2rem;
  margin-top: 2rem;
}

.demo-form-text {
  font-size: 1.125rem;
}

.highlight {
  font-weight: 600;
  color: #4fd1c5;
}

.form-container {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 1rem;
  padding: 1.5rem;
}

.form-group {
  margin-bottom: 1.25rem;
}

.form-group label {
  display: block;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

.form-group input {
  width: 100%;
  padding: 0.875rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0.5rem;
  color: white;
  font-size: 1rem;
}

.form-group input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.submit-button {
  width: 100%;
  padding: 0.875rem;
  background: #4fd1c5;
  color: white;
  border: none;
  border-radius: 0.5rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.submit-button:hover:not(:disabled) {
  background: rgba(79, 209, 197, 0.8);
}

.submit-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.message {
  margin-top: 1rem;
  font-size: 0.875rem;
  text-align: center;
}

.success {
  color: #4ade80;
}

.error {
  color: #fb7185;
}

.privacy-notice {
  margin-top: 1rem;
  font-size: 0.75rem;
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
}

.privacy-notice a {
  color: inherit;
  text-decoration: underline;
  padding: 0.5rem 0;
  display: inline-block;
}

.airtable-section {
  margin-top: 3rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 1rem;
  padding: 1.5rem;
  width: 100%;
}

.airtable-container {
  position: relative;
  width: 100%;
  height: 600px;
  overflow: hidden;
  border-radius: 0.5rem;
  background: rgba(255, 255, 255, 0.05);
}

.airtable-container iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: none;
  background: white;
}

.footer {
  background-color: #111;
  color: #fff;
  padding: 3rem 0;
}

.footer-content {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.footer-section {
  flex: 1;
  margin-bottom: 2rem;
  min-width: 200px;
}

.footer-section h3 {
  color: #4fd1c5;
  margin-bottom: 1rem;
}

.footer-section ul {
  list-style-type: none;
  padding: 0;
}

.footer-section ul li {
  margin-bottom: 0.5rem;
}

.footer-section ul li a {
  color: #ccc;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-section ul li a:hover {
  color: #4fd1c5;
}

.social-icons {
  display: flex;
  gap: 1rem;
}

.social-icons a {
  color: #ccc;
  font-size: 1.5rem;
  transition: color 0.3s ease;
}

.social-icons a:hover {
  color: #4fd1c5;
}

.footer-bottom {
  text-align: center;
  padding-top: 2rem;
  margin-top: 2rem;
  border-top: 1px solid #333;
}

/* New section styles */
.cost-comparison-section {
  padding: 4rem 0;
  background-color: #0a0a0a;
}

.cost-card {
  background: rgba(17, 17, 17, 0.7);
  border-radius: 1rem;
  padding: 2rem;
  transition: all 0.3s ease;
}

.cost-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(79, 209, 197, 0.2);
}

.benefits-section {
  padding: 4rem 0;
  background-color: #0a0a0a;
}

.benefit-card {
  background: rgba(17, 17, 17, 0.7);
  border-radius: 1rem;
  padding: 1.5rem;
  transition: all 0.3s ease;
}

.benefit-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(79, 209, 197, 0.2);
}

.why-choose-section {
  padding: 4rem 0;
  background-color: #0a0a0a;
}

.why-choose-card {
  background: rgba(17, 17, 17, 0.7);
  border-radius: 1rem;
  padding: 1.5rem;
  transition: all 0.3s ease;
}

.why-choose-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(79, 209, 197, 0.2);
}

.case-studies-section {
  padding: 4rem 0;
  background-color: #0a0a0a;
}

.case-study-card {
  background: rgba(17, 17, 17, 0.7);
  border-radius: 1rem;
  padding: 2rem;
  transition: all 0.3s ease;
}

.case-study-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(79, 209, 197, 0.2);
}

.implementation-section {
  padding: 4rem 0;
  background-color: #0a0a0a;
}

.implementation-step {
  background: rgba(17, 17, 17, 0.7);
  border-radius: 1rem;
  padding: 1.5rem;
  transition: all 0.3s ease;
}

.implementation-step:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(79, 209, 197, 0.2);
}

.faq-section {
  padding: 4rem 0;
  background-color: #0a0a0a;
}

.faq-card {
  background: rgba(17, 17, 17, 0.7);
  border-radius: 1rem;
  padding: 1.5rem;
  transition: all 0.3s ease;
}

.faq-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(79, 209, 197, 0.2);
}

.showcase-card {
  position: relative;
  background: rgba(17, 17, 17, 0.3);
  border-radius: 1rem;
  overflow: hidden;
  transition: transform 0.3s ease;
}

.showcase-card::before {
  content: "";
  position: absolute;
  inset: -1px;
  background: linear-gradient(90deg, rgba(79, 209, 197, 0.2) 0%, rgba(56, 178, 172, 0.2) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: inherit;
  z-index: -1;
}

.showcase-card:hover {
  transform: translateY(-4px);
}

.showcase-card:hover::before {
  opacity: 1;
}

.community-card {
  position: relative;
  background-color: rgba(30, 30, 46, 0.15);
  border: 1px solid rgba(42, 42, 60, 0.3);
  border-radius: 0.75rem;
  overflow: hidden;
}

.community-card::before {
  content: "";
  position: absolute;
  inset: -1px;
  background: linear-gradient(90deg, rgba(79, 209, 197, 0.3) 0%, rgba(56, 178, 172, 0.3) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: inherit;
  z-index: -1;
}

.community-card:hover::before {
  opacity: 0.1;
}

.community-image {
  transition: opacity 0.3s ease;
}

.community-image:hover {
  opacity: 0.9;
}

.icon-bg {
  background: rgba(79, 209, 197, 0.1);
}

.cta-button {
  transition: all 0.3s ease;
  background: linear-gradient(90deg, #4fd1c5 0%, #38b2ac 100%);
}

.cta-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(79, 209, 197, 0.2);
  opacity: 0.9;
}

.calendar-section {
  position: relative;
  overflow: hidden;
}

.calendar-wrapper {
  background: rgba(17, 17, 17, 0.3);
  border-radius: 1rem;
  padding: 1px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(79, 209, 197, 0.1);
  transition: all 0.3s ease;
  max-width: 978px;
  margin: 0 auto;
  height: 492px; /* Exact height according to screenshot */
}

.calendar-wrapper:hover {
  border-color: rgba(79, 209, 197, 0.2);
  box-shadow: 0 0 40px rgba(79, 209, 197, 0.1);
}

.calendar-container {
  width: 100%;
  height: 100%;
  border-radius: 1rem;
  overflow: hidden;
}

.calendar-container > iframe {
  width: 100% !important;
  height: 100% !important;
  border: none !important;
  background: transparent !important;
}

.table-wrapper {
  position: relative;
  width: 100%;
  background: #000000;
  border-radius: 16px;
  overflow: hidden;
  padding: 20px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.table-header {
  color: #40e6d2;
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  font-weight: 500;
}

.iframe-container {
  position: relative;
  width: 100%;
  height: 600px;
  border-radius: 8px;
  overflow: hidden;
  background: #000000;
}

/* Dark look for the table */
.iframe-container iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: none;
  filter: invert(1) hue-rotate(180deg) brightness(0.9) contrast(1.1);
  background: transparent;
}

/* Overlay to maintain dark look */
.iframe-container::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  background: rgba(0, 0, 0, 0.1);
  z-index: 1;
}

/* Responsive styles */
@media (min-width: 768px) {
  .demo-form-grid {
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
  }
}

@media (max-width: 767px) {
  .demo-form-section {
    padding: 2rem 0;
  }

  .demo-form-text {
    font-size: 1rem;
  }

  .form-container {
    padding: 1.25rem;
  }

  .airtable-container {
    height: 300px;
  }

  .footer-content {
    flex-direction: column;
  }

  .footer-section {
    margin-bottom: 2rem;
  }

  .iframe-container {
    height: 400px;
  }

  .table-header {
    font-size: 1.25rem;
  }

  .table-wrapper {
    padding: 15px;
  }

  .calendar-wrapper {
    height: 460px;
    border-radius: 0.75rem;
  }

  .calendar-container {
    border-radius: 0.75rem;
  }
}

@media (max-width: 640px) {
  .calendar-wrapper {
    height: 440px;
  }
}


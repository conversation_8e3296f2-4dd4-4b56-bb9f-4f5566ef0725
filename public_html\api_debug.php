<?php
require_once 'config.php';
require_once 'api_functions.php';
require_once 'error_log.php';

header('Content-Type: text/html; charset=utf-8');

function testApiConnection() {
    $apiConfig = getCurrentUserApiKey();
    if (!$apiConfig) {
        echo "<div style='color: red;'>Chyba: Nepodařilo se získat API klíč.</div>";
        return;
    }

    $apiKey = $apiConfig['key'];
    $apiUrl = $apiConfig['url'];

    echo "<h2>Test připojení k API</h2>";
    echo "API URL: " . htmlspecialchars($apiUrl) . "<br>";
    echo "API Klíč: " . substr(htmlspecialchars($apiKey), 0, 5) . "...<br>";

    try {
        $endDate = date('Y-m-d');
        $startDate = date('Y-m-d', strtotime('-30 days'));
        $calls = getCallHistoryFromAPI($apiKey, $apiUrl, $startDate, $endDate);

        echo "<div style='color: green;'>Připojení k API úspěšné. Počet získaných záznamů: " . count($calls) . "</div>";

        if (!empty($calls)) {
            echo "<h3>Ukázka dat z API:</h3>";
            echo "<pre>";
            print_r(array_slice($calls, 0, 3));
            echo "</pre>";
        }
    } catch (Exception $e) {
        echo "<div style='color: red;'>Chyba při připojení k API: " . htmlspecialchars($e->getMessage()) . "</div>";
    }
}

function checkDatabaseConnection() {
    echo "<h2>Kontrola připojení k databázi</h2>";
    
    try {
        $mysqli = getDbConnection();
        echo "<div style='color: green;'>Připojení k databázi úspěšné.</div>";
        
        $tables = ['vapi_calls', 'analytics', 'peak_hours', 'top_reasons'];
        foreach ($tables as $table) {
            $result = $mysqli->query("SELECT COUNT(*) as count FROM $table");
            if ($result) {
                $row = $result->fetch_assoc();
                echo "Počet záznamů v tabulce $table: " . $row['count'] . "<br>";
            } else {
                echo "<div style='color: red;'>Chyba při čtení z tabulky $table: " . $mysqli->error . "</div>";
            }
        }
    } catch (Exception $e) {
        echo "<div style='color: red;'>Chyba při připojení k databázi: " . htmlspecialchars($e->getMessage()) . "</div>";
    }
}

function testDataProcessing() {
    echo "<h2>Test zpracování dat</h2>";
    
    try {
        $analytics = getAnalytics();
        $peakHours = getPeakHours();
        $topReasons = getTopReasons();
        $metrics = getMetrics($analytics);

        echo "<h3>Analytics data:</h3>";
        echo "<pre>";
        print_r(array_slice($analytics['data'], 0, 3));
        echo "</pre>";

        echo "<h3>Peak Hours data:</h3>";
        echo "<pre>";
        print_r(array_slice($peakHours['data'], 0, 3));
        echo "</pre>";

        echo "<h3>Top Reasons data:</h3>";
        echo "<pre>";
        print_r(array_slice($topReasons, 0, 3, true));
        echo "</pre>";

        echo "<h3>Metrics data:</h3>";
        echo "<pre>";
        print_r($metrics);
        echo "</pre>";

    } catch (Exception $e) {
        echo "<div style='color: red;'>Chyba při zpracování dat: " . htmlspecialchars($e->getMessage()) . "</div>";
    }
}

function checkErrorLogs() {
    echo "<h2>Kontrola error logů</h2>";
    
    $logFile = __DIR__ . '/app.log';
    if (file_exists($logFile)) {
        $logs = file_get_contents($logFile);
        $lastLogs = array_slice(explode("\n", $logs), -20);
        echo "<h3>Posledních 20 záznamů z logu:</h3>";
        echo "<pre>";
        foreach ($lastLogs as $log) {
            echo htmlspecialchars($log) . "\n";
        }
        echo "</pre>";
    } else {
        echo "<div style='color: red;'>Log soubor nenalezen.</div>";
    }
}

// Spuštění testů
echo "<html><head><title>API Debug</title></head><body>";
echo "<h1>Diagnostika Dentibot API</h1>";

testApiConnection();
echo "<hr>";
checkDatabaseConnection();
echo "<hr>";
testDataProcessing();
echo "<hr>";
checkErrorLogs();

echo "</body></html>";









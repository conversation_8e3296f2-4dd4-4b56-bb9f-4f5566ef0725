<?php
/**
 * CGM Časová razítka - S<PERSON>r<PERSON><PERSON><PERSON> č<PERSON>ových razítek
 * Implementace RFC 3161 Time-Stamp Protocol pro zdravotnickou dokumentaci
 */

require_once __DIR__ . '/../config.php';

class TimestampManager {
    private $db;
    private $tsa_urls = [
        'primary' => 'http://timestamp.digicert.com',
        'backup' => 'http://timestamp.sectigo.com',
        'fallback' => 'http://timestamp.globalsign.com/scripts/timstamp.dll'
    ];
    
    public function __construct() {
        $this->db = getDbConnection();
    }
    
    /**
     * Vytvoří časové razítko pro dokument
     * @param int $document_id ID dokumentu
     * @param string $document_hash Hash dokumentu
     * @return array Výsledek operace
     */
    public function createTimestamp($document_id, $document_hash) {
        try {
            // Ověření existence dokumentu
            $stmt = $this->db->prepare("SELECT id, title FROM medical_documents WHERE id = ?");
            $stmt->bind_param("i", $document_id);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if (!$result->fetch_assoc()) {
                return ['success' => false, 'error' => 'Dokument nebyl nalezen'];
            }
            
            // Vytvoření timestamp requestu
            $timestamp_request = $this->createTimestampRequest($document_hash);
            
            // Odeslání na TSA server
            $timestamp_response = $this->sendTimestampRequest($timestamp_request);
            
            if (!$timestamp_response['success']) {
                return $timestamp_response;
            }
            
            // Uložení časového razítka do databáze
            $timestamp_hash = hash('sha256', $timestamp_response['data']);
            
            $stmt = $this->db->prepare("
                INSERT INTO document_timestamps 
                (document_id, timestamp_data, timestamp_hash, tsa_url, certificate_info, expires_at) 
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            
            $expires_at = date('Y-m-d H:i:s', strtotime('+10 years'));
            
            $stmt->bind_param("isssss", 
                $document_id, 
                $timestamp_response['data'], 
                $timestamp_hash,
                $timestamp_response['tsa_url'],
                $timestamp_response['certificate_info'],
                $expires_at
            );
            
            if ($stmt->execute()) {
                $timestamp_id = $this->db->insert_id;
                
                // Záznam do audit logu
                $this->logDocumentAction($document_id, $_SESSION['user_id'] ?? 1, 'timestamped', 
                    'Časové razítko vytvořeno s ID: ' . $timestamp_id);
                
                return [
                    'success' => true,
                    'timestamp_id' => $timestamp_id,
                    'timestamp_hash' => $timestamp_hash,
                    'created_at' => date('Y-m-d H:i:s')
                ];
            } else {
                return ['success' => false, 'error' => 'Chyba při ukládání časového razítka'];
            }
            
        } catch (Exception $e) {
            error_log("Timestamp creation error: " . $e->getMessage());
            return ['success' => false, 'error' => 'Systémová chyba při vytváření časového razítka'];
        }
    }
    
    /**
     * Ověří platnost časového razítka
     * @param int $timestamp_id ID časového razítka
     * @return array Výsledek ověření
     */
    public function verifyTimestamp($timestamp_id) {
        try {
            $stmt = $this->db->prepare("
                SELECT dt.*, md.document_hash, md.title 
                FROM document_timestamps dt
                JOIN medical_documents md ON dt.document_id = md.id
                WHERE dt.id = ?
            ");
            $stmt->bind_param("i", $timestamp_id);
            $stmt->execute();
            $result = $stmt->get_result();
            $timestamp = $result->fetch_assoc();
            
            if (!$timestamp) {
                return ['success' => false, 'error' => 'Časové razítko nebylo nalezeno'];
            }
            
            // Kontrola platnosti certifikátu
            $is_valid = $this->validateTimestampCertificate($timestamp['timestamp_data']);
            
            // Kontrola expirace
            $is_expired = strtotime($timestamp['expires_at']) < time();
            
            // Kontrola integrity
            $calculated_hash = hash('sha256', $timestamp['timestamp_data']);
            $integrity_ok = ($calculated_hash === $timestamp['timestamp_hash']);
            
            return [
                'success' => true,
                'is_valid' => $is_valid && !$is_expired && $integrity_ok,
                'is_expired' => $is_expired,
                'integrity_ok' => $integrity_ok,
                'created_at' => $timestamp['created_at'],
                'expires_at' => $timestamp['expires_at'],
                'tsa_url' => $timestamp['tsa_url'],
                'document_title' => $timestamp['title']
            ];
            
        } catch (Exception $e) {
            error_log("Timestamp verification error: " . $e->getMessage());
            return ['success' => false, 'error' => 'Chyba při ověřování časového razítka'];
        }
    }
    
    /**
     * Vytvoří timestamp request podle RFC 3161
     */
    private function createTimestampRequest($document_hash) {
        // Zjednodušená implementace - v produkci by měla být plně RFC 3161 kompatibilní
        $nonce = random_bytes(8);
        $request_data = [
            'version' => 1,
            'messageImprint' => [
                'hashAlgorithm' => 'sha256',
                'hashedMessage' => $document_hash
            ],
            'nonce' => bin2hex($nonce),
            'certReq' => true,
            'tsaPolicyId' => '1.2.3.4.5'
        ];
        
        return base64_encode(json_encode($request_data));
    }
    
    /**
     * Odešle timestamp request na TSA server
     */
    private function sendTimestampRequest($request) {
        foreach ($this->tsa_urls as $name => $url) {
            try {
                // Pro demonstraci vytvoříme mock response
                // V produkci by se použil skutečný TSA server
                $mock_response = $this->createMockTimestampResponse($request, $url);
                
                if ($mock_response['success']) {
                    return $mock_response;
                }
                
            } catch (Exception $e) {
                error_log("TSA request failed for $url: " . $e->getMessage());
                continue;
            }
        }
        
        return ['success' => false, 'error' => 'Všechny TSA servery nedostupné'];
    }
    
    /**
     * Vytvoří mock timestamp response pro demonstraci
     */
    private function createMockTimestampResponse($request, $tsa_url) {
        $timestamp_data = [
            'version' => 1,
            'policy' => '1.2.3.4.5',
            'messageImprint' => json_decode(base64_decode($request), true)['messageImprint'],
            'serialNumber' => random_int(1000000, 9999999),
            'genTime' => date('Y-m-d\TH:i:s\Z'),
            'accuracy' => ['seconds' => 1],
            'ordering' => false,
            'tsa' => [
                'directoryName' => 'CN=Dentibot TSA,O=Dentibot,C=CZ'
            ]
        ];
        
        $certificate_info = json_encode([
            'issuer' => 'CN=Dentibot TSA,O=Dentibot,C=CZ',
            'subject' => 'CN=Dentibot TSA,O=Dentibot,C=CZ',
            'serial' => bin2hex(random_bytes(16)),
            'valid_from' => date('Y-m-d H:i:s'),
            'valid_to' => date('Y-m-d H:i:s', strtotime('+10 years'))
        ]);
        
        return [
            'success' => true,
            'data' => base64_encode(json_encode($timestamp_data)),
            'tsa_url' => $tsa_url,
            'certificate_info' => $certificate_info
        ];
    }
    
    /**
     * Validuje certifikát časového razítka
     */
    private function validateTimestampCertificate($timestamp_data) {
        // Zjednodušená validace - v produkci by se ověřoval skutečný certifikát
        try {
            $data = json_decode(base64_decode($timestamp_data), true);
            return isset($data['genTime']) && isset($data['tsa']);
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * Zaznamenává akci do audit logu
     */
    private function logDocumentAction($document_id, $user_id, $action, $details = null) {
        $stmt = $this->db->prepare("
            INSERT INTO document_audit_log (document_id, user_id, action, details, ip_address, user_agent) 
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        
        $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
        
        $stmt->bind_param("iissss", $document_id, $user_id, $action, $details, $ip_address, $user_agent);
        $stmt->execute();
    }
    
    /**
     * Získá všechna časová razítka pro dokument
     */
    public function getDocumentTimestamps($document_id) {
        $stmt = $this->db->prepare("
            SELECT * FROM document_timestamps 
            WHERE document_id = ? 
            ORDER BY created_at DESC
        ");
        $stmt->bind_param("i", $document_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $timestamps = [];
        while ($row = $result->fetch_assoc()) {
            $timestamps[] = $row;
        }
        
        return $timestamps;
    }
}

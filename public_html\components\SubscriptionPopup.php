<?php
class SubscriptionPopup {
    private $currentPlan;

    public function __construct($currentPlan) {
        $this->currentPlan = $currentPlan;
    }

    public function render() {
        ob_start();
        ?>
        <div id="subscriptionPopup"></div>
        <script src="https://unpkg.com/react@17/umd/react.production.min.js"></script>
        <script src="https://unpkg.com/react-dom@17/umd/react-dom.production.min.js"></script>
        <script src="https://unpkg.com/babel-standalone@6/babel.min.js"></script>
        <script type="text/babel">
            const currentPlan = <?php echo json_encode($this->currentPlan); ?>;
            
            function SubscriptionPopup({ currentPlan, onClose }) {
                const [interval, setInterval] = React.useState("monthly");

                const plans = [
                    {
                        name: "Základní",
                        price: {
                            monthly: "3000 Kč",
                            yearly: "30000 Kč",
                        },
                        features: ["Až 500 minut hovorů měsíčně", "E-mailová podpora", "Základní funkce Dentibota"],
                        url: {
                            monthly: "https://buy.stripe.com/9AQ3eYg8Q4MrgCI3ce",
                            yearly: "https://buy.stripe.com/4gw5n67Ck6UzaekeUX",
                        },
                    },
                    {
                        name: "Pokročilý",
                        price: {
                            monthly: "5000 Kč",
                            yearly: "54000 Kč",
                        },
                        features: [
                            "Až 1000 minut hovorů měsíčně",
                            "Podpora v rámci Discord skupiny",
                            "Pokročilé funkce Dentibota",
                            "Prioritní zpracování",
                        ],
                        url: {
                            monthly: "https://buy.stripe.com/fZe3eY3m45Qv2LS8wA",
                            yearly: "https://buy.stripe.com/aEUbLu0a45Qv8c85km",
                        },
                    },
                    {
                        name: "Enterprise",
                        price: {
                            monthly: "Individuální cena",
                            yearly: "Individuální cena",
                        },
                        features: ["Neomezené minuty hovorů", "Dedikovaná podpora", "Vlastní integrace", "SLA"],
                        url: {
                            monthly: "https://tknurture.com/inteligentni-voicebot-pro-zubare-dentibot",
                            yearly: "https://tknurture.com/inteligentni-voicebot-pro-zubare-dentibot",
                        },
                    },
                ];

                return (
                    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                        <div className="bg-gray-900 text-white p-6 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto relative">
                            <button
                                id="closeSubscriptionPopup"
                                onClick={onClose}
                                className="absolute top-4 right-4 text-gray-400 hover:text-white"
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    className="h-6 w-6"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                            <h2 className="text-2xl font-bold mb-6">Vyberte si plán předplatného</h2>

                            <div className="mb-8 flex justify-center">
                                <div className="inline-flex rounded-md shadow-sm bg-gray-800" role="group">
                                    <button
                                        onClick={() => setInterval("monthly")}
                                        className={`px-4 py-2 text-sm font-medium rounded-l-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                                            interval === "monthly" ? "bg-blue-600 text-white" : "text-gray-400"
                                        }`}
                                    >
                                        Měsíční
                                    </button>
                                    <button
                                        onClick={() => setInterval("yearly")}
                                        className={`px-4 py-2 text-sm font-medium rounded-r-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                                            interval === "yearly" ? "bg-blue-600 text-white" : "text-gray-400"
                                        }`}
                                    >
                                        Roční
                                    </button>
                                </div>
                            </div>

                            <div className="grid md:grid-cols-3 gap-6">
                                {plans.map((plan) => (
                                    <div
                                        key={plan.name}
                                        className={`bg-gray-800 rounded-lg overflow-hidden ${plan.name === "Pokročilý" ? "border-2 border-yellow-500" : ""}`}
                                    >
                                        <div className="p-6">
                                            <h3 className="text-xl font-semibold text-center mb-4">{plan.name}</h3>
                                            <div className="text-center mb-6">
                                                <p className="text-3xl font-bold">{plan.price[interval]}</p>
                                                <p className="text-gray-400">/ {interval === "monthly" ? "měsíc" : "rok"}</p>
                                            </div>
                                            <ul className="mb-6 space-y-2">
                                                {plan.features.map((feature, index) => (
                                                    <li key={index} className="flex items-center">
                                                        <svg
                                                            className="w-4 h-4 mr-2 text-green-500"
                                                            fill="none"
                                                            stroke="currentColor"
                                                            viewBox="0 0 24 24"
                                                            xmlns="http://www.w3.org/2000/svg"
                                                        >
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7"></path>
                                                        </svg>
                                                        {feature}
                                                    </li>
                                                ))}
                                            </ul>
                                        </div>
                                        <div className="px-6 pb-6">
                                            <a
                                                href={plan.url[interval]}
                                                className={`block w-full text-center ${
                                                    plan.name === "Pokročilý"
                                                        ? "bg-yellow-500 hover:bg-yellow-600"
                                                        : plan.name === "Enterprise"
                                                            ? "bg-purple-500 hover:bg-purple-600"
                                                            : "bg-blue-500 hover:bg-blue-600"
                                                } text-white py-2 px-4 rounded-full transition duration-200`}
                                            >
                                                {plan.name === "Enterprise" ? "Kontaktujte nás" : "Vybrat plán"}
                                            </a>
                                        </div>
                                    </div>
                                ))}
                            </div>

                            <div className="mt-8 text-center">
                                <p className="text-lg">
                                    Vaše aktuální předplatné: <strong className="text-blue-400">{currentPlan}</strong>
                                </p>
                            </div>
                        </div>
                    </div>
                );
            }
            
            ReactDOM.render(
                <SubscriptionPopup
                    currentPlan={currentPlan}
                    onClose={() => {
                        document.getElementById('subscriptionPopupContainer').style.display = 'none';
                    }}
                />,
                document.getElementById('subscriptionPopup')
            );
        </script>
        <?php
        return ob_get_clean();
    }
}
?>


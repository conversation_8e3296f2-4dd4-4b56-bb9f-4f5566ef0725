<?php
require_once 'config.php';
require_once 'error_log.php';
require_once 'patient_lookup_functions.php';

// Define the function first
function initializeApiSettings() {
    if (!isset($_SESSION['user_id'])) {
        return;
    }
    
    $apiConfig = getCurrentUserApiKey();
    if ($apiConfig) {
        $_SESSION['api_settings'] = $apiConfig;
        writeErrorLog('API settings initialized', [
            'baserow_token_exists' => !empty($apiConfig['baserow_api_token']),
            'baserow_table_exists' => !empty($apiConfig['baserow_table_id'])
        ]);
    }
}

// Then call it
initializeApiSettings();

// Kontrola přihlášení
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

$currentPage = 'call_history';

// Pagination
$limit = 10;
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$offset = ($page - 1) * $limit;

// Sorting
$validColumns = ['id', 'from_number', 'to_number', 'created_at', 'duration', 'assistant_id'];
$sortColumn = isset($_GET['sort']) && in_array($_GET['sort'], $validColumns) ? $_GET['sort'] : 'created_at';
$sortOrder = isset($_GET['order']) && strtolower($_GET['order']) === 'asc' ? 'ASC' : 'DESC';

// Search
$search = isset($_GET['search']) ? trim($_GET['search']) : '';

$user_id = $_SESSION['user_id'];
$apiConfig = getCurrentUserApiKey();
$assistant_id = $apiConfig['assistant_id'] ?? null;

$calls = [];
$error = null;
$totalPages = 1;

try {
    if (!$assistant_id) {
        $error = "Assistant ID is not set. Please configure it in the settings.";
    } else {
        // Get data from session
        $allCalls = $_SESSION['vapi_calls_data'] ?? [];
        
        // Ensure all required fields have default values
        $allCalls = array_map(function($call) {
            return array_merge([
                'id' => 'N/A',
                'display_name' => 'N/A',
                'from_number' => 'N/A',
                'to_number' => 'N/A',
                'created_at' => 'N/A',
                'duration' => 0,
                'assistant_id' => 'N/A',
                'status' => 'N/A',
                'direction' => 'N/A',
                'ended_reason' => 'N/A',
                'transcript' => 'N/A'
            ], $call ?? []);
        }, $allCalls);
        
        // Filter calls based on search
        if (!empty($search)) {
            $allCalls = array_filter($allCalls, function($call) use ($search) {
                return stripos($call['from_number'], $search) !== false ||
                       stripos($call['to_number'], $search) !== false;
            });
        }
        
        // Sort calls
        usort($allCalls, function($a, $b) use ($sortColumn, $sortOrder) {
            $aVal = $a[$sortColumn] ?? '';
            $bVal = $b[$sortColumn] ?? '';
            
            if ($sortOrder === 'ASC') {
                return $aVal <=> $bVal;
            } else {
                return $bVal <=> $aVal;
            }
        });
        
        // Calculate pagination
        $totalCalls = count($allCalls);
        $totalPages = ceil($totalCalls / $limit);
        
        // Get paginated subset of calls
        $calls = array_slice($allCalls, $offset, $limit);
        
        // Add display names for each call
        foreach ($calls as &$call) {
            $patientName = lookupPatientName($call['from_number']);
            $call['display_name'] = $patientName ?: ($call['from_number'] ?: 'N/A');
        }
    }
} catch (Exception $e) {
    writeErrorLog("Error in call_history.php: " . $e->getMessage());
    $error = "An error occurred while fetching call history. Please try again later.";
}

// Přidejte tuto funkci před HTML kód
function getCallStatusLabel($call) {
    $status = $call['status'] ?? '';
    $endedReason = $call['ended_reason'] ?? '';
    
    if ($status === 'ended') {
        if ($endedReason === 'transferred-call-not-answered') {
            return '<span class="px-2 py-1 rounded-full bg-yellow-500/20 text-yellow-500">Nepřijatý přepojený hovor</span>';
        } elseif ($endedReason === 'transferred') {
            return '<span class="px-2 py-1 rounded-full bg-green-500/20 text-green-500">Úspěšně přepojeno</span>';
        } elseif ($endedReason === 'customer-ended-call') {
            return '<span class="px-2 py-1 rounded-full bg-blue-500/20 text-blue-500">Ukončeno pacientem</span>';
        }
        return '<span class="px-2 py-1 rounded-full bg-green-500/20 text-green-500">Dokončeno</span>';
    } elseif ($status === 'failed') {
        return '<span class="px-2 py-1 rounded-full bg-red-500/20 text-red-500">Selhalo</span>';
    }
    
    return '<span class="px-2 py-1 rounded-full bg-gray-500/20 text-gray-500">Neznámý stav</span>';
}

// Rest of the HTML remains the same as in the original file
?>
<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Historie hovorů - Dentibot</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <style>
        [x-cloak] { 
            display: none !important; 
        }
        body {
            background-color: #1A202C;
            color: white;
        }
        .custom-table {
            background-color: #1E2A3B;
        }
        .custom-table th {
            color: #94A3B8;
            border-bottom: 1px solid #2D3748;
            padding: 12px 24px;
            font-weight: normal;
            font-size: 0.75rem;
            text-transform: uppercase;
        }
        .custom-table td {
            color: #E2E8F0;
            border-bottom: 1px solid #2D3748;
            padding: 12px 24px;
        }
        .btn-primary {
            background-color: #00B8A9;
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 0.875rem;
        }
        .btn-primary:hover {
            background-color: #009B8E;
        }
        .form-input {
            background-color: #1E2A3B;
            border: 1px solid #2D3748;
            color: white;
            padding: 0.5rem 0.75rem;
            border-radius: 0.375rem;
            width: 100%;
            font-size: 0.875rem;
        }
        .form-input:focus {
            outline: none;
            border-color: #00B8A9;
        }
        /* Přidejte tyto styly do existující style sekce */
        .status-badge {
            @apply px-2 py-1 rounded-full text-sm font-medium;
        }
        .status-transferred-not-answered {
            @apply bg-yellow-500/20 text-yellow-500;
        }
        .status-transferred {
            @apply bg-green-500/20 text-green-500;
        }
        .status-ended {
            @apply bg-blue-500/20 text-blue-500;
        }
        .status-failed {
            @apply bg-red-500/20 text-red-500;
        }
    </style>
</head>
<body class="min-h-screen">
    <div x-data="callHistoryApp()" class="flex min-h-screen">
        <?php include 'sidebar.php'; ?>

        <div class="flex-1 p-6">
            <div class="max-w-[1400px] mx-auto space-y-6">
                <div class="flex justify-between items-center">
                    <h1 class="text-2xl font-semibold text-white">Historie hovorů</h1>
                    <form method="POST" action="sync_vapi_data.php" class="inline-block">
                        <button type="submit" name="manual_sync" class="btn-primary">
                            Aktualizovat data
                        </button>
                    </form>
                </div>

                <?php if ($error): ?>
                    <div class="bg-red-500/10 border border-red-500 text-red-500 px-4 py-3 rounded relative mb-4" role="alert">
                        <span class="block sm:inline"><?php echo htmlspecialchars($error); ?></span>
                    </div>
                <?php endif; ?>

                <?php if (empty($calls)): ?>
                    <div class="bg-blue-500/10 border border-blue-500 text-blue-500 px-4 py-3 rounded relative mb-4" role="alert">
                        <span class="block sm:inline">Žádná data k zobrazení. Zkuste kliknout na tlačítko "Aktualizovat data".</span>
                    </div>
                <?php endif; ?>

                <form action="" method="GET" class="mb-4">
                    <div class="flex">
                        <input type="text" 
                               name="search" 
                               placeholder="Hledat podle čísla" 
                               value="<?php echo htmlspecialchars($search); ?>" 
                               class="form-input flex-grow rounded-l-md">
                        <button type="submit" class="btn-primary rounded-r-md">
                            Hledat
                        </button>
                    </div>
                </form>

                <div class="bg-[#1E2A3B] rounded-lg overflow-hidden">
                    <table class="min-w-full divide-y divide-gray-700">
                        <thead class="bg-gray-800">
                            <tr>
                                <?php
                                $columns = [
                                    'id' => 'ID',
                                    'display_name' => 'Pacient',
                                    'from_number' => 'Od',
                                    'to_number' => 'Komu',
                                    'created_at' => 'Vytvořeno',
                                    'duration' => 'Délka hovoru',
                                    'assistant_id' => 'Assistant ID'
                                ];
                                foreach ($columns as $column => $label):
                                    $newOrder = ($column === $sortColumn && $sortOrder === 'ASC') ? 'DESC' : 'ASC';
                                    $sortIcon = ($column === $sortColumn) ? ($sortOrder === 'ASC' ? '▲' : '▼') : '';
                                ?>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                                    <a href="?sort=<?php echo $column; ?>&order=<?php echo $newOrder; ?>&search=<?php echo urlencode($search); ?>" class="hover:text-white">
                                        <?php echo htmlspecialchars($label); ?> <?php echo $sortIcon; ?>
                                    </a>
                                </th>
                                <?php endforeach; ?>
                                <!-- V hlavičce tabulky -->
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                                    Status hovoru
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Akce</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-700">
                            <?php foreach ($calls as $call): ?>
                                <tr class="hover:bg-gray-700">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                                        <?php echo htmlspecialchars($call['id'] ?? 'N/A'); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                                        <?php echo htmlspecialchars($call['display_name'] ?? 'N/A'); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                                        <?php echo htmlspecialchars($call['from_number'] ?? 'N/A'); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                                        <?php echo htmlspecialchars($call['to_number'] ?? 'N/A'); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                                        <?php echo htmlspecialchars($call['created_at'] ?? 'N/A'); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                                        <?php 
                                        if (isset($call['duration']) && is_numeric($call['duration'])) {
                                            echo sprintf('%02d:%02d', floor($call['duration'] / 60), ($call['duration'] % 60));
                                        } else {
                                            echo 'N/A';
                                        }
                                        ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                                        <?php echo htmlspecialchars($call['assistant_id'] ?? 'N/A'); ?>
                                    </td>
                                    <!-- V těle tabulky, před sloupcem "Akce" -->
                                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                                        <?php echo getCallStatusLabel($call); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <button type="button" 
                                                class="text-[#00B8A9] hover:text-[#009B8E]" 
                                                @click="showCallDetails(<?php echo htmlspecialchars(json_encode($call)); ?>)">
                                            Zobrazit detaily
                                        </button>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <?php if ($totalPages > 1): ?>
                    <div class="mt-4 flex justify-center">
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                            <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                <a href="?page=<?php echo $i; ?>&sort=<?php echo $sortColumn; ?>&order=<?php echo $sortOrder; ?>&search=<?php echo urlencode($search); ?>" 
                                   class="relative inline-flex items-center px-4 py-2 border border-gray-700 bg-gray-800 text-sm font-medium text-gray-300 hover:bg-gray-700 <?php echo $i === $page ? 'bg-gray-700 text-white' : ''; ?>">
                                    <?php echo $i; ?>
                                </a>
                            <?php endfor; ?>
                        </nav>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Modal -->
        <div x-show="showModal" 
             x-cloak
             @keydown.escape.window="closeCallDetails()"
             class="fixed z-10 inset-0 overflow-y-auto" 
             aria-labelledby="modal-title" 
             role="dialog" 
             aria-modal="true">
            <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"
                 @click.self="closeCallDetails()">
                <div x-show="showModal" 
                     x-transition:enter="ease-out duration-300" 
                     x-transition:enter-start="opacity-0" 
                     x-transition:enter-end="opacity-100" 
                     x-transition:leave="ease-in duration-200" 
                     x-transition:leave-start="opacity-100" 
                     x-transition:leave-end="opacity-0" 
                     class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" 
                     @click="closeCallDetails()"
                     aria-hidden="true"></div>

                <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
                
                <div x-show="showModal" 
                     x-transition:enter="ease-out duration-300" 
                     x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" 
                     x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100" 
                     x-transition:leave="ease-in duration-200" 
                     x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100" 
                     x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" 
                     class="inline-block align-bottom bg-gray-800 rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6"
                     @click.stop>
                    <div class="absolute top-0 right-0 pt-4 pr-4">
                        <button type="button" 
                                @click="closeCallDetails()" 
                                class="bg-gray-800 rounded-md text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <span class="sr-only">Zavřít</span>
                            <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>

                    <div class="sm:flex sm:items-start">
                        <div class="mt-3 text-center sm:mt-0 sm:text-left w-full">
                            <h3 class="text-lg leading-6 font-medium text-white" id="modal-title">
                                Detaily hovoru
                            </h3>
                            <div class="mt-4">
                                <div id="callDetailsContent" class="text-sm text-gray-300 space-y-3"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
    function callHistoryApp() {
        return {
            showModal: false,
            selectedCall: null,

            showCallDetails(call) {
                this.selectedCall = call;
                this.showModal = true;
                this.$nextTick(() => {
                    this.updateModalContent();
                });
            },

            closeCallDetails() {
                this.showModal = false;
                setTimeout(() => {
                    this.selectedCall = null;
                }, 300);
            },

            updateModalContent() {
                if (!this.selectedCall) return;

                const call = this.selectedCall;
                const duration = call.duration ? 
                    `${Math.floor(call.duration / 60)}:${String(call.duration % 60).padStart(2, '0')}` : 
                    'N/A';
                
                const content = `
                    <div class="grid gap-3">
                        <div class="grid grid-cols-3 items-center gap-4">
                            <span class="font-medium">ID:</span>
                            <span class="col-span-2">${this.escapeHtml(call.id)}</span>
                        </div>
                        <div class="grid grid-cols-3 items-center gap-4">
                            <span class="font-medium">Pacient:</span>
                            <span class="col-span-2">${this.escapeHtml(call.display_name)}</span>
                        </div>
                        <div class="grid grid-cols-3 items-center gap-4">
                            <span class="font-medium">Od:</span>
                            <span class="col-span-2">${this.escapeHtml(call.from_number)}</span>
                        </div>
                        <div class="grid grid-cols-3 items-center gap-4">
                            <span class="font-medium">Komu:</span>
                            <span class="col-span-2">${this.escapeHtml(call.to_number)}</span>
                        </div>
                        <div class="grid grid-cols-3 items-center gap-4">
                            <span class="font-medium">Vytvořeno:</span>
                            <span class="col-span-2">${this.escapeHtml(call.created_at)}</span>
                        </div>
                        <div class="grid grid-cols-3 items-center gap-4">
                            <span class="font-medium">Status:</span>
                            <span class="col-span-2">${this.escapeHtml(call.status || 'N/A')}</span>
                        </div>
                        <div class="grid grid-cols-3 items-center gap-4">
                            <span class="font-medium">Směr:</span>
                            <span class="col-span-2">${this.escapeHtml(call.direction || 'N/A')}</span>
                        </div>
                        <div class="grid grid-cols-3 items-center gap-4">
                            <span class="font-medium">Délka hovoru:</span>
                            <span class="col-span-2">${duration}</span>
                        </div>
                        <div class="grid grid-cols-3 items-center gap-4">
                            <span class="font-medium">Důvod ukončení:</span>
                            <span class="col-span-2">${this.escapeHtml(call.ended_reason || 'N/A')}</span>
                        </div>
                        <div class="grid grid-cols-3 items-center gap-4">
                            <span class="font-medium">Přepis:</span>
                            <span class="col-span-2">${this.escapeHtml(call.transcript || 'N/A')}</span>
                        </div>
                        <div class="grid grid-cols-3 items-center gap-4">
                            <span class="font-medium">Assistant ID:</span>
                            <span class="col-span-2">${this.escapeHtml(call.assistant_id || 'N/A')}</span>
                        </div>
                    </div>
                `;
                
                document.getElementById('callDetailsContent').innerHTML = content;
            },

            escapeHtml(unsafe) {
                if (unsafe === null || unsafe === undefined) return 'N/A';
                return unsafe
                    .toString()
                    .replace(/&/g, "&amp;")
                    .replace(/</g, "&lt;")
                    .replace(/>/g, "&gt;")
                    .replace(/"/g, "&quot;")
                    .replace(/'/g, "&#039;");
            }
        }
    }
    </script>
</body>
</html>

<script>
// V funkci updateModalContent() přidejte do content stringu:
function callHistoryApp() {
        return {
            showModal: false,
            selectedCall: null,

            showCallDetails(call) {
                this.selectedCall = call;
                this.showModal = true;
                this.$nextTick(() => {
                    this.updateModalContent();
                });
            },

            closeCallDetails() {
                this.showModal = false;
                setTimeout(() => {
                    this.selectedCall = null;
                }, 300);
            },

            updateModalContent() {
                if (!this.selectedCall) return;

                const call = this.selectedCall;
                const duration = call.duration ? 
                    `${Math.floor(call.duration / 60)}:${String(call.duration % 60).padStart(2, '0')}` : 
                    'N/A';
                
                const content = `
                    <div class="grid gap-3">
                        <div class="grid grid-cols-3 items-center gap-4">
                            <span class="font-medium">ID:</span>
                            <span class="col-span-2">${this.escapeHtml(call.id)}</span>
                        </div>
                        <div class="grid grid-cols-3 items-center gap-4">
                            <span class="font-medium">Pacient:</span>
                            <span class="col-span-2">${this.escapeHtml(call.display_name)}</span>
                        </div>
                        <div class="grid grid-cols-3 items-center gap-4">
                            <span class="font-medium">Od:</span>
                            <span class="col-span-2">${this.escapeHtml(call.from_number)}</span>
                        </div>
                        <div class="grid grid-cols-3 items-center gap-4">
                            <span class="font-medium">Komu:</span>
                            <span class="col-span-2">${this.escapeHtml(call.to_number)}</span>
                        </div>
                        <div class="grid grid-cols-3 items-center gap-4">
                            <span class="font-medium">Vytvořeno:</span>
                            <span class="col-span-2">${this.escapeHtml(call.created_at)}</span>
                        </div>
                        <div class="grid grid-cols-3 items-center gap-4">
                            <span class="font-medium">Status:</span>
                            <span class="col-span-2">${this.escapeHtml(call.status || 'N/A')}</span>
                        </div>
                        <div class="grid grid-cols-3 items-center gap-4">
                            <span class="font-medium">Směr:</span>
                            <span class="col-span-2">${this.escapeHtml(call.direction || 'N/A')}</span>
                        </div>
                        <div class="grid grid-cols-3 items-center gap-4">
                            <span class="font-medium">Délka hovoru:</span>
                            <span class="col-span-2">${duration}</span>
                        </div>
                        <div class="grid grid-cols-3 items-center gap-4">
                            <span class="font-medium">Důvod ukončení:</span>
                            <span class="col-span-2">${this.escapeHtml(call.ended_reason || 'N/A')}</span>
                        </div>
                        <div class="grid grid-cols-3 items-center gap-4">
                            <span class="font-medium">Přepis:</span>
                            <span class="col-span-2">${this.escapeHtml(call.transcript || 'N/A')}</span>
                        </div>
                        <div class="grid grid-cols-3 items-center gap-4">
                            <span class="font-medium">Assistant ID:</span>
                            <span class="col-span-2">${this.escapeHtml(call.assistant_id || 'N/A')}</span>
                        </div>
                        <div class="grid grid-cols-3 items-center gap-4 mt-4">
                            <span class="font-medium">Status hovoru:</span>
                            <span class="col-span-2">${this.getCallStatusLabel(call)}</span>
                        </div>
                        <div class="grid grid-cols-3 items-center gap-4">
                            <span class="font-medium">Důvod ukončení:</span>
                            <span class="col-span-2">${this.getEndReasonLabel(call.ended_reason)}</span>
                        </div>
                    </div>
                `;
                
                document.getElementById('callDetailsContent').innerHTML = content;
            },

            escapeHtml(unsafe) {
                if (unsafe === null || unsafe === undefined) return 'N/A';
                return unsafe
                    .toString()
                    .replace(/&/g, "&amp;")
                    .replace(/</g, "&lt;")
                    .replace(/>/g, "&gt;")
                    .replace(/"/g, "&quot;")
                    .replace(/'/g, "&#039;");
            },

            // Přidejte tyto pomocné funkce do callHistoryApp:
            getCallStatusLabel(call) {
                const status = call.status || '';
                const endedReason = call.ended_reason || '';
                
                if (status === 'ended') {
                    if (endedReason === 'transferred-call-not-answered') {
                        return 'Nepřijatý přepojený hovor';
                    } else if (endedReason === 'transferred') {
                        return 'Úspěšně přepojeno';
                    } else if (endedReason === 'customer-ended-call') {
                        return 'Ukončeno pacientem';
                    }
                    return 'Dokončeno';
                } else if (status === 'failed') {
                    return 'Selhalo';
                }
                return 'Neznámý stav';
            },

            getEndReasonLabel(reason) {
                const reasons = {
                    'transferred-call-not-answered': 'Přepojený hovor nebyl přijat',
                    'transferred': 'Hovor byl úspěšně přepojen',
                    'customer-ended-call': 'Pacient ukončil hovor',
                    'customer-busy': 'Pacient byl nedostupný',
                    'assistant-ended-call': 'Asistent ukončil hovor',
                    'customer-did-not-give-microphone-permission': 'Pacient nepovolil mikrofon',
                    'twilio-failed-to-connect-call': 'Chyba připojení hovoru',
                    'silence-timed-out': 'Hovor byl ukončen kvůli dlouhé neaktivitě'
                };
                
                return reasons[reason] || reason || 'Neznámý důvod';
            }
        }
    }
</script>


"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import {
  BarChart2,
  Phone,
  MessageSquare,
  Users,
  Calendar,
  MessageCircle,
  CreditCard,
  Settings,
  LogOut,
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"

const navigation = [
  { name: "<PERSON><PERSON><PERSON><PERSON>", href: "/", icon: BarChart2 },
  { name: "Historie hovorů", href: "/historie-hovoru", icon: Phone },
  { name: "Hromadné SMS", href: "/hromadne-sms", icon: MessageSquare },
  { name: "<PERSON><PERSON><PERSON> pacientů", href: "/seznam-pacientu", icon: Users },
  {
    name: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
    href: "/kalendar",
    icon: Calendar,
    badge: "pracujeme na tom",
  },
  {
    name: "Omnichannel",
    href: "/omnichannel",
    icon: MessageCircle,
    badge: "pracujeme na tom",
  },
  {
    name: "Platby",
    href: "/platby",
    icon: CreditCard,
  },
  {
    name: "Na<PERSON><PERSON><PERSON>",
    href: "/nastaveni",
    icon: Settings,
    badge: "přístup pouze na požádání",
  },
]

export function Sidebar() {
  const pathname = usePathname()

  return (
    <div className="w-64 min-h-screen bg-card border-r flex flex-col">
      <div className="flex items-center px-6 py-4 border-b">
        <Link href="/" className="flex items-center space-x-2">
          <div className="h-8 w-8 rounded-full bg-primary flex items-center justify-center text-primary-foreground font-bold">
            D
          </div>
          <span className="text-primary font-semibold text-xl">Dentibot</span>
        </Link>
      </div>

      <nav className="mt-6 px-4 flex-1">
        {navigation.map((item) => {
          const isActive = pathname === item.href
          return (
            <Link
              key={item.name}
              href={item.href}
              className={cn(
                "flex items-center px-2 py-2 mt-2 text-sm rounded-md group relative",
                isActive
                  ? "text-primary bg-primary/10 font-medium"
                  : "text-muted-foreground hover:text-foreground hover:bg-muted",
              )}
            >
              <item.icon className="mr-3 h-5 w-5 flex-shrink-0" />
              {item.name}
              {item.badge && (
                <span className="absolute right-2 text-xs text-muted-foreground italic">({item.badge})</span>
              )}
            </Link>
          )
        })}
      </nav>

      <div className="p-4 border-t">
        <Button variant="ghost" className="w-full justify-start text-muted-foreground">
          <LogOut className="mr-3 h-5 w-5" />
          Odhlásit se
        </Button>
      </div>
    </div>
  )
}


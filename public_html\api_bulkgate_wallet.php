<?php
/**
 * BulkGate Wallet API Integration
 * 
 * Tento soubor obsahuje funkce pro pr<PERSON>ci s peněženka<PERSON> BulkGate.
 */

// Pokus o načtení externích souborů
@include_once __DIR__ . '/db_connection.php';
@include_once __DIR__ . '/error_log.php';

// Definice funkce getDatabaseConnection přímo v tomto souboru pro případ, že by externí soubor nebyl dostupný
if (!function_exists('getDatabaseConnection')) {
    function getDatabaseConnection() {
        static $db = null;
        
        if ($db === null) {
            $db_host = getenv('DB_HOST') ?: 'localhost';
            $db_user = getenv('DB_USER') ?: 'u345712091_dentibot';
            $db_pass = getenv('DB_PASS') ?: 'your_password_here';
            $db_name = getenv('DB_NAME') ?: 'u345712091_dentibot';
            
            $db = new mysqli($db_host, $db_user, $db_pass, $db_name);
            
            if ($db->connect_error) {
                die("Nepodařilo se připojit k databázi: " . $db->connect_error);
            }
            
            $db->set_charset("utf8mb4");
        }
        
        return $db;
    }
}

// Definice funkce writeErrorLog přímo v tomto souboru pro případ, že by externí soubor nebyl dostupný
if (!function_exists('writeErrorLog')) {
    function writeErrorLog($message, $data = [], $level = 'ERROR') {
        $logFile = __DIR__ . '/error_log.txt';
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[$timestamp][$level] $message: " . json_encode($data) . PHP_EOL;
        file_put_contents($logFile, $logMessage, FILE_APPEND);
    }
}

// Funkce pro získání konfigurace BulkGate API
function getWalletApiConfig() {
    $db = getDatabaseConnection();
    
    $stmt = $db->prepare("SELECT * FROM bulkgate_api_config WHERE api_name = 'bulkgate' AND is_active = 1 LIMIT 1");
    if (!$stmt) {
        throw new Exception("Chyba při přípravě SQL dotazu: " . $db->error);
    }
    
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        throw new Exception("Konfigurace BulkGate API nebyla nalezena");
    }
    
    $config = $result->fetch_assoc();
    $stmt->close();
    
    return $config;
}

// Funkce pro odeslání požadavku na BulkGate API
function sendWalletRequest($url, $postData) {
    $ch = curl_init();
    
    // Convert postData to JSON
    $jsonData = json_encode($postData);
    
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_POST => true,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POSTFIELDS => $jsonData,
        CURLOPT_HTTPHEADER => [
            'Content-Type: application/json',
            'Accept: application/json',
            'Content-Length: ' . strlen($jsonData)
        ],
        CURLOPT_TIMEOUT => 30,
        CURLOPT_SSL_VERIFYPEER => true,
        CURLOPT_SSL_VERIFYHOST => 2
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    
    if (curl_errno($ch)) {
        $error = curl_error($ch);
        curl_close($ch);
        throw new Exception("CURL Error: " . $error);
    }
    
    curl_close($ch);
    
    if ($httpCode !== 200) {
        $errorMessage = "HTTP Error $httpCode: ";
        if ($httpCode === 400) {
            $errorMessage .= "Neplatný požadavek";
        } elseif ($httpCode === 401) {
            $errorMessage .= "Neplatné přihlašovací údaje k API";
        } elseif ($httpCode === 403) {
            $errorMessage .= "Přístup zamítnut";
        } else {
            $errorMessage .= "Neočekávaná chyba";
        }
        
        throw new Exception($errorMessage);
    }
    
    // Parse JSON response
    $json = json_decode($response, true);
    if ($json === null) {
        throw new Exception("Neplatná JSON odpověď: " . json_last_error_msg());
    }
    
    return $json;
}

// Funkce pro vytvoření nové peněženky
function createWallet($user_id, $label, $initial_credit = 0, $is_default = false) {
    // Získání konfigurace API
    $config = getWalletApiConfig();
    
    // AKTUALIZOVANÁ URL - používáme správný endpoint pro BulkGate API
    $url = "https://api.bulkgate.com/wallet/create";
    
    // Prepare request data
    $requestData = [
        'application_id' => $config['application_id'],
        'application_token' => $config['application_token'],
        'user_id' => $user_id,
        'label' => $label,
        'initial_credit' => $initial_credit
    ];
    
    // Send request and get response
    $response = sendWalletRequest($url, $requestData);
    
    // Validate response
    if (!isset($response['data']) || !isset($response['data']['wallet_id'])) {
        throw new Exception("Neplatná odpověď od API: Chybí data nebo ID peněženky");
    }
    
    $wallet_id = $response['data']['wallet_id'];
    
    // Save wallet to database
    $db = getDatabaseConnection();
    
    // If this wallet should be default, unset default status for other wallets
    if ($is_default) {
        $stmt = $db->prepare("UPDATE user_wallets SET is_default = 0 WHERE user_id = ?");
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        $stmt->close();
        
        // Also update default wallet in users table
        $stmt = $db->prepare("UPDATE users SET default_wallet_id = ? WHERE id = ?");
        $stmt->bind_param("si", $wallet_id, $user_id);
        $stmt->execute();
        $stmt->close();
    }
    
    // Insert new wallet
    $stmt = $db->prepare("
        INSERT INTO user_wallets 
        (user_id, wallet_id, label, is_default, is_active, created_at) 
        VALUES (?, ?, ?, ?, 1, NOW())
    ");
    
    if (!$stmt) {
        throw new Exception("Chyba při přípravě SQL dotazu: " . $db->error);
    }
    
    $stmt->bind_param("issi", $user_id, $wallet_id, $label, $is_default);
    
    if (!$stmt->execute()) {
        throw new Exception("Chyba při ukládání peněženky: " . $stmt->error);
    }
    
    $stmt->close();
    
    return $wallet_id;
}

// Funkce pro získání peněženek uživatele
function getUserWallets($user_id) {
    $db = getDatabaseConnection();
    
    $stmt = $db->prepare("
        SELECT w.*, 
               COALESCE(
                   (SELECT balance FROM wallet_balances WHERE wallet_id = w.wallet_id ORDER BY created_at DESC LIMIT 1),
                   0
               ) as balance,
               'CZK' as currency
        FROM user_wallets w
        WHERE w.user_id = ? AND w.is_active = 1
        ORDER BY w.is_default DESC, w.created_at DESC
    ");
    
    if (!$stmt) {
        throw new Exception("Chyba při přípravě SQL dotazu: " . $db->error);
    }
    
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $wallets = [];
    while ($row = $result->fetch_assoc()) {
        $wallets[] = $row;
    }
    
    $stmt->close();
    
    return $wallets;
}

// Funkce pro získání výchozí peněženky uživatele
function getUserDefaultWallet($user_id) {
    $db = getDatabaseConnection();
    
    // Nejprve zkusíme získat výchozí peněženku z tabulky users
    $stmt = $db->prepare("SELECT default_wallet_id FROM users WHERE id = ?");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $userData = $result->fetch_assoc();
    $stmt->close();
    
    if ($userData && !empty($userData['default_wallet_id'])) {
        $wallet_id = $userData['default_wallet_id'];
        
        // Získáme detaily peněženky
        $stmt = $db->prepare("
            SELECT w.*, 
                   COALESCE(
                       (SELECT balance FROM wallet_balances WHERE wallet_id = w.wallet_id ORDER BY created_at DESC LIMIT 1),
                       0
                   ) as balance,
                   'CZK' as currency
            FROM user_wallets w
            WHERE w.wallet_id = ? AND w.is_active = 1
            LIMIT 1
        ");
        
        $stmt->bind_param("s", $wallet_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $wallet = $result->fetch_assoc();
        $stmt->close();
        
        if ($wallet) {
            return $wallet;
        }
    }
    
    // Pokud nemáme výchozí peněženku v tabulce users nebo ji nelze najít,
    // zkusíme najít peněženku označenou jako výchozí v tabulce user_wallets
    $stmt = $db->prepare("
        SELECT w.*, 
               COALESCE(
                   (SELECT balance FROM wallet_balances WHERE wallet_id = w.wallet_id ORDER BY created_at DESC LIMIT 1),
                   0
               ) as balance,
               'CZK' as currency
        FROM user_wallets w
        WHERE w.user_id = ? AND w.is_active = 1 AND w.is_default = 1
        LIMIT 1
    ");
    
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $wallet = $result->fetch_assoc();
    $stmt->close();
    
    if ($wallet) {
        return $wallet;
    }
    
    // Pokud stále nemáme výchozí peněženku, vrátíme první aktivní peněženku
    $stmt = $db->prepare("
        SELECT w.*, 
               COALESCE(
                   (SELECT balance FROM wallet_balances WHERE wallet_id = w.wallet_id ORDER BY created_at DESC LIMIT 1),
                   0
               ) as balance,
               'CZK' as currency
        FROM user_wallets w
        WHERE w.user_id = ? AND w.is_active = 1
        ORDER BY w.created_at DESC
        LIMIT 1
    ");
    
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $wallet = $result->fetch_assoc();
    $stmt->close();
    
    return $wallet;
}

// Funkce pro nastavení výchozí peněženky uživatele
function setUserDefaultWallet($user_id, $wallet_id) {
    $db = getDatabaseConnection();
    
    // Ověříme, zda peněženka existuje a patří uživateli
    $stmt = $db->prepare("SELECT id FROM user_wallets WHERE user_id = ? AND wallet_id = ? AND is_active = 1");
    $stmt->bind_param("is", $user_id, $wallet_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        throw new Exception("Peněženka nebyla nalezena nebo nepatří tomuto uživateli");
    }
    
    $stmt->close();
    
    // Zrušíme výchozí status u všech peněženek uživatele
    $stmt = $db->prepare("UPDATE user_wallets SET is_default = 0 WHERE user_id = ?");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $stmt->close();
    
    // Nastavíme výchozí status pro vybranou peněženku
    $stmt = $db->prepare("UPDATE user_wallets SET is_default = 1 WHERE user_id = ? AND wallet_id = ?");
    $stmt->bind_param("is", $user_id, $wallet_id);
    $stmt->execute();
    $stmt->close();
    
    // Aktualizujeme výchozí peněženku v tabulce uživatelů
    $stmt = $db->prepare("UPDATE users SET default_wallet_id = ? WHERE id = ?");
    $stmt->bind_param("si", $wallet_id, $user_id);
    $stmt->execute();
    $stmt->close();
    
    return true;
}

// Funkce pro deaktivaci peněženky
function deactivateWallet($user_id, $wallet_id) {
    $db = getDatabaseConnection();
    
    // Ověříme, zda peněženka existuje a patří uživateli
    $stmt = $db->prepare("SELECT id, is_default FROM user_wallets WHERE user_id = ? AND wallet_id = ? AND is_active = 1");
    $stmt->bind_param("is", $user_id, $wallet_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        throw new Exception("Peněženka nebyla nalezena nebo nepatří tomuto uživateli");
    }
    
    $wallet = $result->fetch_assoc();
    $stmt->close();
    
    // Deaktivujeme peněženku
    $stmt = $db->prepare("UPDATE user_wallets SET is_active = 0 WHERE user_id = ? AND wallet_id = ?");
    $stmt->bind_param("is", $user_id, $wallet_id);
    $stmt->execute();
    $stmt->close();
    
    // Pokud byla deaktivována výchozí peněženka, nastavíme jinou peněženku jako výchozí
    if ($wallet['is_default']) {
        // Najdeme jinou aktivní peněženku
        $stmt = $db->prepare("SELECT wallet_id FROM user_wallets WHERE user_id = ? AND is_active = 1 ORDER BY created_at DESC LIMIT 1");
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            $newDefault = $result->fetch_assoc();
            setUserDefaultWallet($user_id, $newDefault['wallet_id']);
        } else {
            // Pokud uživatel nemá žádné další aktivní peněženky, nastavíme default_wallet_id na NULL
            $stmt = $db->prepare("UPDATE users SET default_wallet_id = NULL WHERE id = ?");
            $stmt->bind_param("i", $user_id);
            $stmt->execute();
        }
        
        $stmt->close();
    }
    
    return true;
}

// Funkce pro aktualizaci zůstatku peněženky
function updateWalletBalance($wallet_id, $balance) {
    $db = getDatabaseConnection();
    
    // Vložíme nový záznam o zůstatku
    $stmt = $db->prepare("
        INSERT INTO wallet_balances 
        (wallet_id, balance, created_at) 
        VALUES (?, ?, NOW())
    ");
    
    if (!$stmt) {
        throw new Exception("Chyba při přípravě SQL dotazu: " . $db->error);
    }
    
    $stmt->bind_param("sd", $wallet_id, $balance);
    
    if (!$stmt->execute()) {
        throw new Exception("Chyba při ukládání zůstatku peněženky: " . $stmt->error);
    }
    
    $stmt->close();
    
    return true;
}

// Funkce pro synchronizaci zůstatků peněženek
function syncWalletBalances($user_id) {
    // Získání peněženek uživatele
    $wallets = getUserWallets($user_id);
    
    if (empty($wallets)) {
        return true; // Uživatel nemá žádné peněženky
    }
    
    // Získání konfigurace API
    $config = getWalletApiConfig();
    
    // AKTUALIZOVANÁ URL - používáme správný endpoint pro BulkGate API
    $url = "https://api.bulkgate.com/wallet/info";
    
    foreach ($wallets as $wallet) {
        // Prepare request data
        $requestData = [
            'application_id' => $config['application_id'],
            'application_token' => $config['application_token'],
            'wallet_id' => $wallet['wallet_id']
        ];
        
        try {
            // Send request and get response
            $response = sendWalletRequest($url, $requestData);
            
            // Validate response
            if (!isset($response['data']) || !isset($response['data']['balance'])) {
                writeErrorLog('Wallet Balance Sync Error', [
                    'error' => 'Neplatná odpověď od API: Chybí data nebo zůstatek',
                    'wallet_id' => $wallet['wallet_id'],
                    'response' => $response
                ]);
                continue;
            }
            
            $balance = $response['data']['balance'];
            
            // Update wallet balance
            updateWalletBalance($wallet['wallet_id'], $balance);
            
        } catch (Exception $e) {
            writeErrorLog('Wallet Balance Sync Error', [
                'error' => $e->getMessage(),
                'wallet_id' => $wallet['wallet_id']
            ]);
        }
    }
    
    return true;
}
?>
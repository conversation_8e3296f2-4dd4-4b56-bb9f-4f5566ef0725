"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>bsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { PageHeader } from "@/components/page-header"
import { SubscriptionOverview } from "@/components/payment/subscription-overview"
import { PaymentMethods } from "@/components/payment/payment-methods"
import { TransactionHistory } from "@/components/payment/transaction-history"
import { OneTimePayment } from "@/components/payment/one-time-payment"
import { SubscriptionPlans } from "@/components/payment/subscription-plans"
import { PaymentProvider } from "@/components/payment/payment-provider"

export default function PaymentDashboard() {
  const [activeTab, setActiveTab] = useState("overview")

  return (
    <PaymentProvider>
      <div className="container mx-auto p-6 max-w-7xl">
        <PageHeader
          heading="Platby a předplatné"
          subheading="Spravujte své platební metody, p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> a zobrazujte historii transakcí"
        />

        <Tabs defaultValue="overview" className="mt-6" onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2 md:grid-cols-5">
            <TabsTrigger value="overview">Přehled</TabsTrigger>
            <TabsTrigger value="subscription">Předplatné</TabsTrigger>
            <TabsTrigger value="payment-methods">Platební metody</TabsTrigger>
            <TabsTrigger value="one-time">Jednorázové platby</TabsTrigger>
            <TabsTrigger value="history">Historie</TabsTrigger>
          </TabsList>

          <div className="mt-6">
            <TabsContent value="overview" className="space-y-6">
              <SubscriptionOverview />
              <Card>
                <CardHeader>
                  <CardTitle>Poslední transakce</CardTitle>
                  <CardDescription>Přehled vašich posledních plateb</CardDescription>
                </CardHeader>
                <CardContent>
                  <TransactionHistory limit={5} />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="subscription" className="space-y-6">
              <SubscriptionPlans />
            </TabsContent>

            <TabsContent value="payment-methods" className="space-y-6">
              <PaymentMethods />
            </TabsContent>

            <TabsContent value="one-time" className="space-y-6">
              <OneTimePayment />
            </TabsContent>

            <TabsContent value="history" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Historie transakcí</CardTitle>
                  <CardDescription>Kompletní přehled všech vašich plateb</CardDescription>
                </CardHeader>
                <CardContent>
                  <TransactionHistory />
                </CardContent>
              </Card>
            </TabsContent>
          </div>
        </Tabs>
      </div>
    </PaymentProvider>
  )
}


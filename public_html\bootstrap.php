<?php
session_start();

require_once 'config.php';
require_once 'airtable_functions.php';
require_once 'error_log.php';

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 0);

// Set default timezone
date_default_timezone_set('Europe/Prague');

// Custom error handler
function customErrorHandler($errno, $errstr, $errfile, $errline) {
    writeErrorLog('PHP Error', [
        'errno' => $errno,
        'errstr' => $errstr,
        'errfile' => $errfile,
        'errline' => $errline
    ]);
    return true;
}
set_error_handler("customErrorHandler");

// Custom exception handler
function customExceptionHandler($exception) {
    writeErrorLog('Uncaught Exception', [
        'message' => $exception->getMessage(),
        'file' => $exception->getFile(),
        'line' => $exception->getLine(),
        'trace' => $exception->getTraceAsString()
    ]);
    http_response_code(500);
    echo json_encode(['error' => 'An unexpected error occurred']);
    exit;
}
set_exception_handler("customExceptionHandler");


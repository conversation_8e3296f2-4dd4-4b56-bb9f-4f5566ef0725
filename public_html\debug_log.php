<?php
function writeBaserowDebugLog($message, $data = null) {
    $logFile = __DIR__ . '/baserow_debug.log';
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[{$timestamp}] {$message}\n";
    
    if ($data !== null) {
        $logMessage .= "Data: " . print_r($data, true) . "\n";
    }
    
    $logMessage .= str_repeat('-', 50) . "\n";
    
    // Append to file
    file_put_contents($logFile, $logMessage, FILE_APPEND);
    
    // Also output to screen (only during debugging)
    echo "<pre>" . htmlspecialchars($logMessage) . "</pre>";
}

// Test the logging immediately
writeBaserowDebugLog("Debug log initialization", ['time' => date('Y-m-d H:i:s'), 'file' => __FILE__]);


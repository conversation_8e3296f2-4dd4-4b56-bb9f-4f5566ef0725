<?php
require_once 'config.php';
require_once 'error_log.php';
require_once 'subscription_functions.php';

function syncDashboardMinutes($user_id, $minutes_used) {
    global $mysqli;
    
    try {
        // Start transaction
        $mysqli->begin_transaction();

        // Update the used_minutes in the users table
        $stmt = $mysqli->prepare("
            UPDATE users 
            SET used_minutes = ?,
                last_minutes_update = CURRENT_TIMESTAMP,
                minutes_remaining = CASE 
                    WHEN custom_minute_limit IS NOT NULL THEN custom_minute_limit - ?
                    WHEN subscription_plan = 'Základní' THEN 100 - ?
                    WHEN subscription_plan = 'Pokročilý' THEN 500 - ?
                    WHEN subscription_plan = 'Enterprise' THEN custom_minute_limit - ?
                    ELSE 0 
                END
            WHERE id = ?
        ");

        if (!$stmt) {
            throw new Exception("Error preparing statement: " . $mysqli->error);
        }

        $stmt->bind_param("iiiiii", 
            $minutes_used, 
            $minutes_used, 
            $minutes_used, 
            $minutes_used, 
            $minutes_used, 
            $user_id
        );

        if (!$stmt->execute()) {
            throw new Exception("Error executing statement: " . $stmt->error);
        }

        $stmt->close();
        $mysqli->commit();

        writeErrorLog('Minutes Sync Success', [
            'user_id' => $user_id,
            'minutes_used' => $minutes_used
        ]);

        return true;

    } catch (Exception $e) {
        $mysqli->rollback();
        writeErrorLog('Minutes Sync Error', [
            'user_id' => $user_id,
            'minutes_used' => $minutes_used,
            'error' => $e->getMessage()
        ]);
        return false;
    }
}


<?php
require_once __DIR__ . '/../config.php';

function testWebhook($username = null, $customMessage = null) {
    // Use the webhook URL from config
    $webhook_url = MAKE_WEBHOOK_URL;
    
    // Pokud není zadáno u<PERSON>k<PERSON> j<PERSON>no, pou<PERSON><PERSON><PERSON>me testovací
    if ($username === null) {
        $username = 'test_user';
    }

    // Pokud není zad<PERSON> zpr<PERSON>, použijeme testovací
    if ($customMessage === null) {
        $customMessage = 'Toto je testovací zpráva pro webhook.';
    }

    // Prepare the data for the webhook
    $data = array(
        'username' => $username,
        'message' => $customMessage,
        'timestamp' => date('Y-m-d H:i:s'),
        'source' => 'DentiBot Minute Limit Alert',
        'test' => true // Flag to indicate this is a test
    );

    // Initialize cURL
    $ch = curl_init($webhook_url);
    
    // Set cURL options
    curl_setopt_array($ch, [
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => json_encode($data),
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_HTTPHEADER => [
            'Content-Type: application/json',
            'Accept: application/json'
        ]
    ]);
    
    try {
        // Execute the request
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        
        if ($response === false) {
            throw new Exception('Curl error: ' . curl_error($ch));
        }
        
        // Log the attempt
        error_log('Webhook Test: ' . json_encode([
            'username' => $username,
            'http_code' => $http_code,
            'response' => $response,
            'timestamp' => date('Y-m-d H:i:s')
        ]));
        
        return [
            'success' => ($http_code >= 200 && $http_code < 300),
            'http_code' => $http_code,
            'response' => $response,
            'sent_data' => $data
        ];
        
    } catch (Exception $e) {
        error_log('Webhook Test Error: ' . $e->getMessage());
        return [
            'success' => false,
            'error' => $e->getMessage(),
            'sent_data' => $data
        ];
        
    } finally {
        curl_close($ch);
    }
}

// Handle direct access
if (php_sapi_name() !== 'cli' && isset($_SERVER['REQUEST_METHOD'])) {
    header('Content-Type: application/json');
    
    $username = $_REQUEST['username'] ?? null;
    $message = $_REQUEST['message'] ?? null;
    
    $result = testWebhook($username, $message);
    echo json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
} elseif (php_sapi_name() === 'cli') {
    // CLI usage
    $username = $argv[1] ?? null;
    $message = $argv[2] ?? null;
    
    $result = testWebhook($username, $message);
    print_r($result);
}


<?php
// Název souboru: validate-credentials.php

function validateCredentials($credentials) {
    echo "Validating Google API credentials for Dentibot\n";

    $requiredFields = ['client_id', 'project_id', 'auth_uri', 'token_uri', 'client_secret', 'redirect_uris'];
    $issues = [];

    // Check if all required fields are present
    foreach ($requiredFields as $field) {
        if (!isset($credentials['web'][$field])) {
            $issues[] = "Missing required field: $field";
        }
    }

    // Validate redirect URI
    $expectedRedirectUri = "https://dentibot.eu/google_auth_callback.php";
    if (!in_array($expectedRedirectUri, $credentials['web']['redirect_uris'])) {
        $issues[] = "Redirect URI mismatch. Expected: $expectedRedirectUri";
    }

    // Check if client secret is not empty
    if (empty($credentials['web']['client_secret'])) {
        $issues[] = "Client secret is empty";
    }

    // Output results
    if (empty($issues)) {
        echo "Credentials appear to be valid.\n";
    } else {
        echo "Issues found with credentials:\n";
        foreach ($issues as $index => $issue) {
            echo ($index + 1) . ". $issue\n";
        }
    }

    echo "\nRecommendations:\n";
    echo "1. Ensure the credentials file is stored securely and not exposed publicly.\n";
    echo "2. Verify that the redirect URI in your PHP code matches exactly: https://dentibot.eu/google_auth_callback.php\n";
    echo "3. Make sure you're using these credentials in your GoogleCalendarSync class initialization.\n";
    echo "4. Double-check that the Google Calendar API is enabled for this project in the Google Cloud Console.\n";
    echo "5. Confirm that your PHP code is using the correct path to access this credentials file.\n";
}

// Read the credentials file
$credentialsFile = __DIR__ . '/credentials.json';
if (file_exists($credentialsFile)) {
    $credentialsContent = file_get_contents($credentialsFile);
    $credentials = json_decode($credentialsContent, true);
    if ($credentials === null) {
        echo "Error decoding JSON from credentials file.\n";
    } else {
        validateCredentials($credentials);
    }
} else {
    echo "Credentials file not found at: $credentialsFile\n";
}